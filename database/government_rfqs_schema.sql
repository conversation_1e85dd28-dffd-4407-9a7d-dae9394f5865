-- =====================================================
-- GOVERNMENT RFQ SCHEMA
-- Handles RFQs issued by government entities (same as tender issuers)
-- =====================================================

-- Government RFQs (issued by government/municipal organizations)
CREATE TABLE IF NOT EXISTS government_rfqs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    rfq_id VARCHAR(100) UNIQUE NOT NULL, -- RFQ-YYYYMMDD-XXXXXXXX
    
    -- Basic Information
    title TEXT NOT NULL,
    description TEXT,
    organization VARCHAR(255) NOT NULL,
    department VARCHAR(255),
    contact_person VARCHAR(255),
    contact_email VARCHAR(255),
    contact_phone VARCHAR(50),
    
    -- Financial Information
    estimated_value DECIMAL(15,2),
    currency VARCHAR(3) DEFAULT 'ZAR',
    budget_range_min DECIMAL(15,2),
    budget_range_max DECIMAL(15,2),
    
    -- Timeline Information
    published_date TIMESTAMPTZ NOT NULL,
    closing_date TIMESTAMPTZ NOT NULL,
    quote_validity_period INTEGER DEFAULT 30, -- days
    expected_award_date TIMESTAMPTZ,
    project_start_date TIMESTAMPTZ,
    project_duration VARCHAR(100),
    
    -- Location Information
    province VARCHAR(50),
    city VARCHAR(100),
    location_details TEXT,
    delivery_location TEXT,
    geographic_scope TEXT[],
    
    -- Requirements
    category VARCHAR(100),
    subcategory VARCHAR(100),
    technical_requirements JSONB DEFAULT '{}',
    commercial_requirements JSONB DEFAULT '{}',
    compliance_requirements JSONB DEFAULT '{}',
    evaluation_criteria JSONB DEFAULT '{}',
    
    -- Documents
    specification_documents TEXT[], -- URLs to specification docs
    terms_and_conditions_url TEXT,
    additional_documents TEXT[],
    
    -- Source Information (for scraping)
    source_id VARCHAR(50), -- etenders, municipal_portal, etc.
    external_id VARCHAR(255), -- Original ID from source
    source_url TEXT,
    scraped_at TIMESTAMPTZ,
    
    -- Processing Status
    status VARCHAR(20) DEFAULT 'active', -- active, closed, cancelled, awarded
    processing_status VARCHAR(20) DEFAULT 'pending', -- pending, processed, failed
    opportunity_type VARCHAR(10) DEFAULT 'government_rfq',
    
    -- AI Analysis
    ai_analysis JSONB DEFAULT '{}',
    match_keywords TEXT[],
    complexity_score INTEGER DEFAULT 50,
    competition_level VARCHAR(20) DEFAULT 'medium',
    
    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    processed_at TIMESTAMPTZ,
    
    -- Indexes for performance
    CONSTRAINT valid_status CHECK (status IN ('active', 'closed', 'cancelled', 'awarded')),
    CONSTRAINT valid_processing_status CHECK (processing_status IN ('pending', 'processed', 'failed'))
);

-- Government RFQ Responses (quotes submitted to government RFQs)
CREATE TABLE IF NOT EXISTS government_rfq_responses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    response_id VARCHAR(100) UNIQUE NOT NULL,
    
    -- Relationships
    government_rfq_id UUID REFERENCES government_rfqs(id) ON DELETE CASCADE,
    bidder_id UUID REFERENCES users(id) ON DELETE CASCADE,
    
    -- Response Details
    total_quote_amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'ZAR',
    quote_breakdown JSONB DEFAULT '{}',
    
    -- Terms
    delivery_timeframe VARCHAR(100),
    delivery_cost DECIMAL(10,2) DEFAULT 0,
    payment_terms TEXT,
    warranty_terms TEXT,
    validity_period_days INTEGER DEFAULT 30,
    
    -- Technical Proposal
    technical_proposal JSONB DEFAULT '{}',
    methodology TEXT,
    team_composition JSONB DEFAULT '{}',
    equipment_list JSONB DEFAULT '{}',
    
    -- Compliance
    compliance_documents TEXT[],
    certifications TEXT[],
    bbbee_certificate_url TEXT,
    tax_clearance_url TEXT,
    company_registration_url TEXT,
    
    -- Submission
    response_status VARCHAR(20) DEFAULT 'draft', -- draft, submitted, under_review, accepted, rejected
    submitted_at TIMESTAMPTZ,
    submission_method VARCHAR(50), -- online, email, physical
    
    -- Evaluation
    evaluation_score DECIMAL(5,2),
    technical_score DECIMAL(5,2),
    commercial_score DECIMAL(5,2),
    compliance_score DECIMAL(5,2),
    evaluation_notes TEXT,
    evaluator_id UUID REFERENCES users(id),
    evaluated_at TIMESTAMPTZ,
    
    -- Award Information
    awarded BOOLEAN DEFAULT FALSE,
    award_amount DECIMAL(15,2),
    award_date TIMESTAMPTZ,
    award_notes TEXT,
    
    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT valid_response_status CHECK (response_status IN ('draft', 'submitted', 'under_review', 'accepted', 'rejected'))
);

-- Portfolio Balance Tracking (for psychological system)
CREATE TABLE IF NOT EXISTS portfolio_balance_tracking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    
    -- Current Portfolio Metrics
    total_activities INTEGER DEFAULT 0,
    rfq_activities INTEGER DEFAULT 0, -- bidder RFQs + government RFQ responses
    tender_activities INTEGER DEFAULT 0, -- government tender bids
    
    -- Ratios (percentages)
    current_rfq_ratio DECIMAL(5,2) DEFAULT 0, -- current % of RFQ activities
    current_tender_ratio DECIMAL(5,2) DEFAULT 0, -- current % of tender activities
    target_rfq_ratio DECIMAL(5,2) DEFAULT 60, -- target % for RFQs
    target_tender_ratio DECIMAL(5,2) DEFAULT 40, -- target % for tenders
    
    -- Balance Status
    balance_status VARCHAR(20) DEFAULT 'balanced', -- balanced, rfq_deficit, tender_deficit
    deviation_score DECIMAL(5,2) DEFAULT 0, -- how far off target (0-100)
    urgency_level VARCHAR(20) DEFAULT 'low', -- low, medium, high, critical
    
    -- Financial Impact
    missed_earnings DECIMAL(15,2) DEFAULT 0,
    potential_earnings DECIMAL(15,2) DEFAULT 0,
    optimization_opportunity DECIMAL(15,2) DEFAULT 0,
    
    -- AI Recommendations
    recommended_actions JSONB DEFAULT '[]',
    ai_suggestions JSONB DEFAULT '[]',
    psychological_triggers JSONB DEFAULT '[]',
    
    -- Tracking
    last_calculated_at TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT valid_balance_status CHECK (balance_status IN ('balanced', 'rfq_deficit', 'tender_deficit')),
    CONSTRAINT valid_urgency_level CHECK (urgency_level IN ('low', 'medium', 'high', 'critical'))
);

-- RFQ Suggestion Tracking (for AI force-feeding)
CREATE TABLE IF NOT EXISTS rfq_suggestions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    
    -- Suggestion Details
    suggestion_type VARCHAR(50), -- create_bidder_rfq, bid_government_rfq, bid_tender
    opportunity_id UUID, -- references government_rfqs.id or tenders.id
    opportunity_type VARCHAR(20), -- government_rfq, tender, bidder_rfq
    
    -- AI Analysis
    ai_confidence DECIMAL(5,2), -- 0-100
    success_probability DECIMAL(5,2), -- 0-100
    potential_earnings DECIMAL(15,2),
    time_to_complete VARCHAR(50),
    
    -- Psychological Triggers
    urgency_level VARCHAR(20),
    psychological_trigger VARCHAR(50), -- fomo, competition, balance_optimization, instant_gratification
    trigger_message TEXT,
    reasoning_factors TEXT[],
    
    -- User Response
    suggestion_status VARCHAR(20) DEFAULT 'pending', -- pending, viewed, clicked, acted, dismissed
    user_response VARCHAR(20), -- interested, not_interested, acted, ignored
    response_timestamp TIMESTAMPTZ,
    
    -- Effectiveness Tracking
    conversion_rate DECIMAL(5,2),
    engagement_score DECIMAL(5,2),
    psychological_effectiveness DECIMAL(5,2),
    
    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ,
    
    CONSTRAINT valid_suggestion_status CHECK (suggestion_status IN ('pending', 'viewed', 'clicked', 'acted', 'dismissed')),
    CONSTRAINT valid_user_response CHECK (user_response IN ('interested', 'not_interested', 'acted', 'ignored'))
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_government_rfqs_status ON government_rfqs(status);
CREATE INDEX IF NOT EXISTS idx_government_rfqs_closing_date ON government_rfqs(closing_date);
CREATE INDEX IF NOT EXISTS idx_government_rfqs_organization ON government_rfqs(organization);
CREATE INDEX IF NOT EXISTS idx_government_rfqs_category ON government_rfqs(category);
CREATE INDEX IF NOT EXISTS idx_government_rfqs_province ON government_rfqs(province);
CREATE INDEX IF NOT EXISTS idx_government_rfqs_source ON government_rfqs(source_id);

CREATE INDEX IF NOT EXISTS idx_government_rfq_responses_rfq_id ON government_rfq_responses(government_rfq_id);
CREATE INDEX IF NOT EXISTS idx_government_rfq_responses_bidder_id ON government_rfq_responses(bidder_id);
CREATE INDEX IF NOT EXISTS idx_government_rfq_responses_status ON government_rfq_responses(response_status);
CREATE INDEX IF NOT EXISTS idx_government_rfq_responses_submitted ON government_rfq_responses(submitted_at);

CREATE INDEX IF NOT EXISTS idx_portfolio_balance_user_id ON portfolio_balance_tracking(user_id);
CREATE INDEX IF NOT EXISTS idx_portfolio_balance_status ON portfolio_balance_tracking(balance_status);
CREATE INDEX IF NOT EXISTS idx_portfolio_balance_urgency ON portfolio_balance_tracking(urgency_level);

CREATE INDEX IF NOT EXISTS idx_rfq_suggestions_user_id ON rfq_suggestions(user_id);
CREATE INDEX IF NOT EXISTS idx_rfq_suggestions_status ON rfq_suggestions(suggestion_status);
CREATE INDEX IF NOT EXISTS idx_rfq_suggestions_type ON rfq_suggestions(suggestion_type);
CREATE INDEX IF NOT EXISTS idx_rfq_suggestions_expires ON rfq_suggestions(expires_at);

-- Bidder Profile Configuration (for opportunity preferences and notifications)
CREATE TABLE IF NOT EXISTS bidder_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE UNIQUE,

    -- Personal Information
    personal_info JSONB DEFAULT '{}',

    -- Business Preferences
    business_preferences JSONB DEFAULT '{}',

    -- Opportunity Configuration
    opportunity_config JSONB DEFAULT '{}',

    -- Notification Preferences
    notification_preferences JSONB DEFAULT '{}',

    -- Portfolio Settings
    portfolio_settings JSONB DEFAULT '{}',

    -- Onboarding Status
    onboarding_completed BOOLEAN DEFAULT FALSE,
    onboarding_completed_at TIMESTAMPTZ,

    -- Profile Completeness
    profile_completeness_score INTEGER DEFAULT 0, -- 0-100
    last_activity_at TIMESTAMPTZ,

    -- AI Analysis
    ai_recommendations JSONB DEFAULT '{}',
    psychological_analysis JSONB DEFAULT '{}',

    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Notification Subscriptions (for managing notification delivery)
CREATE TABLE IF NOT EXISTS notification_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,

    -- Subscription Details
    subscription_type VARCHAR(50) NOT NULL, -- tender, government_rfq, bidder_rfq, portfolio_balance
    category VARCHAR(100), -- specific category filter
    province VARCHAR(50), -- specific province filter
    min_value DECIMAL(15,2), -- minimum opportunity value
    max_value DECIMAL(15,2), -- maximum opportunity value

    -- Notification Channels
    email_enabled BOOLEAN DEFAULT TRUE,
    sms_enabled BOOLEAN DEFAULT FALSE,
    whatsapp_enabled BOOLEAN DEFAULT FALSE,
    push_enabled BOOLEAN DEFAULT TRUE,

    -- Frequency Settings
    frequency VARCHAR(20) DEFAULT 'immediate', -- immediate, hourly, daily, weekly
    last_notification_sent TIMESTAMPTZ,

    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    -- Unique constraint to prevent duplicate subscriptions
    UNIQUE(user_id, subscription_type, category, province)
);

-- User Activity Tracking (for AI recommendations)
CREATE TABLE IF NOT EXISTS user_activity_tracking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,

    -- Activity Details
    activity_type VARCHAR(50) NOT NULL, -- bid_submitted, rfq_created, opportunity_viewed, profile_updated
    opportunity_id UUID, -- references tenders.id, government_rfqs.id, or bidder_rfqs.id
    opportunity_type VARCHAR(20), -- tender, government_rfq, bidder_rfq

    -- Activity Metadata
    activity_data JSONB DEFAULT '{}',
    session_id VARCHAR(100),
    user_agent TEXT,
    ip_address INET,

    -- Timing
    activity_timestamp TIMESTAMPTZ DEFAULT NOW(),

    -- AI Analysis
    engagement_score DECIMAL(5,2), -- 0-100
    conversion_likelihood DECIMAL(5,2), -- 0-100

    -- Indexes for performance
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Profile Configuration History (for tracking changes)
CREATE TABLE IF NOT EXISTS profile_configuration_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,

    -- Change Details
    change_type VARCHAR(50) NOT NULL, -- preferences_updated, notifications_changed, portfolio_settings_modified
    field_changed VARCHAR(100),
    old_value JSONB,
    new_value JSONB,

    -- Change Context
    change_reason VARCHAR(100), -- user_initiated, ai_recommendation, onboarding, system_update
    changed_by VARCHAR(50), -- user, system, ai

    -- Metadata
    changed_at TIMESTAMPTZ DEFAULT NOW(),
    session_id VARCHAR(100)
);

-- Indexes for bidder profiles
CREATE INDEX IF NOT EXISTS idx_bidder_profiles_user_id ON bidder_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_bidder_profiles_onboarding ON bidder_profiles(onboarding_completed);
CREATE INDEX IF NOT EXISTS idx_bidder_profiles_completeness ON bidder_profiles(profile_completeness_score);
CREATE INDEX IF NOT EXISTS idx_bidder_profiles_activity ON bidder_profiles(last_activity_at);

-- Indexes for notification subscriptions
CREATE INDEX IF NOT EXISTS idx_notification_subscriptions_user_id ON notification_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_subscriptions_type ON notification_subscriptions(subscription_type);
CREATE INDEX IF NOT EXISTS idx_notification_subscriptions_active ON notification_subscriptions(is_active);
CREATE INDEX IF NOT EXISTS idx_notification_subscriptions_frequency ON notification_subscriptions(frequency);

-- Indexes for user activity tracking
CREATE INDEX IF NOT EXISTS idx_user_activity_user_id ON user_activity_tracking(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_type ON user_activity_tracking(activity_type);
CREATE INDEX IF NOT EXISTS idx_user_activity_timestamp ON user_activity_tracking(activity_timestamp);
CREATE INDEX IF NOT EXISTS idx_user_activity_opportunity ON user_activity_tracking(opportunity_id, opportunity_type);

-- Indexes for profile configuration history
CREATE INDEX IF NOT EXISTS idx_profile_config_history_user_id ON profile_configuration_history(user_id);
CREATE INDEX IF NOT EXISTS idx_profile_config_history_type ON profile_configuration_history(change_type);
CREATE INDEX IF NOT EXISTS idx_profile_config_history_timestamp ON profile_configuration_history(changed_at);

-- Comments for documentation
COMMENT ON TABLE government_rfqs IS 'RFQs issued by government and municipal organizations';
COMMENT ON TABLE government_rfq_responses IS 'Quotes submitted in response to government RFQs';
COMMENT ON TABLE portfolio_balance_tracking IS 'Tracks user portfolio balance for psychological optimization';
COMMENT ON TABLE rfq_suggestions IS 'AI-generated suggestions for RFQ activities and psychological triggers';
COMMENT ON TABLE bidder_profiles IS 'Comprehensive bidder profile configuration and preferences';
COMMENT ON TABLE notification_subscriptions IS 'User notification subscriptions and delivery preferences';
COMMENT ON TABLE user_activity_tracking IS 'Tracks user activity for AI recommendations and analytics';
COMMENT ON TABLE profile_configuration_history IS 'History of profile configuration changes';
