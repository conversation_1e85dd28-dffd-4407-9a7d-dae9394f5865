-- =====================================================
-- TENDER INGESTION & SCRAPING SYSTEM DATABASE SCHEMA
-- =====================================================

-- Enable Row Level Security
ALTER DATABASE postgres SET row_security = on;

-- =====================================================
-- TENDER SOURCES TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS tender_sources (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    source_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    url TEXT NOT NULL,
    source_type VARCHAR(50) NOT NULL CHECK (source_type IN ('government', 'municipal', 'parastatal', 'private')),
    scraping_enabled BOOLEAN DEFAULT true,
    last_scraped TIMESTAMPTZ,
    scraping_frequency INTEGER DEFAULT 60, -- minutes
    selectors JSONB NOT NULL DEFAULT '{}', -- CSS selectors for scraping
    authentication JSONB, -- Authentication details if needed
    rate_limit INTEGER DEFAULT 10, -- requests per minute
    timeout_seconds INTEGER DEFAULT 30,
    retry_attempts INTEGER DEFAULT 3,
    user_agent TEXT DEFAULT 'BidBeez Tender Bot 1.0',
    headers JSONB DEFAULT '{}',
    cookies JSONB DEFAULT '{}',
    proxy_config JSONB,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'maintenance', 'error')),
    error_count INTEGER DEFAULT 0,
    last_error TEXT,
    success_rate DECIMAL(5,2) DEFAULT 100.00,
    avg_response_time INTEGER, -- milliseconds
    total_requests INTEGER DEFAULT 0,
    successful_requests INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- INGESTION JOBS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS ingestion_jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    job_id VARCHAR(100) UNIQUE NOT NULL,
    source_id TEXT NOT NULL, -- Can be comma-separated for multiple sources
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
    priority VARCHAR(10) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    scheduled_at TIMESTAMPTZ,
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    duration_seconds INTEGER,
    tenders_found INTEGER DEFAULT 0,
    tenders_processed INTEGER DEFAULT 0,
    tenders_new INTEGER DEFAULT 0,
    tenders_updated INTEGER DEFAULT 0,
    tenders_skipped INTEGER DEFAULT 0,
    documents_downloaded INTEGER DEFAULT 0,
    documents_failed INTEGER DEFAULT 0,
    queen_bee_assignments TEXT[] DEFAULT '{}',
    errors TEXT[] DEFAULT '{}',
    warnings TEXT[] DEFAULT '{}',
    performance_metrics JSONB DEFAULT '{}',
    resource_usage JSONB DEFAULT '{}',
    configuration JSONB DEFAULT '{}',
    triggered_by VARCHAR(50) DEFAULT 'manual', -- manual, scheduled, webhook, api
    triggered_by_user UUID,
    parent_job_id UUID REFERENCES ingestion_jobs(id),
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- SCRAPED TENDERS TABLE (Raw scraped data)
-- =====================================================

CREATE TABLE IF NOT EXISTS scraped_tenders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    job_id UUID REFERENCES ingestion_jobs(id) ON DELETE CASCADE,
    source_id VARCHAR(50) NOT NULL,
    external_id VARCHAR(255) NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    organization VARCHAR(255),
    category VARCHAR(100),
    estimated_value DECIMAL(15,2),
    currency VARCHAR(3) DEFAULT 'ZAR',
    location TEXT,
    province VARCHAR(50),
    publish_date TIMESTAMPTZ,
    closing_date TIMESTAMPTZ,
    briefing_date TIMESTAMPTZ,
    briefing_location TEXT,
    reference_number VARCHAR(255),
    contact_info JSONB DEFAULT '{}',
    requirements TEXT[] DEFAULT '{}',
    documents JSONB DEFAULT '[]', -- Array of document objects
    source_url TEXT,
    detail_url TEXT,
    raw_html TEXT,
    raw_data JSONB DEFAULT '{}',
    parsing_confidence DECIMAL(5,2) DEFAULT 0.00,
    quality_score DECIMAL(5,2) DEFAULT 0.00,
    duplicate_check_hash VARCHAR(64),
    processing_status VARCHAR(20) DEFAULT 'pending' CHECK (processing_status IN ('pending', 'processing', 'processed', 'failed', 'skipped')),
    processing_errors TEXT[] DEFAULT '{}',
    queen_bee_assigned BOOLEAN DEFAULT false,
    queen_bee_assignment_id UUID,
    tender_id UUID, -- Reference to final processed tender
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(source_id, external_id),
    FOREIGN KEY (source_id) REFERENCES tender_sources(source_id)
);

-- =====================================================
-- QUEEN BEE ASSIGNMENTS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS queen_bee_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    assignment_id VARCHAR(100) UNIQUE NOT NULL,
    tender_id UUID,
    scraped_tender_id UUID REFERENCES scraped_tenders(id),
    queen_bee_id VARCHAR(100) NOT NULL,
    task_type VARCHAR(50) NOT NULL CHECK (task_type IN ('document_processing', 'compliance_check', 'analysis', 'verification', 'classification')),
    priority VARCHAR(10) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    status VARCHAR(20) DEFAULT 'assigned' CHECK (status IN ('assigned', 'accepted', 'in_progress', 'completed', 'failed', 'cancelled')),
    assigned_at TIMESTAMPTZ DEFAULT NOW(),
    accepted_at TIMESTAMPTZ,
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    deadline TIMESTAMPTZ,
    estimated_duration INTEGER, -- minutes
    actual_duration INTEGER, -- minutes
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    quality_score DECIMAL(5,2),
    client_approval BOOLEAN,
    payment_amount DECIMAL(10,2),
    payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'approved', 'paid', 'cancelled')),
    instructions TEXT,
    deliverables JSONB DEFAULT '{}',
    feedback TEXT,
    issues JSONB DEFAULT '[]',
    updates JSONB DEFAULT '[]',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- DOCUMENT DOWNLOADS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS document_downloads (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    scraped_tender_id UUID REFERENCES scraped_tenders(id) ON DELETE CASCADE,
    document_name VARCHAR(255) NOT NULL,
    document_type VARCHAR(50),
    original_url TEXT NOT NULL,
    file_path TEXT,
    file_size BIGINT,
    content_type VARCHAR(100),
    download_status VARCHAR(20) DEFAULT 'pending' CHECK (download_status IN ('pending', 'downloading', 'completed', 'failed', 'corrupted')),
    download_attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    downloaded_at TIMESTAMPTZ,
    file_hash VARCHAR(64),
    virus_scan_status VARCHAR(20) DEFAULT 'pending' CHECK (virus_scan_status IN ('pending', 'clean', 'infected', 'failed')),
    parsed_status VARCHAR(20) DEFAULT 'pending' CHECK (parsed_status IN ('pending', 'parsing', 'parsed', 'failed')),
    parsed_content TEXT,
    parsed_metadata JSONB DEFAULT '{}',
    error_message TEXT,
    retry_after TIMESTAMPTZ,
    storage_provider VARCHAR(50) DEFAULT 'local',
    storage_metadata JSONB DEFAULT '{}',
    access_permissions JSONB DEFAULT '{}',
    retention_policy VARCHAR(50) DEFAULT 'standard',
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- SCRAPING LOGS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS scraping_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    job_id UUID REFERENCES ingestion_jobs(id) ON DELETE CASCADE,
    source_id VARCHAR(50) NOT NULL,
    log_level VARCHAR(10) NOT NULL CHECK (log_level IN ('DEBUG', 'INFO', 'WARN', 'ERROR', 'FATAL')),
    message TEXT NOT NULL,
    component VARCHAR(50), -- scraper, parser, downloader, etc.
    operation VARCHAR(50), -- fetch_page, parse_tender, download_doc, etc.
    url TEXT,
    http_status INTEGER,
    response_time INTEGER, -- milliseconds
    error_code VARCHAR(50),
    stack_trace TEXT,
    context JSONB DEFAULT '{}',
    user_agent TEXT,
    ip_address INET,
    session_id VARCHAR(100),
    correlation_id VARCHAR(100),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- PERFORMANCE METRICS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS scraping_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    source_id VARCHAR(50) NOT NULL,
    metric_date DATE NOT NULL,
    requests_total INTEGER DEFAULT 0,
    requests_successful INTEGER DEFAULT 0,
    requests_failed INTEGER DEFAULT 0,
    avg_response_time INTEGER, -- milliseconds
    min_response_time INTEGER,
    max_response_time INTEGER,
    tenders_scraped INTEGER DEFAULT 0,
    documents_downloaded INTEGER DEFAULT 0,
    errors_count INTEGER DEFAULT 0,
    uptime_percentage DECIMAL(5,2) DEFAULT 100.00,
    data_quality_score DECIMAL(5,2) DEFAULT 0.00,
    duplicate_rate DECIMAL(5,2) DEFAULT 0.00,
    processing_time INTEGER, -- seconds
    bandwidth_used BIGINT DEFAULT 0, -- bytes
    storage_used BIGINT DEFAULT 0, -- bytes
    cost_estimate DECIMAL(10,2) DEFAULT 0.00,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(source_id, metric_date),
    FOREIGN KEY (source_id) REFERENCES tender_sources(source_id)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Tender Sources
CREATE INDEX IF NOT EXISTS idx_tender_sources_source_id ON tender_sources(source_id);
CREATE INDEX IF NOT EXISTS idx_tender_sources_status ON tender_sources(status);
CREATE INDEX IF NOT EXISTS idx_tender_sources_last_scraped ON tender_sources(last_scraped);

-- Ingestion Jobs
CREATE INDEX IF NOT EXISTS idx_ingestion_jobs_job_id ON ingestion_jobs(job_id);
CREATE INDEX IF NOT EXISTS idx_ingestion_jobs_status ON ingestion_jobs(status);
CREATE INDEX IF NOT EXISTS idx_ingestion_jobs_started_at ON ingestion_jobs(started_at);
CREATE INDEX IF NOT EXISTS idx_ingestion_jobs_source_id ON ingestion_jobs(source_id);

-- Scraped Tenders
CREATE INDEX IF NOT EXISTS idx_scraped_tenders_external_id ON scraped_tenders(source_id, external_id);
CREATE INDEX IF NOT EXISTS idx_scraped_tenders_job_id ON scraped_tenders(job_id);
CREATE INDEX IF NOT EXISTS idx_scraped_tenders_processing_status ON scraped_tenders(processing_status);
CREATE INDEX IF NOT EXISTS idx_scraped_tenders_closing_date ON scraped_tenders(closing_date);
CREATE INDEX IF NOT EXISTS idx_scraped_tenders_duplicate_hash ON scraped_tenders(duplicate_check_hash);

-- Queen Bee Assignments
CREATE INDEX IF NOT EXISTS idx_queen_bee_assignments_assignment_id ON queen_bee_assignments(assignment_id);
CREATE INDEX IF NOT EXISTS idx_queen_bee_assignments_queen_bee_id ON queen_bee_assignments(queen_bee_id);
CREATE INDEX IF NOT EXISTS idx_queen_bee_assignments_status ON queen_bee_assignments(status);
CREATE INDEX IF NOT EXISTS idx_queen_bee_assignments_assigned_at ON queen_bee_assignments(assigned_at);

-- Document Downloads
CREATE INDEX IF NOT EXISTS idx_document_downloads_scraped_tender_id ON document_downloads(scraped_tender_id);
CREATE INDEX IF NOT EXISTS idx_document_downloads_download_status ON document_downloads(download_status);
CREATE INDEX IF NOT EXISTS idx_document_downloads_file_hash ON document_downloads(file_hash);

-- Scraping Logs
CREATE INDEX IF NOT EXISTS idx_scraping_logs_job_id ON scraping_logs(job_id);
CREATE INDEX IF NOT EXISTS idx_scraping_logs_source_id ON scraping_logs(source_id);
CREATE INDEX IF NOT EXISTS idx_scraping_logs_log_level ON scraping_logs(log_level);
CREATE INDEX IF NOT EXISTS idx_scraping_logs_created_at ON scraping_logs(created_at);

-- Performance Metrics
CREATE INDEX IF NOT EXISTS idx_scraping_metrics_source_date ON scraping_metrics(source_id, metric_date);

-- =====================================================
-- ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE tender_sources ENABLE ROW LEVEL SECURITY;
ALTER TABLE ingestion_jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE scraped_tenders ENABLE ROW LEVEL SECURITY;
ALTER TABLE queen_bee_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_downloads ENABLE ROW LEVEL SECURITY;
ALTER TABLE scraping_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE scraping_metrics ENABLE ROW LEVEL SECURITY;

-- Basic RLS policies (can be customized based on requirements)
CREATE POLICY "Allow all operations for authenticated users" ON tender_sources
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Allow all operations for authenticated users" ON ingestion_jobs
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Allow all operations for authenticated users" ON scraped_tenders
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Allow all operations for authenticated users" ON queen_bee_assignments
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Allow all operations for authenticated users" ON document_downloads
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Allow all operations for authenticated users" ON scraping_logs
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Allow all operations for authenticated users" ON scraping_metrics
    FOR ALL USING (auth.role() = 'authenticated');

-- =====================================================
-- TRIGGERS FOR AUTOMATIC TIMESTAMPS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to all tables with updated_at
CREATE TRIGGER update_tender_sources_updated_at BEFORE UPDATE ON tender_sources
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ingestion_jobs_updated_at BEFORE UPDATE ON ingestion_jobs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_scraped_tenders_updated_at BEFORE UPDATE ON scraped_tenders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_queen_bee_assignments_updated_at BEFORE UPDATE ON queen_bee_assignments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_document_downloads_updated_at BEFORE UPDATE ON document_downloads
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- INITIAL DATA - GOVERNMENT TENDER SOURCES
-- =====================================================

INSERT INTO tender_sources (source_id, name, url, source_type, selectors) VALUES
('etenders', 'National Treasury eTenders', 'https://etenders.treasury.gov.za', 'government', '{
    "tender_list": ".tender-item",
    "title": ".tender-title",
    "organization": ".tender-org",
    "closing_date": ".closing-date",
    "reference": ".reference-number",
    "documents": ".document-links a"
}'),
('johannesburg', 'City of Johannesburg', 'https://www.joburg.org.za/tenders', 'municipal', '{
    "tender_list": ".tender-listing",
    "title": "h3.tender-title",
    "description": ".tender-description",
    "closing_date": ".tender-deadline"
}'),
('cape_town', 'City of Cape Town', 'https://www.capetown.gov.za/City-Connect/Tenders', 'municipal', '{
    "tender_list": ".tender-item",
    "title": ".tender-heading",
    "department": ".department",
    "closing_date": ".closing-date"
}'),
('sanral', 'South African National Roads Agency', 'https://www.sanral.co.za/tenders', 'parastatal', '{
    "tender_list": ".tender-row",
    "title": ".tender-title",
    "value": ".tender-value",
    "location": ".tender-location"
}'),
('eskom', 'Eskom Holdings', 'https://www.eskom.co.za/suppliers/tenders', 'parastatal', '{
    "tender_list": ".tender-notice",
    "title": "h4",
    "category": ".category",
    "closing_date": ".deadline"
}')
ON CONFLICT (source_id) DO NOTHING;

-- =====================================================
-- VIEWS FOR REPORTING
-- =====================================================

-- Daily ingestion summary
CREATE OR REPLACE VIEW daily_ingestion_summary AS
SELECT 
    DATE(created_at) as ingestion_date,
    COUNT(*) as total_jobs,
    COUNT(*) FILTER (WHERE status = 'completed') as completed_jobs,
    COUNT(*) FILTER (WHERE status = 'failed') as failed_jobs,
    SUM(tenders_processed) as total_tenders_processed,
    SUM(documents_downloaded) as total_documents_downloaded,
    AVG(duration_seconds) as avg_duration_seconds
FROM ingestion_jobs
GROUP BY DATE(created_at)
ORDER BY ingestion_date DESC;

-- Source performance summary
CREATE OR REPLACE VIEW source_performance_summary AS
SELECT 
    ts.source_id,
    ts.name,
    ts.source_type,
    ts.status,
    ts.last_scraped,
    COUNT(st.id) as total_tenders_scraped,
    COUNT(st.id) FILTER (WHERE st.processing_status = 'processed') as tenders_processed,
    COUNT(dd.id) as total_documents_downloaded,
    AVG(st.quality_score) as avg_quality_score,
    ts.success_rate,
    ts.avg_response_time
FROM tender_sources ts
LEFT JOIN scraped_tenders st ON ts.source_id = st.source_id
LEFT JOIN document_downloads dd ON st.id = dd.scraped_tender_id
GROUP BY ts.source_id, ts.name, ts.source_type, ts.status, ts.last_scraped, ts.success_rate, ts.avg_response_time
ORDER BY total_tenders_scraped DESC;

-- Queen Bee workload summary
CREATE OR REPLACE VIEW queen_bee_workload_summary AS
SELECT 
    queen_bee_id,
    COUNT(*) as total_assignments,
    COUNT(*) FILTER (WHERE status = 'assigned') as pending_assignments,
    COUNT(*) FILTER (WHERE status = 'in_progress') as active_assignments,
    COUNT(*) FILTER (WHERE status = 'completed') as completed_assignments,
    AVG(actual_duration) as avg_completion_time,
    AVG(quality_score) as avg_quality_score
FROM queen_bee_assignments
GROUP BY queen_bee_id
ORDER BY total_assignments DESC;
