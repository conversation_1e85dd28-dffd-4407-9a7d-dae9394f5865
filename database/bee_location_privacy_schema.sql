-- ===================================================================
-- BEE WORKER LOCATION PRIVACY & TRACKING SCHEMA
-- ===================================================================
-- Comprehensive database schema for bee worker location privacy controls
-- Supports POPIA compliance and granular permission management
-- ===================================================================

-- Drop existing tables if they exist (for development)
DROP TABLE IF EXISTS bee_location_history CASCADE;
DROP TABLE IF EXISTS bee_location_sharing_logs CASCADE;
DROP TABLE IF EXISTS bee_location_privacy_settings CASCADE;
DROP TABLE IF EXISTS bee_location_consent_history CASCADE;

-- ===================================================================
-- 1. BEE LOCATION PRIVACY SETTINGS
-- ===================================================================
-- Stores individual bee worker location privacy preferences
CREATE TABLE bee_location_privacy_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bee_worker_id VARCHAR(100) NOT NULL UNIQUE,
    
    -- Core tracking settings
    tracking_enabled BOOLEAN DEFAULT false,
    tracking_mode VARCHAR(20) DEFAULT 'tasks_only' CHECK (tracking_mode IN ('tasks_only', 'working_hours', 'always', 'emergency_only')),
    accuracy_level VARCHAR(20) DEFAULT 'approximate' CHECK (accuracy_level IN ('exact', 'approximate', 'city_only')),
    
    -- Sharing permissions
    share_with_clients BOOLEAN DEFAULT false,
    share_with_queen_bee BOOLEAN DEFAULT true,
    share_location_history BOOLEAN DEFAULT false,
    
    -- Data retention
    data_retention_days INTEGER DEFAULT 30 CHECK (data_retention_days BETWEEN 1 AND 365),
    
    -- Consent tracking
    consent_given_at TIMESTAMPTZ DEFAULT NOW(),
    consent_version VARCHAR(10) DEFAULT '1.0',
    last_updated TIMESTAMPTZ DEFAULT NOW(),
    updated_by VARCHAR(100),
    
    -- Privacy compliance
    popia_compliant BOOLEAN DEFAULT true,
    gdpr_compliant BOOLEAN DEFAULT true,
    
    -- Emergency overrides
    emergency_tracking_enabled BOOLEAN DEFAULT true,
    emergency_contact_sharing BOOLEAN DEFAULT true,
    
    CONSTRAINT fk_bee_worker FOREIGN KEY (bee_worker_id) REFERENCES bee_workers(bee_id) ON DELETE CASCADE
);

-- ===================================================================
-- 2. BEE LOCATION CONSENT HISTORY
-- ===================================================================
-- Tracks all consent changes for audit and compliance
CREATE TABLE bee_location_consent_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bee_worker_id VARCHAR(100) NOT NULL,
    
    -- Consent details
    consent_type VARCHAR(50) NOT NULL CHECK (consent_type IN ('initial_consent', 'settings_update', 'consent_withdrawal', 'emergency_override')),
    consent_given BOOLEAN NOT NULL,
    consent_timestamp TIMESTAMPTZ DEFAULT NOW(),
    
    -- Settings snapshot
    settings_snapshot JSONB NOT NULL,
    
    -- Context
    ip_address INET,
    user_agent TEXT,
    consent_method VARCHAR(50) DEFAULT 'web_interface',
    
    -- Legal compliance
    privacy_policy_version VARCHAR(10),
    terms_version VARCHAR(10),
    
    CONSTRAINT fk_bee_worker_consent FOREIGN KEY (bee_worker_id) REFERENCES bee_workers(bee_id) ON DELETE CASCADE
);

-- ===================================================================
-- 3. BEE LOCATION HISTORY
-- ===================================================================
-- Stores location data with privacy controls
CREATE TABLE bee_location_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bee_worker_id VARCHAR(100) NOT NULL,
    
    -- Location data
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    accuracy_meters INTEGER,
    altitude_meters INTEGER,
    
    -- Processed location data
    address TEXT,
    city VARCHAR(100),
    province VARCHAR(100),
    country VARCHAR(100) DEFAULT 'South Africa',
    
    -- Context
    timestamp TIMESTAMPTZ DEFAULT NOW(),
    location_source VARCHAR(50) DEFAULT 'gps' CHECK (location_source IN ('gps', 'network', 'manual', 'estimated')),
    
    -- Task context
    task_id VARCHAR(100),
    task_status VARCHAR(50),
    is_during_task BOOLEAN DEFAULT false,
    is_during_working_hours BOOLEAN DEFAULT false,
    
    -- Privacy controls
    shared_with_clients BOOLEAN DEFAULT false,
    shared_with_queen_bee BOOLEAN DEFAULT false,
    anonymized BOOLEAN DEFAULT false,
    
    -- Data lifecycle
    expires_at TIMESTAMPTZ,
    deleted_at TIMESTAMPTZ,
    
    CONSTRAINT fk_bee_worker_location FOREIGN KEY (bee_worker_id) REFERENCES bee_workers(bee_id) ON DELETE CASCADE,
    CONSTRAINT fk_task_location FOREIGN KEY (task_id) REFERENCES bee_tasks(task_id) ON DELETE SET NULL
);

-- ===================================================================
-- 4. BEE LOCATION SHARING LOGS
-- ===================================================================
-- Audit trail for location data access
CREATE TABLE bee_location_sharing_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bee_worker_id VARCHAR(100) NOT NULL,
    
    -- Access details
    accessed_by VARCHAR(100) NOT NULL,
    access_type VARCHAR(50) NOT NULL CHECK (access_type IN ('client_view', 'queen_bee_view', 'emergency_access', 'system_access', 'analytics')),
    access_timestamp TIMESTAMPTZ DEFAULT NOW(),
    
    -- Location context
    location_id UUID,
    location_timestamp TIMESTAMPTZ,
    accuracy_level_shared VARCHAR(20),
    
    -- Request context
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(100),
    
    -- Purpose and justification
    access_purpose TEXT,
    legal_basis VARCHAR(100),
    
    CONSTRAINT fk_bee_worker_sharing FOREIGN KEY (bee_worker_id) REFERENCES bee_workers(bee_id) ON DELETE CASCADE,
    CONSTRAINT fk_location_sharing FOREIGN KEY (location_id) REFERENCES bee_location_history(id) ON DELETE SET NULL
);

-- ===================================================================
-- INDEXES FOR PERFORMANCE
-- ===================================================================

-- Privacy settings indexes
CREATE INDEX idx_bee_privacy_worker_id ON bee_location_privacy_settings(bee_worker_id);
CREATE INDEX idx_bee_privacy_tracking_enabled ON bee_location_privacy_settings(tracking_enabled);
CREATE INDEX idx_bee_privacy_updated ON bee_location_privacy_settings(last_updated);

-- Consent history indexes
CREATE INDEX idx_bee_consent_worker_id ON bee_location_consent_history(bee_worker_id);
CREATE INDEX idx_bee_consent_timestamp ON bee_location_consent_history(consent_timestamp);
CREATE INDEX idx_bee_consent_type ON bee_location_consent_history(consent_type);

-- Location history indexes
CREATE INDEX idx_bee_location_worker_id ON bee_location_history(bee_worker_id);
CREATE INDEX idx_bee_location_timestamp ON bee_location_history(timestamp);
CREATE INDEX idx_bee_location_task_id ON bee_location_history(task_id);
CREATE INDEX idx_bee_location_expires ON bee_location_history(expires_at);
CREATE INDEX idx_bee_location_during_task ON bee_location_history(is_during_task);

-- Geospatial index for location queries
CREATE INDEX idx_bee_location_coords ON bee_location_history USING GIST (
    ST_Point(longitude, latitude)
);

-- Sharing logs indexes
CREATE INDEX idx_bee_sharing_worker_id ON bee_location_sharing_logs(bee_worker_id);
CREATE INDEX idx_bee_sharing_accessed_by ON bee_location_sharing_logs(accessed_by);
CREATE INDEX idx_bee_sharing_timestamp ON bee_location_sharing_logs(access_timestamp);
CREATE INDEX idx_bee_sharing_type ON bee_location_sharing_logs(access_type);

-- ===================================================================
-- FUNCTIONS FOR PRIVACY COMPLIANCE
-- ===================================================================

-- Function to automatically expire location data based on retention settings
CREATE OR REPLACE FUNCTION expire_location_data()
RETURNS void AS $$
BEGIN
    UPDATE bee_location_history 
    SET expires_at = NOW()
    WHERE expires_at IS NULL 
    AND timestamp < (
        NOW() - INTERVAL '1 day' * (
            SELECT COALESCE(data_retention_days, 30) 
            FROM bee_location_privacy_settings 
            WHERE bee_worker_id = bee_location_history.bee_worker_id
        )
    );
END;
$$ LANGUAGE plpgsql;

-- Function to anonymize expired location data
CREATE OR REPLACE FUNCTION anonymize_expired_location_data()
RETURNS void AS $$
BEGIN
    UPDATE bee_location_history 
    SET 
        latitude = ROUND(latitude::numeric, 2),  -- Reduce precision
        longitude = ROUND(longitude::numeric, 2),
        address = 'Anonymized',
        anonymized = true
    WHERE expires_at < NOW() 
    AND anonymized = false;
END;
$$ LANGUAGE plpgsql;

-- Function to check if location sharing is allowed
CREATE OR REPLACE FUNCTION is_location_sharing_allowed(
    p_bee_worker_id VARCHAR(100),
    p_requester_type VARCHAR(50)
)
RETURNS boolean AS $$
DECLARE
    privacy_settings RECORD;
BEGIN
    SELECT * INTO privacy_settings 
    FROM bee_location_privacy_settings 
    WHERE bee_worker_id = p_bee_worker_id;
    
    -- If no settings found or tracking disabled, deny access
    IF privacy_settings IS NULL OR NOT privacy_settings.tracking_enabled THEN
        RETURN false;
    END IF;
    
    -- Check specific sharing permissions
    CASE p_requester_type
        WHEN 'client' THEN
            RETURN privacy_settings.share_with_clients;
        WHEN 'queen_bee' THEN
            RETURN privacy_settings.share_with_queen_bee;
        WHEN 'emergency' THEN
            RETURN privacy_settings.emergency_tracking_enabled;
        ELSE
            RETURN false;
    END CASE;
END;
$$ LANGUAGE plpgsql;

-- ===================================================================
-- TRIGGERS FOR AUDIT AND COMPLIANCE
-- ===================================================================

-- Trigger to log privacy settings changes
CREATE OR REPLACE FUNCTION log_privacy_settings_change()
RETURNS trigger AS $$
BEGIN
    INSERT INTO bee_location_consent_history (
        bee_worker_id,
        consent_type,
        consent_given,
        settings_snapshot
    ) VALUES (
        NEW.bee_worker_id,
        'settings_update',
        NEW.tracking_enabled,
        row_to_json(NEW)
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_privacy_settings_change
    AFTER UPDATE ON bee_location_privacy_settings
    FOR EACH ROW
    EXECUTE FUNCTION log_privacy_settings_change();

-- Trigger to set expiration date on new location records
CREATE OR REPLACE FUNCTION set_location_expiration()
RETURNS trigger AS $$
DECLARE
    retention_days INTEGER;
BEGIN
    SELECT data_retention_days INTO retention_days
    FROM bee_location_privacy_settings
    WHERE bee_worker_id = NEW.bee_worker_id;
    
    NEW.expires_at = NEW.timestamp + INTERVAL '1 day' * COALESCE(retention_days, 30);
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_location_expiration
    BEFORE INSERT ON bee_location_history
    FOR EACH ROW
    EXECUTE FUNCTION set_location_expiration();

-- ===================================================================
-- SCHEDULED JOBS (to be set up with pg_cron or similar)
-- ===================================================================

-- Daily cleanup of expired location data
-- SELECT cron.schedule('location-cleanup', '0 2 * * *', 'SELECT expire_location_data(); SELECT anonymize_expired_location_data();');

-- ===================================================================
-- SAMPLE DATA FOR TESTING
-- ===================================================================

-- Insert sample privacy settings
INSERT INTO bee_location_privacy_settings (
    bee_worker_id,
    tracking_enabled,
    tracking_mode,
    accuracy_level,
    share_with_clients,
    share_with_queen_bee,
    share_location_history,
    data_retention_days
) VALUES 
('WB-JHB-001', true, 'tasks_only', 'approximate', true, true, false, 30),
('WB-CPT-002', true, 'working_hours', 'exact', false, true, true, 60),
('WB-DBN-003', false, 'emergency_only', 'city_only', false, false, false, 7);

-- Insert sample consent history
INSERT INTO bee_location_consent_history (
    bee_worker_id,
    consent_type,
    consent_given,
    settings_snapshot,
    privacy_policy_version,
    terms_version
) VALUES 
('WB-JHB-001', 'initial_consent', true, '{"tracking_enabled": true, "tracking_mode": "tasks_only"}', '1.0', '1.0'),
('WB-CPT-002', 'initial_consent', true, '{"tracking_enabled": true, "tracking_mode": "working_hours"}', '1.0', '1.0'),
('WB-DBN-003', 'consent_withdrawal', false, '{"tracking_enabled": false}', '1.0', '1.0');

-- ===================================================================
-- VIEWS FOR EASY ACCESS
-- ===================================================================

-- View for active bee workers with location permissions
CREATE VIEW active_bee_location_permissions AS
SELECT 
    bw.bee_id,
    bw.full_name,
    bw.is_active,
    blps.tracking_enabled,
    blps.tracking_mode,
    blps.accuracy_level,
    blps.share_with_clients,
    blps.share_with_queen_bee,
    blps.last_updated
FROM bee_workers bw
LEFT JOIN bee_location_privacy_settings blps ON bw.bee_id = blps.bee_worker_id
WHERE bw.is_active = true;

-- View for location sharing audit
CREATE VIEW location_sharing_audit AS
SELECT 
    blsl.bee_worker_id,
    bw.full_name,
    blsl.accessed_by,
    blsl.access_type,
    blsl.access_timestamp,
    blsl.access_purpose
FROM bee_location_sharing_logs blsl
JOIN bee_workers bw ON blsl.bee_worker_id = bw.bee_id
ORDER BY blsl.access_timestamp DESC;

-- ===================================================================
-- PERMISSIONS AND SECURITY
-- ===================================================================

-- Grant appropriate permissions (adjust based on your user roles)
-- GRANT SELECT, INSERT, UPDATE ON bee_location_privacy_settings TO bee_worker_role;
-- GRANT SELECT ON bee_location_history TO queen_bee_role;
-- GRANT SELECT ON location_sharing_audit TO admin_role;

-- Row Level Security (RLS) policies
ALTER TABLE bee_location_privacy_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE bee_location_history ENABLE ROW LEVEL SECURITY;

-- Policy: Bee workers can only access their own privacy settings
CREATE POLICY bee_privacy_self_access ON bee_location_privacy_settings
    FOR ALL TO bee_worker_role
    USING (bee_worker_id = current_setting('app.current_bee_id'));

-- Policy: Bee workers can only see their own location history
CREATE POLICY bee_location_self_access ON bee_location_history
    FOR SELECT TO bee_worker_role
    USING (bee_worker_id = current_setting('app.current_bee_id'));

-- ===================================================================
-- COMMENTS FOR DOCUMENTATION
-- ===================================================================

COMMENT ON TABLE bee_location_privacy_settings IS 'Stores bee worker location privacy preferences and consent settings';
COMMENT ON TABLE bee_location_consent_history IS 'Audit trail of all location consent changes for compliance';
COMMENT ON TABLE bee_location_history IS 'Location data with privacy controls and automatic expiration';
COMMENT ON TABLE bee_location_sharing_logs IS 'Audit trail of location data access for transparency';

COMMENT ON COLUMN bee_location_privacy_settings.tracking_mode IS 'When location tracking is active: tasks_only, working_hours, always, emergency_only';
COMMENT ON COLUMN bee_location_privacy_settings.accuracy_level IS 'Location precision shared: exact, approximate, city_only';
COMMENT ON COLUMN bee_location_history.expires_at IS 'Automatic expiration based on retention settings';
COMMENT ON COLUMN bee_location_sharing_logs.legal_basis IS 'Legal justification for accessing location data';

-- ===================================================================
-- END OF SCHEMA
-- ===================================================================
