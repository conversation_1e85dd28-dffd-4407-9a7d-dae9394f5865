-- =====================================================
-- PRODUCT SPECIFICATIONS MODULE - DATABASE SCHEMA
-- =====================================================
-- Creates missing tables for the Supplier Matcher + Compliance Engine
-- Integrates with existing BidBeez Supabase infrastructure

-- =====================================================
-- 1. PARSED SPECIFICATIONS TABLES
-- =====================================================

-- Main parsed specifications table
CREATE TABLE IF NOT EXISTS parsed_specifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tender_id UUID REFERENCES tenders(id) ON DELETE CASCADE,
    document_id TEXT REFERENCES document_content(document_id),
    project_name VARCHAR(255),
    tender_number VARCHAR(100),
    province VARCHAR(50),
    parsing_status VARCHAR(20) DEFAULT 'pending',
    confidence_score DECIMAL(3,2),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Bill of Quantities line items
CREATE TABLE IF NOT EXISTS boq_line_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    spec_id UUID REFERENCES parsed_specifications(id) ON DELETE CASCADE,
    line_number INTEGER,
    quantity DECIMAL(12,2),
    unit VARCHAR(20),
    description TEXT,
    unit_price DECIMAL(12,2),
    total_price DECIMAL(12,2),
    cpv_code VARCHAR(20),
    category VARCHAR(100),
    confidence_score DECIMAL(3,2),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Scope of Work requirements
CREATE TABLE IF NOT EXISTS sow_requirements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    spec_id UUID REFERENCES parsed_specifications(id) ON DELETE CASCADE,
    category VARCHAR(100),
    requirement_type VARCHAR(50),
    description TEXT,
    priority VARCHAR(20) DEFAULT 'normal',
    compliance_level VARCHAR(20) DEFAULT 'mandatory',
    technical_specs JSONB DEFAULT '{}',
    performance_criteria JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Standards and certifications required
CREATE TABLE IF NOT EXISTS specification_standards (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    spec_id UUID REFERENCES parsed_specifications(id) ON DELETE CASCADE,
    standard_type VARCHAR(20), -- SANS, ISO, ASTM, BS, etc.
    standard_code VARCHAR(50),
    standard_title TEXT,
    compliance_level VARCHAR(20) DEFAULT 'mandatory',
    section_reference TEXT,
    equivalents_allowed BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- 2. SUPPLIER PRODUCT CATALOG TABLES
-- =====================================================

-- Comprehensive supplier product database
CREATE TABLE IF NOT EXISTS supplier_products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    supplier_id UUID REFERENCES users(id) ON DELETE CASCADE,
    product_code VARCHAR(100),
    product_name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    subcategory VARCHAR(100),
    unit_of_measure VARCHAR(20),
    current_price DECIMAL(12,2),
    currency VARCHAR(3) DEFAULT 'ZAR',
    availability_status VARCHAR(20) DEFAULT 'available',
    lead_time_days INTEGER,
    minimum_order_quantity DECIMAL(10,2),
    capabilities TEXT[],
    technical_specifications JSONB DEFAULT '{}',
    performance_metrics JSONB DEFAULT '{}',
    geographic_coverage TEXT[],
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Product certifications and standards compliance
CREATE TABLE IF NOT EXISTS product_certifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID REFERENCES supplier_products(id) ON DELETE CASCADE,
    standard_type VARCHAR(20),
    standard_code VARCHAR(50),
    certificate_number VARCHAR(100),
    issuing_authority VARCHAR(255),
    issue_date DATE,
    expiry_date DATE,
    certificate_url TEXT,
    verification_status VARCHAR(20) DEFAULT 'pending',
    verified_at TIMESTAMPTZ,
    verified_by VARCHAR(255),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Product alternatives and equivalents
CREATE TABLE IF NOT EXISTS product_equivalents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    primary_product_id UUID REFERENCES supplier_products(id) ON DELETE CASCADE,
    equivalent_product_id UUID REFERENCES supplier_products(id) ON DELETE CASCADE,
    equivalence_type VARCHAR(50), -- direct, functional, performance
    equivalence_score DECIMAL(3,2),
    comparison_criteria JSONB DEFAULT '{}',
    verified_by VARCHAR(255),
    verified_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- 3. MATCHING ENGINE TABLES
-- =====================================================

-- Supplier-to-specification matching results
CREATE TABLE IF NOT EXISTS specification_matches (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    spec_id UUID REFERENCES parsed_specifications(id) ON DELETE CASCADE,
    supplier_id UUID REFERENCES users(id) ON DELETE CASCADE,
    overall_score DECIMAL(5,2),
    boq_match_score DECIMAL(5,2),
    sow_match_score DECIMAL(5,2),
    compliance_score DECIMAL(5,2),
    geographic_score DECIMAL(5,2),
    trust_score DECIMAL(5,2),
    bee_bonus DECIMAL(5,2),
    provincial_bonus DECIMAL(5,2),
    match_details JSONB DEFAULT '{}',
    confidence_level VARCHAR(20),
    match_status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Detailed line-item matching
CREATE TABLE IF NOT EXISTS line_item_matches (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    match_id UUID REFERENCES specification_matches(id) ON DELETE CASCADE,
    boq_item_id UUID REFERENCES boq_line_items(id) ON DELETE CASCADE,
    product_id UUID REFERENCES supplier_products(id) ON DELETE CASCADE,
    match_score DECIMAL(5,2),
    match_type VARCHAR(50), -- exact, equivalent, partial
    price_competitiveness DECIMAL(5,2),
    availability_score DECIMAL(5,2),
    quality_score DECIMAL(5,2),
    match_reasoning TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Matching algorithm performance tracking
CREATE TABLE IF NOT EXISTS match_performance_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    spec_id UUID REFERENCES parsed_specifications(id),
    algorithm_version VARCHAR(20),
    total_suppliers_evaluated INTEGER,
    matches_generated INTEGER,
    processing_time_ms INTEGER,
    accuracy_feedback DECIMAL(3,2),
    user_satisfaction DECIMAL(3,2),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- 4. COMPLIANCE VALIDATION TABLES
-- =====================================================

-- SABS/SANS/ISO validation results
CREATE TABLE IF NOT EXISTS compliance_validations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    supplier_id UUID REFERENCES users(id) ON DELETE CASCADE,
    spec_id UUID REFERENCES parsed_specifications(id),
    validation_type VARCHAR(50), -- sabs, sans, iso, provincial, bee
    validation_status VARCHAR(20), -- compliant, non_compliant, pending, expired
    standard_codes TEXT[],
    compliance_details JSONB DEFAULT '{}',
    risk_level VARCHAR(20),
    expiry_date DATE,
    next_review_date DATE,
    validated_by VARCHAR(255),
    validation_source VARCHAR(100),
    govchain_hash TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Provincial compliance rules
CREATE TABLE IF NOT EXISTS provincial_compliance_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    province VARCHAR(50) NOT NULL,
    rule_type VARCHAR(50),
    rule_name VARCHAR(255),
    rule_description TEXT,
    minimum_bee_level INTEGER,
    local_content_percentage DECIMAL(5,2),
    geographic_preference BOOLEAN DEFAULT false,
    effective_date DATE,
    expiry_date DATE,
    rule_details JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Certificate expiry monitoring
CREATE TABLE IF NOT EXISTS certificate_monitoring (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    supplier_id UUID REFERENCES users(id) ON DELETE CASCADE,
    certificate_type VARCHAR(100),
    certificate_number VARCHAR(100),
    expiry_date DATE,
    renewal_reminder_sent BOOLEAN DEFAULT false,
    renewal_reminder_date DATE,
    status VARCHAR(20) DEFAULT 'active',
    monitoring_enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- 5. RFQ MANAGEMENT TABLES
-- =====================================================

-- Bidder RFQ creation and management
CREATE TABLE IF NOT EXISTS bidder_rfqs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bidder_id UUID REFERENCES users(id) ON DELETE CASCADE,
    rfq_number VARCHAR(100) UNIQUE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    estimated_value DECIMAL(15,2),
    currency VARCHAR(3) DEFAULT 'ZAR',
    submission_deadline TIMESTAMPTZ,
    evaluation_criteria JSONB DEFAULT '{}',
    terms_and_conditions TEXT,
    status VARCHAR(20) DEFAULT 'draft',
    province VARCHAR(50),
    geographic_scope TEXT[],
    bee_requirements JSONB DEFAULT '{}',
    technical_requirements JSONB DEFAULT '{}',
    commercial_requirements JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- RFQ specification requirements
CREATE TABLE IF NOT EXISTS rfq_specifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    rfq_id UUID REFERENCES bidder_rfqs(id) ON DELETE CASCADE,
    spec_category VARCHAR(100),
    requirement_type VARCHAR(50),
    description TEXT,
    quantity DECIMAL(12,2),
    unit VARCHAR(20),
    technical_specs JSONB DEFAULT '{}',
    compliance_requirements TEXT[],
    evaluation_weight DECIMAL(5,2),
    mandatory BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Supplier RFQ responses
CREATE TABLE IF NOT EXISTS rfq_responses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    rfq_id UUID REFERENCES bidder_rfqs(id) ON DELETE CASCADE,
    supplier_id UUID REFERENCES users(id) ON DELETE CASCADE,
    response_status VARCHAR(20) DEFAULT 'draft',
    total_price DECIMAL(15,2),
    currency VARCHAR(3) DEFAULT 'ZAR',
    delivery_timeframe VARCHAR(100),
    technical_proposal JSONB DEFAULT '{}',
    commercial_proposal JSONB DEFAULT '{}',
    compliance_documents TEXT[],
    submitted_at TIMESTAMPTZ,
    evaluation_score DECIMAL(5,2),
    evaluation_notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- 6. INDEXES FOR PERFORMANCE
-- =====================================================

-- Parsed specifications indexes
CREATE INDEX IF NOT EXISTS idx_parsed_specs_tender_id ON parsed_specifications(tender_id);
CREATE INDEX IF NOT EXISTS idx_parsed_specs_province ON parsed_specifications(province);
CREATE INDEX IF NOT EXISTS idx_parsed_specs_status ON parsed_specifications(parsing_status);

-- BOQ line items indexes
CREATE INDEX IF NOT EXISTS idx_boq_spec_id ON boq_line_items(spec_id);
CREATE INDEX IF NOT EXISTS idx_boq_category ON boq_line_items(category);
CREATE INDEX IF NOT EXISTS idx_boq_cpv_code ON boq_line_items(cpv_code);

-- Supplier products indexes
CREATE INDEX IF NOT EXISTS idx_supplier_products_supplier_id ON supplier_products(supplier_id);
CREATE INDEX IF NOT EXISTS idx_supplier_products_category ON supplier_products(category);
CREATE INDEX IF NOT EXISTS idx_supplier_products_status ON supplier_products(availability_status);

-- Specification matches indexes
CREATE INDEX IF NOT EXISTS idx_spec_matches_spec_id ON specification_matches(spec_id);
CREATE INDEX IF NOT EXISTS idx_spec_matches_supplier_id ON specification_matches(supplier_id);
CREATE INDEX IF NOT EXISTS idx_spec_matches_score ON specification_matches(overall_score DESC);

-- Compliance validations indexes
CREATE INDEX IF NOT EXISTS idx_compliance_supplier_id ON compliance_validations(supplier_id);
CREATE INDEX IF NOT EXISTS idx_compliance_status ON compliance_validations(validation_status);
CREATE INDEX IF NOT EXISTS idx_compliance_expiry ON compliance_validations(expiry_date);

-- RFQ indexes
CREATE INDEX IF NOT EXISTS idx_rfqs_bidder_id ON bidder_rfqs(bidder_id);
CREATE INDEX IF NOT EXISTS idx_rfqs_status ON bidder_rfqs(status);
CREATE INDEX IF NOT EXISTS idx_rfqs_deadline ON bidder_rfqs(submission_deadline);

-- =====================================================
-- 7. ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE parsed_specifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE boq_line_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE sow_requirements ENABLE ROW LEVEL SECURITY;
ALTER TABLE specification_standards ENABLE ROW LEVEL SECURITY;
ALTER TABLE supplier_products ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_certifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE specification_matches ENABLE ROW LEVEL SECURITY;
ALTER TABLE compliance_validations ENABLE ROW LEVEL SECURITY;
ALTER TABLE bidder_rfqs ENABLE ROW LEVEL SECURITY;
ALTER TABLE rfq_responses ENABLE ROW LEVEL SECURITY;

-- Basic RLS policies (can be expanded based on specific requirements)
CREATE POLICY "Users can view their own specifications" ON parsed_specifications
    FOR SELECT USING (auth.uid()::text IN (
        SELECT bidder_id::text FROM bidder_tenders WHERE tender_id = parsed_specifications.tender_id
    ));

CREATE POLICY "Suppliers can view their own products" ON supplier_products
    FOR ALL USING (auth.uid() = supplier_id);

CREATE POLICY "Users can view relevant matches" ON specification_matches
    FOR SELECT USING (
        auth.uid() = supplier_id OR 
        auth.uid()::text IN (
            SELECT bidder_id::text FROM bidder_tenders bt 
            JOIN parsed_specifications ps ON bt.tender_id = ps.tender_id 
            WHERE ps.id = specification_matches.spec_id
        )
    );

-- =====================================================
-- 8. TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Update timestamps trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply update triggers
CREATE TRIGGER update_parsed_specifications_updated_at BEFORE UPDATE ON parsed_specifications FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_supplier_products_updated_at BEFORE UPDATE ON supplier_products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_specification_matches_updated_at BEFORE UPDATE ON specification_matches FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_compliance_validations_updated_at BEFORE UPDATE ON compliance_validations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_bidder_rfqs_updated_at BEFORE UPDATE ON bidder_rfqs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_rfq_responses_updated_at BEFORE UPDATE ON rfq_responses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
