-- =====================================================
-- WHATSAPP AUTO-BIDDING SYSTEM SCHEMA
-- Complete database schema for WhatsApp bid notifications and auto-bidding
-- =====================================================

-- =====================================================
-- AUTO-BID SETTINGS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS auto_bid_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    whatsapp_number VARCHAR(20) NOT NULL UNIQUE,
    auto_bid_preference VARCHAR(20) DEFAULT 'ask_first' CHECK (auto_bid_preference IN ('always', 'ask_first', 'never', 'conditions_based')),
    max_bid_value DECIMAL(15,2),
    preferred_categories TEXT[] DEFAULT '{}',
    excluded_categories TEXT[] DEFAULT '{}',
    minimum_confidence_score DECIMAL(3,2) DEFAULT 0.70 CHECK (minimum_confidence_score BETWEEN 0 AND 1),
    require_feasibility_check BOOLEAN DEFAULT true,
    notify_on_auto_bid BOOLEAN DEFAULT true,
    
    -- Advanced settings
    max_daily_auto_bids INTEGER DEFAULT 5,
    max_monthly_auto_bids INTEGER DEFAULT 50,
    working_hours_only BOOLEAN DEFAULT false,
    working_hours_start TIME DEFAULT '08:00:00',
    working_hours_end TIME DEFAULT '17:00:00',
    weekend_auto_bid BOOLEAN DEFAULT false,
    
    -- Geographic preferences
    preferred_provinces TEXT[] DEFAULT '{}',
    excluded_provinces TEXT[] DEFAULT '{}',
    max_distance_km INTEGER DEFAULT 500,
    
    -- Financial controls
    daily_spend_limit DECIMAL(15,2),
    monthly_spend_limit DECIMAL(15,2),
    require_approval_above DECIMAL(15,2),
    
    -- Notification preferences
    success_notification BOOLEAN DEFAULT true,
    failure_notification BOOLEAN DEFAULT true,
    daily_summary BOOLEAN DEFAULT true,
    
    -- Status and metadata
    is_active BOOLEAN DEFAULT true,
    last_auto_bid_at TIMESTAMPTZ,
    total_auto_bids INTEGER DEFAULT 0,
    successful_auto_bids INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- WHATSAPP MESSAGES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS whatsapp_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    message_id VARCHAR(100) NOT NULL UNIQUE, -- WhatsApp message ID
    from_number VARCHAR(20) NOT NULL,
    to_number VARCHAR(20) NOT NULL,
    message_body TEXT NOT NULL,
    message_type VARCHAR(30) DEFAULT 'unknown' CHECK (message_type IN ('bid_notification', 'tender_alert', 'deadline_reminder', 'document_available', 'user_response', 'unknown')),
    
    -- Processing status
    processing_status VARCHAR(20) DEFAULT 'pending' CHECK (processing_status IN ('pending', 'processing', 'processed', 'failed', 'ignored')),
    confidence_score DECIMAL(3,2) DEFAULT 0.00,
    
    -- Extracted data
    extracted_data JSONB DEFAULT '{}',
    tender_id VARCHAR(100),
    tender_title TEXT,
    organization TEXT,
    estimated_value DECIMAL(15,2),
    closing_date TIMESTAMPTZ,
    location TEXT,
    category TEXT,
    
    -- Auto-bid decision
    auto_bid_triggered BOOLEAN DEFAULT false,
    auto_bid_reason TEXT,
    auto_bid_result JSONB DEFAULT '{}',
    
    -- Timestamps
    whatsapp_timestamp TIMESTAMPTZ NOT NULL,
    received_at TIMESTAMPTZ DEFAULT NOW(),
    processed_at TIMESTAMPTZ,
    
    -- Metadata
    raw_webhook_data JSONB DEFAULT '{}',
    processing_errors TEXT[],
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- AUTO-BID ACTIVITIES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS auto_bid_activities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    tender_id UUID REFERENCES tenders(id) ON DELETE SET NULL,
    whatsapp_message_id UUID REFERENCES whatsapp_messages(id) ON DELETE SET NULL,
    
    -- Trigger information
    trigger_source VARCHAR(20) DEFAULT 'whatsapp' CHECK (trigger_source IN ('whatsapp', 'email', 'sms', 'api', 'manual')),
    trigger_data JSONB DEFAULT '{}',
    
    -- Auto-bid process
    auto_bid_status VARCHAR(20) DEFAULT 'initiated' CHECK (auto_bid_status IN ('initiated', 'feasibility_check', 'ai_processing', 'completed', 'failed', 'cancelled')),
    feasibility_result JSONB DEFAULT '{}',
    ai_result JSONB DEFAULT '{}',
    
    -- Results
    bid_id VARCHAR(100),
    submission_status VARCHAR(20),
    success_probability DECIMAL(5,2),
    estimated_cost DECIMAL(15,2),
    actual_cost DECIMAL(15,2),
    
    -- Economic impact
    jobs_created INTEGER DEFAULT 0,
    economic_value DECIMAL(15,2) DEFAULT 0,
    
    -- Timing
    processing_time_seconds INTEGER,
    started_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    
    -- Notifications
    user_notified BOOLEAN DEFAULT false,
    notification_sent_at TIMESTAMPTZ,
    
    -- Metadata
    error_details TEXT,
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- WHATSAPP TEMPLATES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS whatsapp_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    template_name VARCHAR(50) NOT NULL UNIQUE,
    template_type VARCHAR(30) NOT NULL CHECK (template_type IN ('confirmation', 'permission_request', 'success_notification', 'failure_notification', 'daily_summary')),
    
    -- Template content
    message_template TEXT NOT NULL,
    variables TEXT[] DEFAULT '{}', -- Available variables for template
    
    -- Localization
    language_code VARCHAR(5) DEFAULT 'en',
    
    -- Usage tracking
    usage_count INTEGER DEFAULT 0,
    last_used_at TIMESTAMPTZ,
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    
    -- Metadata
    description TEXT,
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- USER WHATSAPP PREFERENCES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS user_whatsapp_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    whatsapp_number VARCHAR(20) NOT NULL,
    
    -- Verification status
    is_verified BOOLEAN DEFAULT false,
    verification_code VARCHAR(10),
    verification_attempts INTEGER DEFAULT 0,
    verified_at TIMESTAMPTZ,
    
    -- Notification preferences
    receive_bid_notifications BOOLEAN DEFAULT true,
    receive_deadline_reminders BOOLEAN DEFAULT true,
    receive_document_alerts BOOLEAN DEFAULT true,
    receive_auto_bid_confirmations BOOLEAN DEFAULT true,
    receive_daily_summaries BOOLEAN DEFAULT false,
    receive_weekly_reports BOOLEAN DEFAULT false,
    
    -- Timing preferences
    quiet_hours_start TIME DEFAULT '22:00:00',
    quiet_hours_end TIME DEFAULT '07:00:00',
    timezone VARCHAR(50) DEFAULT 'Africa/Johannesburg',
    
    -- Language preferences
    preferred_language VARCHAR(5) DEFAULT 'en',
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    opt_out_date TIMESTAMPTZ,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(user_id, whatsapp_number)
);

-- =====================================================
-- WHATSAPP ANALYTICS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS whatsapp_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    date DATE NOT NULL,
    
    -- Message statistics
    total_messages_received INTEGER DEFAULT 0,
    bid_notifications_received INTEGER DEFAULT 0,
    messages_processed INTEGER DEFAULT 0,
    processing_errors INTEGER DEFAULT 0,
    
    -- Auto-bid statistics
    auto_bids_triggered INTEGER DEFAULT 0,
    auto_bids_successful INTEGER DEFAULT 0,
    auto_bids_failed INTEGER DEFAULT 0,
    permission_requests_sent INTEGER DEFAULT 0,
    user_approvals_received INTEGER DEFAULT 0,
    
    -- Performance metrics
    avg_processing_time_seconds DECIMAL(8,2),
    avg_confidence_score DECIMAL(3,2),
    
    -- Economic impact
    total_jobs_created INTEGER DEFAULT 0,
    total_economic_value DECIMAL(15,2) DEFAULT 0,
    
    -- User engagement
    active_users INTEGER DEFAULT 0,
    new_registrations INTEGER DEFAULT 0,
    opt_outs INTEGER DEFAULT 0,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(date)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Auto-bid settings indexes
CREATE INDEX IF NOT EXISTS idx_auto_bid_settings_user_id ON auto_bid_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_auto_bid_settings_whatsapp_number ON auto_bid_settings(whatsapp_number);
CREATE INDEX IF NOT EXISTS idx_auto_bid_settings_active ON auto_bid_settings(is_active) WHERE is_active = true;

-- WhatsApp messages indexes
CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_from_number ON whatsapp_messages(from_number);
CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_type ON whatsapp_messages(message_type);
CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_status ON whatsapp_messages(processing_status);
CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_timestamp ON whatsapp_messages(whatsapp_timestamp);
CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_tender_id ON whatsapp_messages(tender_id);

-- Auto-bid activities indexes
CREATE INDEX IF NOT EXISTS idx_auto_bid_activities_user_id ON auto_bid_activities(user_id);
CREATE INDEX IF NOT EXISTS idx_auto_bid_activities_tender_id ON auto_bid_activities(tender_id);
CREATE INDEX IF NOT EXISTS idx_auto_bid_activities_status ON auto_bid_activities(auto_bid_status);
CREATE INDEX IF NOT EXISTS idx_auto_bid_activities_started_at ON auto_bid_activities(started_at);

-- User preferences indexes
CREATE INDEX IF NOT EXISTS idx_user_whatsapp_preferences_user_id ON user_whatsapp_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_user_whatsapp_preferences_number ON user_whatsapp_preferences(whatsapp_number);
CREATE INDEX IF NOT EXISTS idx_user_whatsapp_preferences_verified ON user_whatsapp_preferences(is_verified) WHERE is_verified = true;

-- Analytics indexes
CREATE INDEX IF NOT EXISTS idx_whatsapp_analytics_date ON whatsapp_analytics(date);

-- =====================================================
-- TRIGGERS FOR AUTOMATIC TIMESTAMPS
-- =====================================================

-- Auto-bid settings trigger
CREATE OR REPLACE FUNCTION update_auto_bid_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_auto_bid_settings_updated_at
    BEFORE UPDATE ON auto_bid_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_auto_bid_settings_updated_at();

-- WhatsApp messages trigger
CREATE OR REPLACE FUNCTION update_whatsapp_messages_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_whatsapp_messages_updated_at
    BEFORE UPDATE ON whatsapp_messages
    FOR EACH ROW
    EXECUTE FUNCTION update_whatsapp_messages_updated_at();

-- Auto-bid activities trigger
CREATE OR REPLACE FUNCTION update_auto_bid_activities_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_auto_bid_activities_updated_at
    BEFORE UPDATE ON auto_bid_activities
    FOR EACH ROW
    EXECUTE FUNCTION update_auto_bid_activities_updated_at();

-- =====================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE auto_bid_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE whatsapp_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE auto_bid_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_whatsapp_preferences ENABLE ROW LEVEL SECURITY;

-- Auto-bid settings policies
CREATE POLICY "Users can view their own auto-bid settings" ON auto_bid_settings
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own auto-bid settings" ON auto_bid_settings
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own auto-bid settings" ON auto_bid_settings
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- WhatsApp messages policies (admin access for processing)
CREATE POLICY "System can access all WhatsApp messages" ON whatsapp_messages
    FOR ALL USING (true); -- This would be restricted to service accounts in production

-- Auto-bid activities policies
CREATE POLICY "Users can view their own auto-bid activities" ON auto_bid_activities
    FOR SELECT USING (auth.uid() = user_id);

-- User preferences policies
CREATE POLICY "Users can manage their own WhatsApp preferences" ON user_whatsapp_preferences
    FOR ALL USING (auth.uid() = user_id);

-- =====================================================
-- SAMPLE DATA FOR TESTING
-- =====================================================

-- Insert sample WhatsApp templates
INSERT INTO whatsapp_templates (template_name, template_type, message_template, variables) VALUES
('auto_bid_confirmation', 'confirmation', '🤖 Auto-bid initiated successfully!\n\nTender: {{tender_title}}\nOrganization: {{organization}}\nValue: R{{estimated_value}}\nStatus: {{status}}\n\nTrack progress in your BidBeez dashboard.', ARRAY['tender_title', 'organization', 'estimated_value', 'status']),
('permission_request', 'permission_request', '🔔 New bid opportunity detected!\n\nTitle: {{tender_title}}\nOrganization: {{organization}}\nValue: R{{estimated_value}}\nClosing: {{closing_date}}\n\nReply YES to auto-bid or NO to skip.\nOr visit your BidBeez dashboard for details.', ARRAY['tender_title', 'organization', 'estimated_value', 'closing_date']),
('daily_summary', 'daily_summary', '📊 Daily BidBeez Summary\n\nAuto-bids triggered: {{auto_bids_count}}\nJobs created: {{jobs_created}}\nEconomic impact: R{{economic_value}}\n\nYou''re building the economy! 🌍', ARRAY['auto_bids_count', 'jobs_created', 'economic_value']),
('failure_notification', 'failure_notification', '❌ Auto-bid failed\n\nTender: {{tender_title}}\nReason: {{failure_reason}}\n\nPlease check your BidBeez dashboard for details.', ARRAY['tender_title', 'failure_reason']);

-- =====================================================
-- FUNCTIONS FOR ANALYTICS
-- =====================================================

-- Function to update daily analytics
CREATE OR REPLACE FUNCTION update_whatsapp_daily_analytics()
RETURNS void AS $$
DECLARE
    today DATE := CURRENT_DATE;
BEGIN
    INSERT INTO whatsapp_analytics (
        date,
        total_messages_received,
        bid_notifications_received,
        messages_processed,
        processing_errors,
        auto_bids_triggered,
        auto_bids_successful,
        auto_bids_failed,
        avg_processing_time_seconds,
        avg_confidence_score,
        total_jobs_created,
        total_economic_value,
        active_users
    )
    SELECT 
        today,
        COUNT(*) FILTER (WHERE DATE(received_at) = today),
        COUNT(*) FILTER (WHERE DATE(received_at) = today AND message_type = 'bid_notification'),
        COUNT(*) FILTER (WHERE DATE(processed_at) = today),
        COUNT(*) FILTER (WHERE DATE(received_at) = today AND processing_status = 'failed'),
        COUNT(*) FILTER (WHERE DATE(started_at) = today) as auto_bids_triggered,
        COUNT(*) FILTER (WHERE DATE(completed_at) = today AND auto_bid_status = 'completed') as auto_bids_successful,
        COUNT(*) FILTER (WHERE DATE(completed_at) = today AND auto_bid_status = 'failed') as auto_bids_failed,
        AVG(processing_time_seconds) FILTER (WHERE DATE(completed_at) = today),
        AVG(confidence_score) FILTER (WHERE DATE(processed_at) = today),
        COALESCE(SUM(jobs_created) FILTER (WHERE DATE(completed_at) = today), 0),
        COALESCE(SUM(economic_value) FILTER (WHERE DATE(completed_at) = today), 0),
        COUNT(DISTINCT user_id) FILTER (WHERE DATE(started_at) = today)
    FROM whatsapp_messages wm
    LEFT JOIN auto_bid_activities aba ON wm.id = aba.whatsapp_message_id
    ON CONFLICT (date) DO UPDATE SET
        total_messages_received = EXCLUDED.total_messages_received,
        bid_notifications_received = EXCLUDED.bid_notifications_received,
        messages_processed = EXCLUDED.messages_processed,
        processing_errors = EXCLUDED.processing_errors,
        auto_bids_triggered = EXCLUDED.auto_bids_triggered,
        auto_bids_successful = EXCLUDED.auto_bids_successful,
        auto_bids_failed = EXCLUDED.auto_bids_failed,
        avg_processing_time_seconds = EXCLUDED.avg_processing_time_seconds,
        avg_confidence_score = EXCLUDED.avg_confidence_score,
        total_jobs_created = EXCLUDED.total_jobs_created,
        total_economic_value = EXCLUDED.total_economic_value,
        active_users = EXCLUDED.active_users,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;
