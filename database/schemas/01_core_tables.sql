-- =====================================================
-- BIDBEEZ TMS - CORE DATABASE SCHEMAS
-- Complete production-ready database infrastructure
-- =====================================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =====================================================
-- 1. USERS & AUTHENTICATION
-- =====================================================

-- Core users table (extends Supabase auth.users)
CREATE TABLE public.users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    auth_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    avatar_url TEXT,
    phone VARCHAR(50),
    
    -- User type and status
    user_type VARCHAR(50) NOT NULL CHECK (user_type IN ('individual', 'team_lead', 'organization')),
    account_status VARCHAR(50) DEFAULT 'active' CHECK (account_status IN ('active', 'suspended', 'pending', 'deactivated')),
    
    -- Profile completion
    profile_completed BOOLEAN DEFAULT FALSE,
    onboarding_completed BOOLEAN DEFAULT FALSE,
    kyc_verified BOOLEAN DEFAULT FALSE,
    
    -- Preferences
    timezone VARCHAR(100) DEFAULT 'Africa/Johannesburg',
    language VARCHAR(10) DEFAULT 'en',
    notification_preferences JSONB DEFAULT '{}',
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_at TIMESTAMP WITH TIME ZONE,
    
    -- Indexes
    CONSTRAINT users_email_check CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- User profiles table for detailed information
CREATE TABLE public.user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    
    -- Personal information
    date_of_birth DATE,
    gender VARCHAR(20),
    nationality VARCHAR(100),
    id_number VARCHAR(50),
    
    -- Address information
    address_line_1 TEXT,
    address_line_2 TEXT,
    city VARCHAR(100),
    province VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'South Africa',
    
    -- Professional information
    job_title VARCHAR(255),
    company_name VARCHAR(255),
    industry VARCHAR(100),
    years_of_experience INTEGER DEFAULT 0,
    
    -- Skills and specializations
    skills JSONB DEFAULT '[]',
    specializations JSONB DEFAULT '[]',
    certifications JSONB DEFAULT '[]',
    
    -- Portfolio and preferences
    portfolio_settings JSONB DEFAULT '{}',
    bid_preferences JSONB DEFAULT '{}',
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 2. ORGANIZATIONS & TEAMS
-- =====================================================

-- Organizations table
CREATE TABLE public.organizations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    logo_url TEXT,
    
    -- Organization details
    registration_number VARCHAR(100),
    tax_number VARCHAR(100),
    industry VARCHAR(100),
    company_size VARCHAR(50) CHECK (company_size IN ('1-10', '11-50', '51-200', '201-1000', '1000+')),
    
    -- Contact information
    email VARCHAR(255),
    phone VARCHAR(50),
    website VARCHAR(255),
    
    -- Address
    address_line_1 TEXT,
    address_line_2 TEXT,
    city VARCHAR(100),
    province VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'South Africa',
    
    -- Organization settings
    settings JSONB DEFAULT '{}',
    subscription_tier VARCHAR(50) DEFAULT 'basic',
    
    -- Status and verification
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'pending', 'deactivated')),
    verified BOOLEAN DEFAULT FALSE,
    
    -- Ownership
    owner_id UUID REFERENCES public.users(id),
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Team members table
CREATE TABLE public.team_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    organization_id UUID REFERENCES public.organizations(id) ON DELETE CASCADE,
    
    -- Role and permissions
    role VARCHAR(50) NOT NULL CHECK (role IN ('owner', 'admin', 'project_manager', 'estimator', 'technical_lead', 'legal_counsel', 'business_dev', 'finance', 'viewer', 'guest')),
    permissions JSONB DEFAULT '[]',
    
    -- Member details
    title VARCHAR(255),
    department VARCHAR(100),
    specializations JSONB DEFAULT '[]',
    
    -- Availability and workload
    availability JSONB DEFAULT '{}',
    workload VARCHAR(50) DEFAULT 'available' CHECK (workload IN ('available', 'busy', 'overloaded', 'unavailable')),
    max_concurrent_bids INTEGER DEFAULT 5,
    
    -- Status and dates
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_active TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(user_id, organization_id)
);

-- =====================================================
-- 3. TENDERS & BIDS
-- =====================================================

-- Tenders table
CREATE TABLE public.tenders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Basic tender information
    title VARCHAR(500) NOT NULL,
    reference VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    
    -- Tender details
    issuing_organization VARCHAR(255),
    tender_type VARCHAR(100),
    category VARCHAR(100),
    sector VARCHAR(100),
    
    -- Financial information
    estimated_value DECIMAL(15,2),
    currency VARCHAR(10) DEFAULT 'ZAR',
    
    -- Dates and deadlines
    published_date DATE,
    closing_date TIMESTAMP WITH TIME ZONE,
    validity_period INTEGER, -- days
    
    -- Requirements and specifications
    requirements JSONB DEFAULT '{}',
    specifications JSONB DEFAULT '{}',
    evaluation_criteria JSONB DEFAULT '{}',
    
    -- Documents and attachments
    documents JSONB DEFAULT '[]',
    
    -- Location and delivery
    delivery_location TEXT,
    project_duration INTEGER, -- days
    
    -- Status and tracking
    status VARCHAR(50) DEFAULT 'published' CHECK (status IN ('draft', 'published', 'closed', 'awarded', 'cancelled')),
    
    -- Source and metadata
    source VARCHAR(100),
    source_url TEXT,
    scraped_at TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User tender interests (individual bidders)
CREATE TABLE public.user_tender_interests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    tender_id UUID REFERENCES public.tenders(id) ON DELETE CASCADE,
    
    -- Interest details
    interest_level VARCHAR(50) DEFAULT 'interested' CHECK (interest_level IN ('watching', 'interested', 'committed', 'withdrawn')),
    interest_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Analysis and notes
    analysis JSONB DEFAULT '{}',
    notes TEXT,
    risk_assessment JSONB DEFAULT '{}',
    
    -- Bid preparation
    bid_status VARCHAR(50) DEFAULT 'planning' CHECK (bid_status IN ('planning', 'preparing', 'ready', 'submitted', 'withdrawn')),
    submission_date TIMESTAMP WITH TIME ZONE,
    
    -- Team and collaboration
    team_members JSONB DEFAULT '[]',
    external_consultants JSONB DEFAULT '[]',
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(user_id, tender_id)
);

-- Team tender interests (organization bidders)
CREATE TABLE public.team_tender_interests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID REFERENCES public.organizations(id) ON DELETE CASCADE,
    tender_id UUID REFERENCES public.tenders(id) ON DELETE CASCADE,
    
    -- Interest details
    interest_level VARCHAR(50) DEFAULT 'interested' CHECK (interest_level IN ('watching', 'interested', 'committed', 'withdrawn')),
    interest_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    lead_member_id UUID REFERENCES public.team_members(id),
    
    -- Team assignment
    assigned_members JSONB DEFAULT '[]',
    team_structure JSONB DEFAULT '{}',
    
    -- Analysis and planning
    analysis JSONB DEFAULT '{}',
    strategy JSONB DEFAULT '{}',
    risk_assessment JSONB DEFAULT '{}',
    
    -- Bid preparation
    bid_status VARCHAR(50) DEFAULT 'planning' CHECK (bid_status IN ('planning', 'preparing', 'ready', 'submitted', 'withdrawn')),
    submission_date TIMESTAMP WITH TIME ZONE,
    
    -- Collaboration workspace
    workspace_settings JSONB DEFAULT '{}',
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(organization_id, tender_id)
);

-- =====================================================
-- 4. SUBSCRIPTIONS & BILLING
-- =====================================================

-- Subscription plans
CREATE TABLE public.subscription_plans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    
    -- Pricing
    price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'ZAR',
    billing_interval VARCHAR(20) NOT NULL CHECK (billing_interval IN ('monthly', 'quarterly', 'annually')),
    
    -- Features and limits
    features JSONB DEFAULT '{}',
    limits JSONB DEFAULT '{}',
    
    -- Plan details
    tier_level INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_popular BOOLEAN DEFAULT FALSE,
    
    -- Stripe integration
    stripe_price_id VARCHAR(255),
    stripe_product_id VARCHAR(255),
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User subscriptions
CREATE TABLE public.subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    organization_id UUID REFERENCES public.organizations(id) ON DELETE SET NULL,
    plan_id UUID REFERENCES public.subscription_plans(id),
    
    -- Subscription details
    status VARCHAR(50) NOT NULL CHECK (status IN ('active', 'cancelled', 'past_due', 'unpaid', 'trialing')),
    current_period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    current_period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- Billing
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'ZAR',
    
    -- Stripe integration
    stripe_subscription_id VARCHAR(255) UNIQUE,
    stripe_customer_id VARCHAR(255),
    
    -- Trial and cancellation
    trial_start TIMESTAMP WITH TIME ZONE,
    trial_end TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    cancel_at_period_end BOOLEAN DEFAULT FALSE,
    
    -- Usage tracking
    usage_data JSONB DEFAULT '{}',
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 5. INDEXES FOR PERFORMANCE
-- =====================================================

-- Users indexes
CREATE INDEX idx_users_auth_user_id ON public.users(auth_user_id);
CREATE INDEX idx_users_email ON public.users(email);
CREATE INDEX idx_users_user_type ON public.users(user_type);
CREATE INDEX idx_users_created_at ON public.users(created_at);

-- Organizations indexes
CREATE INDEX idx_organizations_owner_id ON public.organizations(owner_id);
CREATE INDEX idx_organizations_slug ON public.organizations(slug);
CREATE INDEX idx_organizations_status ON public.organizations(status);

-- Team members indexes
CREATE INDEX idx_team_members_user_id ON public.team_members(user_id);
CREATE INDEX idx_team_members_organization_id ON public.team_members(organization_id);
CREATE INDEX idx_team_members_role ON public.team_members(role);

-- Tenders indexes
CREATE INDEX idx_tenders_reference ON public.tenders(reference);
CREATE INDEX idx_tenders_status ON public.tenders(status);
CREATE INDEX idx_tenders_closing_date ON public.tenders(closing_date);
CREATE INDEX idx_tenders_category ON public.tenders(category);
CREATE INDEX idx_tenders_sector ON public.tenders(sector);
CREATE INDEX idx_tenders_estimated_value ON public.tenders(estimated_value);

-- Tender interests indexes
CREATE INDEX idx_user_tender_interests_user_id ON public.user_tender_interests(user_id);
CREATE INDEX idx_user_tender_interests_tender_id ON public.user_tender_interests(tender_id);
CREATE INDEX idx_team_tender_interests_organization_id ON public.team_tender_interests(organization_id);
CREATE INDEX idx_team_tender_interests_tender_id ON public.team_tender_interests(tender_id);

-- Subscriptions indexes
CREATE INDEX idx_subscriptions_user_id ON public.subscriptions(user_id);
CREATE INDEX idx_subscriptions_organization_id ON public.subscriptions(organization_id);
CREATE INDEX idx_subscriptions_status ON public.subscriptions(status);
CREATE INDEX idx_subscriptions_stripe_subscription_id ON public.subscriptions(stripe_subscription_id);

-- =====================================================
-- 6. UPDATED_AT TRIGGERS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to all tables with updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON public.user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_organizations_updated_at BEFORE UPDATE ON public.organizations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_team_members_updated_at BEFORE UPDATE ON public.team_members FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tenders_updated_at BEFORE UPDATE ON public.tenders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_tender_interests_updated_at BEFORE UPDATE ON public.user_tender_interests FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_team_tender_interests_updated_at BEFORE UPDATE ON public.team_tender_interests FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_subscription_plans_updated_at BEFORE UPDATE ON public.subscription_plans FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_subscriptions_updated_at BEFORE UPDATE ON public.subscriptions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
