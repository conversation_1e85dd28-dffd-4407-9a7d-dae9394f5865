-- =====================================================
-- BIDBEEZ TMS - COMMUNICATION & COLLABORATION SCHEMAS
-- Real-time communication and team collaboration
-- =====================================================

-- =====================================================
-- 1. COMMUNICATION WORKSPACES
-- =====================================================

-- Communication workspaces for tender-specific collaboration
CREATE TABLE public.communication_workspaces (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tender_id UUID REFERENCES public.tenders(id) ON DELETE CASCADE,
    organization_id UUID REFERENCES public.organizations(id) ON DELETE CASCADE,
    
    -- Workspace details
    name VARCHAR(255) NOT NULL,
    description TEXT,
    workspace_type VARCHAR(50) DEFAULT 'tender' CHECK (workspace_type IN ('tender', 'project', 'general')),
    
    -- Settings and configuration
    settings JSONB DEFAULT '{}',
    auto_created BOOLEAN DEFAULT FALSE,
    
    -- Status
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'archived', 'suspended')),
    
    -- Metadata
    created_by UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 2. COMMUNICATION CHANNELS
-- =====================================================

-- Channels within workspaces
CREATE TABLE public.communication_channels (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workspace_id UUID REFERENCES public.communication_workspaces(id) ON DELETE CASCADE,
    
    -- Channel details
    name VARCHAR(100) NOT NULL,
    description TEXT,
    channel_type VARCHAR(50) NOT NULL CHECK (channel_type IN ('general', 'technical', 'commercial', 'compliance', 'coordination', 'announcements')),
    
    -- Channel settings
    is_private BOOLEAN DEFAULT FALSE,
    invite_only BOOLEAN DEFAULT FALSE,
    moderation_required BOOLEAN DEFAULT FALSE,
    
    -- Features
    allow_file_uploads BOOLEAN DEFAULT TRUE,
    allow_voice_messages BOOLEAN DEFAULT TRUE,
    allow_video_messages BOOLEAN DEFAULT TRUE,
    allow_screen_sharing BOOLEAN DEFAULT TRUE,
    
    -- Integration settings
    linked_to_tasks BOOLEAN DEFAULT TRUE,
    linked_to_documents BOOLEAN DEFAULT TRUE,
    auto_created BOOLEAN DEFAULT FALSE,
    creation_trigger VARCHAR(100),
    
    -- Status
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'archived', 'suspended')),
    
    -- Metadata
    created_by UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Channel participants
CREATE TABLE public.channel_participants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    channel_id UUID REFERENCES public.communication_channels(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    
    -- Participation details
    role VARCHAR(50) DEFAULT 'member' CHECK (role IN ('owner', 'moderator', 'member', 'guest')),
    permissions JSONB DEFAULT '[]',
    
    -- Activity tracking
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_read_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_active_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Settings
    notification_settings JSONB DEFAULT '{}',
    muted BOOLEAN DEFAULT FALSE,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(channel_id, user_id)
);

-- =====================================================
-- 3. MESSAGES & COMMUNICATION
-- =====================================================

-- Messages within channels
CREATE TABLE public.messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    channel_id UUID REFERENCES public.communication_channels(id) ON DELETE CASCADE,
    sender_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    
    -- Message content
    content TEXT NOT NULL,
    message_type VARCHAR(50) DEFAULT 'text' CHECK (message_type IN ('text', 'file', 'image', 'voice', 'video', 'system', 'tender_update')),
    
    -- Rich content
    attachments JSONB DEFAULT '[]',
    mentions JSONB DEFAULT '[]',
    reactions JSONB DEFAULT '{}',
    
    -- Threading
    parent_message_id UUID REFERENCES public.messages(id) ON DELETE SET NULL,
    thread_count INTEGER DEFAULT 0,
    
    -- Message metadata
    edited BOOLEAN DEFAULT FALSE,
    edited_at TIMESTAMP WITH TIME ZONE,
    deleted BOOLEAN DEFAULT FALSE,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Tender context
    tender_context JSONB DEFAULT '{}',
    linked_tasks JSONB DEFAULT '[]',
    linked_documents JSONB DEFAULT '[]',
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Message attachments
CREATE TABLE public.message_attachments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    message_id UUID REFERENCES public.messages(id) ON DELETE CASCADE,
    
    -- File details
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_url TEXT NOT NULL,
    
    -- File metadata
    file_type VARCHAR(50) NOT NULL CHECK (file_type IN ('document', 'image', 'video', 'audio', 'archive', 'other')),
    thumbnail_url TEXT,
    
    -- Processing status
    processing_status VARCHAR(50) DEFAULT 'completed' CHECK (processing_status IN ('pending', 'processing', 'completed', 'failed')),
    
    -- Security
    virus_scan_status VARCHAR(50) DEFAULT 'pending' CHECK (virus_scan_status IN ('pending', 'clean', 'infected', 'failed')),
    
    -- Metadata
    uploaded_by UUID REFERENCES public.users(id),
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 4. TEAM ONBOARDING & INVITATIONS
-- =====================================================

-- Team member invitations
CREATE TABLE public.team_invitations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID REFERENCES public.organizations(id) ON DELETE CASCADE,
    
    -- Invitation details
    invited_email VARCHAR(255) NOT NULL,
    invited_role VARCHAR(50) NOT NULL,
    invited_by UUID REFERENCES public.users(id),
    inviter_name VARCHAR(255),
    
    -- Invitation content
    personal_message TEXT,
    suggested_title VARCHAR(255),
    suggested_department VARCHAR(100),
    expected_responsibilities JSONB DEFAULT '[]',
    
    -- Invitation status
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'viewed', 'accepted', 'declined', 'expired', 'cancelled')),
    invitation_token VARCHAR(255) UNIQUE NOT NULL,
    
    -- Timing
    sent_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    responded_at TIMESTAMP WITH TIME ZONE,
    accepted_at TIMESTAMP WITH TIME ZONE,
    declined_at TIMESTAMP WITH TIME ZONE,
    
    -- Follow-up
    reminders_sent INTEGER DEFAULT 0,
    last_reminder_at TIMESTAMP WITH TIME ZONE,
    
    -- Onboarding preparation
    onboarding_template VARCHAR(100),
    required_documents JSONB DEFAULT '[]',
    access_level VARCHAR(50) DEFAULT 'pending_approval',
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Team member onboarding flows
CREATE TABLE public.team_onboarding (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invitation_id UUID REFERENCES public.team_invitations(id) ON DELETE SET NULL,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    organization_id UUID REFERENCES public.organizations(id) ON DELETE CASCADE,
    
    -- Onboarding flow
    current_step INTEGER DEFAULT 0,
    total_steps INTEGER NOT NULL,
    role VARCHAR(50) NOT NULL,
    
    -- Member information
    personal_info JSONB DEFAULT '{}',
    professional_info JSONB DEFAULT '{}',
    organization_info JSONB DEFAULT '{}',
    
    -- Progress tracking
    completed_steps JSONB DEFAULT '[]',
    skipped_steps JSONB DEFAULT '[]',
    step_data JSONB DEFAULT '{}',
    
    -- Approval workflow
    requires_approval BOOLEAN DEFAULT TRUE,
    approval_status VARCHAR(50) DEFAULT 'pending' CHECK (approval_status IN ('not_required', 'pending', 'under_review', 'approved', 'rejected', 'conditional')),
    approved_by UUID REFERENCES public.users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    approval_comments TEXT,
    
    -- Completion
    completed_at TIMESTAMP WITH TIME ZONE,
    activated_at TIMESTAMP WITH TIME ZONE,
    onboarding_score INTEGER DEFAULT 0,
    member_id UUID REFERENCES public.team_members(id),
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Onboarding documents
CREATE TABLE public.onboarding_documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    onboarding_id UUID REFERENCES public.team_onboarding(id) ON DELETE CASCADE,
    
    -- Document details
    document_name VARCHAR(255) NOT NULL,
    document_type VARCHAR(100) NOT NULL,
    document_category VARCHAR(100) NOT NULL,
    file_url TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    
    -- Verification
    required BOOLEAN DEFAULT TRUE,
    verified BOOLEAN DEFAULT FALSE,
    verified_by UUID REFERENCES public.users(id),
    verified_at TIMESTAMP WITH TIME ZONE,
    verification_notes TEXT,
    
    -- Compliance
    expiry_date DATE,
    reminder_date DATE,
    
    -- Metadata
    uploaded_by UUID REFERENCES public.users(id),
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 5. SKILLSYNC INTEGRATION
-- =====================================================

-- Cross-platform users
CREATE TABLE public.cross_platform_users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    bidbeez_user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    skillsync_user_id VARCHAR(255),
    
    -- Integration settings
    cross_platform_enabled BOOLEAN DEFAULT TRUE,
    data_sharing_settings JSONB DEFAULT '{}',
    
    -- Sync status
    last_sync_at TIMESTAMP WITH TIME ZONE,
    sync_status VARCHAR(50) DEFAULT 'active' CHECK (sync_status IN ('active', 'paused', 'error')),
    
    -- Metadata
    linked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(bidbeez_user_id),
    UNIQUE(skillsync_user_id)
);

-- Talent requests for SkillSync integration
CREATE TABLE public.talent_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tender_id UUID REFERENCES public.tenders(id) ON DELETE CASCADE,
    organization_id UUID REFERENCES public.organizations(id) ON DELETE CASCADE,
    requested_by UUID REFERENCES public.users(id),
    
    -- Request details
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    required_skills JSONB DEFAULT '[]',
    preferred_experience INTEGER DEFAULT 0,
    project_duration INTEGER NOT NULL, -- days
    workload INTEGER DEFAULT 40, -- hours per week
    
    -- Compensation
    budget_min DECIMAL(10,2),
    budget_max DECIMAL(10,2),
    currency VARCHAR(10) DEFAULT 'ZAR',
    payment_terms JSONB DEFAULT '{}',
    
    -- Requirements
    location_requirement VARCHAR(50) DEFAULT 'remote',
    travel_required BOOLEAN DEFAULT FALSE,
    security_clearance VARCHAR(100),
    
    -- Timeline
    application_deadline TIMESTAMP WITH TIME ZONE,
    project_start_date TIMESTAMP WITH TIME ZONE,
    tender_submission_date TIMESTAMP WITH TIME ZONE,
    
    -- Status
    status VARCHAR(50) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'receiving_applications', 'reviewing', 'candidate_selected', 'completed', 'cancelled')),
    urgency VARCHAR(50) DEFAULT 'medium' CHECK (urgency IN ('low', 'medium', 'high', 'urgent')),
    
    -- SkillSync sync
    skillsync_synced BOOLEAN DEFAULT FALSE,
    skillsync_id VARCHAR(255),
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Talent applications from SkillSync
CREATE TABLE public.talent_applications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    talent_request_id UUID REFERENCES public.talent_requests(id) ON DELETE CASCADE,
    skillsync_user_id VARCHAR(255) NOT NULL,
    skillsync_application_id VARCHAR(255),
    
    -- Application details
    cover_letter TEXT,
    proposed_rate DECIMAL(8,2),
    currency VARCHAR(10) DEFAULT 'ZAR',
    availability JSONB DEFAULT '{}',
    
    -- Qualifications
    relevant_experience JSONB DEFAULT '[]',
    portfolio_samples JSONB DEFAULT '[]',
    references JSONB DEFAULT '[]',
    
    -- Proposal
    approach_description TEXT,
    timeline_proposal TEXT,
    value_proposition TEXT,
    
    -- Evaluation scores
    skills_match INTEGER DEFAULT 0,
    experience_match INTEGER DEFAULT 0,
    budget_fit INTEGER DEFAULT 0,
    overall_score INTEGER DEFAULT 0,
    
    -- Application status
    status VARCHAR(50) DEFAULT 'submitted' CHECK (status IN ('submitted', 'under_review', 'shortlisted', 'interview_scheduled', 'selected', 'rejected', 'withdrawn')),
    
    -- Communication
    messages JSONB DEFAULT '[]',
    interview_details JSONB DEFAULT '{}',
    
    -- Selection
    selected_at TIMESTAMP WITH TIME ZONE,
    contract_details JSONB DEFAULT '{}',
    
    -- Metadata
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    reviewed_at TIMESTAMP WITH TIME ZONE,
    response_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 6. INDEXES FOR COMMUNICATION TABLES
-- =====================================================

-- Communication workspaces indexes
CREATE INDEX idx_communication_workspaces_tender_id ON public.communication_workspaces(tender_id);
CREATE INDEX idx_communication_workspaces_organization_id ON public.communication_workspaces(organization_id);

-- Channels indexes
CREATE INDEX idx_communication_channels_workspace_id ON public.communication_channels(workspace_id);
CREATE INDEX idx_channel_participants_channel_id ON public.channel_participants(channel_id);
CREATE INDEX idx_channel_participants_user_id ON public.channel_participants(user_id);

-- Messages indexes
CREATE INDEX idx_messages_channel_id ON public.messages(channel_id);
CREATE INDEX idx_messages_sender_id ON public.messages(sender_id);
CREATE INDEX idx_messages_created_at ON public.messages(created_at);
CREATE INDEX idx_messages_parent_message_id ON public.messages(parent_message_id);

-- Team invitations indexes
CREATE INDEX idx_team_invitations_organization_id ON public.team_invitations(organization_id);
CREATE INDEX idx_team_invitations_invited_email ON public.team_invitations(invited_email);
CREATE INDEX idx_team_invitations_status ON public.team_invitations(status);
CREATE INDEX idx_team_invitations_token ON public.team_invitations(invitation_token);

-- Onboarding indexes
CREATE INDEX idx_team_onboarding_user_id ON public.team_onboarding(user_id);
CREATE INDEX idx_team_onboarding_organization_id ON public.team_onboarding(organization_id);
CREATE INDEX idx_team_onboarding_invitation_id ON public.team_onboarding(invitation_id);

-- SkillSync integration indexes
CREATE INDEX idx_cross_platform_users_bidbeez_user_id ON public.cross_platform_users(bidbeez_user_id);
CREATE INDEX idx_cross_platform_users_skillsync_user_id ON public.cross_platform_users(skillsync_user_id);
CREATE INDEX idx_talent_requests_tender_id ON public.talent_requests(tender_id);
CREATE INDEX idx_talent_requests_organization_id ON public.talent_requests(organization_id);
CREATE INDEX idx_talent_applications_talent_request_id ON public.talent_applications(talent_request_id);
CREATE INDEX idx_talent_applications_skillsync_user_id ON public.talent_applications(skillsync_user_id);

-- =====================================================
-- 7. TRIGGERS FOR COMMUNICATION TABLES
-- =====================================================

-- Apply updated_at triggers
CREATE TRIGGER update_communication_workspaces_updated_at BEFORE UPDATE ON public.communication_workspaces FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_communication_channels_updated_at BEFORE UPDATE ON public.communication_channels FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_channel_participants_updated_at BEFORE UPDATE ON public.channel_participants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_messages_updated_at BEFORE UPDATE ON public.messages FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_team_invitations_updated_at BEFORE UPDATE ON public.team_invitations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_team_onboarding_updated_at BEFORE UPDATE ON public.team_onboarding FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_cross_platform_users_updated_at BEFORE UPDATE ON public.cross_platform_users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_talent_requests_updated_at BEFORE UPDATE ON public.talent_requests FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_talent_applications_updated_at BEFORE UPDATE ON public.talent_applications FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
