-- =====================================================
-- BIDBEEZ TMS - ROW LEVEL SECURITY (RLS) POLICIES
-- Secure multi-tenant data access control
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tenders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_tender_interests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_tender_interests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscription_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.communication_workspaces ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.communication_channels ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.channel_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.message_attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_onboarding ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.onboarding_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cross_platform_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.talent_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.talent_applications ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 1. USERS & PROFILES POLICIES
-- =====================================================

-- Users can view and update their own profile
CREATE POLICY "Users can view own profile" ON public.users
    FOR SELECT USING (auth.uid() = auth_user_id);

CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = auth_user_id);

-- User profiles policies
CREATE POLICY "Users can view own user profile" ON public.user_profiles
    FOR SELECT USING (
        user_id IN (
            SELECT id FROM public.users WHERE auth_user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update own user profile" ON public.user_profiles
    FOR UPDATE USING (
        user_id IN (
            SELECT id FROM public.users WHERE auth_user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert own user profile" ON public.user_profiles
    FOR INSERT WITH CHECK (
        user_id IN (
            SELECT id FROM public.users WHERE auth_user_id = auth.uid()
        )
    );

-- =====================================================
-- 2. ORGANIZATIONS POLICIES
-- =====================================================

-- Organization owners can manage their organizations
CREATE POLICY "Organization owners can manage organizations" ON public.organizations
    FOR ALL USING (
        owner_id IN (
            SELECT id FROM public.users WHERE auth_user_id = auth.uid()
        )
    );

-- Team members can view their organizations
CREATE POLICY "Team members can view organizations" ON public.organizations
    FOR SELECT USING (
        id IN (
            SELECT organization_id FROM public.team_members 
            WHERE user_id IN (
                SELECT id FROM public.users WHERE auth_user_id = auth.uid()
            )
        )
    );

-- =====================================================
-- 3. TEAM MEMBERS POLICIES
-- =====================================================

-- Team members can view other members in their organizations
CREATE POLICY "Team members can view organization members" ON public.team_members
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM public.team_members 
            WHERE user_id IN (
                SELECT id FROM public.users WHERE auth_user_id = auth.uid()
            )
        )
    );

-- Organization owners and admins can manage team members
CREATE POLICY "Owners and admins can manage team members" ON public.team_members
    FOR ALL USING (
        organization_id IN (
            SELECT tm.organization_id FROM public.team_members tm
            JOIN public.users u ON tm.user_id = u.id
            WHERE u.auth_user_id = auth.uid() 
            AND tm.role IN ('owner', 'admin')
        )
    );

-- Users can update their own team member record
CREATE POLICY "Users can update own team member record" ON public.team_members
    FOR UPDATE USING (
        user_id IN (
            SELECT id FROM public.users WHERE auth_user_id = auth.uid()
        )
    );

-- =====================================================
-- 4. TENDERS POLICIES
-- =====================================================

-- All authenticated users can view published tenders
CREATE POLICY "Authenticated users can view published tenders" ON public.tenders
    FOR SELECT USING (
        auth.uid() IS NOT NULL AND status = 'published'
    );

-- System can insert tenders (for scraping)
CREATE POLICY "System can insert tenders" ON public.tenders
    FOR INSERT WITH CHECK (true);

-- System can update tenders
CREATE POLICY "System can update tenders" ON public.tenders
    FOR UPDATE USING (true);

-- =====================================================
-- 5. TENDER INTERESTS POLICIES
-- =====================================================

-- Users can manage their own tender interests
CREATE POLICY "Users can manage own tender interests" ON public.user_tender_interests
    FOR ALL USING (
        user_id IN (
            SELECT id FROM public.users WHERE auth_user_id = auth.uid()
        )
    );

-- Team members can view organization tender interests
CREATE POLICY "Team members can view organization tender interests" ON public.team_tender_interests
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM public.team_members 
            WHERE user_id IN (
                SELECT id FROM public.users WHERE auth_user_id = auth.uid()
            )
        )
    );

-- Project managers and above can manage organization tender interests
CREATE POLICY "Project managers can manage organization tender interests" ON public.team_tender_interests
    FOR ALL USING (
        organization_id IN (
            SELECT tm.organization_id FROM public.team_members tm
            JOIN public.users u ON tm.user_id = u.id
            WHERE u.auth_user_id = auth.uid() 
            AND tm.role IN ('owner', 'admin', 'project_manager')
        )
    );

-- =====================================================
-- 6. SUBSCRIPTIONS POLICIES
-- =====================================================

-- Subscription plans are viewable by all authenticated users
CREATE POLICY "Authenticated users can view subscription plans" ON public.subscription_plans
    FOR SELECT USING (auth.uid() IS NOT NULL AND is_active = true);

-- Users can view their own subscriptions
CREATE POLICY "Users can view own subscriptions" ON public.subscriptions
    FOR SELECT USING (
        user_id IN (
            SELECT id FROM public.users WHERE auth_user_id = auth.uid()
        )
    );

-- Organization owners can view organization subscriptions
CREATE POLICY "Organization owners can view organization subscriptions" ON public.subscriptions
    FOR SELECT USING (
        organization_id IN (
            SELECT id FROM public.organizations 
            WHERE owner_id IN (
                SELECT id FROM public.users WHERE auth_user_id = auth.uid()
            )
        )
    );

-- =====================================================
-- 7. COMMUNICATION POLICIES
-- =====================================================

-- Team members can view workspaces for their organization tenders
CREATE POLICY "Team members can view communication workspaces" ON public.communication_workspaces
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM public.team_members 
            WHERE user_id IN (
                SELECT id FROM public.users WHERE auth_user_id = auth.uid()
            )
        )
    );

-- Project managers can manage communication workspaces
CREATE POLICY "Project managers can manage communication workspaces" ON public.communication_workspaces
    FOR ALL USING (
        organization_id IN (
            SELECT tm.organization_id FROM public.team_members tm
            JOIN public.users u ON tm.user_id = u.id
            WHERE u.auth_user_id = auth.uid() 
            AND tm.role IN ('owner', 'admin', 'project_manager')
        )
    );

-- Channel participants can view channels
CREATE POLICY "Channel participants can view channels" ON public.communication_channels
    FOR SELECT USING (
        id IN (
            SELECT channel_id FROM public.channel_participants 
            WHERE user_id IN (
                SELECT id FROM public.users WHERE auth_user_id = auth.uid()
            )
        )
        OR workspace_id IN (
            SELECT id FROM public.communication_workspaces
            WHERE organization_id IN (
                SELECT tm.organization_id FROM public.team_members tm
                JOIN public.users u ON tm.user_id = u.id
                WHERE u.auth_user_id = auth.uid() 
                AND tm.role IN ('owner', 'admin', 'project_manager')
            )
        )
    );

-- Channel participants can view their participation records
CREATE POLICY "Users can view own channel participation" ON public.channel_participants
    FOR SELECT USING (
        user_id IN (
            SELECT id FROM public.users WHERE auth_user_id = auth.uid()
        )
    );

-- Channel participants can view messages in their channels
CREATE POLICY "Channel participants can view messages" ON public.messages
    FOR SELECT USING (
        channel_id IN (
            SELECT channel_id FROM public.channel_participants 
            WHERE user_id IN (
                SELECT id FROM public.users WHERE auth_user_id = auth.uid()
            )
        )
    );

-- Channel participants can send messages
CREATE POLICY "Channel participants can send messages" ON public.messages
    FOR INSERT WITH CHECK (
        channel_id IN (
            SELECT channel_id FROM public.channel_participants 
            WHERE user_id IN (
                SELECT id FROM public.users WHERE auth_user_id = auth.uid()
            )
        )
        AND sender_id IN (
            SELECT id FROM public.users WHERE auth_user_id = auth.uid()
        )
    );

-- Users can update their own messages
CREATE POLICY "Users can update own messages" ON public.messages
    FOR UPDATE USING (
        sender_id IN (
            SELECT id FROM public.users WHERE auth_user_id = auth.uid()
        )
    );

-- =====================================================
-- 8. TEAM ONBOARDING POLICIES
-- =====================================================

-- Organization owners and admins can manage invitations
CREATE POLICY "Owners and admins can manage invitations" ON public.team_invitations
    FOR ALL USING (
        organization_id IN (
            SELECT tm.organization_id FROM public.team_members tm
            JOIN public.users u ON tm.user_id = u.id
            WHERE u.auth_user_id = auth.uid() 
            AND tm.role IN ('owner', 'admin')
        )
    );

-- Users can view invitations sent to their email
CREATE POLICY "Users can view invitations to their email" ON public.team_invitations
    FOR SELECT USING (
        invited_email IN (
            SELECT email FROM public.users WHERE auth_user_id = auth.uid()
        )
    );

-- Users can manage their own onboarding
CREATE POLICY "Users can manage own onboarding" ON public.team_onboarding
    FOR ALL USING (
        user_id IN (
            SELECT id FROM public.users WHERE auth_user_id = auth.uid()
        )
    );

-- Organization owners and admins can view onboarding in their organization
CREATE POLICY "Owners and admins can view organization onboarding" ON public.team_onboarding
    FOR SELECT USING (
        organization_id IN (
            SELECT tm.organization_id FROM public.team_members tm
            JOIN public.users u ON tm.user_id = u.id
            WHERE u.auth_user_id = auth.uid() 
            AND tm.role IN ('owner', 'admin')
        )
    );

-- =====================================================
-- 9. SKILLSYNC INTEGRATION POLICIES
-- =====================================================

-- Users can manage their own cross-platform integration
CREATE POLICY "Users can manage own cross-platform integration" ON public.cross_platform_users
    FOR ALL USING (
        bidbeez_user_id IN (
            SELECT id FROM public.users WHERE auth_user_id = auth.uid()
        )
    );

-- Organization members can view talent requests for their organization
CREATE POLICY "Organization members can view talent requests" ON public.talent_requests
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM public.team_members 
            WHERE user_id IN (
                SELECT id FROM public.users WHERE auth_user_id = auth.uid()
            )
        )
    );

-- Project managers can manage talent requests
CREATE POLICY "Project managers can manage talent requests" ON public.talent_requests
    FOR ALL USING (
        organization_id IN (
            SELECT tm.organization_id FROM public.team_members tm
            JOIN public.users u ON tm.user_id = u.id
            WHERE u.auth_user_id = auth.uid() 
            AND tm.role IN ('owner', 'admin', 'project_manager', 'business_dev')
        )
    );

-- Organization members can view talent applications for their requests
CREATE POLICY "Organization members can view talent applications" ON public.talent_applications
    FOR SELECT USING (
        talent_request_id IN (
            SELECT id FROM public.talent_requests
            WHERE organization_id IN (
                SELECT organization_id FROM public.team_members 
                WHERE user_id IN (
                    SELECT id FROM public.users WHERE auth_user_id = auth.uid()
                )
            )
        )
    );

-- =====================================================
-- 10. HELPER FUNCTIONS FOR RLS
-- =====================================================

-- Function to check if user is organization owner
CREATE OR REPLACE FUNCTION is_organization_owner(org_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.organizations o
        JOIN public.users u ON o.owner_id = u.id
        WHERE o.id = org_id AND u.auth_user_id = auth.uid()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is organization admin
CREATE OR REPLACE FUNCTION is_organization_admin(org_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.team_members tm
        JOIN public.users u ON tm.user_id = u.id
        WHERE tm.organization_id = org_id 
        AND u.auth_user_id = auth.uid()
        AND tm.role IN ('owner', 'admin')
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is organization member
CREATE OR REPLACE FUNCTION is_organization_member(org_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.team_members tm
        JOIN public.users u ON tm.user_id = u.id
        WHERE tm.organization_id = org_id 
        AND u.auth_user_id = auth.uid()
        AND tm.status = 'active'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's organizations
CREATE OR REPLACE FUNCTION get_user_organizations()
RETURNS TABLE(organization_id UUID) AS $$
BEGIN
    RETURN QUERY
    SELECT tm.organization_id
    FROM public.team_members tm
    JOIN public.users u ON tm.user_id = u.id
    WHERE u.auth_user_id = auth.uid()
    AND tm.status = 'active';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check channel access
CREATE OR REPLACE FUNCTION has_channel_access(channel_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.channel_participants cp
        JOIN public.users u ON cp.user_id = u.id
        WHERE cp.channel_id = has_channel_access.channel_id
        AND u.auth_user_id = auth.uid()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
