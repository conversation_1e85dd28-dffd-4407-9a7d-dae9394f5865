-- =====================================================
-- CORE TENDERS & QUOTES SCHEMA
-- Missing tables for the BidBeez platform
-- =====================================================

-- =====================================================
-- MAIN TENDERS TABLE (PROCESSED TENDER DATA)
-- =====================================================

CREATE TABLE IF NOT EXISTS tenders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tender_id VARCHAR(100) UNIQUE NOT NULL,
    
    -- Basic Information
    title TEXT NOT NULL,
    description TEXT,
    organization VARCHAR(255) NOT NULL,
    department VARCHAR(255),
    contact_person VARCHAR(255),
    contact_email VARCHAR(255),
    contact_phone VARCHAR(50),
    
    -- Financial Information
    estimated_value DECIMAL(15,2),
    currency VARCHAR(3) DEFAULT 'ZAR',
    budget_range JSONB DEFAULT '{}', -- min_value, max_value
    
    -- Dates & Deadlines
    publish_date TIMESTAMPTZ,
    closing_date TIMESTAMPTZ NOT NULL,
    briefing_date TIMESTAMPTZ,
    briefing_location TEXT,
    award_date TIMESTAMPTZ,
    project_start_date TIMESTAMPTZ,
    project_end_date TIMESTAMPTZ,
    
    -- Location Information
    location TEXT,
    province VARCHAR(50),
    city VARCHAR(100),
    coordinates JSONB DEFAULT '{}', -- lat, lng
    service_area TEXT[],
    
    -- Classification
    category VARCHAR(100),
    subcategory VARCHAR(100),
    tender_type VARCHAR(50) DEFAULT 'open' CHECK (tender_type IN ('open', 'closed', 'limited', 'emergency', 'framework')),
    procurement_method VARCHAR(50) DEFAULT 'competitive' CHECK (procurement_method IN ('competitive', 'negotiated', 'single_source', 'framework')),
    
    -- Requirements
    requirements TEXT[] DEFAULT '{}',
    technical_requirements JSONB DEFAULT '{}',
    commercial_requirements JSONB DEFAULT '{}',
    compliance_requirements JSONB DEFAULT '{}',
    
    -- B-BBEE & Compliance
    bbbee_required BOOLEAN DEFAULT false,
    bbbee_minimum_level INTEGER CHECK (bbbee_minimum_level >= 1 AND bbbee_minimum_level <= 8),
    cidb_grade_required VARCHAR(20),
    tax_clearance_required BOOLEAN DEFAULT true,
    
    -- Documents
    documents JSONB DEFAULT '[]', -- Array of document objects
    specifications_url TEXT,
    
    -- Status & Processing
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('draft', 'active', 'closed', 'awarded', 'cancelled', 'suspended')),
    processing_status VARCHAR(20) DEFAULT 'processed' CHECK (processing_status IN ('pending', 'processing', 'processed', 'failed')),
    
    -- AI & Intelligence
    ai_analysis JSONB DEFAULT '{}',
    match_score DECIMAL(5,2) DEFAULT 0.00,
    complexity_score DECIMAL(5,2) DEFAULT 0.00,
    risk_score DECIMAL(5,2) DEFAULT 0.00,
    competition_level VARCHAR(20) DEFAULT 'medium' CHECK (competition_level IN ('low', 'medium', 'high', 'extreme')),
    
    -- Source Information
    source_id VARCHAR(50),
    external_id VARCHAR(255),
    source_url TEXT,
    reference_number VARCHAR(255),
    scraped_tender_id UUID REFERENCES scraped_tenders(id),
    
    -- Metrics
    view_count INTEGER DEFAULT 0,
    bid_count INTEGER DEFAULT 0,
    download_count INTEGER DEFAULT 0,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    FOREIGN KEY (source_id) REFERENCES tender_sources(source_id)
);

-- =====================================================
-- SUPPLIER QUOTES TABLE (MAIN QUOTES SYSTEM)
-- =====================================================

CREATE TABLE IF NOT EXISTS supplier_quotes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    quote_id VARCHAR(100) UNIQUE NOT NULL,
    
    -- Relationships
    tender_id UUID REFERENCES tenders(id) ON DELETE CASCADE,
    supplier_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    rfq_id UUID REFERENCES bidder_rfqs(id),
    
    -- Quote Details
    quote_number VARCHAR(100),
    total_amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'ZAR',
    
    -- Line Items
    line_items JSONB DEFAULT '[]', -- Array of line item objects
    
    -- Terms & Conditions
    delivery_timeframe VARCHAR(100),
    delivery_cost DECIMAL(10,2) DEFAULT 0.00,
    delivery_location TEXT,
    validity_period_days INTEGER DEFAULT 30,
    payment_terms TEXT,
    warranty_terms TEXT,
    
    -- Compliance & Certifications
    compliance_documents TEXT[] DEFAULT '{}',
    certifications TEXT[] DEFAULT '{}',
    bbbee_certificate_url TEXT,
    tax_clearance_url TEXT,
    
    -- Status & Lifecycle
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'submitted', 'under_review', 'accepted', 'rejected', 'expired', 'withdrawn')),
    submission_date TIMESTAMPTZ,
    review_date TIMESTAMPTZ,
    decision_date TIMESTAMPTZ,
    expiry_date TIMESTAMPTZ,
    
    -- Evaluation
    evaluation_score DECIMAL(5,2),
    technical_score DECIMAL(5,2),
    commercial_score DECIMAL(5,2),
    compliance_score DECIMAL(5,2),
    evaluation_notes TEXT,
    evaluator_id UUID REFERENCES users(id),
    
    -- AI & Intelligence
    trust_score DECIMAL(5,2) DEFAULT 0.00,
    competitive_score DECIMAL(5,2) DEFAULT 0.00,
    ai_analysis JSONB DEFAULT '{}',
    
    -- Commission & Revenue
    commission_rate DECIMAL(5,2) DEFAULT 0.00,
    estimated_commission DECIMAL(10,2) DEFAULT 0.00,
    commission_status VARCHAR(20) DEFAULT 'pending' CHECK (commission_status IN ('pending', 'calculated', 'approved', 'paid')),
    
    -- Smart Contracts & Blockchain
    smart_contract_enabled BOOLEAN DEFAULT false,
    smart_contract_address TEXT,
    blockchain_hash TEXT,
    
    -- Documents & Files
    quote_document_url TEXT,
    supporting_documents JSONB DEFAULT '[]',
    
    -- Supplier Information (cached for performance)
    supplier_name VARCHAR(255),
    supplier_location VARCHAR(255),
    supplier_bbbee_level INTEGER,
    
    -- Metrics
    view_count INTEGER DEFAULT 0,
    download_count INTEGER DEFAULT 0,
    
    -- Versioning
    version INTEGER DEFAULT 1,
    parent_quote_id UUID REFERENCES supplier_quotes(id),
    is_latest_version BOOLEAN DEFAULT true,
    
    -- Metadata
    notes TEXT,
    internal_notes TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- TENDER BIDS TABLE (BIDDER SUBMISSIONS)
-- =====================================================

CREATE TABLE IF NOT EXISTS tender_bids (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bid_id VARCHAR(100) UNIQUE NOT NULL,
    
    -- Relationships
    tender_id UUID NOT NULL REFERENCES tenders(id) ON DELETE CASCADE,
    bidder_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Bid Information
    bid_amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'ZAR',
    
    -- Submission Details
    submission_method VARCHAR(20) DEFAULT 'online' CHECK (submission_method IN ('online', 'physical', 'email', 'hybrid')),
    submission_date TIMESTAMPTZ DEFAULT NOW(),
    submission_location TEXT,
    
    -- Documents
    bid_documents JSONB DEFAULT '[]',
    technical_proposal_url TEXT,
    commercial_proposal_url TEXT,
    compliance_documents_url TEXT,
    
    -- Status
    status VARCHAR(20) DEFAULT 'submitted' CHECK (status IN ('draft', 'submitted', 'under_review', 'shortlisted', 'awarded', 'not_awarded', 'disqualified', 'withdrawn')),
    
    -- Evaluation
    technical_score DECIMAL(5,2),
    commercial_score DECIMAL(5,2),
    compliance_score DECIMAL(5,2),
    total_score DECIMAL(5,2),
    ranking INTEGER,
    evaluation_notes TEXT,
    
    -- Award Information
    awarded BOOLEAN DEFAULT false,
    award_date TIMESTAMPTZ,
    award_amount DECIMAL(15,2),
    award_reason TEXT,
    
    -- Compliance
    bbbee_points DECIMAL(5,2),
    local_content_percentage DECIMAL(5,2),
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Tenders Table Indexes
CREATE INDEX IF NOT EXISTS idx_tenders_tender_id ON tenders(tender_id);
CREATE INDEX IF NOT EXISTS idx_tenders_status ON tenders(status);
CREATE INDEX IF NOT EXISTS idx_tenders_closing_date ON tenders(closing_date);
CREATE INDEX IF NOT EXISTS idx_tenders_category ON tenders(category);
CREATE INDEX IF NOT EXISTS idx_tenders_province ON tenders(province);
CREATE INDEX IF NOT EXISTS idx_tenders_organization ON tenders(organization);
CREATE INDEX IF NOT EXISTS idx_tenders_estimated_value ON tenders(estimated_value);
CREATE INDEX IF NOT EXISTS idx_tenders_created_at ON tenders(created_at);
CREATE INDEX IF NOT EXISTS idx_tenders_source_id ON tenders(source_id);

-- Supplier Quotes Table Indexes
CREATE INDEX IF NOT EXISTS idx_supplier_quotes_quote_id ON supplier_quotes(quote_id);
CREATE INDEX IF NOT EXISTS idx_supplier_quotes_tender_id ON supplier_quotes(tender_id);
CREATE INDEX IF NOT EXISTS idx_supplier_quotes_supplier_id ON supplier_quotes(supplier_id);
CREATE INDEX IF NOT EXISTS idx_supplier_quotes_status ON supplier_quotes(status);
CREATE INDEX IF NOT EXISTS idx_supplier_quotes_submission_date ON supplier_quotes(submission_date);
CREATE INDEX IF NOT EXISTS idx_supplier_quotes_total_amount ON supplier_quotes(total_amount);
CREATE INDEX IF NOT EXISTS idx_supplier_quotes_trust_score ON supplier_quotes(trust_score);
CREATE INDEX IF NOT EXISTS idx_supplier_quotes_expiry_date ON supplier_quotes(expiry_date);

-- Tender Bids Table Indexes
CREATE INDEX IF NOT EXISTS idx_tender_bids_bid_id ON tender_bids(bid_id);
CREATE INDEX IF NOT EXISTS idx_tender_bids_tender_id ON tender_bids(tender_id);
CREATE INDEX IF NOT EXISTS idx_tender_bids_bidder_id ON tender_bids(bidder_id);
CREATE INDEX IF NOT EXISTS idx_tender_bids_status ON tender_bids(status);
CREATE INDEX IF NOT EXISTS idx_tender_bids_submission_date ON tender_bids(submission_date);
CREATE INDEX IF NOT EXISTS idx_tender_bids_awarded ON tender_bids(awarded);

-- =====================================================
-- ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Enable RLS
ALTER TABLE tenders ENABLE ROW LEVEL SECURITY;
ALTER TABLE supplier_quotes ENABLE ROW LEVEL SECURITY;
ALTER TABLE tender_bids ENABLE ROW LEVEL SECURITY;

-- Basic RLS policies
CREATE POLICY "Allow read access to active tenders" ON tenders
    FOR SELECT USING (status = 'active');

CREATE POLICY "Allow suppliers to manage their quotes" ON supplier_quotes
    FOR ALL USING (auth.uid() = supplier_id);

CREATE POLICY "Allow bidders to manage their bids" ON tender_bids
    FOR ALL USING (auth.uid() = bidder_id);

-- =====================================================
-- TRIGGERS FOR UPDATED_AT
-- =====================================================

CREATE TRIGGER update_tenders_updated_at BEFORE UPDATE ON tenders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_supplier_quotes_updated_at BEFORE UPDATE ON supplier_quotes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tender_bids_updated_at BEFORE UPDATE ON tender_bids
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
