-- =====================================================
-- CONTRACTOR-SUPPLIER ACCESS SYSTEM DATABASE SCHEMA
-- =====================================================

-- Enable Row Level Security
ALTER DATABASE postgres SET row_security = on;

-- =====================================================
-- CONTRACTOR PROFILES TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS contractor_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    contractor_id VARCHAR(100) UNIQUE NOT NULL,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    company_name VARCHAR(255) NOT NULL,
    registration_number VARCHAR(100),
    contractor_type VARCHAR(50) NOT NULL CHECK (contractor_type IN ('general', 'specialist', 'subcontractor', 'consultant')),
    specializations TEXT[] DEFAULT '{}',
    cidb_grade VARCHAR(10),
    bbbee_level INTEGER CHECK (bbbee_level >= 1 AND bbbee_level <= 8),
    location JSONB DEFAULT '{}',
    service_areas TEXT[] DEFAULT '{}',
    capacity JSONB DEFAULT '{}', -- project capacity, team size, equipment, etc.
    certifications JSONB DEFAULT '[]',
    insurance_details JSONB DEFAULT '{}',
    performance_rating DECIMAL(3,2) DEFAULT 0.00 CHECK (performance_rating >= 0 AND performance_rating <= 5),
    completed_projects INTEGER DEFAULT 0,
    active_projects INTEGER DEFAULT 0,
    total_contract_value DECIMAL(15,2) DEFAULT 0.00,
    supplier_access_level VARCHAR(20) DEFAULT 'basic' CHECK (supplier_access_level IN ('basic', 'premium', 'enterprise')),
    verification_status VARCHAR(20) DEFAULT 'pending' CHECK (verification_status IN ('pending', 'verified', 'rejected', 'suspended')),
    verification_date TIMESTAMPTZ,
    verified_by UUID REFERENCES users(id),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended', 'banned')),
    profile_completion_percentage INTEGER DEFAULT 0 CHECK (profile_completion_percentage >= 0 AND profile_completion_percentage <= 100),
    last_activity TIMESTAMPTZ,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- CONTRACTOR SUPPLIER ACCESS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS contractor_supplier_access (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    access_id VARCHAR(100) UNIQUE NOT NULL,
    contractor_id VARCHAR(100) NOT NULL REFERENCES contractor_profiles(contractor_id) ON DELETE CASCADE,
    access_level VARCHAR(20) NOT NULL CHECK (access_level IN ('basic', 'premium', 'enterprise')),
    permissions TEXT[] DEFAULT '{}',
    usage_limits JSONB DEFAULT '{}',
    current_usage JSONB DEFAULT '{}',
    granted_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ,
    last_used TIMESTAMPTZ,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'expired', 'revoked')),
    granted_by UUID REFERENCES users(id),
    suspension_reason TEXT,
    suspension_date TIMESTAMPTZ,
    auto_renewal BOOLEAN DEFAULT true,
    billing_cycle VARCHAR(20) DEFAULT 'monthly' CHECK (billing_cycle IN ('monthly', 'quarterly', 'yearly')),
    next_billing_date TIMESTAMPTZ,
    payment_status VARCHAR(20) DEFAULT 'current' CHECK (payment_status IN ('current', 'overdue', 'suspended')),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- CONTRACTOR QUOTE REQUESTS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS contractor_quote_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    request_id VARCHAR(100) UNIQUE NOT NULL,
    contractor_id VARCHAR(100) NOT NULL REFERENCES contractor_profiles(contractor_id) ON DELETE CASCADE,
    supplier_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    tender_id UUID REFERENCES tenders(id),
    category VARCHAR(100) NOT NULL,
    specifications JSONB DEFAULT '{}',
    quantity INTEGER,
    unit_of_measurement VARCHAR(50),
    delivery_location TEXT NOT NULL,
    delivery_deadline TIMESTAMPTZ NOT NULL,
    budget_range JSONB DEFAULT '{}', -- min_budget, max_budget, currency
    quality_requirements TEXT[] DEFAULT '{}',
    compliance_requirements TEXT[] DEFAULT '{}',
    special_requirements TEXT[] DEFAULT '{}',
    preferred_delivery_method VARCHAR(50),
    payment_terms TEXT,
    warranty_requirements TEXT,
    urgency VARCHAR(20) DEFAULT 'normal' CHECK (urgency IN ('low', 'normal', 'high', 'urgent')),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'acknowledged', 'quoted', 'accepted', 'rejected', 'expired', 'cancelled')),
    requested_at TIMESTAMPTZ DEFAULT NOW(),
    acknowledged_at TIMESTAMPTZ,
    quoted_at TIMESTAMPTZ,
    response_deadline TIMESTAMPTZ,
    contractor_notes TEXT,
    supplier_notes TEXT,
    internal_notes TEXT,
    priority_score INTEGER DEFAULT 50 CHECK (priority_score >= 0 AND priority_score <= 100),
    estimated_response_time VARCHAR(50),
    follow_up_required BOOLEAN DEFAULT false,
    follow_up_date TIMESTAMPTZ,
    communication_log JSONB DEFAULT '[]',
    attachments JSONB DEFAULT '[]',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- SUPPLIER QUOTES TABLE (for contractor requests)
-- =====================================================

CREATE TABLE IF NOT EXISTS contractor_supplier_quotes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    quote_id VARCHAR(100) UNIQUE NOT NULL,
    request_id VARCHAR(100) NOT NULL REFERENCES contractor_quote_requests(request_id) ON DELETE CASCADE,
    supplier_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    contractor_id VARCHAR(100) NOT NULL REFERENCES contractor_profiles(contractor_id) ON DELETE CASCADE,
    quote_number VARCHAR(100),
    total_amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'ZAR',
    unit_price DECIMAL(15,2),
    quantity_quoted INTEGER,
    delivery_time VARCHAR(100),
    delivery_cost DECIMAL(10,2) DEFAULT 0.00,
    validity_period_days INTEGER DEFAULT 30,
    payment_terms TEXT,
    warranty_terms TEXT,
    compliance_certifications TEXT[] DEFAULT '{}',
    technical_specifications JSONB DEFAULT '{}',
    alternative_options JSONB DEFAULT '[]',
    terms_and_conditions TEXT,
    exclusions TEXT,
    assumptions TEXT,
    status VARCHAR(20) DEFAULT 'submitted' CHECK (status IN ('draft', 'submitted', 'revised', 'accepted', 'rejected', 'expired', 'withdrawn')),
    submitted_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ,
    accepted_at TIMESTAMPTZ,
    rejected_at TIMESTAMPTZ,
    rejection_reason TEXT,
    revision_count INTEGER DEFAULT 0,
    is_preferred BOOLEAN DEFAULT false,
    competitive_score DECIMAL(5,2) DEFAULT 0.00,
    evaluation_notes TEXT,
    attachments JSONB DEFAULT '[]',
    supplier_notes TEXT,
    internal_notes TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- CONTRACTOR SUPPLIER RELATIONSHIPS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS contractor_supplier_relationships (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    contractor_id VARCHAR(100) NOT NULL REFERENCES contractor_profiles(contractor_id) ON DELETE CASCADE,
    supplier_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    relationship_type VARCHAR(50) NOT NULL CHECK (relationship_type IN ('preferred', 'approved', 'blacklisted', 'trial', 'partner')),
    relationship_status VARCHAR(20) DEFAULT 'active' CHECK (relationship_status IN ('active', 'inactive', 'suspended', 'terminated')),
    established_date TIMESTAMPTZ DEFAULT NOW(),
    last_transaction_date TIMESTAMPTZ,
    total_transactions INTEGER DEFAULT 0,
    total_transaction_value DECIMAL(15,2) DEFAULT 0.00,
    average_rating DECIMAL(3,2) DEFAULT 0.00,
    performance_score DECIMAL(5,2) DEFAULT 0.00,
    reliability_score DECIMAL(5,2) DEFAULT 0.00,
    quality_score DECIMAL(5,2) DEFAULT 0.00,
    communication_score DECIMAL(5,2) DEFAULT 0.00,
    payment_terms_agreed TEXT,
    special_rates JSONB DEFAULT '{}',
    contract_terms JSONB DEFAULT '{}',
    notes TEXT,
    tags TEXT[] DEFAULT '{}',
    created_by UUID REFERENCES users(id),
    terminated_by UUID REFERENCES users(id),
    termination_reason TEXT,
    termination_date TIMESTAMPTZ,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(contractor_id, supplier_id)
);

-- =====================================================
-- ECOSYSTEM INTEGRATION LOG TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS ecosystem_integration_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    integration_id VARCHAR(100) UNIQUE NOT NULL,
    service_name VARCHAR(50) NOT NULL CHECK (service_name IN ('skillsync', 'toolsync', 'contractorsync', 'suppliersync')),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    action VARCHAR(100) NOT NULL,
    source_system VARCHAR(50) NOT NULL,
    target_system VARCHAR(50) NOT NULL,
    data_payload JSONB DEFAULT '{}',
    status VARCHAR(20) NOT NULL CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'retrying')),
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    processing_time_ms INTEGER,
    correlation_id VARCHAR(100),
    session_id VARCHAR(100),
    ip_address INET,
    user_agent TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Contractor Profiles
CREATE INDEX IF NOT EXISTS idx_contractor_profiles_contractor_id ON contractor_profiles(contractor_id);
CREATE INDEX IF NOT EXISTS idx_contractor_profiles_user_id ON contractor_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_contractor_profiles_company_name ON contractor_profiles(company_name);
CREATE INDEX IF NOT EXISTS idx_contractor_profiles_contractor_type ON contractor_profiles(contractor_type);
CREATE INDEX IF NOT EXISTS idx_contractor_profiles_cidb_grade ON contractor_profiles(cidb_grade);
CREATE INDEX IF NOT EXISTS idx_contractor_profiles_bbbee_level ON contractor_profiles(bbbee_level);
CREATE INDEX IF NOT EXISTS idx_contractor_profiles_status ON contractor_profiles(status);
CREATE INDEX IF NOT EXISTS idx_contractor_profiles_specializations ON contractor_profiles USING GIN(specializations);
CREATE INDEX IF NOT EXISTS idx_contractor_profiles_location ON contractor_profiles USING GIN(location);

-- Contractor Supplier Access
CREATE INDEX IF NOT EXISTS idx_contractor_supplier_access_contractor_id ON contractor_supplier_access(contractor_id);
CREATE INDEX IF NOT EXISTS idx_contractor_supplier_access_access_level ON contractor_supplier_access(access_level);
CREATE INDEX IF NOT EXISTS idx_contractor_supplier_access_status ON contractor_supplier_access(status);
CREATE INDEX IF NOT EXISTS idx_contractor_supplier_access_expires_at ON contractor_supplier_access(expires_at);

-- Contractor Quote Requests
CREATE INDEX IF NOT EXISTS idx_contractor_quote_requests_request_id ON contractor_quote_requests(request_id);
CREATE INDEX IF NOT EXISTS idx_contractor_quote_requests_contractor_id ON contractor_quote_requests(contractor_id);
CREATE INDEX IF NOT EXISTS idx_contractor_quote_requests_supplier_id ON contractor_quote_requests(supplier_id);
CREATE INDEX IF NOT EXISTS idx_contractor_quote_requests_tender_id ON contractor_quote_requests(tender_id);
CREATE INDEX IF NOT EXISTS idx_contractor_quote_requests_status ON contractor_quote_requests(status);
CREATE INDEX IF NOT EXISTS idx_contractor_quote_requests_category ON contractor_quote_requests(category);
CREATE INDEX IF NOT EXISTS idx_contractor_quote_requests_delivery_deadline ON contractor_quote_requests(delivery_deadline);
CREATE INDEX IF NOT EXISTS idx_contractor_quote_requests_urgency ON contractor_quote_requests(urgency);

-- Contractor Supplier Quotes
CREATE INDEX IF NOT EXISTS idx_contractor_supplier_quotes_quote_id ON contractor_supplier_quotes(quote_id);
CREATE INDEX IF NOT EXISTS idx_contractor_supplier_quotes_request_id ON contractor_supplier_quotes(request_id);
CREATE INDEX IF NOT EXISTS idx_contractor_supplier_quotes_supplier_id ON contractor_supplier_quotes(supplier_id);
CREATE INDEX IF NOT EXISTS idx_contractor_supplier_quotes_contractor_id ON contractor_supplier_quotes(contractor_id);
CREATE INDEX IF NOT EXISTS idx_contractor_supplier_quotes_status ON contractor_supplier_quotes(status);
CREATE INDEX IF NOT EXISTS idx_contractor_supplier_quotes_total_amount ON contractor_supplier_quotes(total_amount);
CREATE INDEX IF NOT EXISTS idx_contractor_supplier_quotes_expires_at ON contractor_supplier_quotes(expires_at);

-- Contractor Supplier Relationships
CREATE INDEX IF NOT EXISTS idx_contractor_supplier_relationships_contractor_id ON contractor_supplier_relationships(contractor_id);
CREATE INDEX IF NOT EXISTS idx_contractor_supplier_relationships_supplier_id ON contractor_supplier_relationships(supplier_id);
CREATE INDEX IF NOT EXISTS idx_contractor_supplier_relationships_type ON contractor_supplier_relationships(relationship_type);
CREATE INDEX IF NOT EXISTS idx_contractor_supplier_relationships_status ON contractor_supplier_relationships(relationship_status);

-- Ecosystem Integration Log
CREATE INDEX IF NOT EXISTS idx_ecosystem_integration_log_service_name ON ecosystem_integration_log(service_name);
CREATE INDEX IF NOT EXISTS idx_ecosystem_integration_log_user_id ON ecosystem_integration_log(user_id);
CREATE INDEX IF NOT EXISTS idx_ecosystem_integration_log_status ON ecosystem_integration_log(status);
CREATE INDEX IF NOT EXISTS idx_ecosystem_integration_log_created_at ON ecosystem_integration_log(created_at);
CREATE INDEX IF NOT EXISTS idx_ecosystem_integration_log_correlation_id ON ecosystem_integration_log(correlation_id);

-- =====================================================
-- ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE contractor_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE contractor_supplier_access ENABLE ROW LEVEL SECURITY;
ALTER TABLE contractor_quote_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE contractor_supplier_quotes ENABLE ROW LEVEL SECURITY;
ALTER TABLE contractor_supplier_relationships ENABLE ROW LEVEL SECURITY;
ALTER TABLE ecosystem_integration_log ENABLE ROW LEVEL SECURITY;

-- Basic RLS policies (can be customized based on requirements)
CREATE POLICY "Allow contractors to manage their own profiles" ON contractor_profiles
    FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Allow contractors to view their own access" ON contractor_supplier_access
    FOR SELECT USING (contractor_id IN (SELECT contractor_id FROM contractor_profiles WHERE user_id = auth.uid()));

CREATE POLICY "Allow contractors to manage their quote requests" ON contractor_quote_requests
    FOR ALL USING (contractor_id IN (SELECT contractor_id FROM contractor_profiles WHERE user_id = auth.uid()));

CREATE POLICY "Allow suppliers to view quotes for their requests" ON contractor_supplier_quotes
    FOR ALL USING (supplier_id = auth.uid() OR contractor_id IN (SELECT contractor_id FROM contractor_profiles WHERE user_id = auth.uid()));

CREATE POLICY "Allow users to manage their relationships" ON contractor_supplier_relationships
    FOR ALL USING (
        contractor_id IN (SELECT contractor_id FROM contractor_profiles WHERE user_id = auth.uid()) 
        OR supplier_id = auth.uid()
    );

CREATE POLICY "Allow users to view their integration logs" ON ecosystem_integration_log
    FOR SELECT USING (user_id = auth.uid());

-- =====================================================
-- TRIGGERS FOR AUTOMATIC TIMESTAMPS
-- =====================================================

-- Apply triggers to all tables with updated_at
CREATE TRIGGER update_contractor_profiles_updated_at BEFORE UPDATE ON contractor_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_contractor_supplier_access_updated_at BEFORE UPDATE ON contractor_supplier_access
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_contractor_quote_requests_updated_at BEFORE UPDATE ON contractor_quote_requests
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_contractor_supplier_quotes_updated_at BEFORE UPDATE ON contractor_supplier_quotes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_contractor_supplier_relationships_updated_at BEFORE UPDATE ON contractor_supplier_relationships
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ecosystem_integration_log_updated_at BEFORE UPDATE ON ecosystem_integration_log
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- VIEWS FOR REPORTING
-- =====================================================

-- Contractor performance summary
CREATE OR REPLACE VIEW contractor_performance_summary AS
SELECT 
    cp.contractor_id,
    cp.company_name,
    cp.contractor_type,
    cp.cidb_grade,
    cp.bbbee_level,
    cp.performance_rating,
    cp.completed_projects,
    cp.active_projects,
    cp.total_contract_value,
    cp.supplier_access_level,
    COUNT(cqr.id) as total_quote_requests,
    COUNT(cqr.id) FILTER (WHERE cqr.status = 'quoted') as quotes_received,
    COUNT(cqr.id) FILTER (WHERE cqr.status = 'accepted') as quotes_accepted,
    AVG(csq.total_amount) as avg_quote_value,
    COUNT(csr.id) as supplier_relationships
FROM contractor_profiles cp
LEFT JOIN contractor_quote_requests cqr ON cp.contractor_id = cqr.contractor_id
LEFT JOIN contractor_supplier_quotes csq ON cqr.request_id = csq.request_id
LEFT JOIN contractor_supplier_relationships csr ON cp.contractor_id = csr.contractor_id
GROUP BY cp.contractor_id, cp.company_name, cp.contractor_type, cp.cidb_grade, cp.bbbee_level, 
         cp.performance_rating, cp.completed_projects, cp.active_projects, cp.total_contract_value, cp.supplier_access_level
ORDER BY cp.performance_rating DESC, cp.total_contract_value DESC;

-- Supplier quote performance
CREATE OR REPLACE VIEW supplier_quote_performance AS
SELECT 
    csq.supplier_id,
    u.email as supplier_email,
    COUNT(csq.id) as total_quotes_submitted,
    COUNT(csq.id) FILTER (WHERE csq.status = 'accepted') as quotes_accepted,
    COUNT(csq.id) FILTER (WHERE csq.status = 'rejected') as quotes_rejected,
    ROUND(COUNT(csq.id) FILTER (WHERE csq.status = 'accepted')::decimal / NULLIF(COUNT(csq.id), 0) * 100, 2) as acceptance_rate,
    AVG(csq.total_amount) as avg_quote_amount,
    AVG(csq.competitive_score) as avg_competitive_score,
    AVG(EXTRACT(EPOCH FROM (csq.submitted_at - cqr.requested_at))/3600) as avg_response_time_hours
FROM contractor_supplier_quotes csq
JOIN users u ON csq.supplier_id = u.id
JOIN contractor_quote_requests cqr ON csq.request_id = cqr.request_id
GROUP BY csq.supplier_id, u.email
ORDER BY acceptance_rate DESC, avg_competitive_score DESC;
