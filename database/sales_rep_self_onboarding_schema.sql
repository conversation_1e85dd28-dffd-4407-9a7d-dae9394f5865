-- =====================================================
-- SALES REP SELF-ONBOARDING DATABASE SCHEMA
-- Psychological onboarding with company pathway
-- =====================================================

-- Enable Row Level Security
ALTER DATABASE postgres SET row_security = on;

-- =====================================================
-- SALES REP ONBOARDING TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS sales_rep_onboarding (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    rep_id VARCHAR(100) UNIQUE NOT NULL,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Personal Information
    email VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    
    -- Onboarding State
    onboarding_state VARCHAR(50) NOT NULL DEFAULT 'discovery' CHECK (
        onboarding_state IN ('discovery', 'hooked', 'engaged', 'invested', 'company_curious', 'company_motivated', 'company_onboarding', 'fully_integrated')
    ),
    feature_tier VARCHAR(20) NOT NULL DEFAULT 'solo_rep' CHECK (
        feature_tier IN ('solo_rep', 'team_rep', 'enterprise_rep')
    ),
    
    -- Psychological Profile
    psychological_profile JSONB DEFAULT '{}',
    quiz_responses JSONB DEFAULT '[]',
    quiz_completion_time_ms INTEGER,
    archetype VARCHAR(50),
    motivation_factors TEXT[] DEFAULT '{}',
    confidence_scores JSONB DEFAULT '{}',
    
    -- Onboarding Progress
    onboarding_completed_at TIMESTAMPTZ,
    state_updated_at TIMESTAMPTZ DEFAULT NOW(),
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    
    -- Feature Access
    temporary_access BOOLEAN DEFAULT false,
    temporary_access_expires TIMESTAMPTZ,
    feature_unlocks JSONB DEFAULT '{}',
    usage_limits JSONB DEFAULT '{}',
    current_usage JSONB DEFAULT '{}',
    
    -- Company Integration
    company_verification_status VARCHAR(50) DEFAULT 'not_started' CHECK (
        company_verification_status IN ('not_started', 'initiated', 'in_progress', 'completed', 'failed')
    ),
    company_onboarding_initiated BOOLEAN DEFAULT false,
    company_benefits_page_views INTEGER DEFAULT 0,
    team_features_previewed BOOLEAN DEFAULT false,
    
    -- Engagement Tracking
    dashboard_sessions INTEGER DEFAULT 0,
    quotes_requested INTEGER DEFAULT 0,
    monthly_quotes_used INTEGER DEFAULT 0,
    leaderboard_views INTEGER DEFAULT 0,
    achievements_unlocked INTEGER DEFAULT 0,
    targets_set INTEGER DEFAULT 0,
    last_activity TIMESTAMPTZ,
    
    -- Attribution
    referral_source VARCHAR(100),
    utm_data JSONB DEFAULT '{}',
    landing_page VARCHAR(255),
    signup_ip INET,
    signup_user_agent TEXT,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- COMPANY VERIFICATIONS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS company_verifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    verification_id VARCHAR(100) UNIQUE NOT NULL,
    rep_id VARCHAR(100) NOT NULL REFERENCES sales_rep_onboarding(rep_id) ON DELETE CASCADE,
    
    -- Company Information
    company_name VARCHAR(255) NOT NULL,
    industry VARCHAR(100),
    company_size VARCHAR(20),
    annual_revenue VARCHAR(50),
    registration_number VARCHAR(100),
    tax_number VARCHAR(100),
    
    -- Rep Information
    rep_role VARCHAR(50) NOT NULL,
    authorization_level VARCHAR(50) NOT NULL,
    can_authorize_company BOOLEAN DEFAULT false,
    
    -- Verification Process
    status VARCHAR(50) NOT NULL DEFAULT 'initiated' CHECK (
        status IN ('initiated', 'research_in_progress', 'pending_confirmation', 'completed', 'failed', 'rejected')
    ),
    initiated_at TIMESTAMPTZ DEFAULT NOW(),
    estimated_completion TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    
    -- Research Data
    automated_research_data JSONB DEFAULT '{}',
    manual_verification_notes TEXT,
    verification_documents JSONB DEFAULT '[]',
    compliance_checks JSONB DEFAULT '{}',
    
    -- Verification Results
    company_verified BOOLEAN DEFAULT false,
    verification_confidence DECIMAL(3,2) DEFAULT 0.00,
    risk_assessment JSONB DEFAULT '{}',
    verification_method VARCHAR(50),
    verified_by UUID REFERENCES users(id),
    
    -- Company Profile Creation
    company_profile_created BOOLEAN DEFAULT false,
    company_id UUID,
    supplier_profile_created BOOLEAN DEFAULT false,
    
    -- Failure Handling
    failure_reason TEXT,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    next_retry_at TIMESTAMPTZ,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- ONBOARDING PROGRESSION TRIGGERS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS onboarding_progression_triggers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    trigger_id VARCHAR(100) UNIQUE NOT NULL,
    rep_id VARCHAR(100) NOT NULL REFERENCES sales_rep_onboarding(rep_id) ON DELETE CASCADE,
    
    -- Trigger Details
    trigger_type VARCHAR(50) NOT NULL CHECK (
        trigger_type IN ('time_based', 'achievement_based', 'usage_based', 'social_based', 'limitation_hit')
    ),
    trigger_name VARCHAR(100) NOT NULL,
    current_state VARCHAR(50) NOT NULL,
    target_state VARCHAR(50) NOT NULL,
    
    -- Trigger Conditions
    conditions JSONB NOT NULL DEFAULT '{}',
    conditions_met BOOLEAN DEFAULT false,
    conditions_checked_at TIMESTAMPTZ,
    
    -- Trigger Execution
    triggered BOOLEAN DEFAULT false,
    triggered_at TIMESTAMPTZ,
    reward_delivered BOOLEAN DEFAULT false,
    reward_details JSONB DEFAULT '{}',
    
    -- Effectiveness Tracking
    user_response VARCHAR(50), -- 'positive', 'negative', 'neutral', 'ignored'
    response_time_seconds INTEGER,
    conversion_achieved BOOLEAN DEFAULT false,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- SALES REP NOTIFICATIONS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS sales_rep_notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    notification_id VARCHAR(100) UNIQUE NOT NULL,
    rep_id VARCHAR(100) NOT NULL REFERENCES sales_rep_onboarding(rep_id) ON DELETE CASCADE,
    
    -- Notification Details
    type VARCHAR(50) NOT NULL CHECK (
        type IN ('progression_reward', 'achievement_unlock', 'company_nudge', 'feature_unlock', 'limitation_warning', 'engagement_boost')
    ),
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    
    -- Notification Targeting
    psychological_trigger VARCHAR(50),
    archetype_targeted VARCHAR(50),
    urgency_level VARCHAR(20) DEFAULT 'normal' CHECK (urgency_level IN ('low', 'normal', 'high', 'urgent')),
    
    -- Delivery
    delivery_method VARCHAR(50) DEFAULT 'in_app' CHECK (delivery_method IN ('in_app', 'email', 'sms', 'push')),
    scheduled_for TIMESTAMPTZ,
    delivered_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ,
    
    -- Interaction
    read BOOLEAN DEFAULT false,
    read_at TIMESTAMPTZ,
    clicked BOOLEAN DEFAULT false,
    clicked_at TIMESTAMPTZ,
    dismissed BOOLEAN DEFAULT false,
    dismissed_at TIMESTAMPTZ,
    action_taken BOOLEAN DEFAULT false,
    action_taken_at TIMESTAMPTZ,
    
    -- Effectiveness
    engagement_score DECIMAL(3,2) DEFAULT 0.00,
    conversion_achieved BOOLEAN DEFAULT false,
    feedback_rating INTEGER CHECK (feedback_rating >= 1 AND feedback_rating <= 5),
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- FEATURE ACCESS LOG TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS feature_access_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    rep_id VARCHAR(100) NOT NULL REFERENCES sales_rep_onboarding(rep_id) ON DELETE CASCADE,
    
    -- Feature Access
    feature_name VARCHAR(100) NOT NULL,
    feature_category VARCHAR(50) NOT NULL,
    access_level VARCHAR(20) NOT NULL,
    
    -- Access Event
    event_type VARCHAR(50) NOT NULL CHECK (event_type IN ('granted', 'revoked', 'upgraded', 'downgraded', 'expired')),
    previous_access_level VARCHAR(20),
    reason VARCHAR(100),
    
    -- Context
    triggered_by VARCHAR(50), -- 'onboarding_progression', 'company_verification', 'subscription_change', 'manual'
    session_id VARCHAR(100),
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- ONBOARDING ANALYTICS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS onboarding_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    rep_id VARCHAR(100) NOT NULL REFERENCES sales_rep_onboarding(rep_id) ON DELETE CASCADE,
    
    -- Analytics Event
    event_type VARCHAR(100) NOT NULL,
    event_category VARCHAR(50) NOT NULL,
    event_data JSONB DEFAULT '{}',
    
    -- Timing
    event_timestamp TIMESTAMPTZ DEFAULT NOW(),
    session_id VARCHAR(100),
    page_url VARCHAR(500),
    
    -- User Context
    onboarding_state VARCHAR(50),
    feature_tier VARCHAR(20),
    days_since_signup INTEGER,
    
    -- Device/Browser
    user_agent TEXT,
    ip_address INET,
    device_type VARCHAR(50),
    browser VARCHAR(50),
    
    -- Psychological Context
    archetype VARCHAR(50),
    motivation_level DECIMAL(3,2),
    stress_level DECIMAL(3,2),
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Sales Rep Onboarding
CREATE INDEX IF NOT EXISTS idx_sales_rep_onboarding_rep_id ON sales_rep_onboarding(rep_id);
CREATE INDEX IF NOT EXISTS idx_sales_rep_onboarding_user_id ON sales_rep_onboarding(user_id);
CREATE INDEX IF NOT EXISTS idx_sales_rep_onboarding_email ON sales_rep_onboarding(email);
CREATE INDEX IF NOT EXISTS idx_sales_rep_onboarding_state ON sales_rep_onboarding(onboarding_state);
CREATE INDEX IF NOT EXISTS idx_sales_rep_onboarding_tier ON sales_rep_onboarding(feature_tier);
CREATE INDEX IF NOT EXISTS idx_sales_rep_onboarding_archetype ON sales_rep_onboarding(archetype);
CREATE INDEX IF NOT EXISTS idx_sales_rep_onboarding_created_at ON sales_rep_onboarding(created_at);
CREATE INDEX IF NOT EXISTS idx_sales_rep_onboarding_last_activity ON sales_rep_onboarding(last_activity);

-- Company Verifications
CREATE INDEX IF NOT EXISTS idx_company_verifications_rep_id ON company_verifications(rep_id);
CREATE INDEX IF NOT EXISTS idx_company_verifications_status ON company_verifications(status);
CREATE INDEX IF NOT EXISTS idx_company_verifications_company_name ON company_verifications(company_name);
CREATE INDEX IF NOT EXISTS idx_company_verifications_initiated_at ON company_verifications(initiated_at);

-- Progression Triggers
CREATE INDEX IF NOT EXISTS idx_onboarding_progression_triggers_rep_id ON onboarding_progression_triggers(rep_id);
CREATE INDEX IF NOT EXISTS idx_onboarding_progression_triggers_type ON onboarding_progression_triggers(trigger_type);
CREATE INDEX IF NOT EXISTS idx_onboarding_progression_triggers_triggered ON onboarding_progression_triggers(triggered);

-- Notifications
CREATE INDEX IF NOT EXISTS idx_sales_rep_notifications_rep_id ON sales_rep_notifications(rep_id);
CREATE INDEX IF NOT EXISTS idx_sales_rep_notifications_type ON sales_rep_notifications(type);
CREATE INDEX IF NOT EXISTS idx_sales_rep_notifications_read ON sales_rep_notifications(read);
CREATE INDEX IF NOT EXISTS idx_sales_rep_notifications_delivered_at ON sales_rep_notifications(delivered_at);

-- Feature Access Log
CREATE INDEX IF NOT EXISTS idx_feature_access_log_rep_id ON feature_access_log(rep_id);
CREATE INDEX IF NOT EXISTS idx_feature_access_log_feature_name ON feature_access_log(feature_name);
CREATE INDEX IF NOT EXISTS idx_feature_access_log_event_type ON feature_access_log(event_type);
CREATE INDEX IF NOT EXISTS idx_feature_access_log_created_at ON feature_access_log(created_at);

-- Analytics
CREATE INDEX IF NOT EXISTS idx_onboarding_analytics_rep_id ON onboarding_analytics(rep_id);
CREATE INDEX IF NOT EXISTS idx_onboarding_analytics_event_type ON onboarding_analytics(event_type);
CREATE INDEX IF NOT EXISTS idx_onboarding_analytics_event_timestamp ON onboarding_analytics(event_timestamp);
CREATE INDEX IF NOT EXISTS idx_onboarding_analytics_session_id ON onboarding_analytics(session_id);

-- =====================================================
-- ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE sales_rep_onboarding ENABLE ROW LEVEL SECURITY;
ALTER TABLE company_verifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE onboarding_progression_triggers ENABLE ROW LEVEL SECURITY;
ALTER TABLE sales_rep_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE feature_access_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE onboarding_analytics ENABLE ROW LEVEL SECURITY;

-- Basic RLS policies
CREATE POLICY "Allow reps to manage their own onboarding" ON sales_rep_onboarding
    FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Allow reps to view their company verifications" ON company_verifications
    FOR SELECT USING (rep_id IN (SELECT rep_id FROM sales_rep_onboarding WHERE user_id = auth.uid()));

CREATE POLICY "Allow reps to view their progression triggers" ON onboarding_progression_triggers
    FOR SELECT USING (rep_id IN (SELECT rep_id FROM sales_rep_onboarding WHERE user_id = auth.uid()));

CREATE POLICY "Allow reps to manage their notifications" ON sales_rep_notifications
    FOR ALL USING (rep_id IN (SELECT rep_id FROM sales_rep_onboarding WHERE user_id = auth.uid()));

CREATE POLICY "Allow reps to view their feature access log" ON feature_access_log
    FOR SELECT USING (rep_id IN (SELECT rep_id FROM sales_rep_onboarding WHERE user_id = auth.uid()));

CREATE POLICY "Allow reps to view their analytics" ON onboarding_analytics
    FOR SELECT USING (rep_id IN (SELECT rep_id FROM sales_rep_onboarding WHERE user_id = auth.uid()));

-- =====================================================
-- TRIGGERS FOR AUTOMATIC TIMESTAMPS
-- =====================================================

CREATE TRIGGER update_sales_rep_onboarding_updated_at BEFORE UPDATE ON sales_rep_onboarding
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_company_verifications_updated_at BEFORE UPDATE ON company_verifications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_onboarding_progression_triggers_updated_at BEFORE UPDATE ON onboarding_progression_triggers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sales_rep_notifications_updated_at BEFORE UPDATE ON sales_rep_notifications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- VIEWS FOR REPORTING
-- =====================================================

-- Onboarding funnel analysis
CREATE OR REPLACE VIEW onboarding_funnel_analysis AS
SELECT 
    onboarding_state,
    feature_tier,
    COUNT(*) as rep_count,
    AVG(EXTRACT(EPOCH FROM (NOW() - created_at))/86400) as avg_days_in_state,
    COUNT(*) FILTER (WHERE company_onboarding_initiated = true) as company_interested,
    COUNT(*) FILTER (WHERE company_verification_status = 'completed') as company_verified,
    AVG(progress_percentage) as avg_progress,
    AVG(achievements_unlocked) as avg_achievements,
    AVG(dashboard_sessions) as avg_sessions
FROM sales_rep_onboarding
GROUP BY onboarding_state, feature_tier
ORDER BY 
    CASE onboarding_state
        WHEN 'discovery' THEN 1
        WHEN 'hooked' THEN 2
        WHEN 'engaged' THEN 3
        WHEN 'invested' THEN 4
        WHEN 'company_curious' THEN 5
        WHEN 'company_motivated' THEN 6
        WHEN 'company_onboarding' THEN 7
        WHEN 'fully_integrated' THEN 8
    END;

-- Company onboarding conversion rates
CREATE OR REPLACE VIEW company_onboarding_conversion AS
SELECT 
    archetype,
    COUNT(*) as total_reps,
    COUNT(*) FILTER (WHERE company_benefits_page_views > 0) as viewed_benefits,
    COUNT(*) FILTER (WHERE team_features_previewed = true) as previewed_features,
    COUNT(*) FILTER (WHERE company_onboarding_initiated = true) as initiated_onboarding,
    COUNT(*) FILTER (WHERE company_verification_status = 'completed') as completed_verification,
    ROUND(COUNT(*) FILTER (WHERE company_onboarding_initiated = true)::decimal / COUNT(*) * 100, 2) as conversion_rate
FROM sales_rep_onboarding
WHERE onboarding_state IN ('invested', 'company_curious', 'company_motivated', 'company_onboarding', 'fully_integrated')
GROUP BY archetype
ORDER BY conversion_rate DESC;
