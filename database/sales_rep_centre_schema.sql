-- =====================================================
-- SALES REP CENTRE DATABASE SCHEMA
-- Behavioral Psychology & Gamification for Supplier Sales Reps
-- =====================================================

-- Enable Row Level Security
ALTER DATABASE postgres SET row_security = on;

-- =====================================================
-- SALES REP PROFILES TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS sales_rep_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    rep_id VARCHAR(100) UNIQUE NOT NULL,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    supplier_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Psychological Profile
    archetype VARCHAR(50) NOT NULL CHECK (archetype IN ('achiever', 'hunter', 'relationship_builder', 'analyst')),
    motivation_factors TEXT[] DEFAULT '{}',
    psychological_state JSONB DEFAULT '{}',
    target_preferences JSONB DEFAULT '{}',
    behavioral_patterns JSONB DEFAULT '{}',
    performance_history JSONB DEFAULT '{}',
    
    -- Profile Status
    onboarding_completed BOOLEAN DEFAULT false,
    profile_completion_percentage INTEGER DEFAULT 0 CHECK (profile_completion_percentage >= 0 AND profile_completion_percentage <= 100),
    last_psychological_update TIMESTAMPTZ,
    
    -- Engagement Metrics
    total_xp INTEGER DEFAULT 0,
    current_level INTEGER DEFAULT 1,
    engagement_score DECIMAL(5,2) DEFAULT 0.00,
    platform_usage_hours DECIMAL(8,2) DEFAULT 0.00,
    last_active TIMESTAMPTZ,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- SALES TARGETS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS sales_targets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    target_id VARCHAR(100) UNIQUE NOT NULL,
    rep_id VARCHAR(100) NOT NULL REFERENCES sales_rep_profiles(rep_id) ON DELETE CASCADE,
    
    -- Target Details
    target_type VARCHAR(50) NOT NULL CHECK (target_type IN ('revenue', 'volume', 'deals', 'clients', 'retention', 'upsell')),
    period VARCHAR(20) NOT NULL CHECK (period IN ('daily', 'weekly', 'monthly', 'quarterly', 'yearly')),
    target_value DECIMAL(15,2) NOT NULL,
    current_value DECIMAL(15,2) DEFAULT 0.00,
    unit VARCHAR(20) NOT NULL, -- ZAR, units, count, percentage
    
    -- Time Boundaries
    start_date TIMESTAMPTZ NOT NULL,
    end_date TIMESTAMPTZ NOT NULL,
    days_remaining INTEGER GENERATED ALWAYS AS (EXTRACT(DAY FROM (end_date - NOW()))) STORED,
    completion_percentage DECIMAL(5,2) GENERATED ALWAYS AS (
        CASE 
            WHEN target_value > 0 THEN LEAST(100.0, (current_value / target_value) * 100)
            ELSE 0.0
        END
    ) STORED,
    
    -- Psychological Calibration
    psychological_calibration JSONB DEFAULT '{}',
    milestone_rewards JSONB DEFAULT '[]',
    stress_indicators JSONB DEFAULT '{}',
    motivation_triggers JSONB DEFAULT '{}',
    
    -- Target Status
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'paused', 'completed', 'failed', 'cancelled')),
    auto_adjust BOOLEAN DEFAULT true,
    last_adjustment TIMESTAMPTZ,
    adjustment_reason TEXT,
    
    -- Performance Tracking
    daily_progress JSONB DEFAULT '{}',
    weekly_progress JSONB DEFAULT '{}',
    velocity_trend DECIMAL(5,2) DEFAULT 0.00,
    predicted_completion DECIMAL(5,2) DEFAULT 0.00,
    
    -- Metadata
    notes TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- SALES REP ACHIEVEMENTS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS sales_rep_achievements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    achievement_id VARCHAR(100) UNIQUE NOT NULL,
    rep_id VARCHAR(100) NOT NULL REFERENCES sales_rep_profiles(rep_id) ON DELETE CASCADE,
    
    -- Achievement Details
    name VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    icon VARCHAR(10) NOT NULL,
    tier VARCHAR(20) NOT NULL CHECK (tier IN ('bronze', 'silver', 'gold', 'platinum', 'diamond')),
    category VARCHAR(50) NOT NULL CHECK (category IN ('revenue', 'target', 'consistency', 'growth', 'relationship', 'milestone', 'special')),
    rarity VARCHAR(20) NOT NULL CHECK (rarity IN ('common', 'uncommon', 'rare', 'epic', 'legendary')),
    
    -- Rewards
    xp_reward INTEGER NOT NULL DEFAULT 0,
    psychological_benefit TEXT,
    unlock_requirements JSONB DEFAULT '{}',
    
    -- Achievement Status
    unlocked_at TIMESTAMPTZ DEFAULT NOW(),
    progress_when_unlocked JSONB DEFAULT '{}',
    celebration_shown BOOLEAN DEFAULT false,
    shared_with_team BOOLEAN DEFAULT false,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- BEHAVIORAL NUDGES TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS sales_rep_nudges (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    nudge_id VARCHAR(100) UNIQUE NOT NULL,
    rep_id VARCHAR(100) NOT NULL REFERENCES sales_rep_profiles(rep_id) ON DELETE CASCADE,
    
    -- Nudge Details
    trigger_type VARCHAR(50) NOT NULL CHECK (trigger_type IN ('urgency', 'scarcity', 'social_proof', 'authority', 'commitment', 'reciprocity', 'loss_aversion', 'achievement')),
    message TEXT NOT NULL,
    intensity VARCHAR(20) NOT NULL CHECK (intensity IN ('low', 'medium', 'high')),
    timing VARCHAR(20) NOT NULL CHECK (timing IN ('immediate', 'scheduled', 'optimal')),
    
    -- Psychological Framework
    psychological_principle TEXT NOT NULL,
    effectiveness_score DECIMAL(3,2) DEFAULT 0.00,
    personalized_for VARCHAR(50),
    context_data JSONB DEFAULT '{}',
    
    -- Nudge Lifecycle
    created_at TIMESTAMPTZ DEFAULT NOW(),
    scheduled_for TIMESTAMPTZ,
    delivered_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ,
    
    -- Response Tracking
    viewed BOOLEAN DEFAULT false,
    viewed_at TIMESTAMPTZ,
    clicked BOOLEAN DEFAULT false,
    clicked_at TIMESTAMPTZ,
    dismissed BOOLEAN DEFAULT false,
    dismissed_at TIMESTAMPTZ,
    action_taken BOOLEAN DEFAULT false,
    action_taken_at TIMESTAMPTZ,
    
    -- Effectiveness Metrics
    response_time_seconds INTEGER,
    conversion_achieved BOOLEAN DEFAULT false,
    feedback_rating INTEGER CHECK (feedback_rating >= 1 AND feedback_rating <= 5),
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- SALES REP LEADERBOARD TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS sales_rep_leaderboard (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    rep_id VARCHAR(100) NOT NULL REFERENCES sales_rep_profiles(rep_id) ON DELETE CASCADE,
    
    -- Ranking Details
    period VARCHAR(20) NOT NULL CHECK (period IN ('daily', 'weekly', 'monthly', 'quarterly', 'yearly', 'all_time')),
    category VARCHAR(50) NOT NULL CHECK (category IN ('revenue', 'targets', 'growth', 'consistency', 'relationships', 'overall')),
    rank INTEGER NOT NULL,
    score DECIMAL(10,2) NOT NULL,
    
    -- Performance Metrics
    total_revenue DECIMAL(15,2) DEFAULT 0.00,
    target_achievement_rate DECIMAL(5,2) DEFAULT 0.00,
    growth_rate DECIMAL(5,2) DEFAULT 0.00,
    consistency_score DECIMAL(5,2) DEFAULT 0.00,
    relationship_score DECIMAL(5,2) DEFAULT 0.00,
    
    -- Ranking Changes
    previous_rank INTEGER,
    rank_change INTEGER GENERATED ALWAYS AS (COALESCE(previous_rank, rank) - rank) STORED,
    rank_change_direction VARCHAR(10) GENERATED ALWAYS AS (
        CASE 
            WHEN previous_rank IS NULL THEN 'new'
            WHEN previous_rank > rank THEN 'up'
            WHEN previous_rank < rank THEN 'down'
            ELSE 'same'
        END
    ) STORED,
    
    -- Time Tracking
    period_start TIMESTAMPTZ NOT NULL,
    period_end TIMESTAMPTZ NOT NULL,
    last_updated TIMESTAMPTZ DEFAULT NOW(),
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(rep_id, period, category, period_start)
);

-- =====================================================
-- SALES REP ACTIVITY LOG TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS sales_rep_activity_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    activity_id VARCHAR(100) UNIQUE NOT NULL,
    rep_id VARCHAR(100) NOT NULL REFERENCES sales_rep_profiles(rep_id) ON DELETE CASCADE,
    
    -- Activity Details
    activity_type VARCHAR(100) NOT NULL,
    activity_category VARCHAR(50) NOT NULL CHECK (activity_category IN ('target', 'achievement', 'nudge', 'engagement', 'performance', 'social')),
    description TEXT,
    
    -- Activity Data
    activity_data JSONB DEFAULT '{}',
    impact_score DECIMAL(5,2) DEFAULT 0.00,
    xp_earned INTEGER DEFAULT 0,
    
    -- Psychological Impact
    psychological_effect JSONB DEFAULT '{}',
    motivation_change DECIMAL(3,2) DEFAULT 0.00,
    confidence_change DECIMAL(3,2) DEFAULT 0.00,
    stress_change DECIMAL(3,2) DEFAULT 0.00,
    
    -- Context
    session_id VARCHAR(100),
    ip_address INET,
    user_agent TEXT,
    device_type VARCHAR(50),
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- SALES REP PSYCHOLOGICAL STATES TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS sales_rep_psychological_states (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    rep_id VARCHAR(100) NOT NULL REFERENCES sales_rep_profiles(rep_id) ON DELETE CASCADE,
    
    -- Psychological Metrics (0.0 to 1.0 scale)
    stress_level DECIMAL(3,2) NOT NULL DEFAULT 0.30 CHECK (stress_level >= 0 AND stress_level <= 1),
    motivation_level DECIMAL(3,2) NOT NULL DEFAULT 0.50 CHECK (motivation_level >= 0 AND motivation_level <= 1),
    confidence_level DECIMAL(3,2) NOT NULL DEFAULT 0.50 CHECK (confidence_level >= 0 AND confidence_level <= 1),
    urgency_response DECIMAL(3,2) NOT NULL DEFAULT 0.50 CHECK (urgency_response >= 0 AND urgency_response <= 1),
    competitive_spirit DECIMAL(3,2) NOT NULL DEFAULT 0.50 CHECK (competitive_spirit >= 0 AND competitive_spirit <= 1),
    financial_motivation DECIMAL(3,2) NOT NULL DEFAULT 0.50 CHECK (financial_motivation >= 0 AND financial_motivation <= 1),
    cognitive_load DECIMAL(3,2) NOT NULL DEFAULT 0.30 CHECK (cognitive_load >= 0 AND cognitive_load <= 1),
    engagement_level DECIMAL(3,2) NOT NULL DEFAULT 0.50 CHECK (engagement_level >= 0 AND engagement_level <= 1),
    
    -- Behavioral Indicators
    mouse_velocity_avg DECIMAL(8,2) DEFAULT 0.00,
    click_intensity_avg DECIMAL(5,2) DEFAULT 0.00,
    scroll_consistency DECIMAL(3,2) DEFAULT 0.50,
    session_duration_minutes INTEGER DEFAULT 0,
    page_switches_per_minute DECIMAL(5,2) DEFAULT 0.00,
    
    -- Calculation Context
    calculation_method VARCHAR(50) DEFAULT 'behavioral_analysis',
    data_points_used INTEGER DEFAULT 0,
    confidence_score DECIMAL(3,2) DEFAULT 0.50,
    
    -- Time Tracking
    measured_at TIMESTAMPTZ DEFAULT NOW(),
    valid_until TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '1 hour'),
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Sales Rep Profiles
CREATE INDEX IF NOT EXISTS idx_sales_rep_profiles_rep_id ON sales_rep_profiles(rep_id);
CREATE INDEX IF NOT EXISTS idx_sales_rep_profiles_user_id ON sales_rep_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_sales_rep_profiles_supplier_id ON sales_rep_profiles(supplier_id);
CREATE INDEX IF NOT EXISTS idx_sales_rep_profiles_archetype ON sales_rep_profiles(archetype);
CREATE INDEX IF NOT EXISTS idx_sales_rep_profiles_last_active ON sales_rep_profiles(last_active);

-- Sales Targets
CREATE INDEX IF NOT EXISTS idx_sales_targets_rep_id ON sales_targets(rep_id);
CREATE INDEX IF NOT EXISTS idx_sales_targets_target_type ON sales_targets(target_type);
CREATE INDEX IF NOT EXISTS idx_sales_targets_period ON sales_targets(period);
CREATE INDEX IF NOT EXISTS idx_sales_targets_status ON sales_targets(status);
CREATE INDEX IF NOT EXISTS idx_sales_targets_end_date ON sales_targets(end_date);
CREATE INDEX IF NOT EXISTS idx_sales_targets_completion ON sales_targets(completion_percentage);

-- Sales Rep Achievements
CREATE INDEX IF NOT EXISTS idx_sales_rep_achievements_rep_id ON sales_rep_achievements(rep_id);
CREATE INDEX IF NOT EXISTS idx_sales_rep_achievements_category ON sales_rep_achievements(category);
CREATE INDEX IF NOT EXISTS idx_sales_rep_achievements_tier ON sales_rep_achievements(tier);
CREATE INDEX IF NOT EXISTS idx_sales_rep_achievements_unlocked_at ON sales_rep_achievements(unlocked_at);

-- Behavioral Nudges
CREATE INDEX IF NOT EXISTS idx_sales_rep_nudges_rep_id ON sales_rep_nudges(rep_id);
CREATE INDEX IF NOT EXISTS idx_sales_rep_nudges_trigger_type ON sales_rep_nudges(trigger_type);
CREATE INDEX IF NOT EXISTS idx_sales_rep_nudges_delivered_at ON sales_rep_nudges(delivered_at);
CREATE INDEX IF NOT EXISTS idx_sales_rep_nudges_expires_at ON sales_rep_nudges(expires_at);

-- Leaderboard
CREATE INDEX IF NOT EXISTS idx_sales_rep_leaderboard_period ON sales_rep_leaderboard(period);
CREATE INDEX IF NOT EXISTS idx_sales_rep_leaderboard_category ON sales_rep_leaderboard(category);
CREATE INDEX IF NOT EXISTS idx_sales_rep_leaderboard_rank ON sales_rep_leaderboard(rank);
CREATE INDEX IF NOT EXISTS idx_sales_rep_leaderboard_score ON sales_rep_leaderboard(score);

-- Activity Log
CREATE INDEX IF NOT EXISTS idx_sales_rep_activity_log_rep_id ON sales_rep_activity_log(rep_id);
CREATE INDEX IF NOT EXISTS idx_sales_rep_activity_log_type ON sales_rep_activity_log(activity_type);
CREATE INDEX IF NOT EXISTS idx_sales_rep_activity_log_created_at ON sales_rep_activity_log(created_at);

-- Psychological States
CREATE INDEX IF NOT EXISTS idx_sales_rep_psychological_states_rep_id ON sales_rep_psychological_states(rep_id);
CREATE INDEX IF NOT EXISTS idx_sales_rep_psychological_states_measured_at ON sales_rep_psychological_states(measured_at);

-- =====================================================
-- ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE sales_rep_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE sales_targets ENABLE ROW LEVEL SECURITY;
ALTER TABLE sales_rep_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE sales_rep_nudges ENABLE ROW LEVEL SECURITY;
ALTER TABLE sales_rep_leaderboard ENABLE ROW LEVEL SECURITY;
ALTER TABLE sales_rep_activity_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE sales_rep_psychological_states ENABLE ROW LEVEL SECURITY;

-- Basic RLS policies
CREATE POLICY "Allow reps to manage their own profiles" ON sales_rep_profiles
    FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Allow reps to manage their own targets" ON sales_targets
    FOR ALL USING (rep_id IN (SELECT rep_id FROM sales_rep_profiles WHERE user_id = auth.uid()));

CREATE POLICY "Allow reps to view their own achievements" ON sales_rep_achievements
    FOR SELECT USING (rep_id IN (SELECT rep_id FROM sales_rep_profiles WHERE user_id = auth.uid()));

CREATE POLICY "Allow reps to view their own nudges" ON sales_rep_nudges
    FOR ALL USING (rep_id IN (SELECT rep_id FROM sales_rep_profiles WHERE user_id = auth.uid()));

CREATE POLICY "Allow reps to view leaderboard" ON sales_rep_leaderboard
    FOR SELECT USING (true); -- Leaderboard is public within organization

CREATE POLICY "Allow reps to view their own activity" ON sales_rep_activity_log
    FOR SELECT USING (rep_id IN (SELECT rep_id FROM sales_rep_profiles WHERE user_id = auth.uid()));

CREATE POLICY "Allow reps to view their own psychological states" ON sales_rep_psychological_states
    FOR SELECT USING (rep_id IN (SELECT rep_id FROM sales_rep_profiles WHERE user_id = auth.uid()));

-- =====================================================
-- TRIGGERS FOR AUTOMATIC TIMESTAMPS
-- =====================================================

CREATE TRIGGER update_sales_rep_profiles_updated_at BEFORE UPDATE ON sales_rep_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sales_targets_updated_at BEFORE UPDATE ON sales_targets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sales_rep_nudges_updated_at BEFORE UPDATE ON sales_rep_nudges
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- VIEWS FOR REPORTING
-- =====================================================

-- Sales rep performance summary
CREATE OR REPLACE VIEW sales_rep_performance_summary AS
SELECT 
    srp.rep_id,
    srp.archetype,
    srp.total_xp,
    srp.current_level,
    srp.engagement_score,
    COUNT(st.id) as total_targets,
    COUNT(st.id) FILTER (WHERE st.status = 'completed') as completed_targets,
    AVG(st.completion_percentage) as avg_completion_rate,
    COUNT(sra.id) as total_achievements,
    COUNT(sra.id) FILTER (WHERE sra.tier = 'gold') as gold_achievements,
    AVG(srps.motivation_level) as avg_motivation,
    AVG(srps.confidence_level) as avg_confidence,
    AVG(srps.stress_level) as avg_stress
FROM sales_rep_profiles srp
LEFT JOIN sales_targets st ON srp.rep_id = st.rep_id
LEFT JOIN sales_rep_achievements sra ON srp.rep_id = sra.rep_id
LEFT JOIN sales_rep_psychological_states srps ON srp.rep_id = srps.rep_id
GROUP BY srp.rep_id, srp.archetype, srp.total_xp, srp.current_level, srp.engagement_score
ORDER BY srp.total_xp DESC, srp.engagement_score DESC;
