-- =====================================================
-- BID INFORMATION TRACKING & ALERT SYSTEM DATABASE SCHEMA
-- Comprehensive bid monitoring and multi-channel alerts
-- =====================================================

-- Bid Tracking Table - Track all bids users are involved with
CREATE TABLE bid_tracking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    opportunity_id UUID NOT NULL, -- References tenders, government_rfqs, or bidder_rfqs
    opportunity_type VARCHAR(20) NOT NULL CHECK (opportunity_type IN ('tender', 'government_rfq', 'bidder_rfq')),
    
    -- Bid Details
    bid_reference VARCHAR(100) NOT NULL,
    organization_name VARCHAR(200) NOT NULL,
    bid_title VARCHAR(500) NOT NULL,
    bid_value DECIMAL(15,2),
    submission_date TIMESTAMP,
    closing_date TIMESTAMP NOT NULL,
    
    -- Tracking Status
    tracking_status VARCHAR(20) DEFAULT 'active' CHECK (tracking_status IN ('active', 'completed', 'cancelled', 'paused')),
    current_status VARCHAR(30) DEFAULT 'submitted' CHECK (current_status IN (
        'draft', 'submitted', 'under_evaluation', 'shortlisted', 'awarded', 'rejected', 'cancelled', 'extended'
    )),
    
    -- Monitoring Configuration
    monitor_awards BOOLEAN DEFAULT true,
    monitor_addendums BOOLEAN DEFAULT true,
    monitor_clarifications BOOLEAN DEFAULT true,
    monitor_competitors BOOLEAN DEFAULT true,
    monitor_deadlines BOOLEAN DEFAULT true,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_checked TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes
    UNIQUE(user_id, opportunity_id, opportunity_type)
);

-- Bid Updates Table - Log all detected changes and developments
CREATE TABLE bid_updates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bid_tracking_id UUID NOT NULL REFERENCES bid_tracking(id) ON DELETE CASCADE,
    
    -- Update Details
    update_type VARCHAR(30) NOT NULL CHECK (update_type IN (
        'award_announcement', 'rejection_notice', 'cancellation', 'addendum', 'clarification',
        'deadline_extension', 'status_change', 'competitor_activity', 'document_update'
    )),
    update_priority VARCHAR(10) NOT NULL CHECK (update_priority IN ('critical', 'high', 'medium', 'low')),
    
    -- Content
    title VARCHAR(500) NOT NULL,
    description TEXT,
    source_url VARCHAR(1000),
    source_type VARCHAR(50), -- 'official_portal', 'government_site', 'organization_website', 'bee_worker'
    
    -- Status Information
    previous_status VARCHAR(30),
    new_status VARCHAR(30),
    
    -- Documents
    document_urls TEXT[], -- Array of document URLs
    document_names TEXT[], -- Array of document names
    
    -- Competitive Intelligence
    competitor_info JSONB, -- Store competitor-related information
    
    -- Detection Metadata
    detected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    confidence_score DECIMAL(3,2) DEFAULT 1.00, -- 0.00 to 1.00
    verification_status VARCHAR(20) DEFAULT 'pending' CHECK (verification_status IN ('pending', 'verified', 'disputed', 'false_positive')),
    verified_by UUID REFERENCES users(id), -- Bee worker or admin who verified
    verified_at TIMESTAMP,
    
    -- Alert Status
    alerts_sent BOOLEAN DEFAULT false,
    alert_channels TEXT[], -- Array of channels used: ['email', 'sms', 'whatsapp', 'push']
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Alert Preferences Table - User notification preferences
CREATE TABLE alert_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Channel Preferences
    email_enabled BOOLEAN DEFAULT true,
    sms_enabled BOOLEAN DEFAULT true,
    whatsapp_enabled BOOLEAN DEFAULT true,
    push_enabled BOOLEAN DEFAULT true,
    
    -- Priority-based Channel Preferences
    critical_channels TEXT[] DEFAULT ARRAY['email', 'sms', 'whatsapp', 'push'],
    high_channels TEXT[] DEFAULT ARRAY['email', 'whatsapp', 'push'],
    medium_channels TEXT[] DEFAULT ARRAY['email', 'push'],
    low_channels TEXT[] DEFAULT ARRAY['push'],
    
    -- Frequency Preferences
    immediate_alerts BOOLEAN DEFAULT true,
    daily_digest BOOLEAN DEFAULT true,
    weekly_summary BOOLEAN DEFAULT true,
    
    -- Quiet Hours
    quiet_hours_enabled BOOLEAN DEFAULT false,
    quiet_start_time TIME DEFAULT '22:00:00',
    quiet_end_time TIME DEFAULT '07:00:00',
    quiet_timezone VARCHAR(50) DEFAULT 'Africa/Johannesburg',
    
    -- Update Type Preferences
    award_alerts BOOLEAN DEFAULT true,
    addendum_alerts BOOLEAN DEFAULT true,
    clarification_alerts BOOLEAN DEFAULT true,
    competitor_alerts BOOLEAN DEFAULT true,
    deadline_alerts BOOLEAN DEFAULT true,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(user_id)
);

-- Competitive Intelligence Table - Track competitor activity
CREATE TABLE competitive_intelligence (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bid_tracking_id UUID NOT NULL REFERENCES bid_tracking(id) ON DELETE CASCADE,
    
    -- Competitor Information
    competitor_name VARCHAR(200),
    competitor_type VARCHAR(50), -- 'known_company', 'new_entrant', 'frequent_competitor'
    
    -- Activity Details
    activity_type VARCHAR(50) NOT NULL CHECK (activity_type IN (
        'bid_submission', 'clarification_request', 'site_visit', 'document_collection',
        'addendum_response', 'protest_filing', 'award_received', 'contract_signed'
    )),
    activity_description TEXT,
    activity_date TIMESTAMP,
    
    -- Intelligence Source
    source_type VARCHAR(50) NOT NULL, -- 'official_records', 'bee_worker_observation', 'public_announcement'
    source_details TEXT,
    confidence_level VARCHAR(10) CHECK (confidence_level IN ('high', 'medium', 'low')),
    
    -- Impact Assessment
    threat_level VARCHAR(10) CHECK (threat_level IN ('high', 'medium', 'low', 'none')),
    impact_description TEXT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Alert History Table - Track all sent alerts
CREATE TABLE alert_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bid_update_id UUID NOT NULL REFERENCES bid_updates(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Alert Details
    channel VARCHAR(20) NOT NULL CHECK (channel IN ('email', 'sms', 'whatsapp', 'push')),
    alert_type VARCHAR(30) NOT NULL,
    priority VARCHAR(10) NOT NULL,
    
    -- Content
    subject VARCHAR(500),
    message TEXT NOT NULL,
    
    -- Delivery Status
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'delivered', 'failed', 'bounced')),
    sent_at TIMESTAMP,
    delivered_at TIMESTAMP,
    error_message TEXT,
    
    -- Engagement
    opened_at TIMESTAMP,
    clicked_at TIMESTAMP,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bid Monitoring Sources Table - Track information sources
CREATE TABLE bid_monitoring_sources (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Source Details
    source_name VARCHAR(200) NOT NULL,
    source_type VARCHAR(50) NOT NULL, -- 'government_portal', 'organization_website', 'tender_portal'
    base_url VARCHAR(1000) NOT NULL,
    
    -- Monitoring Configuration
    is_active BOOLEAN DEFAULT true,
    check_frequency_minutes INTEGER DEFAULT 60,
    last_checked TIMESTAMP,
    
    -- Authentication
    requires_auth BOOLEAN DEFAULT false,
    auth_type VARCHAR(20), -- 'basic', 'oauth', 'api_key'
    auth_config JSONB, -- Store authentication configuration
    
    -- Parsing Configuration
    parsing_rules JSONB, -- Store rules for extracting information
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Bid Tracking Indexes
CREATE INDEX idx_bid_tracking_user_id ON bid_tracking(user_id);
CREATE INDEX idx_bid_tracking_status ON bid_tracking(tracking_status, current_status);
CREATE INDEX idx_bid_tracking_closing_date ON bid_tracking(closing_date);
CREATE INDEX idx_bid_tracking_last_checked ON bid_tracking(last_checked);

-- Bid Updates Indexes
CREATE INDEX idx_bid_updates_tracking_id ON bid_updates(bid_tracking_id);
CREATE INDEX idx_bid_updates_type_priority ON bid_updates(update_type, update_priority);
CREATE INDEX idx_bid_updates_detected_at ON bid_updates(detected_at);
CREATE INDEX idx_bid_updates_alerts_sent ON bid_updates(alerts_sent);

-- Alert History Indexes
CREATE INDEX idx_alert_history_user_id ON alert_history(user_id);
CREATE INDEX idx_alert_history_channel ON alert_history(channel);
CREATE INDEX idx_alert_history_status ON alert_history(status);
CREATE INDEX idx_alert_history_sent_at ON alert_history(sent_at);

-- Competitive Intelligence Indexes
CREATE INDEX idx_competitive_intelligence_bid_id ON competitive_intelligence(bid_tracking_id);
CREATE INDEX idx_competitive_intelligence_competitor ON competitive_intelligence(competitor_name);
CREATE INDEX idx_competitive_intelligence_activity_date ON competitive_intelligence(activity_date);

-- =====================================================
-- FUNCTIONS AND TRIGGERS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_bid_tracking_updated_at BEFORE UPDATE ON bid_tracking FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_alert_preferences_updated_at BEFORE UPDATE ON alert_preferences FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_bid_monitoring_sources_updated_at BEFORE UPDATE ON bid_monitoring_sources FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to automatically create alert preferences for new users
CREATE OR REPLACE FUNCTION create_default_alert_preferences()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO alert_preferences (user_id) VALUES (NEW.id);
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to create default alert preferences
CREATE TRIGGER create_user_alert_preferences AFTER INSERT ON users FOR EACH ROW EXECUTE FUNCTION create_default_alert_preferences();
