"""
Sales Rep Behavioral Psychology Engine
Implements the same psychological engagement strategies as the bidder module
"""

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, <PERSON>
from typing import Dict, List, Optional, Union
import uuid
import json
import logging
from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decimal
from enum import Enum
import asyncio

# Supabase client
from supabase import create_client, Client
import os

# Initialize Supabase
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_ANON_KEY")
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# Logging setup
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="BidBeez Sales Rep Behavioral Engine",
    description="Psychological engagement system for supplier sales representatives",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# =====================================================
# PSYCHOLOGICAL MODELS
# =====================================================

class SalesRepArchetype(str, Enum):
    ACHIEVER = "achiever"          # 35% - Recognition, competition, status
    HUNTER = "hunter"              # 30% - Financial rewards, commission
    RELATIONSHIP_BUILDER = "relationship_builder"  # 25% - Client satisfaction
    ANALYST = "analyst"            # 10% - Data insights, optimization

class MotivationFactor(str, Enum):
    ACHIEVEMENT = "achievement"
    FINANCIAL = "financial"
    RECOGNITION = "recognition"
    COMPETITION = "competition"
    RELATIONSHIP = "relationship"
    GROWTH = "growth"
    SECURITY = "security"

class BehavioralTrigger(str, Enum):
    URGENCY = "urgency"
    SCARCITY = "scarcity"
    SOCIAL_PROOF = "social_proof"
    AUTHORITY = "authority"
    COMMITMENT = "commitment"
    RECIPROCITY = "reciprocity"
    LOSS_AVERSION = "loss_aversion"

class PsychologicalState(BaseModel):
    stress_level: float = Field(ge=0, le=1)
    motivation_level: float = Field(ge=0, le=1)
    confidence_level: float = Field(ge=0, le=1)
    urgency_response: float = Field(ge=0, le=1)
    competitive_spirit: float = Field(ge=0, le=1)
    financial_motivation: float = Field(ge=0, le=1)
    cognitive_load: float = Field(ge=0, le=1)
    engagement_level: float = Field(ge=0, le=1)

class SalesRepProfile(BaseModel):
    rep_id: str
    user_id: str
    supplier_id: str
    archetype: SalesRepArchetype
    motivation_factors: List[MotivationFactor]
    psychological_state: PsychologicalState
    target_preferences: Dict
    behavioral_patterns: Dict
    performance_history: Dict
    onboarding_completed: bool = False

class SalesTarget(BaseModel):
    target_id: str
    rep_id: str
    target_type: str  # revenue, volume, deals, clients
    period: str  # monthly, quarterly, yearly
    target_value: Decimal
    current_value: Decimal = Decimal('0')
    unit: str  # ZAR, units, count
    start_date: datetime
    end_date: datetime
    psychological_calibration: Dict
    milestone_rewards: List[Dict]

class BehavioralNudge(BaseModel):
    nudge_id: str
    rep_id: str
    trigger_type: BehavioralTrigger
    message: str
    intensity: str  # low, medium, high
    timing: str  # immediate, scheduled, optimal
    psychological_principle: str
    effectiveness_score: float
    personalized_for: SalesRepArchetype
    expires_at: Optional[datetime] = None

class Achievement(BaseModel):
    achievement_id: str
    name: str
    description: str
    icon: str
    tier: str  # bronze, silver, gold, platinum, diamond
    category: str  # revenue, target, consistency, growth, relationship
    xp_reward: int
    psychological_benefit: str
    requirements: Dict
    rarity: str  # common, uncommon, rare, epic, legendary

# =====================================================
# SALES REP NEUROMARKETING ENGINE
# =====================================================

class SalesRepNeuroEngine:
    def __init__(self):
        self.psychological_profiles = {}
        self.behavioral_patterns = {}
        self.nudge_effectiveness = {}
        
        # Achievement definitions
        self.achievements = [
            {
                "name": "First Sale",
                "description": "Close your first deal",
                "icon": "🎯",
                "tier": "bronze",
                "category": "milestone",
                "xp_reward": 100,
                "psychological_benefit": "Confidence building and momentum creation",
                "requirements": {"deals_closed": 1},
                "rarity": "common"
            },
            {
                "name": "Target Crusher",
                "description": "Hit monthly target 3 months in a row",
                "icon": "💪",
                "tier": "silver",
                "category": "consistency",
                "xp_reward": 500,
                "psychological_benefit": "Consistency validation and habit reinforcement",
                "requirements": {"consecutive_target_hits": 3},
                "rarity": "uncommon"
            },
            {
                "name": "Revenue Rocket",
                "description": "Exceed monthly target by 150%",
                "icon": "🚀",
                "tier": "gold",
                "category": "revenue",
                "xp_reward": 1000,
                "psychological_benefit": "Achievement satisfaction and status elevation",
                "requirements": {"monthly_target_percentage": 150},
                "rarity": "rare"
            },
            {
                "name": "Million Maker",
                "description": "Generate R1M in annual revenue",
                "icon": "💎",
                "tier": "diamond",
                "category": "revenue",
                "xp_reward": 5000,
                "psychological_benefit": "Elite status and self-actualization",
                "requirements": {"annual_revenue": 1000000},
                "rarity": "legendary"
            },
            {
                "name": "Client Champion",
                "description": "Maintain 95%+ client satisfaction for 6 months",
                "icon": "🤝",
                "tier": "platinum",
                "category": "relationship",
                "xp_reward": 2000,
                "psychological_benefit": "Relationship mastery and trust building",
                "requirements": {"client_satisfaction": 95, "duration_months": 6},
                "rarity": "epic"
            }
        ]
    
    async def analyze_rep_psychology(self, rep_id: str, behavioral_data: Dict) -> SalesRepProfile:
        """Analyze sales rep psychology and create profile"""
        try:
            # Determine archetype based on behavior patterns
            archetype = await self.determine_archetype(behavioral_data)
            
            # Identify motivation factors
            motivation_factors = await self.identify_motivation_factors(behavioral_data, archetype)
            
            # Calculate psychological state
            psychological_state = await self.calculate_psychological_state(behavioral_data)
            
            # Get target preferences
            target_preferences = await self.analyze_target_preferences(behavioral_data, archetype)
            
            profile = SalesRepProfile(
                rep_id=rep_id,
                user_id=behavioral_data.get("user_id"),
                supplier_id=behavioral_data.get("supplier_id"),
                archetype=archetype,
                motivation_factors=motivation_factors,
                psychological_state=psychological_state,
                target_preferences=target_preferences,
                behavioral_patterns=behavioral_data,
                performance_history=behavioral_data.get("performance_history", {})
            )
            
            # Save profile
            await self.save_psychological_profile(profile)
            
            return profile
            
        except Exception as e:
            logger.error(f"Error analyzing rep psychology: {e}")
            raise
    
    async def determine_archetype(self, behavioral_data: Dict) -> SalesRepArchetype:
        """Determine sales rep psychological archetype"""
        try:
            scores = {
                SalesRepArchetype.ACHIEVER: 0,
                SalesRepArchetype.HUNTER: 0,
                SalesRepArchetype.RELATIONSHIP_BUILDER: 0,
                SalesRepArchetype.ANALYST: 0
            }
            
            # Analyze behavioral indicators
            if behavioral_data.get("competition_engagement", 0) > 0.7:
                scores[SalesRepArchetype.ACHIEVER] += 30
            
            if behavioral_data.get("revenue_focus", 0) > 0.8:
                scores[SalesRepArchetype.HUNTER] += 30
            
            if behavioral_data.get("client_interaction_time", 0) > 0.7:
                scores[SalesRepArchetype.RELATIONSHIP_BUILDER] += 30
            
            if behavioral_data.get("analytics_usage", 0) > 0.8:
                scores[SalesRepArchetype.ANALYST] += 30
            
            # Additional scoring based on preferences
            preferences = behavioral_data.get("preferences", {})
            if preferences.get("leaderboard_views", 0) > 10:
                scores[SalesRepArchetype.ACHIEVER] += 20
            
            if preferences.get("commission_calculator_usage", 0) > 5:
                scores[SalesRepArchetype.HUNTER] += 20
            
            if preferences.get("client_feedback_checks", 0) > 8:
                scores[SalesRepArchetype.RELATIONSHIP_BUILDER] += 20
            
            if preferences.get("report_generation", 0) > 3:
                scores[SalesRepArchetype.ANALYST] += 20
            
            # Return highest scoring archetype
            return max(scores, key=scores.get)
            
        except Exception as e:
            logger.error(f"Error determining archetype: {e}")
            return SalesRepArchetype.ACHIEVER  # Default
    
    async def identify_motivation_factors(self, behavioral_data: Dict, archetype: SalesRepArchetype) -> List[MotivationFactor]:
        """Identify primary motivation factors"""
        factors = []
        
        # Base factors by archetype
        archetype_factors = {
            SalesRepArchetype.ACHIEVER: [MotivationFactor.ACHIEVEMENT, MotivationFactor.RECOGNITION, MotivationFactor.COMPETITION],
            SalesRepArchetype.HUNTER: [MotivationFactor.FINANCIAL, MotivationFactor.ACHIEVEMENT, MotivationFactor.COMPETITION],
            SalesRepArchetype.RELATIONSHIP_BUILDER: [MotivationFactor.RELATIONSHIP, MotivationFactor.RECOGNITION, MotivationFactor.GROWTH],
            SalesRepArchetype.ANALYST: [MotivationFactor.GROWTH, MotivationFactor.ACHIEVEMENT, MotivationFactor.SECURITY]
        }
        
        factors.extend(archetype_factors[archetype])
        
        # Add additional factors based on behavior
        if behavioral_data.get("risk_tolerance", 0) < 0.3:
            factors.append(MotivationFactor.SECURITY)
        
        if behavioral_data.get("team_collaboration", 0) > 0.7:
            factors.append(MotivationFactor.RELATIONSHIP)
        
        return list(set(factors))  # Remove duplicates
    
    async def calculate_psychological_state(self, behavioral_data: Dict) -> PsychologicalState:
        """Calculate current psychological state"""
        try:
            # Base calculations from behavioral data
            stress_level = min(1.0, behavioral_data.get("error_rate", 0) * 2 + 
                             behavioral_data.get("time_pressure_indicators", 0))
            
            motivation_level = min(1.0, behavioral_data.get("engagement_score", 0.5) + 
                                 behavioral_data.get("goal_interaction", 0) * 0.3)
            
            confidence_level = min(1.0, behavioral_data.get("performance_trend", 0.5) + 
                                 behavioral_data.get("achievement_rate", 0) * 0.4)
            
            urgency_response = behavioral_data.get("deadline_sensitivity", 0.5)
            competitive_spirit = behavioral_data.get("leaderboard_engagement", 0.5)
            financial_motivation = behavioral_data.get("commission_focus", 0.5)
            cognitive_load = behavioral_data.get("multitasking_indicators", 0.3)
            engagement_level = behavioral_data.get("platform_usage_intensity", 0.5)
            
            return PsychologicalState(
                stress_level=stress_level,
                motivation_level=motivation_level,
                confidence_level=confidence_level,
                urgency_response=urgency_response,
                competitive_spirit=competitive_spirit,
                financial_motivation=financial_motivation,
                cognitive_load=cognitive_load,
                engagement_level=engagement_level
            )
            
        except Exception as e:
            logger.error(f"Error calculating psychological state: {e}")
            return PsychologicalState(
                stress_level=0.3, motivation_level=0.5, confidence_level=0.5,
                urgency_response=0.5, competitive_spirit=0.5, financial_motivation=0.5,
                cognitive_load=0.3, engagement_level=0.5
            )
    
    async def generate_behavioral_nudges(self, rep_id: str, context: Dict) -> List[BehavioralNudge]:
        """Generate personalized behavioral nudges"""
        try:
            profile = await self.get_psychological_profile(rep_id)
            if not profile:
                return []
            
            nudges = []
            current_time = datetime.now()
            
            # Target-based nudges
            if context.get("target_completion", 0) < 0.8 and context.get("days_left", 30) <= 5:
                nudges.append(BehavioralNudge(
                    nudge_id=str(uuid.uuid4()),
                    rep_id=rep_id,
                    trigger_type=BehavioralTrigger.URGENCY,
                    message=f"🔥 Only {context.get('days_left')} days left! You're {(1-context.get('target_completion', 0))*100:.0f}% away from your target - time to accelerate!",
                    intensity="high",
                    timing="immediate",
                    psychological_principle="Loss aversion + time pressure",
                    effectiveness_score=0.8,
                    personalized_for=profile.archetype,
                    expires_at=current_time + timedelta(hours=24)
                ))
            
            # Competition nudges for achievers
            if profile.archetype == SalesRepArchetype.ACHIEVER and context.get("rank_dropped"):
                nudges.append(BehavioralNudge(
                    nudge_id=str(uuid.uuid4()),
                    rep_id=rep_id,
                    trigger_type=BehavioralTrigger.SOCIAL_PROOF,
                    message=f"🏃‍♂️ {context.get('top_performer')} just hit 120% of their target - you're only 15% behind!",
                    intensity="medium",
                    timing="immediate",
                    psychological_principle="Social comparison + achievable gap",
                    effectiveness_score=0.7,
                    personalized_for=profile.archetype
                ))
            
            # Financial nudges for hunters
            if profile.archetype == SalesRepArchetype.HUNTER and context.get("commission_opportunity"):
                commission_amount = context.get("potential_commission", 0)
                nudges.append(BehavioralNudge(
                    nudge_id=str(uuid.uuid4()),
                    rep_id=rep_id,
                    trigger_type=BehavioralTrigger.FINANCIAL,
                    message=f"💰 This deal could earn you R{commission_amount:,.0f} commission - 50% above your average!",
                    intensity="high",
                    timing="immediate",
                    psychological_principle="Financial incentive + concrete visualization",
                    effectiveness_score=0.9,
                    personalized_for=profile.archetype
                ))
            
            # Relationship nudges for relationship builders
            if profile.archetype == SalesRepArchetype.RELATIONSHIP_BUILDER and context.get("client_satisfaction_drop"):
                nudges.append(BehavioralNudge(
                    nudge_id=str(uuid.uuid4()),
                    rep_id=rep_id,
                    trigger_type=BehavioralTrigger.RECIPROCITY,
                    message="🤝 Your client satisfaction score dropped to 85% - time to reconnect with your clients!",
                    intensity="medium",
                    timing="immediate",
                    psychological_principle="Relationship preservation + immediate action",
                    effectiveness_score=0.8,
                    personalized_for=profile.archetype
                ))

            # Momentum nudges for low motivation
            if profile.psychological_state.motivation_level < 0.4:
                nudges.append(BehavioralNudge(
                    nudge_id=str(uuid.uuid4()),
                    rep_id=rep_id,
                    trigger_type=BehavioralTrigger.ACHIEVEMENT,
                    message="🎯 You're just one deal away from your next achievement! Keep pushing!",
                    intensity="medium",
                    timing="immediate",
                    psychological_principle="Goal proximity + achievement motivation",
                    effectiveness_score=0.6,
                    personalized_for=profile.archetype
                ))
            
            return nudges
            
        except Exception as e:
            logger.error(f"Error generating behavioral nudges: {e}")
            return []
    
    async def check_achievements(self, rep_id: str, activity_data: Dict) -> List[Achievement]:
        """Check for new achievements based on activity"""
        try:
            unlocked_achievements = []
            
            for achievement_config in self.achievements:
                # Check if already unlocked
                existing = await self.get_rep_achievement(rep_id, achievement_config["name"])
                if existing:
                    continue
                
                # Check requirements
                requirements_met = True
                for req_key, req_value in achievement_config["requirements"].items():
                    if activity_data.get(req_key, 0) < req_value:
                        requirements_met = False
                        break
                
                if requirements_met:
                    achievement = Achievement(
                        achievement_id=str(uuid.uuid4()),
                        name=achievement_config["name"],
                        description=achievement_config["description"],
                        icon=achievement_config["icon"],
                        tier=achievement_config["tier"],
                        category=achievement_config["category"],
                        xp_reward=achievement_config["xp_reward"],
                        psychological_benefit=achievement_config["psychological_benefit"],
                        requirements=achievement_config["requirements"],
                        rarity=achievement_config["rarity"]
                    )
                    
                    unlocked_achievements.append(achievement)
                    await self.save_rep_achievement(rep_id, achievement)
            
            return unlocked_achievements
            
        except Exception as e:
            logger.error(f"Error checking achievements: {e}")
            return []
    
    async def save_psychological_profile(self, profile: SalesRepProfile):
        """Save psychological profile to database"""
        try:
            profile_record = {
                "rep_id": profile.rep_id,
                "user_id": profile.user_id,
                "supplier_id": profile.supplier_id,
                "archetype": profile.archetype,
                "motivation_factors": profile.motivation_factors,
                "psychological_state": profile.psychological_state.dict(),
                "target_preferences": profile.target_preferences,
                "behavioral_patterns": profile.behavioral_patterns,
                "performance_history": profile.performance_history,
                "onboarding_completed": profile.onboarding_completed,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }
            
            supabase.table("sales_rep_profiles").upsert(profile_record).execute()
            
        except Exception as e:
            logger.error(f"Error saving psychological profile: {e}")
    
    async def get_psychological_profile(self, rep_id: str) -> Optional[SalesRepProfile]:
        """Get psychological profile from database"""
        try:
            result = supabase.table("sales_rep_profiles").select("*").eq("rep_id", rep_id).execute()
            
            if result.data:
                data = result.data[0]
                return SalesRepProfile(
                    rep_id=data["rep_id"],
                    user_id=data["user_id"],
                    supplier_id=data["supplier_id"],
                    archetype=SalesRepArchetype(data["archetype"]),
                    motivation_factors=[MotivationFactor(f) for f in data["motivation_factors"]],
                    psychological_state=PsychologicalState(**data["psychological_state"]),
                    target_preferences=data["target_preferences"],
                    behavioral_patterns=data["behavioral_patterns"],
                    performance_history=data["performance_history"],
                    onboarding_completed=data["onboarding_completed"]
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting psychological profile: {e}")
            return None
    
    async def save_rep_achievement(self, rep_id: str, achievement: Achievement):
        """Save rep achievement to database"""
        try:
            achievement_record = {
                "rep_id": rep_id,
                "achievement_id": achievement.achievement_id,
                "name": achievement.name,
                "description": achievement.description,
                "icon": achievement.icon,
                "tier": achievement.tier,
                "category": achievement.category,
                "xp_reward": achievement.xp_reward,
                "psychological_benefit": achievement.psychological_benefit,
                "unlocked_at": datetime.now().isoformat()
            }
            
            supabase.table("sales_rep_achievements").insert(achievement_record).execute()
            
        except Exception as e:
            logger.error(f"Error saving rep achievement: {e}")
    
    async def get_rep_achievement(self, rep_id: str, achievement_name: str) -> Optional[Dict]:
        """Check if rep has specific achievement"""
        try:
            result = supabase.table("sales_rep_achievements").select("*").eq("rep_id", rep_id).eq("name", achievement_name).execute()
            
            return result.data[0] if result.data else None
            
        except Exception as e:
            logger.error(f"Error getting rep achievement: {e}")
            return None
    
    async def analyze_target_preferences(self, behavioral_data: Dict, archetype: SalesRepArchetype) -> Dict:
        """Analyze target setting preferences based on psychology"""
        try:
            preferences = {
                "target_style": "balanced",  # conservative, balanced, aggressive
                "milestone_frequency": "weekly",  # daily, weekly, monthly
                "reward_preference": "recognition",  # financial, recognition, achievement
                "feedback_frequency": "real_time",  # real_time, daily, weekly
                "comparison_comfort": "team"  # individual, team, company, public
            }
            
            # Adjust based on archetype
            if archetype == SalesRepArchetype.ACHIEVER:
                preferences.update({
                    "target_style": "aggressive",
                    "reward_preference": "recognition",
                    "comparison_comfort": "public"
                })
            elif archetype == SalesRepArchetype.HUNTER:
                preferences.update({
                    "target_style": "aggressive",
                    "reward_preference": "financial",
                    "feedback_frequency": "real_time"
                })
            elif archetype == SalesRepArchetype.RELATIONSHIP_BUILDER:
                preferences.update({
                    "target_style": "balanced",
                    "milestone_frequency": "monthly",
                    "comparison_comfort": "team"
                })
            elif archetype == SalesRepArchetype.ANALYST:
                preferences.update({
                    "target_style": "conservative",
                    "feedback_frequency": "weekly",
                    "comparison_comfort": "individual"
                })
            
            # Adjust based on behavioral data
            if behavioral_data.get("risk_tolerance", 0.5) > 0.8:
                preferences["target_style"] = "aggressive"
            elif behavioral_data.get("risk_tolerance", 0.5) < 0.3:
                preferences["target_style"] = "conservative"
            
            return preferences
            
        except Exception as e:
            logger.error(f"Error analyzing target preferences: {e}")
            return {"target_style": "balanced", "milestone_frequency": "weekly"}

# =====================================================
# GLOBAL INSTANCE
# =====================================================

neuro_engine = SalesRepNeuroEngine()

# =====================================================
# API ENDPOINTS
# =====================================================

@app.post("/sales-rep/onboard")
async def onboard_sales_rep(onboarding_data: Dict):
    """Onboard new sales rep with psychological profiling"""
    try:
        rep_id = onboarding_data.get("rep_id") or str(uuid.uuid4())
        
        # Analyze psychology from onboarding responses
        profile = await neuro_engine.analyze_rep_psychology(rep_id, onboarding_data)
        
        return {
            "rep_id": rep_id,
            "profile": profile.dict(),
            "onboarding_complete": True
        }
        
    except Exception as e:
        logger.error(f"Error onboarding sales rep: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/sales-rep/{rep_id}/profile")
async def get_rep_profile(rep_id: str):
    """Get sales rep psychological profile"""
    try:
        profile = await neuro_engine.get_psychological_profile(rep_id)
        
        if not profile:
            raise HTTPException(status_code=404, detail="Rep profile not found")
        
        return profile.dict()
        
    except Exception as e:
        logger.error(f"Error getting rep profile: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/sales-rep/{rep_id}/nudges")
async def generate_nudges(rep_id: str, context: Dict):
    """Generate personalized behavioral nudges"""
    try:
        nudges = await neuro_engine.generate_behavioral_nudges(rep_id, context)
        
        return {"nudges": [nudge.dict() for nudge in nudges]}
        
    except Exception as e:
        logger.error(f"Error generating nudges: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/sales-rep/{rep_id}/achievements/check")
async def check_achievements(rep_id: str, activity_data: Dict):
    """Check for new achievements"""
    try:
        achievements = await neuro_engine.check_achievements(rep_id, activity_data)
        
        return {"achievements": [achievement.dict() for achievement in achievements]}
        
    except Exception as e:
        logger.error(f"Error checking achievements: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/sales-rep/{rep_id}/achievements")
async def get_rep_achievements(rep_id: str):
    """Get all achievements for sales rep"""
    try:
        result = supabase.table("sales_rep_achievements").select("*").eq("rep_id", rep_id).order("unlocked_at", desc=True).execute()
        
        return {"achievements": result.data}
        
    except Exception as e:
        logger.error(f"Error getting rep achievements: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8008)
