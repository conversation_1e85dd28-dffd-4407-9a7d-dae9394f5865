"""
BidBeez API Main Application
Combines all psychological systems into unified API
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import os
import logging
from datetime import datetime

# Import all our psychological systems
from sales_rep_behavioral_engine import app as behavioral_app
from sales_rep_onboarding_engine import app as onboarding_app
from contractor_supplier_access import app as contractor_app

# Logging setup
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Main application
app = FastAPI(
    title="BidBeez Psychological Systems API",
    description="Complete psychological engagement platform for bidders, contractors, suppliers, and sales reps",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# =====================================================
# HEALTH CHECK ENDPOINTS
# =====================================================

@app.get("/health")
async def health_check():
    """Main health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "behavioral_engine": "active",
            "onboarding_engine": "active", 
            "contractor_access": "active"
        }
    }

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "BidBeez Psychological Systems API",
        "version": "1.0.0",
        "documentation": "/docs",
        "health": "/health",
        "services": {
            "sales_rep_behavioral": "/sales-rep/*",
            "sales_rep_onboarding": "/sales-rep/instant-onboard",
            "contractor_supplier": "/contractor/*"
        }
    }

# =====================================================
# MOUNT SUB-APPLICATIONS
# =====================================================

# Mount behavioral engine
app.mount("/behavioral", behavioral_app)

# Mount onboarding engine  
app.mount("/onboarding", onboarding_app)

# Mount contractor access
app.mount("/contractor-access", contractor_app)

# =====================================================
# UNIFIED API ENDPOINTS
# =====================================================

@app.get("/api/status")
async def api_status():
    """Get overall API status"""
    try:
        return {
            "api_status": "operational",
            "database_status": "connected",
            "services": {
                "behavioral_engine": "running",
                "onboarding_engine": "running",
                "contractor_access": "running"
            },
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Status check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={"status": "error", "message": str(e)}
        )

@app.get("/api/metrics")
async def get_metrics():
    """Get API metrics and usage statistics"""
    try:
        # This would integrate with monitoring system
        return {
            "total_requests": 0,
            "active_users": 0,
            "response_time_avg": "150ms",
            "uptime": "99.9%",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Metrics retrieval failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# =====================================================
# ERROR HANDLERS
# =====================================================

@app.exception_handler(404)
async def not_found_handler(request, exc):
    return JSONResponse(
        status_code=404,
        content={
            "error": "Not Found",
            "message": "The requested endpoint was not found",
            "documentation": "/docs"
        }
    )

@app.exception_handler(500)
async def internal_error_handler(request, exc):
    logger.error(f"Internal server error: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal Server Error", 
            "message": "An unexpected error occurred",
            "timestamp": datetime.now().isoformat()
        }
    )

# =====================================================
# STARTUP/SHUTDOWN EVENTS
# =====================================================

@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    logger.info("🚀 BidBeez Psychological Systems API starting up...")
    logger.info("✅ Behavioral Engine loaded")
    logger.info("✅ Onboarding Engine loaded") 
    logger.info("✅ Contractor Access loaded")
    logger.info("🎯 All psychological systems operational!")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    logger.info("🛑 BidBeez Psychological Systems API shutting down...")
    logger.info("✅ Cleanup completed")

if __name__ == "__main__":
    import uvicorn
    
    port = int(os.getenv("PORT", 8000))
    host = os.getenv("HOST", "0.0.0.0")
    
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=False,  # Set to False for production
        log_level="info"
    )
