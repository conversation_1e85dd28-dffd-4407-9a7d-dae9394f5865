"""
Tender Ingestion & Scraping API
Automated government tender scraping, document downloading, and Queen Bee AI integration
"""

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Union
import uuid
import json
import logging
from datetime import datetime, timedelta
import asyncio
import aiohttp
import requests
from bs4 import BeautifulSoup
import re
import hashlib

# Supabase client
from supabase import create_client, Client
import os

# Initialize Supabase
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_ANON_KEY")
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# Logging setup
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="BidBeez Tender Ingestion API",
    description="Government tender scraping and automated ingestion system",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# =====================================================
# PYDANTIC MODELS
# =====================================================

class TenderSource(BaseModel):
    source_id: str
    name: str
    url: str
    source_type: str  # government, municipal, parastatal
    scraping_enabled: bool = True
    last_scraped: Optional[datetime] = None
    scraping_frequency: int = 60  # minutes
    selectors: Dict[str, str]  # CSS selectors for scraping
    authentication: Optional[Dict] = None

class ScrapedTender(BaseModel):
    source_id: str
    external_id: str
    title: str
    description: str
    organization: str
    category: str
    estimated_value: Optional[float] = None
    currency: str = "ZAR"
    location: str
    province: str
    publish_date: datetime
    closing_date: datetime
    briefing_date: Optional[datetime] = None
    briefing_location: Optional[str] = None
    reference_number: str
    contact_info: Optional[Dict] = None
    requirements: List[str] = []
    documents: List[Dict] = []  # Document URLs and metadata
    source_url: str
    raw_data: Dict = {}

class IngestionJob(BaseModel):
    job_id: str
    source_id: str
    status: str  # pending, running, completed, failed
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    tenders_found: int = 0
    tenders_processed: int = 0
    documents_downloaded: int = 0
    errors: List[str] = []
    queen_bee_assignments: List[str] = []

class QueenBeeAssignment(BaseModel):
    assignment_id: str
    tender_id: str
    queen_bee_id: str
    task_type: str  # document_processing, compliance_check, analysis
    priority: str = "medium"
    status: str = "assigned"
    assigned_at: datetime
    metadata: Dict = {}

# =====================================================
# GOVERNMENT TENDER SOURCES
# =====================================================

class TenderScrapingEngine:
    def __init__(self):
        self.sources = {
            "etenders": {
                "name": "National Treasury eTenders",
                "url": "https://etenders.treasury.gov.za",
                "source_type": "government",
                "selectors": {
                    "tender_list": ".tender-item",
                    "title": ".tender-title",
                    "organization": ".tender-org",
                    "closing_date": ".closing-date",
                    "reference": ".reference-number",
                    "documents": ".document-links a"
                }
            },
            "johannesburg": {
                "name": "City of Johannesburg",
                "url": "https://www.joburg.org.za/tenders",
                "source_type": "municipal",
                "selectors": {
                    "tender_list": ".tender-listing",
                    "title": "h3.tender-title",
                    "description": ".tender-description",
                    "closing_date": ".tender-deadline"
                }
            },
            "cape_town": {
                "name": "City of Cape Town",
                "url": "https://www.capetown.gov.za/City-Connect/Tenders",
                "source_type": "municipal",
                "selectors": {
                    "tender_list": ".tender-item",
                    "title": ".tender-heading",
                    "department": ".department",
                    "closing_date": ".closing-date"
                }
            },
            "sanral": {
                "name": "South African National Roads Agency",
                "url": "https://www.sanral.co.za/tenders",
                "source_type": "parastatal",
                "selectors": {
                    "tender_list": ".tender-row",
                    "title": ".tender-title",
                    "value": ".tender-value",
                    "location": ".tender-location"
                }
            },
            "eskom": {
                "name": "Eskom Holdings",
                "url": "https://www.eskom.co.za/suppliers/tenders",
                "source_type": "parastatal",
                "selectors": {
                    "tender_list": ".tender-notice",
                    "title": "h4",
                    "category": ".category",
                    "closing_date": ".deadline"
                }
            }
        }
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'BidBeez Tender Bot 1.0 (<EMAIL>)'
        })
    
    async def scrape_source(self, source_id: str) -> List[ScrapedTender]:
        """Scrape tenders from a specific source"""
        try:
            if source_id not in self.sources:
                raise ValueError(f"Unknown source: {source_id}")
            
            source = self.sources[source_id]
            logger.info(f"Scraping {source['name']} ({source['url']})")
            
            # Get the main page
            response = self.session.get(source['url'], timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract tender listings
            tender_elements = soup.select(source['selectors']['tender_list'])
            scraped_tenders = []
            
            for element in tender_elements:
                try:
                    tender = await self.extract_tender_data(element, source, source_id)
                    if tender:
                        scraped_tenders.append(tender)
                except Exception as e:
                    logger.error(f"Error extracting tender from {source_id}: {e}")
                    continue
            
            logger.info(f"Scraped {len(scraped_tenders)} tenders from {source['name']}")
            return scraped_tenders
            
        except Exception as e:
            logger.error(f"Error scraping {source_id}: {e}")
            return []
    
    async def extract_tender_data(self, element, source: Dict, source_id: str) -> Optional[ScrapedTender]:
        """Extract tender data from HTML element"""
        try:
            # Extract basic information
            title_elem = element.select_one(source['selectors']['title'])
            title = title_elem.get_text(strip=True) if title_elem else "Unknown Title"
            
            # Generate external ID from title and source
            external_id = hashlib.md5(f"{source_id}_{title}".encode()).hexdigest()[:16]
            
            # Extract organization
            org_elem = element.select_one(source['selectors'].get('organization', source['selectors'].get('department')))
            organization = org_elem.get_text(strip=True) if org_elem else source['name']
            
            # Extract closing date
            closing_elem = element.select_one(source['selectors']['closing_date'])
            closing_date = self.parse_date(closing_elem.get_text(strip=True)) if closing_elem else datetime.now() + timedelta(days=30)
            
            # Extract description
            desc_elem = element.select_one(source['selectors'].get('description'))
            description = desc_elem.get_text(strip=True) if desc_elem else title
            
            # Extract reference number
            ref_elem = element.select_one(source['selectors'].get('reference'))
            reference_number = ref_elem.get_text(strip=True) if ref_elem else f"{source_id.upper()}-{external_id}"
            
            # Extract value
            value_elem = element.select_one(source['selectors'].get('value'))
            estimated_value = self.parse_value(value_elem.get_text(strip=True)) if value_elem else None
            
            # Extract location
            location_elem = element.select_one(source['selectors'].get('location'))
            location = location_elem.get_text(strip=True) if location_elem else self.get_default_location(source_id)
            
            # Extract documents
            doc_elements = element.select(source['selectors'].get('documents', ''))
            documents = []
            for doc_elem in doc_elements:
                doc_url = doc_elem.get('href')
                if doc_url:
                    documents.append({
                        'name': doc_elem.get_text(strip=True),
                        'url': self.resolve_url(doc_url, source['url']),
                        'type': self.classify_document_type(doc_elem.get_text(strip=True))
                    })
            
            # Classify category
            category = self.classify_tender_category(title, description)
            
            # Determine province
            province = self.determine_province(location, source_id)
            
            return ScrapedTender(
                source_id=source_id,
                external_id=external_id,
                title=title,
                description=description,
                organization=organization,
                category=category,
                estimated_value=estimated_value,
                currency="ZAR",
                location=location,
                province=province,
                publish_date=datetime.now(),
                closing_date=closing_date,
                reference_number=reference_number,
                documents=documents,
                source_url=source['url'],
                raw_data={
                    'html': str(element),
                    'scraped_at': datetime.now().isoformat()
                }
            )
            
        except Exception as e:
            logger.error(f"Error extracting tender data: {e}")
            return None
    
    def parse_date(self, date_str: str) -> datetime:
        """Parse date string to datetime"""
        try:
            # Common date formats
            formats = [
                "%Y-%m-%d",
                "%d/%m/%Y",
                "%d-%m-%Y",
                "%Y/%m/%d",
                "%d %B %Y",
                "%B %d, %Y"
            ]
            
            # Clean the date string
            date_str = re.sub(r'[^\w\s/\-:]', '', date_str).strip()
            
            for fmt in formats:
                try:
                    return datetime.strptime(date_str, fmt)
                except ValueError:
                    continue
            
            # If no format matches, return 30 days from now
            return datetime.now() + timedelta(days=30)
            
        except Exception:
            return datetime.now() + timedelta(days=30)
    
    def parse_value(self, value_str: str) -> Optional[float]:
        """Parse value string to float"""
        try:
            # Remove currency symbols and spaces
            value_str = re.sub(r'[R$€£,\s]', '', value_str)
            
            # Handle millions/thousands
            if 'million' in value_str.lower() or 'm' in value_str.lower():
                value_str = re.sub(r'[^\d.]', '', value_str)
                return float(value_str) * 1000000
            elif 'thousand' in value_str.lower() or 'k' in value_str.lower():
                value_str = re.sub(r'[^\d.]', '', value_str)
                return float(value_str) * 1000
            else:
                value_str = re.sub(r'[^\d.]', '', value_str)
                return float(value_str) if value_str else None
                
        except Exception:
            return None
    
    def classify_tender_category(self, title: str, description: str) -> str:
        """Classify tender category based on title and description"""
        text = f"{title} {description}".lower()
        
        if any(word in text for word in ['construction', 'building', 'road', 'infrastructure', 'civil']):
            return 'construction'
        elif any(word in text for word in ['it', 'software', 'technology', 'system', 'digital']):
            return 'it_services'
        elif any(word in text for word in ['cleaning', 'maintenance', 'security', 'catering']):
            return 'services'
        elif any(word in text for word in ['supply', 'goods', 'equipment', 'materials']):
            return 'goods_supply'
        elif any(word in text for word in ['consulting', 'advisory', 'professional']):
            return 'professional_services'
        else:
            return 'other'
    
    def classify_document_type(self, doc_name: str) -> str:
        """Classify document type"""
        name = doc_name.lower()
        
        if any(word in name for word in ['specification', 'spec', 'technical']):
            return 'specification'
        elif any(word in name for word in ['terms', 'conditions', 'contract']):
            return 'terms'
        elif any(word in name for word in ['pricing', 'schedule', 'boq', 'bill']):
            return 'pricing_schedule'
        elif any(word in name for word in ['compliance', 'requirement']):
            return 'compliance'
        else:
            return 'general'
    
    def get_default_location(self, source_id: str) -> str:
        """Get default location for source"""
        locations = {
            'etenders': 'Pretoria, GP',
            'johannesburg': 'Johannesburg, GP',
            'cape_town': 'Cape Town, WC',
            'sanral': 'Pretoria, GP',
            'eskom': 'Johannesburg, GP'
        }
        return locations.get(source_id, 'South Africa')
    
    def determine_province(self, location: str, source_id: str) -> str:
        """Determine province from location"""
        location = location.lower()
        
        if any(city in location for city in ['johannesburg', 'pretoria', 'sandton', 'midrand']):
            return 'Gauteng'
        elif any(city in location for city in ['cape town', 'stellenbosch', 'paarl']):
            return 'Western Cape'
        elif any(city in location for city in ['durban', 'pietermaritzburg']):
            return 'KwaZulu-Natal'
        elif any(city in location for city in ['bloemfontein']):
            return 'Free State'
        elif any(city in location for city in ['port elizabeth', 'east london']):
            return 'Eastern Cape'
        elif any(city in location for city in ['polokwane', 'limpopo']):
            return 'Limpopo'
        elif any(city in location for city in ['nelspruit', 'mbombela']):
            return 'Mpumalanga'
        elif any(city in location for city in ['rustenburg', 'mahikeng']):
            return 'North West'
        elif any(city in location for city in ['kimberley']):
            return 'Northern Cape'
        else:
            # Default based on source
            defaults = {
                'johannesburg': 'Gauteng',
                'cape_town': 'Western Cape',
                'etenders': 'Gauteng'
            }
            return defaults.get(source_id, 'Gauteng')
    
    def resolve_url(self, url: str, base_url: str) -> str:
        """Resolve relative URLs"""
        if url.startswith('http'):
            return url
        elif url.startswith('/'):
            from urllib.parse import urljoin
            return urljoin(base_url, url)
        else:
            return f"{base_url.rstrip('/')}/{url.lstrip('/')}"

# =====================================================
# DOCUMENT DOWNLOADING SERVICE
# =====================================================

class DocumentDownloadService:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'BidBeez Document Bot 1.0'
        })
    
    async def download_tender_documents(self, tender: ScrapedTender) -> List[Dict]:
        """Download all documents for a tender"""
        downloaded_docs = []
        
        for doc in tender.documents:
            try:
                doc_data = await self.download_document(doc['url'], doc['name'])
                if doc_data:
                    downloaded_docs.append({
                        'name': doc['name'],
                        'type': doc['type'],
                        'url': doc['url'],
                        'file_path': doc_data['file_path'],
                        'file_size': doc_data['file_size'],
                        'downloaded_at': datetime.now().isoformat()
                    })
            except Exception as e:
                logger.error(f"Error downloading document {doc['name']}: {e}")
                continue
        
        return downloaded_docs
    
    async def download_document(self, url: str, filename: str) -> Optional[Dict]:
        """Download a single document"""
        try:
            response = self.session.get(url, timeout=60)
            response.raise_for_status()
            
            # Generate file path
            safe_filename = re.sub(r'[^\w\-_\.]', '_', filename)
            file_path = f"documents/{datetime.now().strftime('%Y/%m/%d')}/{safe_filename}"
            
            # In production, save to cloud storage
            # For now, simulate file storage
            file_size = len(response.content)
            
            logger.info(f"Downloaded document: {filename} ({file_size} bytes)")
            
            return {
                'file_path': file_path,
                'file_size': file_size,
                'content_type': response.headers.get('content-type', 'application/octet-stream')
            }
            
        except Exception as e:
            logger.error(f"Error downloading document from {url}: {e}")
            return None

# =====================================================
# QUEEN BEE INTEGRATION SERVICE
# =====================================================

class QueenBeeIntegrationService:
    def __init__(self):
        self.assignment_queue = []
    
    async def assign_tender_to_queen_bee(self, tender_id: str, task_type: str = "document_processing") -> str:
        """Assign tender processing to Queen Bee"""
        try:
            # Find suitable Queen Bee based on location and workload
            queen_bee_id = await self.find_suitable_queen_bee(tender_id)
            
            assignment = QueenBeeAssignment(
                assignment_id=str(uuid.uuid4()),
                tender_id=tender_id,
                queen_bee_id=queen_bee_id,
                task_type=task_type,
                priority="medium",
                status="assigned",
                assigned_at=datetime.now(),
                metadata={
                    'auto_assigned': True,
                    'assignment_reason': 'tender_ingestion'
                }
            )
            
            # Save assignment to database
            assignment_record = assignment.dict()
            result = supabase.table("queen_bee_assignments").insert(assignment_record).execute()
            
            logger.info(f"Assigned tender {tender_id} to Queen Bee {queen_bee_id}")
            return assignment.assignment_id
            
        except Exception as e:
            logger.error(f"Error assigning tender to Queen Bee: {e}")
            return ""
    
    async def find_suitable_queen_bee(self, tender_id: str) -> str:
        """Find the most suitable Queen Bee for the tender"""
        try:
            # Get tender location
            tender_result = supabase.table("tenders").select("location, province").eq("id", tender_id).execute()
            
            if not tender_result.data:
                return "default-queen-bee"
            
            tender = tender_result.data[0]
            
            # Get available Queen Bees
            queen_bees_result = supabase.table("queen_bees").select("*").eq("status", "active").execute()
            
            if not queen_bees_result.data:
                return "default-queen-bee"
            
            # Simple assignment logic - find Queen Bee with lowest workload in same province
            best_queen_bee = None
            lowest_workload = float('inf')
            
            for queen_bee in queen_bees_result.data:
                # Check if Queen Bee covers this province
                if tender['province'] in queen_bee.get('territory', {}).get('provinces', []):
                    workload = queen_bee.get('current_workload', 0)
                    if workload < lowest_workload:
                        lowest_workload = workload
                        best_queen_bee = queen_bee
            
            return best_queen_bee['id'] if best_queen_bee else queen_bees_result.data[0]['id']
            
        except Exception as e:
            logger.error(f"Error finding suitable Queen Bee: {e}")
            return "default-queen-bee"

# =====================================================
# MAIN INGESTION SERVICE
# =====================================================

class TenderIngestionService:
    def __init__(self):
        self.scraping_engine = TenderScrapingEngine()
        self.download_service = DocumentDownloadService()
        self.queen_bee_service = QueenBeeIntegrationService()
    
    async def run_ingestion_job(self, source_ids: List[str] = None) -> IngestionJob:
        """Run complete tender ingestion job"""
        job_id = str(uuid.uuid4())
        
        if source_ids is None:
            source_ids = list(self.scraping_engine.sources.keys())
        
        job = IngestionJob(
            job_id=job_id,
            source_id=",".join(source_ids),
            status="running",
            started_at=datetime.now()
        )
        
        try:
            all_scraped_tenders = []
            
            # Scrape each source
            for source_id in source_ids:
                scraped_tenders = await self.scraping_engine.scrape_source(source_id)
                all_scraped_tenders.extend(scraped_tenders)
            
            job.tenders_found = len(all_scraped_tenders)
            
            # Process each tender
            for tender in all_scraped_tenders:
                try:
                    # Check if tender already exists
                    existing = supabase.table("tenders").select("id").eq("external_id", tender.external_id).execute()
                    
                    if existing.data:
                        logger.info(f"Tender {tender.external_id} already exists, skipping")
                        continue
                    
                    # Save tender to database
                    tender_id = await self.save_tender(tender)
                    
                    # Download documents
                    downloaded_docs = await self.download_service.download_tender_documents(tender)
                    job.documents_downloaded += len(downloaded_docs)
                    
                    # Assign to Queen Bee for processing
                    assignment_id = await self.queen_bee_service.assign_tender_to_queen_bee(tender_id)
                    if assignment_id:
                        job.queen_bee_assignments.append(assignment_id)
                    
                    job.tenders_processed += 1
                    
                except Exception as e:
                    error_msg = f"Error processing tender {tender.external_id}: {e}"
                    logger.error(error_msg)
                    job.errors.append(error_msg)
            
            job.status = "completed"
            job.completed_at = datetime.now()
            
        except Exception as e:
            job.status = "failed"
            job.errors.append(str(e))
            job.completed_at = datetime.now()
        
        # Save job record
        await self.save_ingestion_job(job)
        
        return job
    
    async def save_tender(self, tender: ScrapedTender) -> str:
        """Save scraped tender to database"""
        try:
            tender_record = {
                "external_id": tender.external_id,
                "title": tender.title,
                "description": tender.description,
                "organization": tender.organization,
                "category": tender.category,
                "estimated_value": tender.estimated_value,
                "currency": tender.currency,
                "location": tender.location,
                "province": tender.province,
                "publish_date": tender.publish_date.isoformat(),
                "closing_date": tender.closing_date.isoformat(),
                "briefing_date": tender.briefing_date.isoformat() if tender.briefing_date else None,
                "briefing_location": tender.briefing_location,
                "reference_number": tender.reference_number,
                "contact_info": tender.contact_info,
                "requirements": tender.requirements,
                "documents": tender.documents,
                "source_url": tender.source_url,
                "source_id": tender.source_id,
                "status": "open",
                "ingestion_metadata": {
                    "scraped_at": datetime.now().isoformat(),
                    "raw_data": tender.raw_data
                }
            }
            
            result = supabase.table("tenders").insert(tender_record).execute()
            
            if result.data:
                logger.info(f"Saved tender: {tender.title}")
                return result.data[0]["id"]
            else:
                raise Exception("Failed to save tender")
                
        except Exception as e:
            logger.error(f"Error saving tender: {e}")
            raise
    
    async def save_ingestion_job(self, job: IngestionJob):
        """Save ingestion job record"""
        try:
            job_record = job.dict()
            job_record["started_at"] = job_record["started_at"].isoformat() if job_record["started_at"] else None
            job_record["completed_at"] = job_record["completed_at"].isoformat() if job_record["completed_at"] else None
            
            supabase.table("ingestion_jobs").insert(job_record).execute()
            
        except Exception as e:
            logger.error(f"Error saving ingestion job: {e}")

# =====================================================
# API ENDPOINTS
# =====================================================

ingestion_service = TenderIngestionService()

@app.post("/ingestion/run")
async def run_ingestion(
    background_tasks: BackgroundTasks,
    source_ids: Optional[List[str]] = None
):
    """Start tender ingestion job"""
    try:
        # Run ingestion in background
        background_tasks.add_task(ingestion_service.run_ingestion_job, source_ids)
        
        return {
            "status": "started",
            "message": "Tender ingestion job started",
            "sources": source_ids or list(ingestion_service.scraping_engine.sources.keys())
        }
        
    except Exception as e:
        logger.error(f"Error starting ingestion: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/ingestion/sources")
async def get_sources():
    """Get available tender sources"""
    return {
        "sources": [
            {
                "source_id": source_id,
                **source_data
            }
            for source_id, source_data in ingestion_service.scraping_engine.sources.items()
        ]
    }

@app.get("/ingestion/jobs")
async def get_ingestion_jobs():
    """Get ingestion job history"""
    try:
        result = supabase.table("ingestion_jobs").select("*").order("started_at", desc=True).limit(50).execute()
        
        return {"jobs": result.data}
        
    except Exception as e:
        logger.error(f"Error getting ingestion jobs: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/ingestion/jobs/{job_id}")
async def get_ingestion_job(job_id: str):
    """Get specific ingestion job details"""
    try:
        result = supabase.table("ingestion_jobs").select("*").eq("job_id", job_id).execute()
        
        if not result.data:
            raise HTTPException(status_code=404, detail="Job not found")
        
        return result.data[0]
        
    except Exception as e:
        logger.error(f"Error getting ingestion job: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/ingestion/test-scrape/{source_id}")
async def test_scrape_source(source_id: str):
    """Test scraping a specific source"""
    try:
        scraped_tenders = await ingestion_service.scraping_engine.scrape_source(source_id)
        
        return {
            "source_id": source_id,
            "tenders_found": len(scraped_tenders),
            "sample_tenders": scraped_tenders[:3]  # Return first 3 as sample
        }
        
    except Exception as e:
        logger.error(f"Error testing scrape: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8005)
