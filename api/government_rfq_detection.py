"""
Government RFQ Detection and Processing Service
Detects and processes RFQs from government portals alongside tenders
"""

import asyncio
import logging
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

import aiohttp
from bs4 import BeautifulSoup
from supabase import create_client, Client
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Supabase client
supabase: Client = create_client(
    os.getenv("SUPABASE_URL", ""),
    os.getenv("SUPABASE_SERVICE_KEY", "")
)

class OpportunityType(str, Enum):
    TENDER = "tender"
    GOVERNMENT_RFQ = "government_rfq"
    UNKNOWN = "unknown"

@dataclass
class GovernmentRFQ:
    rfq_id: str
    title: str
    description: str
    organization: str
    estimated_value: Optional[float]
    closing_date: datetime
    category: str
    province: str
    source_id: str
    external_id: str
    source_url: str
    technical_requirements: Dict
    commercial_requirements: Dict
    evaluation_criteria: Dict
    documents: List[str]

class GovernmentRFQDetectionService:
    """Service to detect and process government RFQs from portal scraping"""
    
    def __init__(self):
        self.rfq_keywords = [
            'rfq', 'request for quote', 'request for quotation', 'quotation',
            'price inquiry', 'price request', 'quote request', 'quotation request',
            'supply quote', 'service quote', 'pricing request'
        ]
        
        self.tender_keywords = [
            'tender', 'bid', 'proposal', 'tender notice', 'invitation to bid',
            'request for proposal', 'rfp', 'tender invitation'
        ]
        
        self.sources = {
            "etenders": {
                "name": "National Treasury eTenders",
                "url": "https://etenders.treasury.gov.za",
                "selectors": {
                    "opportunity_list": ".tender-item, .rfq-item, .opportunity-item",
                    "title": ".tender-title, .rfq-title, .opportunity-title",
                    "organization": ".tender-org, .rfq-org, .opportunity-org",
                    "closing_date": ".closing-date, .deadline",
                    "documents": ".document-links a, .attachment-links a",
                    "description": ".tender-description, .rfq-description",
                    "value": ".tender-value, .estimated-value"
                }
            },
            "joburg": {
                "name": "City of Johannesburg",
                "url": "https://www.joburg.org.za/tenders",
                "selectors": {
                    "opportunity_list": ".tender-listing, .rfq-listing",
                    "title": ".tender-title, .rfq-title",
                    "organization": ".department",
                    "closing_date": ".closing-date",
                    "documents": ".document-download a"
                }
            },
            "capetown": {
                "name": "City of Cape Town",
                "url": "https://www.capetown.gov.za/City-Connect/Tenders",
                "selectors": {
                    "opportunity_list": ".tender-item, .rfq-item",
                    "title": ".title",
                    "organization": ".department",
                    "closing_date": ".deadline",
                    "documents": ".attachments a"
                }
            }
        }

    async def classify_opportunity_type(self, title: str, description: str = "") -> OpportunityType:
        """Classify if opportunity is tender or RFQ based on content analysis"""
        
        content = f"{title} {description}".lower()
        
        # Count RFQ keywords
        rfq_score = sum(1 for keyword in self.rfq_keywords if keyword in content)
        
        # Count tender keywords
        tender_score = sum(1 for keyword in self.tender_keywords if keyword in content)
        
        # Additional pattern matching
        if re.search(r'\b(quote|quotation|pricing)\b', content):
            rfq_score += 2
            
        if re.search(r'\b(tender|bid|proposal)\b', content):
            tender_score += 2
            
        # Decision logic
        if rfq_score > tender_score:
            return OpportunityType.GOVERNMENT_RFQ
        elif tender_score > rfq_score:
            return OpportunityType.TENDER
        else:
            # Default to tender if unclear
            return OpportunityType.TENDER

    async def extract_government_rfq_data(self, element, source: Dict, source_id: str) -> Optional[GovernmentRFQ]:
        """Extract government RFQ data from scraped element"""
        
        try:
            selectors = source["selectors"]
            
            # Extract basic information
            title_elem = element.select_one(selectors["title"])
            title = title_elem.get_text(strip=True) if title_elem else "Unknown RFQ"
            
            org_elem = element.select_one(selectors["organization"])
            organization = org_elem.get_text(strip=True) if org_elem else source["name"]
            
            desc_elem = element.select_one(selectors.get("description", ""))
            description = desc_elem.get_text(strip=True) if desc_elem else ""
            
            # Extract closing date
            date_elem = element.select_one(selectors["closing_date"])
            closing_date = self._parse_date(date_elem.get_text(strip=True) if date_elem else "")
            
            # Extract estimated value
            value_elem = element.select_one(selectors.get("value", ""))
            estimated_value = self._parse_value(value_elem.get_text(strip=True) if value_elem else "")
            
            # Extract documents
            doc_elements = element.select(selectors.get("documents", ""))
            documents = [elem.get('href') for elem in doc_elements if elem.get('href')]
            
            # Generate RFQ ID
            rfq_id = f"RFQ-{datetime.now().strftime('%Y%m%d')}-{hash(title) % 1000000:06d}"
            
            # Determine category from title/description
            category = self._determine_category(title, description)
            
            # Determine province from organization or content
            province = self._determine_province(organization, description)
            
            # Create external ID
            external_id = f"{source_id}-{hash(title + organization) % 1000000:06d}"
            
            return GovernmentRFQ(
                rfq_id=rfq_id,
                title=title,
                description=description,
                organization=organization,
                estimated_value=estimated_value,
                closing_date=closing_date,
                category=category,
                province=province,
                source_id=source_id,
                external_id=external_id,
                source_url=source["url"],
                technical_requirements={},
                commercial_requirements={},
                evaluation_criteria={},
                documents=documents
            )
            
        except Exception as e:
            logger.error(f"Error extracting government RFQ data: {e}")
            return None

    async def process_scraped_opportunities(self, scraped_content: str, source_id: str) -> Tuple[List[GovernmentRFQ], List[Dict]]:
        """Process scraped content and separate RFQs from tenders"""
        
        government_rfqs = []
        tenders = []
        
        try:
            soup = BeautifulSoup(scraped_content, 'html.parser')
            source = self.sources.get(source_id, {})
            
            if not source:
                logger.warning(f"Unknown source: {source_id}")
                return government_rfqs, tenders
            
            # Find all opportunity elements
            opportunity_elements = soup.select(source["selectors"]["opportunity_list"])
            
            for element in opportunity_elements:
                try:
                    # Extract title and description for classification
                    title_elem = element.select_one(source["selectors"]["title"])
                    title = title_elem.get_text(strip=True) if title_elem else ""
                    
                    desc_elem = element.select_one(source["selectors"].get("description", ""))
                    description = desc_elem.get_text(strip=True) if desc_elem else ""
                    
                    # Classify opportunity type
                    opportunity_type = await self.classify_opportunity_type(title, description)
                    
                    if opportunity_type == OpportunityType.GOVERNMENT_RFQ:
                        # Process as government RFQ
                        rfq = await self.extract_government_rfq_data(element, source, source_id)
                        if rfq:
                            government_rfqs.append(rfq)
                            logger.info(f"Detected government RFQ: {rfq.title}")
                    
                    elif opportunity_type == OpportunityType.TENDER:
                        # Process as tender (existing logic)
                        tender_data = await self._extract_tender_data(element, source, source_id)
                        if tender_data:
                            tenders.append(tender_data)
                            logger.info(f"Detected tender: {tender_data.get('title', 'Unknown')}")
                            
                except Exception as e:
                    logger.error(f"Error processing opportunity element: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"Error processing scraped content: {e}")
            
        return government_rfqs, tenders

    async def save_government_rfq(self, rfq: GovernmentRFQ) -> str:
        """Save government RFQ to database"""
        
        try:
            rfq_record = {
                "rfq_id": rfq.rfq_id,
                "title": rfq.title,
                "description": rfq.description,
                "organization": rfq.organization,
                "estimated_value": rfq.estimated_value,
                "closing_date": rfq.closing_date.isoformat(),
                "category": rfq.category,
                "province": rfq.province,
                "source_id": rfq.source_id,
                "external_id": rfq.external_id,
                "source_url": rfq.source_url,
                "technical_requirements": rfq.technical_requirements,
                "commercial_requirements": rfq.commercial_requirements,
                "evaluation_criteria": rfq.evaluation_criteria,
                "specification_documents": rfq.documents,
                "published_date": datetime.now().isoformat(),
                "status": "active",
                "processing_status": "processed",
                "opportunity_type": "government_rfq",
                "scraped_at": datetime.now().isoformat()
            }
            
            result = supabase.table("government_rfqs").insert(rfq_record).execute()
            
            if result.data:
                logger.info(f"Saved government RFQ: {rfq.rfq_id}")
                return result.data[0]["id"]
            else:
                logger.error(f"Failed to save government RFQ: {rfq.rfq_id}")
                return ""
                
        except Exception as e:
            logger.error(f"Error saving government RFQ: {e}")
            return ""

    def _parse_date(self, date_str: str) -> datetime:
        """Parse date string to datetime object"""
        try:
            # Try various date formats
            formats = [
                "%Y-%m-%d",
                "%d/%m/%Y",
                "%d-%m-%Y",
                "%Y/%m/%d",
                "%d %B %Y",
                "%B %d, %Y"
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(date_str.strip(), fmt)
                except ValueError:
                    continue
                    
            # Default to 30 days from now if parsing fails
            return datetime.now() + timedelta(days=30)
            
        except Exception:
            return datetime.now() + timedelta(days=30)

    def _parse_value(self, value_str: str) -> Optional[float]:
        """Parse value string to float"""
        try:
            # Remove currency symbols and spaces
            clean_str = re.sub(r'[R$€£,\s]', '', value_str)
            
            # Handle millions/thousands notation
            if 'M' in clean_str.upper():
                return float(clean_str.upper().replace('M', '')) * 1000000
            elif 'K' in clean_str.upper():
                return float(clean_str.upper().replace('K', '')) * 1000
            else:
                return float(clean_str) if clean_str else None
                
        except Exception:
            return None

    def _determine_category(self, title: str, description: str) -> str:
        """Determine category from title and description"""
        content = f"{title} {description}".lower()
        
        categories = {
            "IT Services": ["it", "software", "hardware", "computer", "technology", "system"],
            "Construction": ["construction", "building", "infrastructure", "civil", "engineering"],
            "Office Supplies": ["office", "supplies", "stationery", "equipment", "furniture"],
            "Security": ["security", "guard", "surveillance", "protection"],
            "Cleaning": ["cleaning", "janitorial", "maintenance", "hygiene"],
            "Catering": ["catering", "food", "meals", "refreshments"],
            "Transport": ["transport", "vehicle", "logistics", "delivery"],
            "Professional Services": ["consulting", "professional", "advisory", "legal", "audit"]
        }
        
        for category, keywords in categories.items():
            if any(keyword in content for keyword in keywords):
                return category
                
        return "General"

    def _determine_province(self, organization: str, description: str) -> str:
        """Determine province from organization or content"""
        content = f"{organization} {description}".lower()
        
        provinces = {
            "Gauteng": ["johannesburg", "pretoria", "gauteng", "joburg", "tshwane"],
            "Western Cape": ["cape town", "western cape", "stellenbosch", "paarl"],
            "KwaZulu-Natal": ["durban", "kwazulu", "natal", "pietermaritzburg"],
            "Eastern Cape": ["port elizabeth", "east london", "eastern cape"],
            "Free State": ["bloemfontein", "free state"],
            "Limpopo": ["polokwane", "limpopo"],
            "Mpumalanga": ["nelspruit", "mpumalanga"],
            "North West": ["mafikeng", "north west"],
            "Northern Cape": ["kimberley", "northern cape"]
        }
        
        for province, keywords in provinces.items():
            if any(keyword in content for keyword in keywords):
                return province
                
        return "National"

    async def _extract_tender_data(self, element, source: Dict, source_id: str) -> Optional[Dict]:
        """Extract tender data (existing logic for tenders)"""
        # This would call the existing tender extraction logic
        # Placeholder for now
        return None

# API endpoint integration
async def run_government_rfq_detection():
    """Main function to run government RFQ detection"""
    
    detection_service = GovernmentRFQDetectionService()
    
    # This would be called by the existing tender ingestion system
    # to also detect and process government RFQs
    
    logger.info("Government RFQ detection service initialized")
    return detection_service
