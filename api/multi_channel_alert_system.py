"""
Multi-Channel Alert System
Sends bid updates through WhatsApp, SMS, Email, and Push notifications
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json
import aiohttp
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON>ultipart
from supabase import create_client, Client
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Supabase client
supabase: Client = create_client(
    os.getenv("SUPABASE_URL", ""),
    os.getenv("SUPABASE_SERVICE_KEY", "")
)

class MultiChannelAlertSystem:
    """Comprehensive alert system for bid updates"""
    
    def __init__(self):
        self.whatsapp_api_url = os.getenv("WHATSAPP_API_URL", "")
        self.whatsapp_token = os.getenv("WHATSAPP_TOKEN", "")
        self.sms_api_url = os.getenv("SMS_API_URL", "")
        self.sms_api_key = os.getenv("SMS_API_KEY", "")
        self.smtp_server = os.getenv("SMTP_SERVER", "smtp.gmail.com")
        self.smtp_port = int(os.getenv("SMTP_PORT", "587"))
        self.email_user = os.getenv("EMAIL_USER", "")
        self.email_password = os.getenv("EMAIL_PASSWORD", "")
        self.push_service_url = os.getenv("PUSH_SERVICE_URL", "")
        
    async def process_pending_alerts(self):
        """Process all pending alerts"""
        
        logger.info("Processing pending alerts...")
        
        try:
            # Get all bid updates that haven't sent alerts yet
            result = supabase.table("bid_updates").select("*").eq("alerts_sent", False).execute()
            
            pending_updates = result.data or []
            
            logger.info(f"Found {len(pending_updates)} pending alerts")
            
            for update in pending_updates:
                try:
                    await self._process_update_alerts(update)
                except Exception as e:
                    logger.error(f"Error processing alert for update {update['id']}: {e}")
                    
        except Exception as e:
            logger.error(f"Error processing pending alerts: {e}")

    async def _process_update_alerts(self, update: Dict):
        """Process alerts for a specific update"""
        
        update_id = update['id']
        bid_tracking_id = update['bid_tracking_id']
        
        try:
            # Get bid tracking info and user
            bid_result = supabase.table("bid_tracking").select("user_id, bid_reference, organization_name, bid_title").eq("id", bid_tracking_id).execute()
            
            if not bid_result.data:
                logger.warning(f"No bid tracking found for update {update_id}")
                return
            
            bid_info = bid_result.data[0]
            user_id = bid_info['user_id']
            
            # Get user contact information
            user_result = supabase.table("users").select("email, phone_number, whatsapp_number").eq("id", user_id).execute()
            
            if not user_result.data:
                logger.warning(f"No user found for update {update_id}")
                return
            
            user_info = user_result.data[0]
            
            # Get user alert preferences
            prefs_result = supabase.table("alert_preferences").select("*").eq("user_id", user_id).execute()
            
            if not prefs_result.data:
                # Create default preferences
                await self._create_default_preferences(user_id)
                preferences = self._get_default_preferences()
            else:
                preferences = prefs_result.data[0]
            
            # Check if user wants this type of alert
            if not self._should_send_alert(update, preferences):
                await self._mark_alerts_sent(update_id, [])
                return
            
            # Determine channels based on priority and preferences
            channels = self._get_alert_channels(update['update_priority'], preferences)
            
            # Check quiet hours
            if self._is_quiet_hours(preferences) and update['update_priority'] not in ['critical']:
                logger.info(f"Skipping non-critical alert during quiet hours for user {user_id}")
                return
            
            # Send alerts through each channel
            sent_channels = []
            for channel in channels:
                try:
                    success = await self._send_channel_alert(channel, user_info, bid_info, update)
                    if success:
                        sent_channels.append(channel)
                        await self._log_alert(update_id, user_id, channel, update, 'sent')
                    else:
                        await self._log_alert(update_id, user_id, channel, update, 'failed')
                        
                except Exception as e:
                    logger.error(f"Error sending {channel} alert: {e}")
                    await self._log_alert(update_id, user_id, channel, update, 'failed', str(e))
            
            # Mark alerts as sent
            await self._mark_alerts_sent(update_id, sent_channels)
            
        except Exception as e:
            logger.error(f"Error processing update alerts: {e}")

    def _should_send_alert(self, update: Dict, preferences: Dict) -> bool:
        """Check if alert should be sent based on preferences"""
        
        update_type = update['update_type']
        
        # Check type-specific preferences
        type_preferences = {
            'award_announcement': preferences.get('award_alerts', True),
            'rejection_notice': preferences.get('award_alerts', True),
            'cancellation': preferences.get('award_alerts', True),
            'addendum': preferences.get('addendum_alerts', True),
            'clarification': preferences.get('clarification_alerts', True),
            'deadline_extension': preferences.get('deadline_alerts', True),
            'competitor_activity': preferences.get('competitor_alerts', True),
            'status_change': True,  # Always send status changes
            'document_update': preferences.get('addendum_alerts', True)
        }
        
        return type_preferences.get(update_type, True)

    def _get_alert_channels(self, priority: str, preferences: Dict) -> List[str]:
        """Get appropriate channels based on priority and preferences"""
        
        # Default channel mapping
        default_channels = {
            'critical': ['email', 'sms', 'whatsapp', 'push'],
            'high': ['email', 'whatsapp', 'push'],
            'medium': ['email', 'push'],
            'low': ['push']
        }
        
        # Get channels from preferences or use defaults
        channel_key = f"{priority}_channels"
        channels = preferences.get(channel_key, default_channels.get(priority, ['push']))
        
        # Filter based on enabled channels
        enabled_channels = []
        if preferences.get('email_enabled', True) and 'email' in channels:
            enabled_channels.append('email')
        if preferences.get('sms_enabled', True) and 'sms' in channels:
            enabled_channels.append('sms')
        if preferences.get('whatsapp_enabled', True) and 'whatsapp' in channels:
            enabled_channels.append('whatsapp')
        if preferences.get('push_enabled', True) and 'push' in channels:
            enabled_channels.append('push')
        
        return enabled_channels

    def _is_quiet_hours(self, preferences: Dict) -> bool:
        """Check if current time is within quiet hours"""
        
        if not preferences.get('quiet_hours_enabled', False):
            return False
        
        now = datetime.now().time()
        quiet_start = datetime.strptime(preferences.get('quiet_start_time', '22:00:00'), '%H:%M:%S').time()
        quiet_end = datetime.strptime(preferences.get('quiet_end_time', '07:00:00'), '%H:%M:%S').time()
        
        if quiet_start <= quiet_end:
            return quiet_start <= now <= quiet_end
        else:
            return now >= quiet_start or now <= quiet_end

    async def _send_channel_alert(self, channel: str, user_info: Dict, bid_info: Dict, update: Dict) -> bool:
        """Send alert through specific channel"""
        
        try:
            if channel == 'email':
                return await self._send_email_alert(user_info, bid_info, update)
            elif channel == 'sms':
                return await self._send_sms_alert(user_info, bid_info, update)
            elif channel == 'whatsapp':
                return await self._send_whatsapp_alert(user_info, bid_info, update)
            elif channel == 'push':
                return await self._send_push_alert(user_info, bid_info, update)
            else:
                logger.warning(f"Unknown channel: {channel}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending {channel} alert: {e}")
            return False

    async def _send_email_alert(self, user_info: Dict, bid_info: Dict, update: Dict) -> bool:
        """Send email alert"""
        
        try:
            # Create email content
            subject = f"🔔 BidBeez Alert: {update['title']}"
            
            # HTML email template
            html_content = f"""
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                        <h1 style="margin: 0; font-size: 24px;">🐝 BidBeez Alert</h1>
                        <p style="margin: 10px 0 0 0; opacity: 0.9;">{self._get_priority_emoji(update['update_priority'])} {update['update_priority'].upper()} PRIORITY</p>
                    </div>
                    
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                        <h2 style="color: #2c3e50; margin-top: 0;">{update['title']}</h2>
                        <p><strong>Bid Reference:</strong> {bid_info['bid_reference']}</p>
                        <p><strong>Organization:</strong> {bid_info['organization_name']}</p>
                        <p><strong>Bid Title:</strong> {bid_info['bid_title']}</p>
                    </div>
                    
                    <div style="background: white; padding: 20px; border: 1px solid #e9ecef; border-radius: 10px; margin-bottom: 20px;">
                        <h3 style="color: #495057; margin-top: 0;">Update Details</h3>
                        <p>{update['description']}</p>
                        
                        {f'<p><strong>New Status:</strong> <span style="color: #28a745;">{update["new_status"]}</span></p>' if update.get('new_status') else ''}
                        
                        <p style="color: #6c757d; font-size: 14px;">
                            <strong>Detected:</strong> {datetime.fromisoformat(update['detected_at']).strftime('%Y-%m-%d %H:%M')} | 
                            <strong>Source:</strong> {update['source_type'].replace('_', ' ').title()}
                        </p>
                    </div>
                    
                    <div style="text-align: center; margin: 30px 0;">
                        <a href="https://bidbeez.com/bid-tracking" style="background: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">View Full Details</a>
                    </div>
                    
                    <div style="border-top: 1px solid #e9ecef; padding-top: 20px; text-align: center; color: #6c757d; font-size: 12px;">
                        <p>This alert was sent by BidBeez Bid Tracking System</p>
                        <p>To manage your alert preferences, <a href="https://bidbeez.com/settings/alerts">click here</a></p>
                    </div>
                </div>
            </body>
            </html>
            """
            
            # Send email
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = self.email_user
            msg['To'] = user_info['email']
            
            html_part = MIMEText(html_content, 'html')
            msg.attach(html_part)
            
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.email_user, self.email_password)
                server.send_message(msg)
            
            logger.info(f"Email alert sent to {user_info['email']}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending email alert: {e}")
            return False

    async def _send_sms_alert(self, user_info: Dict, bid_info: Dict, update: Dict) -> bool:
        """Send SMS alert"""
        
        try:
            if not user_info.get('phone_number'):
                logger.warning("No phone number for SMS alert")
                return False
            
            # Create SMS content (160 character limit)
            message = f"🔔 BidBeez: {update['title'][:50]}... Bid: {bid_info['bid_reference']} - {update['update_priority'].upper()} priority. Check app for details."
            
            # Send SMS via API
            async with aiohttp.ClientSession() as session:
                payload = {
                    'to': user_info['phone_number'],
                    'message': message,
                    'api_key': self.sms_api_key
                }
                
                async with session.post(self.sms_api_url, json=payload) as response:
                    if response.status == 200:
                        logger.info(f"SMS alert sent to {user_info['phone_number']}")
                        return True
                    else:
                        logger.error(f"SMS API error: {response.status}")
                        return False
                        
        except Exception as e:
            logger.error(f"Error sending SMS alert: {e}")
            return False

    async def _send_whatsapp_alert(self, user_info: Dict, bid_info: Dict, update: Dict) -> bool:
        """Send WhatsApp alert"""
        
        try:
            if not user_info.get('whatsapp_number'):
                logger.warning("No WhatsApp number for alert")
                return False
            
            # Create WhatsApp message
            message = f"""🔔 *BidBeez Alert*
            
{self._get_priority_emoji(update['update_priority'])} *{update['update_priority'].upper()} PRIORITY*

*{update['title']}*

📋 *Bid Details:*
• Reference: {bid_info['bid_reference']}
• Organization: {bid_info['organization_name']}
• Title: {bid_info['bid_title']}

📝 *Update:*
{update['description']}

{f"🎯 *New Status:* {update['new_status']}" if update.get('new_status') else ''}

🕒 Detected: {datetime.fromisoformat(update['detected_at']).strftime('%Y-%m-%d %H:%M')}
📊 Source: {update['source_type'].replace('_', ' ').title()}

View full details: https://bidbeez.com/bid-tracking"""
            
            # Send WhatsApp message via API
            async with aiohttp.ClientSession() as session:
                headers = {
                    'Authorization': f'Bearer {self.whatsapp_token}',
                    'Content-Type': 'application/json'
                }
                
                payload = {
                    'messaging_product': 'whatsapp',
                    'to': user_info['whatsapp_number'],
                    'type': 'text',
                    'text': {'body': message}
                }
                
                async with session.post(self.whatsapp_api_url, json=payload, headers=headers) as response:
                    if response.status == 200:
                        logger.info(f"WhatsApp alert sent to {user_info['whatsapp_number']}")
                        return True
                    else:
                        logger.error(f"WhatsApp API error: {response.status}")
                        return False
                        
        except Exception as e:
            logger.error(f"Error sending WhatsApp alert: {e}")
            return False

    async def _send_push_alert(self, user_info: Dict, bid_info: Dict, update: Dict) -> bool:
        """Send push notification"""
        
        try:
            # Create push notification
            notification = {
                'title': f"{self._get_priority_emoji(update['update_priority'])} {update['title']}",
                'body': f"{bid_info['bid_reference']}: {update['description'][:100]}...",
                'icon': '/icons/bidbeez-icon.png',
                'badge': '/icons/bidbeez-badge.png',
                'data': {
                    'bid_tracking_id': update['bid_tracking_id'],
                    'update_id': update['id'],
                    'priority': update['update_priority'],
                    'url': '/bid-tracking'
                }
            }
            
            # Send push notification via service
            async with aiohttp.ClientSession() as session:
                payload = {
                    'user_id': user_info.get('id'),
                    'notification': notification
                }
                
                async with session.post(self.push_service_url, json=payload) as response:
                    if response.status == 200:
                        logger.info(f"Push notification sent to user")
                        return True
                    else:
                        logger.error(f"Push service error: {response.status}")
                        return False
                        
        except Exception as e:
            logger.error(f"Error sending push notification: {e}")
            return False

    def _get_priority_emoji(self, priority: str) -> str:
        """Get emoji for priority level"""
        
        emojis = {
            'critical': '🚨',
            'high': '⚠️',
            'medium': 'ℹ️',
            'low': '📝'
        }
        return emojis.get(priority, 'ℹ️')

    async def _log_alert(self, update_id: str, user_id: str, channel: str, update: Dict, status: str, error_message: str = None):
        """Log sent alert"""
        
        try:
            alert_data = {
                "bid_update_id": update_id,
                "user_id": user_id,
                "channel": channel,
                "alert_type": update['update_type'],
                "priority": update['update_priority'],
                "subject": update['title'],
                "message": update['description'],
                "status": status,
                "sent_at": datetime.now().isoformat() if status == 'sent' else None,
                "error_message": error_message
            }
            
            supabase.table("alert_history").insert(alert_data).execute()
            
        except Exception as e:
            logger.error(f"Error logging alert: {e}")

    async def _mark_alerts_sent(self, update_id: str, channels: List[str]):
        """Mark update as having alerts sent"""
        
        try:
            supabase.table("bid_updates").update({
                "alerts_sent": True,
                "alert_channels": channels
            }).eq("id", update_id).execute()
            
        except Exception as e:
            logger.error(f"Error marking alerts sent: {e}")

    async def _create_default_preferences(self, user_id: str):
        """Create default alert preferences for user"""
        
        try:
            default_prefs = self._get_default_preferences()
            default_prefs['user_id'] = user_id
            
            supabase.table("alert_preferences").insert(default_prefs).execute()
            
        except Exception as e:
            logger.error(f"Error creating default preferences: {e}")

    def _get_default_preferences(self) -> Dict:
        """Get default alert preferences"""
        
        return {
            "email_enabled": True,
            "sms_enabled": True,
            "whatsapp_enabled": True,
            "push_enabled": True,
            "critical_channels": ['email', 'sms', 'whatsapp', 'push'],
            "high_channels": ['email', 'whatsapp', 'push'],
            "medium_channels": ['email', 'push'],
            "low_channels": ['push'],
            "immediate_alerts": True,
            "daily_digest": True,
            "weekly_summary": True,
            "quiet_hours_enabled": False,
            "quiet_start_time": '22:00:00',
            "quiet_end_time": '07:00:00',
            "award_alerts": True,
            "addendum_alerts": True,
            "clarification_alerts": True,
            "competitor_alerts": True,
            "deadline_alerts": True
        }

# Service instance
alert_system = MultiChannelAlertSystem()

# Main function to start alert processing
async def start_alert_processing():
    """Start the alert processing service"""
    
    logger.info("Starting multi-channel alert system...")
    
    while True:
        try:
            await alert_system.process_pending_alerts()
            await asyncio.sleep(60)  # Check every minute
            
        except Exception as e:
            logger.error(f"Error in alert processing loop: {e}")
            await asyncio.sleep(30)  # Wait 30 seconds before retrying

if __name__ == "__main__":
    asyncio.run(start_alert_processing())
