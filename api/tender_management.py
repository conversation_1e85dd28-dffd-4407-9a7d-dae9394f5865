"""
Enhanced Tender Management API Endpoints
Integrates with new database schema and tender intelligence
"""

from fastapi import APIRouter, HTTPException, Depends, Query, Path
from typing import List, Optional
import asyncpg
from datetime import datetime
import logging

from .database.connection import get_database_connection
from .auth.dependencies import get_current_user
from .models.tender_models import (
    Tender, SupplierQuote, TenderBid,
    TenderCreateRequest, QuoteCreateRequest, BidCreateRequest,
    TenderListResponse, QuoteListResponse
)
from .services.tender_service import tender_service

router = APIRouter(prefix="/api/tenders", tags=["tenders"])
logger = logging.getLogger(__name__)

# =====================================================
# TENDER ENDPOINTS
# =====================================================

@router.get("/", response_model=TenderListResponse)
async def get_tenders(
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None),
    category: Optional[str] = Query(None),
    province: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    current_user = Depends(get_current_user)
):
    """Get paginated list of tenders with filters"""
    try:
        result = await tender_service.get_tenders(
            page=page,
            limit=limit,
            status=status,
            category=category,
            province=province,
            search=search
        )
        return TenderListResponse(**result)
    except Exception as e:
        logger.error(f"Error fetching tenders: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch tenders")

@router.get("/{tender_id}", response_model=Tender)
async def get_tender(
    tender_id: str = Path(...),
    current_user = Depends(get_current_user)
):
    """Get tender by ID"""
    try:
        # Update view count
        await tender_service.update_tender_metrics(tender_id, "view")
        
        tender = await tender_service.get_tender_by_id(tender_id)
        if not tender:
            raise HTTPException(status_code=404, detail="Tender not found")
        
        return tender
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching tender {tender_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch tender")

@router.post("/", response_model=Tender)
async def create_tender(
    tender_data: TenderCreateRequest,
    current_user = Depends(get_current_user)
):
    """Create a new tender (admin only)"""
    try:
        # Check if user has permission to create tenders
        if not current_user.get("is_admin", False):
            raise HTTPException(status_code=403, detail="Only administrators can create tenders")
        
        tender = await tender_service.create_tender(tender_data, current_user["id"])
        return tender
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating tender: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to create tender")

@router.post("/{tender_id}/download")
async def download_tender_documents(
    tender_id: str = Path(...),
    current_user = Depends(get_current_user)
):
    """Track tender document downloads"""
    try:
        await tender_service.update_tender_metrics(tender_id, "download")
        return {"message": "Download tracked successfully"}
    except Exception as e:
        logger.error(f"Error tracking download for tender {tender_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to track download")

# =====================================================
# QUOTE ENDPOINTS
# =====================================================

@router.post("/{tender_id}/quotes", response_model=SupplierQuote)
async def create_quote(
    tender_id: str = Path(...),
    quote_data: QuoteCreateRequest,
    current_user = Depends(get_current_user)
):
    """Create a new quote for a tender"""
    try:
        # Verify tender exists
        tender = await tender_service.get_tender_by_id(tender_id)
        if not tender:
            raise HTTPException(status_code=404, detail="Tender not found")
        
        # Check if tender is still open
        if tender.status != "active":
            raise HTTPException(status_code=400, detail="Tender is not accepting quotes")
        
        # Set tender_id in quote data
        quote_data.tender_id = tender_id
        
        quote = await tender_service.create_quote(quote_data, current_user["id"])
        return quote
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating quote: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to create quote")

@router.get("/quotes/my-quotes", response_model=QuoteListResponse)
async def get_my_quotes(
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None),
    current_user = Depends(get_current_user)
):
    """Get current user's quotes"""
    try:
        result = await tender_service.get_quotes_by_supplier(
            supplier_id=current_user["id"],
            page=page,
            limit=limit,
            status=status
        )
        return QuoteListResponse(**result)
    except Exception as e:
        logger.error(f"Error fetching user quotes: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch quotes")

@router.get("/quotes/{quote_id}", response_model=SupplierQuote)
async def get_quote(
    quote_id: str = Path(...),
    current_user = Depends(get_current_user)
):
    """Get quote by ID"""
    try:
        quote = await tender_service.get_quote_by_id(quote_id, current_user["id"])
        if not quote:
            raise HTTPException(status_code=404, detail="Quote not found")
        
        return quote
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching quote {quote_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch quote")

@router.post("/quotes/{quote_id}/submit", response_model=SupplierQuote)
async def submit_quote(
    quote_id: str = Path(...),
    current_user = Depends(get_current_user)
):
    """Submit a quote"""
    try:
        quote = await tender_service.submit_quote(quote_id, current_user["id"])
        return quote
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error submitting quote {quote_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to submit quote")

# =====================================================
# BID ENDPOINTS
# =====================================================

@router.post("/{tender_id}/bids", response_model=TenderBid)
async def create_bid(
    tender_id: str = Path(...),
    bid_data: BidCreateRequest,
    current_user = Depends(get_current_user)
):
    """Create a new bid for a tender"""
    try:
        # Verify tender exists
        tender = await tender_service.get_tender_by_id(tender_id)
        if not tender:
            raise HTTPException(status_code=404, detail="Tender not found")
        
        # Check if tender is still open
        if tender.status != "active":
            raise HTTPException(status_code=400, detail="Tender is not accepting bids")
        
        # Check if closing date has passed
        if datetime.now() > tender.closing_date:
            raise HTTPException(status_code=400, detail="Tender closing date has passed")
        
        # Set tender_id in bid data
        bid_data.tender_id = tender_id
        
        bid = await tender_service.create_bid(bid_data, current_user["id"])
        return bid
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating bid: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to create bid")

@router.get("/bids/my-bids")
async def get_my_bids(
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None),
    current_user = Depends(get_current_user)
):
    """Get current user's bids"""
    try:
        result = await tender_service.get_bids_by_bidder(
            bidder_id=current_user["id"],
            page=page,
            limit=limit,
            status=status
        )
        return result
    except Exception as e:
        logger.error(f"Error fetching user bids: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch bids")

# =====================================================
# ANALYTICS ENDPOINTS
# =====================================================

@router.get("/analytics/dashboard")
async def get_tender_analytics(
    current_user = Depends(get_current_user)
):
    """Get tender analytics for dashboard"""
    try:
        conn = await get_database_connection()
        
        # Get basic stats
        stats_query = """
            SELECT 
                COUNT(*) as total_tenders,
                COUNT(*) FILTER (WHERE status = 'active') as active_tenders,
                COALESCE(SUM(estimated_value), 0) as total_value,
                COALESCE(AVG(estimated_value), 0) as avg_value
            FROM tenders
            WHERE status != 'draft'
        """
        
        stats = await conn.fetchrow(stats_query)
        
        # Get category breakdown
        category_query = """
            SELECT 
                category,
                COUNT(*) as count,
                COALESCE(SUM(estimated_value), 0) as value
            FROM tenders
            WHERE status != 'draft' AND category IS NOT NULL
            GROUP BY category
            ORDER BY count DESC
            LIMIT 10
        """
        
        categories = await conn.fetch(category_query)
        
        return {
            "total_tenders": stats["total_tenders"],
            "active_tenders": stats["active_tenders"],
            "total_value": float(stats["total_value"]),
            "avg_tender_value": float(stats["avg_value"]),
            "top_categories": [
                {
                    "category": row["category"],
                    "count": row["count"],
                    "value": float(row["value"])
                }
                for row in categories
            ]
        }
        
    except Exception as e:
        logger.error(f"Error fetching tender analytics: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch analytics")

@router.get("/analytics/quotes")
async def get_quote_analytics(
    current_user = Depends(get_current_user)
):
    """Get quote analytics for current user"""
    try:
        conn = await get_database_connection()
        
        # Get user's quote stats
        stats_query = """
            SELECT 
                COUNT(*) as total_quotes,
                COALESCE(SUM(total_amount), 0) as total_value,
                COALESCE(AVG(total_amount), 0) as avg_value,
                COUNT(*) FILTER (WHERE status = 'accepted') as accepted_quotes,
                COALESCE(SUM(estimated_commission), 0) as total_commission
            FROM supplier_quotes
            WHERE supplier_id = $1
        """
        
        stats = await conn.fetchrow(stats_query, current_user["id"])
        
        success_rate = 0
        if stats["total_quotes"] > 0:
            success_rate = (stats["accepted_quotes"] / stats["total_quotes"]) * 100
        
        return {
            "total_quotes": stats["total_quotes"],
            "total_value": float(stats["total_value"]),
            "avg_quote_value": float(stats["avg_value"]),
            "success_rate": round(success_rate, 2),
            "commission_earned": float(stats["total_commission"])
        }
        
    except Exception as e:
        logger.error(f"Error fetching quote analytics: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch quote analytics")
