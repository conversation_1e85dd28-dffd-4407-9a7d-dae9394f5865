"""
Hybrid Tender Ingestion System
Integrates automated scraping with manual hard copy document handling
"""

from fastapi import FastAPI, HTTPException, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Union
import uuid
import json
import logging
from datetime import datetime, timed<PERSON>ta
from enum import Enum

# Supabase client
from supabase import create_client, Client
import os

# Initialize Supabase
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_ANON_KEY")
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# Logging setup
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="BidBeez Hybrid Tender Ingestion API",
    description="Unified system for automated scraping and manual hard copy document handling",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# =====================================================
# ENUMS AND MODELS
# =====================================================

class DocumentAvailability(str, Enum):
    DIGITAL_DOWNLOAD = "digital_download"
    HARD_COPY_PURCHASE = "hard_copy_purchase"
    MIXED = "mixed"
    UNKNOWN = "unknown"

class TenderIngestionMethod(str, Enum):
    AUTOMATED_SCRAPING = "automated_scraping"
    MANUAL_UPLOAD = "manual_upload"
    QUEEN_BEE_PURCHASE = "queen_bee_purchase"

class DocumentPurchaseStatus(str, Enum):
    NOT_REQUIRED = "not_required"
    REQUIRED = "required"
    PURCHASED = "purchased"
    SCANNING = "scanning"
    UPLOADED = "uploaded"
    PROCESSED = "processed"

class HybridTender(BaseModel):
    tender_id: str
    title: str
    organization: str
    reference_number: str
    closing_date: datetime
    document_availability: DocumentAvailability
    ingestion_method: TenderIngestionMethod
    digital_documents: List[Dict] = []
    hard_copy_requirements: Optional[Dict] = None
    purchase_status: DocumentPurchaseStatus = DocumentPurchaseStatus.NOT_REQUIRED
    queen_bee_assigned: Optional[str] = None
    estimated_purchase_cost: Optional[float] = None
    purchase_location: Optional[str] = None
    purchase_deadline: Optional[datetime] = None

class DocumentPurchaseRequest(BaseModel):
    tender_id: str
    queen_bee_id: str
    purchase_location: str
    estimated_cost: float
    urgency: str = "normal"  # low, normal, high, urgent
    special_instructions: Optional[str] = None

class HardCopyUpload(BaseModel):
    tender_id: str
    queen_bee_id: str
    document_name: str
    document_type: str
    scan_quality: str = "high"  # low, medium, high
    pages_count: int
    file_size: int
    notes: Optional[str] = None

# =====================================================
# HYBRID INGESTION SERVICE
# =====================================================

class HybridIngestionService:
    def __init__(self):
        self.document_purchase_locations = {
            "johannesburg": {
                "address": "City of Johannesburg Tender Office, 158 Loveday Street, Johannesburg",
                "hours": "08:00-16:00 Mon-Fri",
                "contact": "011 407 6000",
                "payment_methods": ["cash", "eft", "card"]
            },
            "cape_town": {
                "address": "City of Cape Town Tender Office, 12 Hertzog Boulevard, Cape Town",
                "hours": "08:00-16:00 Mon-Fri", 
                "contact": "021 400 1111",
                "payment_methods": ["cash", "eft", "card"]
            },
            "pretoria": {
                "address": "National Treasury, 40 Church Square, Pretoria",
                "hours": "08:00-16:00 Mon-Fri",
                "contact": "012 315 5111",
                "payment_methods": ["eft", "card"]
            }
        }
    
    async def analyze_tender_documents(self, tender_data: Dict) -> HybridTender:
        """Analyze tender to determine document availability and ingestion method"""
        try:
            tender_id = tender_data.get("id", str(uuid.uuid4()))
            
            # Check if documents are available for download
            digital_docs = tender_data.get("documents", [])
            has_digital_docs = len(digital_docs) > 0
            
            # Check for hard copy requirements
            description = tender_data.get("description", "").lower()
            title = tender_data.get("title", "").lower()
            
            hard_copy_indicators = [
                "documents must be purchased",
                "hard copy only",
                "collect from office",
                "purchase at",
                "available for collection",
                "tender documents available at",
                "documents can be obtained from"
            ]
            
            requires_hard_copy = any(indicator in description or indicator in title 
                                   for indicator in hard_copy_indicators)
            
            # Determine document availability
            if has_digital_docs and not requires_hard_copy:
                doc_availability = DocumentAvailability.DIGITAL_DOWNLOAD
                ingestion_method = TenderIngestionMethod.AUTOMATED_SCRAPING
                purchase_status = DocumentPurchaseStatus.NOT_REQUIRED
            elif requires_hard_copy and not has_digital_docs:
                doc_availability = DocumentAvailability.HARD_COPY_PURCHASE
                ingestion_method = TenderIngestionMethod.QUEEN_BEE_PURCHASE
                purchase_status = DocumentPurchaseStatus.REQUIRED
            elif has_digital_docs and requires_hard_copy:
                doc_availability = DocumentAvailability.MIXED
                ingestion_method = TenderIngestionMethod.AUTOMATED_SCRAPING  # Start with digital
                purchase_status = DocumentPurchaseStatus.REQUIRED
            else:
                doc_availability = DocumentAvailability.UNKNOWN
                ingestion_method = TenderIngestionMethod.MANUAL_UPLOAD
                purchase_status = DocumentPurchaseStatus.NOT_REQUIRED
            
            # Extract hard copy requirements
            hard_copy_requirements = None
            if requires_hard_copy:
                hard_copy_requirements = await self.extract_purchase_requirements(
                    description, title, tender_data.get("organization", "")
                )
            
            return HybridTender(
                tender_id=tender_id,
                title=tender_data.get("title", ""),
                organization=tender_data.get("organization", ""),
                reference_number=tender_data.get("reference_number", ""),
                closing_date=datetime.fromisoformat(tender_data.get("closing_date", datetime.now().isoformat())),
                document_availability=doc_availability,
                ingestion_method=ingestion_method,
                digital_documents=digital_docs,
                hard_copy_requirements=hard_copy_requirements,
                purchase_status=purchase_status,
                estimated_purchase_cost=hard_copy_requirements.get("cost") if hard_copy_requirements else None,
                purchase_location=hard_copy_requirements.get("location") if hard_copy_requirements else None,
                purchase_deadline=hard_copy_requirements.get("deadline") if hard_copy_requirements else None
            )
            
        except Exception as e:
            logger.error(f"Error analyzing tender documents: {e}")
            raise
    
    async def extract_purchase_requirements(self, description: str, title: str, organization: str) -> Dict:
        """Extract hard copy purchase requirements from tender text"""
        try:
            text = f"{title} {description}".lower()
            
            # Extract cost
            cost_patterns = [
                r"r\s*(\d+(?:\.\d{2})?)",
                r"(\d+(?:\.\d{2})?)\s*rand",
                r"cost.*?r\s*(\d+(?:\.\d{2})?)",
                r"fee.*?r\s*(\d+(?:\.\d{2})?)"
            ]
            
            estimated_cost = 100.0  # Default cost
            import re
            for pattern in cost_patterns:
                match = re.search(pattern, text)
                if match:
                    estimated_cost = float(match.group(1))
                    break
            
            # Extract location
            location = "unknown"
            org_lower = organization.lower()
            if "johannesburg" in org_lower or "joburg" in org_lower:
                location = "johannesburg"
            elif "cape town" in org_lower or "capetown" in org_lower:
                location = "cape_town"
            elif "pretoria" in org_lower or "tshwane" in org_lower:
                location = "pretoria"
            elif "national" in org_lower or "treasury" in org_lower:
                location = "pretoria"
            
            # Extract deadline (usually a few days before closing)
            deadline = datetime.now() + timedelta(days=3)  # Default 3 days
            
            return {
                "cost": estimated_cost,
                "location": location,
                "deadline": deadline.isoformat(),
                "purchase_info": self.document_purchase_locations.get(location, {}),
                "extracted_from": "tender_description"
            }
            
        except Exception as e:
            logger.error(f"Error extracting purchase requirements: {e}")
            return {"cost": 100.0, "location": "unknown", "deadline": (datetime.now() + timedelta(days=3)).isoformat()}
    
    async def assign_queen_bee_for_purchase(self, tender_id: str, purchase_requirements: Dict) -> str:
        """Assign Queen Bee for hard copy document purchase"""
        try:
            # Find Queen Bee in the required location
            location = purchase_requirements.get("location", "unknown")
            
            # Get available Queen Bees in the area
            queen_bees_result = supabase.table("queen_bees").select("*").eq("status", "active").execute()
            
            suitable_queen_bees = []
            for queen_bee in queen_bees_result.data:
                territory = queen_bee.get("territory", {})
                if location in territory.get("cities", []) or location in territory.get("provinces", []):
                    suitable_queen_bees.append(queen_bee)
            
            if not suitable_queen_bees:
                # Assign to any available Queen Bee
                suitable_queen_bees = queen_bees_result.data
            
            if not suitable_queen_bees:
                raise Exception("No Queen Bees available for document purchase")
            
            # Select Queen Bee with lowest workload
            selected_queen_bee = min(suitable_queen_bees, key=lambda qb: qb.get("current_workload", 0))
            
            # Create purchase assignment
            assignment = {
                "assignment_id": str(uuid.uuid4()),
                "tender_id": tender_id,
                "queen_bee_id": selected_queen_bee["id"],
                "task_type": "document_purchase",
                "priority": "high",
                "status": "assigned",
                "assigned_at": datetime.now().isoformat(),
                "deadline": purchase_requirements.get("deadline"),
                "metadata": {
                    "purchase_requirements": purchase_requirements,
                    "assignment_reason": "hard_copy_documents"
                }
            }
            
            result = supabase.table("queen_bee_assignments").insert(assignment).execute()
            
            logger.info(f"Assigned Queen Bee {selected_queen_bee['id']} for document purchase: {tender_id}")
            return assignment["assignment_id"]
            
        except Exception as e:
            logger.error(f"Error assigning Queen Bee for purchase: {e}")
            raise
    
    async def process_hard_copy_upload(self, tender_id: str, files: List[UploadFile], metadata: HardCopyUpload) -> Dict:
        """Process uploaded hard copy documents"""
        try:
            uploaded_docs = []
            
            for file in files:
                # Save file (in production, save to cloud storage)
                file_content = await file.read()
                file_path = f"hard_copies/{tender_id}/{file.filename}"
                
                # Create document record
                doc_record = {
                    "tender_id": tender_id,
                    "queen_bee_id": metadata.queen_bee_id,
                    "document_name": file.filename,
                    "document_type": metadata.document_type,
                    "file_path": file_path,
                    "file_size": len(file_content),
                    "scan_quality": metadata.scan_quality,
                    "pages_count": metadata.pages_count,
                    "upload_method": "hard_copy_scan",
                    "uploaded_at": datetime.now().isoformat(),
                    "notes": metadata.notes,
                    "processing_status": "pending"
                }
                
                result = supabase.table("document_uploads").insert(doc_record).execute()
                uploaded_docs.append(result.data[0])
                
                # Trigger AI processing
                await self.trigger_ai_processing(result.data[0]["id"], file_content)
            
            # Update tender status
            await self.update_tender_status(tender_id, "documents_uploaded")
            
            return {
                "status": "success",
                "uploaded_documents": len(uploaded_docs),
                "documents": uploaded_docs
            }
            
        except Exception as e:
            logger.error(f"Error processing hard copy upload: {e}")
            raise
    
    async def trigger_ai_processing(self, document_id: str, file_content: bytes):
        """Trigger AI processing for uploaded document"""
        try:
            # This would integrate with the existing BidBeez AI Engine
            # For now, simulate the processing
            
            processing_job = {
                "document_id": document_id,
                "processing_type": "hard_copy_scan",
                "status": "queued",
                "created_at": datetime.now().isoformat()
            }
            
            supabase.table("ai_processing_jobs").insert(processing_job).execute()
            
            logger.info(f"AI processing queued for document {document_id}")
            
        except Exception as e:
            logger.error(f"Error triggering AI processing: {e}")
    
    async def update_tender_status(self, tender_id: str, status: str):
        """Update tender processing status"""
        try:
            update_data = {
                "processing_status": status,
                "updated_at": datetime.now().isoformat()
            }
            
            supabase.table("tenders").update(update_data).eq("id", tender_id).execute()
            
        except Exception as e:
            logger.error(f"Error updating tender status: {e}")

# =====================================================
# API ENDPOINTS
# =====================================================

hybrid_service = HybridIngestionService()

@app.post("/analyze-tender")
async def analyze_tender_documents(tender_data: Dict):
    """Analyze tender to determine document availability and ingestion method"""
    try:
        hybrid_tender = await hybrid_service.analyze_tender_documents(tender_data)
        return hybrid_tender.dict()
        
    except Exception as e:
        logger.error(f"Error analyzing tender: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/request-document-purchase")
async def request_document_purchase(purchase_request: DocumentPurchaseRequest):
    """Request Queen Bee to purchase hard copy documents"""
    try:
        # Get tender details
        tender_result = supabase.table("tenders").select("*").eq("id", purchase_request.tender_id).execute()
        
        if not tender_result.data:
            raise HTTPException(status_code=404, detail="Tender not found")
        
        tender = tender_result.data[0]
        
        # Create purchase requirements
        purchase_requirements = {
            "cost": purchase_request.estimated_cost,
            "location": purchase_request.purchase_location,
            "deadline": (datetime.now() + timedelta(days=2)).isoformat(),
            "urgency": purchase_request.urgency,
            "special_instructions": purchase_request.special_instructions
        }
        
        # Assign Queen Bee
        assignment_id = await hybrid_service.assign_queen_bee_for_purchase(
            purchase_request.tender_id, 
            purchase_requirements
        )
        
        return {
            "status": "assigned",
            "assignment_id": assignment_id,
            "queen_bee_id": purchase_request.queen_bee_id,
            "estimated_cost": purchase_request.estimated_cost
        }
        
    except Exception as e:
        logger.error(f"Error requesting document purchase: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/upload-hard-copy-documents")
async def upload_hard_copy_documents(
    tender_id: str = Form(...),
    queen_bee_id: str = Form(...),
    document_type: str = Form(...),
    scan_quality: str = Form("high"),
    pages_count: int = Form(...),
    notes: str = Form(None),
    files: List[UploadFile] = File(...)
):
    """Upload scanned hard copy documents"""
    try:
        metadata = HardCopyUpload(
            tender_id=tender_id,
            queen_bee_id=queen_bee_id,
            document_name=files[0].filename if files else "unknown",
            document_type=document_type,
            scan_quality=scan_quality,
            pages_count=pages_count,
            file_size=0,  # Will be calculated
            notes=notes
        )
        
        result = await hybrid_service.process_hard_copy_upload(tender_id, files, metadata)
        
        return result
        
    except Exception as e:
        logger.error(f"Error uploading hard copy documents: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/tender/{tender_id}/ingestion-status")
async def get_tender_ingestion_status(tender_id: str):
    """Get tender ingestion status and method"""
    try:
        # Get tender details
        tender_result = supabase.table("tenders").select("*").eq("id", tender_id).execute()
        
        if not tender_result.data:
            raise HTTPException(status_code=404, detail="Tender not found")
        
        tender = tender_result.data[0]
        
        # Get Queen Bee assignments
        assignments_result = supabase.table("queen_bee_assignments").select("*").eq("tender_id", tender_id).execute()
        
        # Get uploaded documents
        docs_result = supabase.table("document_uploads").select("*").eq("tender_id", tender_id).execute()
        
        return {
            "tender_id": tender_id,
            "title": tender.get("title"),
            "processing_status": tender.get("processing_status", "pending"),
            "ingestion_method": tender.get("ingestion_method", "unknown"),
            "document_availability": tender.get("document_availability", "unknown"),
            "queen_bee_assignments": assignments_result.data,
            "uploaded_documents": docs_result.data,
            "last_updated": tender.get("updated_at")
        }
        
    except Exception as e:
        logger.error(f"Error getting ingestion status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/purchase-locations")
async def get_purchase_locations():
    """Get available document purchase locations"""
    return {"locations": hybrid_service.document_purchase_locations}

@app.get("/queen-bees/available")
async def get_available_queen_bees(location: Optional[str] = None):
    """Get available Queen Bees for document purchase"""
    try:
        query = supabase.table("queen_bees").select("*").eq("status", "active")
        
        if location:
            # Filter by location (simplified)
            result = query.execute()
            filtered_bees = []
            for bee in result.data:
                territory = bee.get("territory", {})
                if location in territory.get("cities", []) or location in territory.get("provinces", []):
                    filtered_bees.append(bee)
            return {"queen_bees": filtered_bees}
        else:
            result = query.execute()
            return {"queen_bees": result.data}
        
    except Exception as e:
        logger.error(f"Error getting available Queen Bees: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8006)
