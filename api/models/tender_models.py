"""
Enhanced Tender and Quote Models for BidBeez Platform
Integrates with the new database schema for tenders and supplier_quotes tables
"""

from datetime import datetime, timezone
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, validator
from enum import Enum
import uuid

# =====================================================
# ENUMS
# =====================================================

class TenderStatus(str, Enum):
    DRAFT = "draft"
    ACTIVE = "active"
    CLOSED = "closed"
    AWARDED = "awarded"
    CANCELLED = "cancelled"
    SUSPENDED = "suspended"

class TenderType(str, Enum):
    OPEN = "open"
    CLOSED = "closed"
    LIMITED = "limited"
    EMERGENCY = "emergency"
    FRAMEWORK = "framework"

class ProcurementMethod(str, Enum):
    COMPETITIVE = "competitive"
    NEGOTIATED = "negotiated"
    SINGLE_SOURCE = "single_source"
    FRAMEWORK = "framework"

class ProcessingStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    PROCESSED = "processed"
    FAILED = "failed"

class CompetitionLevel(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    EXTREME = "extreme"

class QuoteStatus(str, Enum):
    DRAFT = "draft"
    SUBMITTED = "submitted"
    UNDER_REVIEW = "under_review"
    ACCEPTED = "accepted"
    REJECTED = "rejected"
    EXPIRED = "expired"
    WITHDRAWN = "withdrawn"

class CommissionStatus(str, Enum):
    PENDING = "pending"
    CALCULATED = "calculated"
    APPROVED = "approved"
    PAID = "paid"

class BidStatus(str, Enum):
    DRAFT = "draft"
    SUBMITTED = "submitted"
    UNDER_REVIEW = "under_review"
    SHORTLISTED = "shortlisted"
    AWARDED = "awarded"
    NOT_AWARDED = "not_awarded"
    DISQUALIFIED = "disqualified"
    WITHDRAWN = "withdrawn"

class SubmissionMethod(str, Enum):
    ONLINE = "online"
    PHYSICAL = "physical"
    EMAIL = "email"
    HYBRID = "hybrid"

# =====================================================
# CORE MODELS
# =====================================================

class TenderDocument(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    type: str
    url: str
    size: Optional[int] = None
    download_count: int = 0

class BudgetRange(BaseModel):
    min_value: Optional[float] = None
    max_value: Optional[float] = None

class Coordinates(BaseModel):
    lat: float
    lng: float

class Tender(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    tender_id: str
    
    # Basic Information
    title: str
    description: Optional[str] = None
    organization: str
    department: Optional[str] = None
    contact_person: Optional[str] = None
    contact_email: Optional[str] = None
    contact_phone: Optional[str] = None
    
    # Financial Information
    estimated_value: Optional[float] = None
    currency: str = "ZAR"
    budget_range: Optional[BudgetRange] = None
    
    # Dates & Deadlines
    publish_date: Optional[datetime] = None
    closing_date: datetime
    briefing_date: Optional[datetime] = None
    briefing_location: Optional[str] = None
    award_date: Optional[datetime] = None
    project_start_date: Optional[datetime] = None
    project_end_date: Optional[datetime] = None
    
    # Location Information
    location: Optional[str] = None
    province: Optional[str] = None
    city: Optional[str] = None
    coordinates: Optional[Coordinates] = None
    service_area: List[str] = []
    
    # Classification
    category: Optional[str] = None
    subcategory: Optional[str] = None
    tender_type: TenderType = TenderType.OPEN
    procurement_method: ProcurementMethod = ProcurementMethod.COMPETITIVE
    
    # Requirements
    requirements: List[str] = []
    technical_requirements: Dict[str, Any] = {}
    commercial_requirements: Dict[str, Any] = {}
    compliance_requirements: Dict[str, Any] = {}
    
    # B-BBEE & Compliance
    bbbee_required: bool = False
    bbbee_minimum_level: Optional[int] = None
    cidb_grade_required: Optional[str] = None
    tax_clearance_required: bool = True
    
    # Documents
    documents: List[TenderDocument] = []
    specifications_url: Optional[str] = None
    
    # Status & Processing
    status: TenderStatus = TenderStatus.ACTIVE
    processing_status: ProcessingStatus = ProcessingStatus.PROCESSED
    
    # AI & Intelligence
    ai_analysis: Dict[str, Any] = {}
    match_score: float = 0.0
    complexity_score: float = 0.0
    risk_score: float = 0.0
    competition_level: CompetitionLevel = CompetitionLevel.MEDIUM
    
    # Source Information
    source_id: Optional[str] = None
    external_id: Optional[str] = None
    source_url: Optional[str] = None
    reference_number: Optional[str] = None
    scraped_tender_id: Optional[str] = None
    
    # Metrics
    view_count: int = 0
    bid_count: int = 0
    download_count: int = 0
    
    # Metadata
    metadata: Dict[str, Any] = {}
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    @validator('bbbee_minimum_level')
    def validate_bbbee_level(cls, v):
        if v is not None and (v < 1 or v > 8):
            raise ValueError('B-BBEE level must be between 1 and 8')
        return v

class QuoteLineItem(BaseModel):
    id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()))
    description: str
    quantity: int = Field(gt=0)
    unit_price: float = Field(ge=0)
    total: float = Field(ge=0)
    unit_of_measurement: Optional[str] = None
    specifications: Dict[str, Any] = {}

class QuoteDocument(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    type: str
    url: str
    size: Optional[int] = None

class SupplierQuote(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    quote_id: str
    
    # Relationships
    tender_id: str
    supplier_id: str
    rfq_id: Optional[str] = None
    
    # Quote Details
    quote_number: Optional[str] = None
    total_amount: float = Field(ge=0)
    currency: str = "ZAR"
    
    # Line Items
    line_items: List[QuoteLineItem] = []
    
    # Terms & Conditions
    delivery_timeframe: Optional[str] = None
    delivery_cost: float = 0.0
    delivery_location: Optional[str] = None
    validity_period_days: int = 30
    payment_terms: Optional[str] = None
    warranty_terms: Optional[str] = None
    
    # Compliance & Certifications
    compliance_documents: List[str] = []
    certifications: List[str] = []
    bbbee_certificate_url: Optional[str] = None
    tax_clearance_url: Optional[str] = None
    
    # Status & Lifecycle
    status: QuoteStatus = QuoteStatus.DRAFT
    submission_date: Optional[datetime] = None
    review_date: Optional[datetime] = None
    decision_date: Optional[datetime] = None
    expiry_date: Optional[datetime] = None
    
    # Evaluation
    evaluation_score: Optional[float] = None
    technical_score: Optional[float] = None
    commercial_score: Optional[float] = None
    compliance_score: Optional[float] = None
    evaluation_notes: Optional[str] = None
    evaluator_id: Optional[str] = None
    
    # AI & Intelligence
    trust_score: float = 0.0
    competitive_score: float = 0.0
    ai_analysis: Dict[str, Any] = {}
    
    # Commission & Revenue
    commission_rate: float = 0.0
    estimated_commission: float = 0.0
    commission_status: CommissionStatus = CommissionStatus.PENDING
    
    # Smart Contracts & Blockchain
    smart_contract_enabled: bool = False
    smart_contract_address: Optional[str] = None
    blockchain_hash: Optional[str] = None
    
    # Documents & Files
    quote_document_url: Optional[str] = None
    supporting_documents: List[QuoteDocument] = []
    
    # Supplier Information (cached)
    supplier_name: Optional[str] = None
    supplier_location: Optional[str] = None
    supplier_bbbee_level: Optional[int] = None
    
    # Metrics
    view_count: int = 0
    download_count: int = 0
    
    # Versioning
    version: int = 1
    parent_quote_id: Optional[str] = None
    is_latest_version: bool = True
    
    # Metadata
    notes: Optional[str] = None
    internal_notes: Optional[str] = None
    metadata: Dict[str, Any] = {}
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

class BidDocument(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    type: str
    url: str
    size: Optional[int] = None

class TenderBid(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    bid_id: str
    
    # Relationships
    tender_id: str
    bidder_id: str
    
    # Bid Information
    bid_amount: float = Field(ge=0)
    currency: str = "ZAR"
    
    # Submission Details
    submission_method: SubmissionMethod = SubmissionMethod.ONLINE
    submission_date: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    submission_location: Optional[str] = None
    
    # Documents
    bid_documents: List[BidDocument] = []
    technical_proposal_url: Optional[str] = None
    commercial_proposal_url: Optional[str] = None
    compliance_documents_url: Optional[str] = None
    
    # Status
    status: BidStatus = BidStatus.SUBMITTED
    
    # Evaluation
    technical_score: Optional[float] = None
    commercial_score: Optional[float] = None
    compliance_score: Optional[float] = None
    total_score: Optional[float] = None
    ranking: Optional[int] = None
    evaluation_notes: Optional[str] = None
    
    # Award Information
    awarded: bool = False
    award_date: Optional[datetime] = None
    award_amount: Optional[float] = None
    award_reason: Optional[str] = None
    
    # Compliance
    bbbee_points: Optional[float] = None
    local_content_percentage: Optional[float] = None
    
    # Metadata
    metadata: Dict[str, Any] = {}
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

# =====================================================
# REQUEST/RESPONSE MODELS
# =====================================================

class TenderCreateRequest(BaseModel):
    title: str
    description: Optional[str] = None
    organization: str
    estimated_value: Optional[float] = None
    closing_date: datetime
    location: Optional[str] = None
    province: Optional[str] = None
    category: Optional[str] = None
    requirements: List[str] = []
    bbbee_required: bool = False
    bbbee_minimum_level: Optional[int] = None

class QuoteCreateRequest(BaseModel):
    tender_id: str
    total_amount: float
    currency: str = "ZAR"
    delivery_timeframe: Optional[str] = None
    validity_period_days: int = 30
    line_items: List[QuoteLineItem] = []
    terms_and_conditions: Optional[str] = None
    payment_terms: Optional[str] = None
    warranty_terms: Optional[str] = None
    smart_contract_enabled: bool = False

class BidCreateRequest(BaseModel):
    tender_id: str
    bid_amount: float
    currency: str = "ZAR"
    submission_method: SubmissionMethod = SubmissionMethod.ONLINE

class TenderListResponse(BaseModel):
    tenders: List[Tender]
    total: int
    page: int
    limit: int
    has_more: bool

class QuoteListResponse(BaseModel):
    quotes: List[SupplierQuote]
    total: int
    page: int
    limit: int
    has_more: bool
