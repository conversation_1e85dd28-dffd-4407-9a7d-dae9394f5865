"""
Bid Analytics Engine API
Comprehensive bid performance analytics, summaries, and insights for bidders
"""

from fastapi import FastAPI, HTTPException, Depends, Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Union
import uuid
import json
import logging
from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decimal
import asyncio
from enum import Enum

# Supabase client
from supabase import create_client, Client
import os

# Initialize Supabase
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_ANON_KEY")
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# Logging setup
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="BidBeez Bid Analytics Engine",
    description="Comprehensive bid performance analytics and insights",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# =====================================================
# MODELS
# =====================================================

class TimeRange(str, Enum):
    WEEK = "week"
    MONTH = "month"
    QUARTER = "quarter"
    YEAR = "year"
    ALL_TIME = "all_time"

class AnalyticsCategory(str, Enum):
    PERFORMANCE = "performance"
    FINANCIAL = "financial"
    COMPETITIVE = "competitive"
    PSYCHOLOGICAL = "psychological"
    ECONOMIC_IMPACT = "economic_impact"

class BidPerformanceMetrics(BaseModel):
    # Core Performance
    total_bids: int
    successful_bids: int
    success_rate: float
    total_value: float
    won_value: float
    average_bid_value: float
    
    # Financial Analytics
    revenue: float
    profit: float
    profit_margin: float
    roi: float
    cost_per_bid: float
    revenue_per_bid: float
    
    # Competitive Analytics
    market_position: int
    competitive_win_rate: float
    average_competitors: float
    win_rate_vs_competitors: float
    
    # Efficiency Metrics
    average_response_time: float
    average_preparation_time: float
    document_completion_rate: float
    compliance_score: float
    
    # Economic Impact
    jobs_created: int
    economic_value: float
    community_impact: str

class BidSummary(BaseModel):
    user_id: str
    time_range: TimeRange
    
    # Quick Performance
    recent_performance: Dict
    top_insights: List[Dict]
    quick_stats: Dict
    
    # Impact Metrics
    economic_impact: Dict
    psychological_state: Dict
    
    # Alerts & Recommendations
    alerts: List[Dict]
    recommendations: List[Dict]

class CompetitorAnalysis(BaseModel):
    competitor_name: str
    heads_up_wins: int
    heads_up_losses: int
    win_rate: float
    avg_bid_difference: float
    last_encounter: datetime
    threat_level: str

class CategoryPerformance(BaseModel):
    category: str
    bids: int
    wins: int
    success_rate: float
    total_value: float
    avg_value: float
    profitability: float

# =====================================================
# BID ANALYTICS ENGINE
# =====================================================

class BidAnalyticsEngine:
    """Comprehensive bid analytics and insights engine"""
    
    def __init__(self):
        self.psychological_weights = {
            'stress_impact': -0.125,
            'confidence_boost': 0.15,
            'optimal_timing': 0.08,
            'preparation_time': 0.12
        }
    
    async def get_bid_performance_metrics(
        self, 
        user_id: str, 
        time_range: TimeRange = TimeRange.MONTH
    ) -> BidPerformanceMetrics:
        """Get comprehensive bid performance metrics"""
        try:
            # Calculate date range
            end_date = datetime.now()
            start_date = self._calculate_start_date(end_date, time_range)
            
            # Get bid data from database
            bid_data = await self._fetch_bid_data(user_id, start_date, end_date)
            
            # Calculate core metrics
            core_metrics = self._calculate_core_metrics(bid_data)
            
            # Calculate financial metrics
            financial_metrics = self._calculate_financial_metrics(bid_data, core_metrics)
            
            # Calculate competitive metrics
            competitive_metrics = await self._calculate_competitive_metrics(user_id, bid_data)
            
            # Calculate efficiency metrics
            efficiency_metrics = self._calculate_efficiency_metrics(bid_data)
            
            # Calculate economic impact
            economic_impact = self._calculate_economic_impact(bid_data)
            
            return BidPerformanceMetrics(
                **core_metrics,
                **financial_metrics,
                **competitive_metrics,
                **efficiency_metrics,
                **economic_impact
            )
            
        except Exception as e:
            logger.error(f"Error calculating bid performance metrics: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def get_bid_summary(
        self, 
        user_id: str, 
        compact: bool = False
    ) -> BidSummary:
        """Get quick bid performance summary"""
        try:
            # Get recent performance (last 30 days)
            recent_performance = await self._get_recent_performance(user_id)
            
            # Generate top insights
            top_insights = await self._generate_top_insights(user_id)
            
            # Get quick stats
            quick_stats = await self._get_quick_stats(user_id)
            
            # Calculate economic impact
            economic_impact = await self._get_economic_impact_summary(user_id)
            
            # Get psychological state
            psychological_state = await self._get_psychological_state(user_id)
            
            # Generate alerts
            alerts = await self._generate_alerts(user_id)
            
            # Generate recommendations
            recommendations = await self._generate_recommendations(user_id, compact)
            
            return BidSummary(
                user_id=user_id,
                time_range=TimeRange.MONTH,
                recent_performance=recent_performance,
                top_insights=top_insights,
                quick_stats=quick_stats,
                economic_impact=economic_impact,
                psychological_state=psychological_state,
                alerts=alerts,
                recommendations=recommendations
            )
            
        except Exception as e:
            logger.error(f"Error generating bid summary: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def get_competitive_analysis(
        self, 
        user_id: str, 
        time_range: TimeRange = TimeRange.QUARTER
    ) -> List[CompetitorAnalysis]:
        """Get detailed competitive analysis"""
        try:
            # Get competitor data
            competitors = await self._fetch_competitor_data(user_id, time_range)
            
            analysis = []
            for competitor in competitors:
                competitor_analysis = CompetitorAnalysis(
                    competitor_name=competitor['name'],
                    heads_up_wins=competitor['wins'],
                    heads_up_losses=competitor['losses'],
                    win_rate=competitor['win_rate'],
                    avg_bid_difference=competitor['avg_difference'],
                    last_encounter=competitor['last_encounter'],
                    threat_level=self._calculate_threat_level(competitor)
                )
                analysis.append(competitor_analysis)
            
            return sorted(analysis, key=lambda x: x.win_rate, reverse=True)
            
        except Exception as e:
            logger.error(f"Error generating competitive analysis: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def get_category_performance(
        self, 
        user_id: str, 
        time_range: TimeRange = TimeRange.YEAR
    ) -> List[CategoryPerformance]:
        """Get performance breakdown by category"""
        try:
            # Get category data
            categories = await self._fetch_category_data(user_id, time_range)
            
            performance = []
            for category in categories:
                category_perf = CategoryPerformance(
                    category=category['name'],
                    bids=category['total_bids'],
                    wins=category['successful_bids'],
                    success_rate=category['success_rate'],
                    total_value=category['total_value'],
                    avg_value=category['avg_value'],
                    profitability=category['profitability']
                )
                performance.append(category_perf)
            
            return sorted(performance, key=lambda x: x.success_rate, reverse=True)
            
        except Exception as e:
            logger.error(f"Error calculating category performance: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def get_psychological_insights(
        self, 
        user_id: str
    ) -> Dict:
        """Get psychological insights and behavioral patterns"""
        try:
            # Get behavioral data
            behavioral_data = await self._fetch_behavioral_data(user_id)
            
            # Analyze patterns
            patterns = self._analyze_behavioral_patterns(behavioral_data)
            
            # Calculate stress impact
            stress_impact = self._calculate_stress_impact(behavioral_data)
            
            # Get optimal times
            optimal_times = self._calculate_optimal_times(behavioral_data)
            
            # Generate recommendations
            psych_recommendations = self._generate_psychological_recommendations(
                patterns, stress_impact, optimal_times
            )
            
            return {
                'behavioral_patterns': patterns,
                'stress_impact_on_success': stress_impact,
                'optimal_bidding_times': optimal_times,
                'current_psychological_state': self._assess_current_state(behavioral_data),
                'recommendations': psych_recommendations
            }
            
        except Exception as e:
            logger.error(f"Error generating psychological insights: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    # Private helper methods
    def _calculate_start_date(self, end_date: datetime, time_range: TimeRange) -> datetime:
        """Calculate start date based on time range"""
        if time_range == TimeRange.WEEK:
            return end_date - timedelta(weeks=1)
        elif time_range == TimeRange.MONTH:
            return end_date - timedelta(days=30)
        elif time_range == TimeRange.QUARTER:
            return end_date - timedelta(days=90)
        elif time_range == TimeRange.YEAR:
            return end_date - timedelta(days=365)
        else:  # ALL_TIME
            return datetime(2020, 1, 1)  # Platform start date
    
    async def _fetch_bid_data(self, user_id: str, start_date: datetime, end_date: datetime) -> List[Dict]:
        """Fetch bid data from database"""
        try:
            # Query bids table
            result = supabase.table("bids").select("*").eq("user_id", user_id).gte("created_at", start_date.isoformat()).lte("created_at", end_date.isoformat()).execute()
            
            return result.data if result.data else []
            
        except Exception as e:
            logger.error(f"Error fetching bid data: {e}")
            return []
    
    def _calculate_core_metrics(self, bid_data: List[Dict]) -> Dict:
        """Calculate core performance metrics"""
        total_bids = len(bid_data)
        successful_bids = len([bid for bid in bid_data if bid.get('status') == 'won'])
        success_rate = (successful_bids / total_bids * 100) if total_bids > 0 else 0
        
        total_value = sum(bid.get('bid_amount', 0) for bid in bid_data)
        won_value = sum(bid.get('bid_amount', 0) for bid in bid_data if bid.get('status') == 'won')
        average_bid_value = total_value / total_bids if total_bids > 0 else 0
        
        return {
            'total_bids': total_bids,
            'successful_bids': successful_bids,
            'success_rate': success_rate,
            'total_value': total_value,
            'won_value': won_value,
            'average_bid_value': average_bid_value
        }
    
    def _calculate_financial_metrics(self, bid_data: List[Dict], core_metrics: Dict) -> Dict:
        """Calculate financial performance metrics"""
        # Estimate costs and profits (would be more sophisticated in production)
        revenue = core_metrics['won_value']
        estimated_costs = revenue * 0.85  # Assume 15% margin
        profit = revenue - estimated_costs
        profit_margin = (profit / revenue * 100) if revenue > 0 else 0
        
        # Calculate ROI
        total_bid_costs = len(bid_data) * 45000  # Estimated cost per bid
        roi = (profit / total_bid_costs * 100) if total_bid_costs > 0 else 0
        
        cost_per_bid = total_bid_costs / len(bid_data) if len(bid_data) > 0 else 0
        revenue_per_bid = revenue / len(bid_data) if len(bid_data) > 0 else 0
        
        return {
            'revenue': revenue,
            'profit': profit,
            'profit_margin': profit_margin,
            'roi': roi,
            'cost_per_bid': cost_per_bid,
            'revenue_per_bid': revenue_per_bid
        }
    
    async def _calculate_competitive_metrics(self, user_id: str, bid_data: List[Dict]) -> Dict:
        """Calculate competitive performance metrics"""
        # Simulate competitive metrics (would query actual competitor data)
        return {
            'market_position': 7,
            'competitive_win_rate': 72.3,
            'average_competitors': 8.4,
            'win_rate_vs_competitors': 15.8
        }
    
    def _calculate_efficiency_metrics(self, bid_data: List[Dict]) -> Dict:
        """Calculate efficiency metrics"""
        # Simulate efficiency calculations
        return {
            'average_response_time': 2.3,
            'average_preparation_time': 18.5,
            'document_completion_rate': 94.7,
            'compliance_score': 96.2
        }
    
    def _calculate_economic_impact(self, bid_data: List[Dict]) -> Dict:
        """Calculate economic impact metrics"""
        # Calculate jobs created based on bid values
        total_value = sum(bid.get('bid_amount', 0) for bid in bid_data if bid.get('status') == 'won')
        jobs_created = int(total_value / 500000)  # 1 job per R500k
        economic_value = jobs_created * 45000  # Average salary impact
        
        community_impact = self._get_community_impact_message(jobs_created)
        
        return {
            'jobs_created': jobs_created,
            'economic_value': economic_value,
            'community_impact': community_impact
        }
    
    def _get_community_impact_message(self, jobs: int) -> str:
        """Get community impact message based on jobs created"""
        if jobs >= 50:
            return f"Major community impact - Supporting {jobs} jobs across multiple sectors!"
        elif jobs >= 20:
            return f"Significant local impact - Creating {jobs} employment opportunities!"
        elif jobs >= 10:
            return f"Meaningful contribution - Generating {jobs} jobs in your community!"
        elif jobs >= 5:
            return f"Direct employment - Supporting {jobs} workers and their families!"
        else:
            return "Economic participation - Contributing to local job creation!"
    
    async def _get_recent_performance(self, user_id: str) -> Dict:
        """Get recent performance summary"""
        # Simulate recent performance data
        return {
            'last_30_days': {
                'bids': 12,
                'wins': 8,
                'success_rate': 66.7,
                'value': 24500000
            },
            'trend': 'up',
            'trend_percentage': 12.5
        }
    
    async def _generate_top_insights(self, user_id: str) -> List[Dict]:
        """Generate top insights for user"""
        return [
            {
                'type': 'success',
                'title': 'Strong Month',
                'description': 'Your success rate is 12.5% above average',
                'value': '66.7%',
                'trend': 'up'
            },
            {
                'type': 'achievement',
                'title': 'New Record',
                'description': 'Highest monthly value achieved',
                'value': 'R24.5M',
                'trend': 'up'
            },
            {
                'type': 'warning',
                'title': 'Competition Alert',
                'description': 'ABC Construction won 3 recent head-to-heads',
                'trend': 'down'
            }
        ]
    
    async def _get_quick_stats(self, user_id: str) -> Dict:
        """Get quick statistics"""
        return {
            'avg_response_time': 2.1,
            'compliance_score': 94.8,
            'competitive_position': 7,
            'next_deadline': '2024-12-15T17:00:00Z'
        }
    
    async def _get_economic_impact_summary(self, user_id: str) -> Dict:
        """Get economic impact summary"""
        return {
            'jobs_created': 89,
            'economic_value': 4005000,
            'community_rank': 'Top 5% Economic Contributor'
        }
    
    async def _get_psychological_state(self, user_id: str) -> Dict:
        """Get current psychological state"""
        return {
            'current_mood': 'confident',
            'stress_level': 0.3,
            'optimal_time': '14:00-16:00',
            'recommendation': 'Great time for high-value bids - you\'re in peak performance mode!'
        }
    
    async def _generate_alerts(self, user_id: str) -> List[Dict]:
        """Generate alerts for user"""
        return [
            {
                'severity': 'warning',
                'title': 'Deadline Approaching',
                'message': 'Municipal Infrastructure bid due in 3 days',
                'action_required': True,
                'deadline': '2024-12-15T17:00:00Z'
            },
            {
                'severity': 'info',
                'title': 'New Opportunity',
                'message': '3 new tenders match your profile',
                'action_required': False
            }
        ]
    
    async def _generate_recommendations(self, user_id: str, compact: bool = False) -> List[Dict]:
        """Generate recommendations for user"""
        recommendations = [
            {
                'priority': 'high',
                'category': 'performance',
                'title': 'Focus on IT Services',
                'description': 'Your success rate in IT is 15% higher than construction',
                'expected_impact': '+R2.5M potential revenue'
            },
            {
                'priority': 'medium',
                'category': 'psychological',
                'title': 'Optimize Timing',
                'description': 'Submit bids during your peak hours (14:00-16:00)',
                'expected_impact': '+8% success rate'
            },
            {
                'priority': 'low',
                'category': 'competitive',
                'title': 'Monitor ABC Construction',
                'description': 'They\'ve won 3 recent head-to-heads against you',
                'expected_impact': 'Improved competitive positioning'
            }
        ]
        
        return recommendations[:2] if compact else recommendations

# =====================================================
# API ENDPOINTS
# =====================================================

analytics_engine = BidAnalyticsEngine()

@app.get("/analytics/performance/{user_id}")
async def get_bid_performance(
    user_id: str,
    time_range: TimeRange = Query(TimeRange.MONTH, description="Time range for analysis")
):
    """Get comprehensive bid performance metrics"""
    return await analytics_engine.get_bid_performance_metrics(user_id, time_range)

@app.get("/analytics/summary/{user_id}")
async def get_bid_summary(
    user_id: str,
    compact: bool = Query(False, description="Return compact summary")
):
    """Get quick bid performance summary"""
    return await analytics_engine.get_bid_summary(user_id, compact)

@app.get("/analytics/competitive/{user_id}")
async def get_competitive_analysis(
    user_id: str,
    time_range: TimeRange = Query(TimeRange.QUARTER, description="Time range for analysis")
):
    """Get competitive analysis"""
    return await analytics_engine.get_competitive_analysis(user_id, time_range)

@app.get("/analytics/categories/{user_id}")
async def get_category_performance(
    user_id: str,
    time_range: TimeRange = Query(TimeRange.YEAR, description="Time range for analysis")
):
    """Get performance by category"""
    return await analytics_engine.get_category_performance(user_id, time_range)

@app.get("/analytics/psychological/{user_id}")
async def get_psychological_insights(user_id: str):
    """Get psychological insights and behavioral patterns"""
    return await analytics_engine.get_psychological_insights(user_id)

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "bid_analytics_engine"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8008)
