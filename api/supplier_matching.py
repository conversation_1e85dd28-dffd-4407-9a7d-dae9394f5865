"""
Supplier Matching Engine API
AI-powered supplier-to-specification matching with B-BBEE and provincial weighting
"""

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, <PERSON>
from typing import Dict, List, Optional, Union
import uuid
import json
import logging
from datetime import datetime
import asyncio
import math
from geopy.distance import geodesic

# Supabase client
from supabase import create_client, Client
import os

# Initialize Supabase
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_ANON_KEY")
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# Logging setup
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="BidBeez Supplier Matching API",
    description="AI-powered supplier-to-specification matching engine",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# =====================================================
# PYDANTIC MODELS
# =====================================================

class MatchRequest(BaseModel):
    spec_id: str
    max_suppliers: int = Field(default=20, description="Maximum number of suppliers to return")
    min_score: float = Field(default=0.3, description="Minimum match score threshold")
    include_bee_weighting: bool = Field(default=True, description="Apply B-BBEE weighting")
    include_geographic_weighting: bool = Field(default=True, description="Apply geographic proximity weighting")
    province_preference: Optional[str] = None
    category_filter: Optional[str] = None

class SupplierMatch(BaseModel):
    supplier_id: str
    supplier_name: str
    overall_score: float
    boq_match_score: float
    sow_match_score: float
    compliance_score: float
    geographic_score: float
    trust_score: float
    bee_bonus: float
    provincial_bonus: float
    confidence_level: str
    match_details: Dict
    contact_info: Dict
    capabilities: List[str]
    certifications: List[Dict]

class MatchResponse(BaseModel):
    spec_id: str
    total_suppliers_evaluated: int
    matches_returned: int
    processing_time_ms: int
    matches: List[SupplierMatch]
    algorithm_version: str = "1.0"

class ProductMatch(BaseModel):
    product_id: str
    product_name: str
    match_score: float
    price_competitiveness: float
    availability_score: float
    compliance_status: str

# =====================================================
# MATCHING ALGORITHMS
# =====================================================

class SupplierMatchingEngine:
    def __init__(self):
        self.algorithm_version = "1.0"
        self.bee_weights = {
            1: 1.20,  # Level 1 B-BBEE gets 20% bonus
            2: 1.15,  # Level 2 gets 15% bonus
            3: 1.10,  # Level 3 gets 10% bonus
            4: 1.05,  # Level 4 gets 5% bonus
        }
        
    async def match_suppliers(self, spec_id: str, options: MatchRequest) -> MatchResponse:
        """Main matching function"""
        start_time = datetime.now()
        
        try:
            # Get specification details
            spec_data = await self.get_specification_data(spec_id)
            if not spec_data:
                raise HTTPException(status_code=404, detail="Specification not found")
            
            # Get available suppliers
            suppliers = await self.get_available_suppliers(options.category_filter)
            
            # Calculate matches
            matches = []
            for supplier in suppliers:
                match_score = await self.calculate_match_score(spec_data, supplier, options)
                
                if match_score["overall_score"] >= options.min_score:
                    matches.append(match_score)
            
            # Sort by overall score
            matches.sort(key=lambda x: x["overall_score"], reverse=True)
            
            # Limit results
            matches = matches[:options.max_suppliers]
            
            # Save matching results
            await self.save_matching_results(spec_id, matches)
            
            processing_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            return MatchResponse(
                spec_id=spec_id,
                total_suppliers_evaluated=len(suppliers),
                matches_returned=len(matches),
                processing_time_ms=processing_time,
                matches=[SupplierMatch(**match) for match in matches],
                algorithm_version=self.algorithm_version
            )
            
        except Exception as e:
            logger.error(f"Error in supplier matching: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def get_specification_data(self, spec_id: str) -> Dict:
        """Get specification data with BOQ, SOW, and standards"""
        try:
            # Get main specification
            spec_result = supabase.table("parsed_specifications").select("*").eq("id", spec_id).execute()
            if not spec_result.data:
                return None
            
            spec = spec_result.data[0]
            
            # Get BOQ items
            boq_result = supabase.table("boq_line_items").select("*").eq("spec_id", spec_id).execute()
            
            # Get SOW requirements
            sow_result = supabase.table("sow_requirements").select("*").eq("spec_id", spec_id).execute()
            
            # Get standards
            standards_result = supabase.table("specification_standards").select("*").eq("spec_id", spec_id).execute()
            
            return {
                "specification": spec,
                "boq_items": boq_result.data,
                "sow_requirements": sow_result.data,
                "standards": standards_result.data
            }
            
        except Exception as e:
            logger.error(f"Error getting specification data: {e}")
            return None
    
    async def get_available_suppliers(self, category_filter: Optional[str] = None) -> List[Dict]:
        """Get available suppliers with their products and capabilities"""
        try:
            # Get suppliers with basic info
            query = supabase.table("users").select("""
                id, email, 
                supplier_blockchain_profiles(*)
            """).not_.is_("supplier_blockchain_profiles", "null")
            
            suppliers_result = query.execute()
            
            suppliers = []
            for supplier_data in suppliers_result.data:
                if supplier_data.get("supplier_blockchain_profiles"):
                    profile = supplier_data["supplier_blockchain_profiles"][0]
                    
                    # Get supplier products
                    products_result = supabase.table("supplier_products").select("*").eq("supplier_id", supplier_data["id"]).execute()
                    
                    # Get compliance records
                    compliance_result = supabase.table("supplier_compliance_record").select("*").eq("supplier_id", supplier_data["id"]).execute()
                    
                    supplier = {
                        "id": supplier_data["id"],
                        "email": supplier_data["email"],
                        "profile": profile,
                        "products": products_result.data,
                        "compliance": compliance_result.data[0] if compliance_result.data else None
                    }
                    
                    # Apply category filter
                    if category_filter:
                        matching_products = [p for p in supplier["products"] if p.get("category") == category_filter]
                        if matching_products:
                            suppliers.append(supplier)
                    else:
                        suppliers.append(supplier)
            
            return suppliers
            
        except Exception as e:
            logger.error(f"Error getting suppliers: {e}")
            return []
    
    async def calculate_match_score(self, spec_data: Dict, supplier: Dict, options: MatchRequest) -> Dict:
        """Calculate comprehensive match score for a supplier"""
        try:
            # Initialize scores
            boq_score = await self.calculate_boq_match(spec_data["boq_items"], supplier["products"])
            sow_score = await self.calculate_sow_match(spec_data["sow_requirements"], supplier["products"])
            compliance_score = await self.calculate_compliance_score(spec_data["standards"], supplier)
            geographic_score = await self.calculate_geographic_score(spec_data["specification"], supplier["profile"])
            trust_score = self.calculate_trust_score(supplier)
            
            # Base overall score (weighted average)
            base_score = (
                boq_score * 0.35 +
                sow_score * 0.25 +
                compliance_score * 0.25 +
                geographic_score * 0.10 +
                trust_score * 0.05
            )
            
            # Apply B-BBEE bonus
            bee_bonus = 0.0
            if options.include_bee_weighting and supplier.get("compliance"):
                bee_level = supplier["compliance"].get("bbbee_level", 8)
                if bee_level in self.bee_weights:
                    bee_bonus = (self.bee_weights[bee_level] - 1.0) * base_score
            
            # Apply provincial bonus
            provincial_bonus = 0.0
            if options.include_geographic_weighting and options.province_preference:
                supplier_location = supplier["profile"].get("location", {})
                if isinstance(supplier_location, dict):
                    supplier_province = supplier_location.get("province")
                    if supplier_province == options.province_preference:
                        provincial_bonus = 0.15 * base_score
            
            # Calculate final score
            overall_score = base_score + bee_bonus + provincial_bonus
            overall_score = min(overall_score, 1.0)  # Cap at 1.0
            
            # Determine confidence level
            confidence_level = self.determine_confidence_level(overall_score, spec_data, supplier)
            
            # Get supplier contact info
            contact_info = {
                "email": supplier["email"],
                "company_name": supplier["profile"].get("company_name", ""),
                "location": supplier["profile"].get("location", {})
            }
            
            # Get capabilities
            capabilities = []
            for product in supplier["products"]:
                if product.get("capabilities"):
                    capabilities.extend(product["capabilities"])
            capabilities = list(set(capabilities))  # Remove duplicates
            
            # Get certifications
            certifications = []
            for product in supplier["products"]:
                cert_result = supabase.table("product_certifications").select("*").eq("product_id", product["id"]).execute()
                certifications.extend(cert_result.data)
            
            return {
                "supplier_id": supplier["id"],
                "supplier_name": supplier["profile"].get("company_name", supplier["email"]),
                "overall_score": round(overall_score, 3),
                "boq_match_score": round(boq_score, 3),
                "sow_match_score": round(sow_score, 3),
                "compliance_score": round(compliance_score, 3),
                "geographic_score": round(geographic_score, 3),
                "trust_score": round(trust_score, 3),
                "bee_bonus": round(bee_bonus, 3),
                "provincial_bonus": round(provincial_bonus, 3),
                "confidence_level": confidence_level,
                "match_details": {
                    "boq_matches": len(spec_data["boq_items"]),
                    "sow_matches": len(spec_data["sow_requirements"]),
                    "standards_compliance": len(spec_data["standards"]),
                    "product_count": len(supplier["products"])
                },
                "contact_info": contact_info,
                "capabilities": capabilities,
                "certifications": certifications
            }
            
        except Exception as e:
            logger.error(f"Error calculating match score: {e}")
            return {
                "supplier_id": supplier["id"],
                "supplier_name": supplier.get("email", "Unknown"),
                "overall_score": 0.0,
                "boq_match_score": 0.0,
                "sow_match_score": 0.0,
                "compliance_score": 0.0,
                "geographic_score": 0.0,
                "trust_score": 0.0,
                "bee_bonus": 0.0,
                "provincial_bonus": 0.0,
                "confidence_level": "low",
                "match_details": {},
                "contact_info": {},
                "capabilities": [],
                "certifications": []
            }
    
    async def calculate_boq_match(self, boq_items: List[Dict], products: List[Dict]) -> float:
        """Calculate BOQ matching score"""
        if not boq_items or not products:
            return 0.0
        
        matches = 0
        total_items = len(boq_items)
        
        for boq_item in boq_items:
            boq_desc = boq_item.get("description", "").lower()
            boq_category = boq_item.get("category", "").lower()
            
            for product in products:
                product_desc = product.get("description", "").lower()
                product_category = product.get("category", "").lower()
                
                # Simple keyword matching (can be enhanced with NLP)
                if (any(word in product_desc for word in boq_desc.split() if len(word) > 3) or
                    boq_category == product_category):
                    matches += 1
                    break
        
        return matches / total_items if total_items > 0 else 0.0
    
    async def calculate_sow_match(self, sow_requirements: List[Dict], products: List[Dict]) -> float:
        """Calculate SOW matching score"""
        if not sow_requirements or not products:
            return 0.0
        
        matches = 0
        total_requirements = len(sow_requirements)
        
        for sow_req in sow_requirements:
            req_desc = sow_req.get("description", "").lower()
            req_category = sow_req.get("category", "").lower()
            
            for product in products:
                product_capabilities = product.get("capabilities", [])
                product_category = product.get("category", "").lower()
                
                # Check if supplier has matching capabilities
                if (any(req_category in cap.lower() for cap in product_capabilities) or
                    req_category == product_category):
                    matches += 1
                    break
        
        return matches / total_requirements if total_requirements > 0 else 0.0
    
    async def calculate_compliance_score(self, standards: List[Dict], supplier: Dict) -> float:
        """Calculate compliance score based on standards"""
        if not standards:
            return 1.0  # No standards required
        
        # Get supplier's product certifications
        all_certifications = []
        for product in supplier["products"]:
            cert_result = supabase.table("product_certifications").select("*").eq("product_id", product["id"]).execute()
            all_certifications.extend(cert_result.data)
        
        if not all_certifications:
            return 0.0
        
        compliant_standards = 0
        for standard in standards:
            standard_type = standard.get("standard_type", "").upper()
            standard_code = standard.get("standard_code", "")
            
            # Check if supplier has matching certification
            for cert in all_certifications:
                if (cert.get("standard_type", "").upper() == standard_type and
                    cert.get("standard_code", "") == standard_code and
                    cert.get("verification_status") == "verified"):
                    compliant_standards += 1
                    break
        
        return compliant_standards / len(standards)
    
    async def calculate_geographic_score(self, specification: Dict, supplier_profile: Dict) -> float:
        """Calculate geographic proximity score"""
        spec_province = specification.get("province", "")
        supplier_location = supplier_profile.get("location", {})
        
        if not spec_province or not supplier_location:
            return 0.5  # Neutral score if location data is missing
        
        if isinstance(supplier_location, dict):
            supplier_province = supplier_location.get("province", "")
            if supplier_province == spec_province:
                return 1.0  # Same province
            else:
                return 0.3  # Different province
        
        return 0.5
    
    def calculate_trust_score(self, supplier: Dict) -> float:
        """Calculate supplier trust score"""
        profile = supplier.get("profile", {})
        
        # Base score from profile completeness
        score = 0.5
        
        # Verification bonus
        if profile.get("is_verified"):
            score += 0.3
        
        # Experience bonus
        experience = profile.get("industry_experience", 0)
        if experience > 5:
            score += 0.2
        elif experience > 2:
            score += 0.1
        
        # Performance score
        performance = profile.get("performance_score", 0)
        if performance > 0:
            score += (performance / 100) * 0.3
        
        return min(score, 1.0)
    
    def determine_confidence_level(self, overall_score: float, spec_data: Dict, supplier: Dict) -> str:
        """Determine confidence level of the match"""
        if overall_score >= 0.8:
            return "high"
        elif overall_score >= 0.6:
            return "medium"
        elif overall_score >= 0.4:
            return "low"
        else:
            return "very_low"
    
    async def save_matching_results(self, spec_id: str, matches: List[Dict]):
        """Save matching results to database"""
        try:
            # Prepare match records
            match_records = []
            for match in matches:
                record = {
                    "spec_id": spec_id,
                    "supplier_id": match["supplier_id"],
                    "overall_score": match["overall_score"],
                    "boq_match_score": match["boq_match_score"],
                    "sow_match_score": match["sow_match_score"],
                    "compliance_score": match["compliance_score"],
                    "geographic_score": match["geographic_score"],
                    "trust_score": match["trust_score"],
                    "bee_bonus": match["bee_bonus"],
                    "provincial_bonus": match["provincial_bonus"],
                    "match_details": match["match_details"],
                    "confidence_level": match["confidence_level"],
                    "match_status": "active"
                }
                match_records.append(record)
            
            # Insert match records
            if match_records:
                supabase.table("specification_matches").insert(match_records).execute()
            
        except Exception as e:
            logger.error(f"Error saving matching results: {e}")

# =====================================================
# API ENDPOINTS
# =====================================================

matching_engine = SupplierMatchingEngine()

@app.post("/match", response_model=MatchResponse)
async def match_suppliers(request: MatchRequest):
    """Match suppliers to a specification"""
    return await matching_engine.match_suppliers(request.spec_id, request)

@app.get("/matches/{spec_id}")
async def get_matches(spec_id: str):
    """Get existing matches for a specification"""
    try:
        result = supabase.table("specification_matches").select("*").eq("spec_id", spec_id).order("overall_score", desc=True).execute()
        return {"matches": result.data}
    except Exception as e:
        logger.error(f"Error getting matches: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/suppliers/{supplier_id}/products")
async def get_supplier_products(supplier_id: str):
    """Get products for a specific supplier"""
    try:
        result = supabase.table("supplier_products").select("*").eq("supplier_id", supplier_id).execute()
        return {"products": result.data}
    except Exception as e:
        logger.error(f"Error getting supplier products: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
