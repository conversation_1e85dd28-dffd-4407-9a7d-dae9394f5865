"""
Portfolio Balance AI Service
Analyzes user portfolio balance and generates psychological triggers for RFQ/Tender optimization
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from supabase import create_client, Client
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Supabase client
supabase: Client = create_client(
    os.getenv("SUPABASE_URL", ""),
    os.getenv("SUPABASE_SERVICE_KEY", "")
)

class BalanceStatus(str, Enum):
    BALANCED = "balanced"
    RFQ_DEFICIT = "rfq_deficit"
    TENDER_DEFICIT = "tender_deficit"
    CRITICAL_IMBALANCE = "critical_imbalance"

class UrgencyLevel(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class PsychologicalTrigger(str, Enum):
    FOMO = "fomo"
    COMPETITION = "competition"
    BALANCE_OPTIMIZATION = "balance_optimization"
    INSTANT_GRATIFICATION = "instant_gratification"
    FINANCIAL_PRESSURE = "financial_pressure"
    ACHIEVEMENT = "achievement"

@dataclass
class PortfolioBalance:
    user_id: str
    total_activities: int
    rfq_activities: int
    tender_activities: int
    current_rfq_ratio: float
    current_tender_ratio: float
    target_rfq_ratio: float
    target_tender_ratio: float
    balance_status: BalanceStatus
    deviation_score: float
    urgency_level: UrgencyLevel
    missed_earnings: float
    potential_earnings: float

@dataclass
class RFQSuggestion:
    suggestion_id: str
    suggestion_type: str  # create_bidder_rfq, bid_government_rfq, bid_tender
    opportunity_id: Optional[str]
    opportunity_type: str
    ai_confidence: float
    success_probability: float
    potential_earnings: float
    time_to_complete: str
    urgency_level: UrgencyLevel
    psychological_trigger: PsychologicalTrigger
    trigger_message: str
    reasoning_factors: List[str]

class PortfolioBalanceAI:
    """AI service for portfolio balance optimization and psychological manipulation"""
    
    def __init__(self):
        self.target_ratios = {
            "rfq_ratio": 60.0,  # 60% RFQ activities
            "tender_ratio": 40.0  # 40% Tender activities
        }
        
        self.psychological_profiles = {
            "achiever": {
                "triggers": [PsychologicalTrigger.ACHIEVEMENT, PsychologicalTrigger.COMPETITION],
                "success_motivation": 0.9,
                "financial_motivation": 0.7
            },
            "hunter": {
                "triggers": [PsychologicalTrigger.FOMO, PsychologicalTrigger.INSTANT_GRATIFICATION],
                "success_motivation": 0.8,
                "financial_motivation": 0.95
            },
            "analyst": {
                "triggers": [PsychologicalTrigger.BALANCE_OPTIMIZATION, PsychologicalTrigger.FINANCIAL_PRESSURE],
                "success_motivation": 0.7,
                "financial_motivation": 0.8
            },
            "relationship_builder": {
                "triggers": [PsychologicalTrigger.ACHIEVEMENT, PsychologicalTrigger.BALANCE_OPTIMIZATION],
                "success_motivation": 0.6,
                "financial_motivation": 0.6
            }
        }

    async def analyze_portfolio_balance(self, user_id: str) -> PortfolioBalance:
        """Analyze user's current portfolio balance"""
        
        try:
            # Get current activities
            activities = await self._get_user_activities(user_id)
            
            # Calculate ratios
            total_activities = activities["total"]
            rfq_activities = activities["rfq"]
            tender_activities = activities["tender"]
            
            if total_activities > 0:
                current_rfq_ratio = (rfq_activities / total_activities) * 100
                current_tender_ratio = (tender_activities / total_activities) * 100
            else:
                current_rfq_ratio = 0
                current_tender_ratio = 0
            
            # Calculate deviation from target
            rfq_deviation = abs(current_rfq_ratio - self.target_ratios["rfq_ratio"])
            tender_deviation = abs(current_tender_ratio - self.target_ratios["tender_ratio"])
            deviation_score = max(rfq_deviation, tender_deviation)
            
            # Determine balance status
            balance_status = self._determine_balance_status(
                current_rfq_ratio, current_tender_ratio, deviation_score
            )
            
            # Calculate urgency level
            urgency_level = self._calculate_urgency_level(deviation_score, total_activities)
            
            # Calculate financial impact
            financial_impact = await self._calculate_financial_impact(
                user_id, current_rfq_ratio, current_tender_ratio
            )
            
            balance = PortfolioBalance(
                user_id=user_id,
                total_activities=total_activities,
                rfq_activities=rfq_activities,
                tender_activities=tender_activities,
                current_rfq_ratio=current_rfq_ratio,
                current_tender_ratio=current_tender_ratio,
                target_rfq_ratio=self.target_ratios["rfq_ratio"],
                target_tender_ratio=self.target_ratios["tender_ratio"],
                balance_status=balance_status,
                deviation_score=deviation_score,
                urgency_level=urgency_level,
                missed_earnings=financial_impact["missed_earnings"],
                potential_earnings=financial_impact["potential_earnings"]
            )
            
            # Save to database
            await self._save_portfolio_balance(balance)
            
            return balance
            
        except Exception as e:
            logger.error(f"Error analyzing portfolio balance: {e}")
            raise

    async def generate_rfq_suggestions(
        self,
        user_id: str,
        balance: PortfolioBalance,
        limit: int = 5
    ) -> List[RFQSuggestion]:
        """Generate AI-driven RFQ suggestions based on portfolio imbalance"""
        
        try:
            suggestions = []
            
            # Get user psychological profile
            user_profile = await self._get_user_psychological_profile(user_id)
            archetype = user_profile.get("archetype", "analyst")
            
            # Generate suggestions based on balance status
            if balance.balance_status == BalanceStatus.RFQ_DEFICIT:
                # User needs more RFQ activities
                suggestions.extend(await self._generate_rfq_deficit_suggestions(
                    user_id, balance, archetype, limit
                ))
                
            elif balance.balance_status == BalanceStatus.TENDER_DEFICIT:
                # User needs more tender activities
                suggestions.extend(await self._generate_tender_deficit_suggestions(
                    user_id, balance, archetype, limit
                ))
                
            elif balance.balance_status == BalanceStatus.CRITICAL_IMBALANCE:
                # Critical imbalance - aggressive suggestions
                suggestions.extend(await self._generate_critical_imbalance_suggestions(
                    user_id, balance, archetype, limit
                ))
            
            # Save suggestions to database
            for suggestion in suggestions:
                await self._save_rfq_suggestion(suggestion)
            
            return suggestions[:limit]
            
        except Exception as e:
            logger.error(f"Error generating RFQ suggestions: {e}")
            return []

    async def _generate_rfq_deficit_suggestions(
        self,
        user_id: str,
        balance: PortfolioBalance,
        archetype: str,
        limit: int
    ) -> List[RFQSuggestion]:
        """Generate suggestions for RFQ deficit (need more RFQ activities)"""
        
        suggestions = []
        
        try:
            # Get available government RFQs
            gov_rfqs = await self._get_available_government_rfqs(user_id, limit=3)
            
            # Suggest bidding on government RFQs
            for rfq in gov_rfqs:
                suggestion = RFQSuggestion(
                    suggestion_id=f"gov-rfq-{rfq['id']}-{datetime.now().timestamp()}",
                    suggestion_type="bid_government_rfq",
                    opportunity_id=rfq["id"],
                    opportunity_type="government_rfq",
                    ai_confidence=88.0,  # High confidence for government RFQs
                    success_probability=88.0,  # Higher than tenders
                    potential_earnings=rfq.get("estimated_value", 0) * 0.15,  # 15% margin
                    time_to_complete="2-3 days",
                    urgency_level=balance.urgency_level,
                    psychological_trigger=self._select_psychological_trigger(archetype, "rfq_deficit"),
                    trigger_message=self._generate_trigger_message(
                        "bid_government_rfq", rfq, archetype, balance
                    ),
                    reasoning_factors=[
                        f"88% success rate for government RFQs",
                        f"Improves your RFQ ratio from {balance.current_rfq_ratio:.1f}% to target 60%",
                        f"Quick turnaround - complete in 2-3 days",
                        f"Low competition - government RFQs have fewer bidders"
                    ]
                )
                suggestions.append(suggestion)
            
            # Suggest creating bidder RFQ
            create_rfq_suggestion = RFQSuggestion(
                suggestion_id=f"create-rfq-{datetime.now().timestamp()}",
                suggestion_type="create_bidder_rfq",
                opportunity_id=None,
                opportunity_type="bidder_rfq",
                ai_confidence=94.0,  # Highest confidence for RFQ creation
                success_probability=92.0,  # Highest success rate
                potential_earnings=150000,  # Average RFQ value
                time_to_complete="90 seconds",
                urgency_level=balance.urgency_level,
                psychological_trigger=PsychologicalTrigger.INSTANT_GRATIFICATION,
                trigger_message=self._generate_create_rfq_trigger_message(archetype, balance),
                reasoning_factors=[
                    "92% success rate - highest of all activities",
                    "Create in 90 seconds, get quotes in 2 hours",
                    f"Fix your portfolio balance ({balance.current_rfq_ratio:.1f}% → 60%)",
                    "Instant supplier interest - average 15 responses"
                ]
            )
            suggestions.append(create_rfq_suggestion)
            
        except Exception as e:
            logger.error(f"Error generating RFQ deficit suggestions: {e}")
            
        return suggestions

    async def _generate_tender_deficit_suggestions(
        self,
        user_id: str,
        balance: PortfolioBalance,
        archetype: str,
        limit: int
    ) -> List[RFQSuggestion]:
        """Generate suggestions for tender deficit (need more tender activities)"""
        
        suggestions = []
        
        try:
            # Get available tenders
            tenders = await self._get_available_tenders(user_id, limit=3)
            
            for tender in tenders:
                suggestion = RFQSuggestion(
                    suggestion_id=f"tender-{tender['id']}-{datetime.now().timestamp()}",
                    suggestion_type="bid_tender",
                    opportunity_id=tender["id"],
                    opportunity_type="tender",
                    ai_confidence=75.0,
                    success_probability=75.0,
                    potential_earnings=tender.get("estimated_value", 0) * 0.15,
                    time_to_complete="3-4 weeks",
                    urgency_level=balance.urgency_level,
                    psychological_trigger=self._select_psychological_trigger(archetype, "tender_deficit"),
                    trigger_message=self._generate_trigger_message(
                        "bid_tender", tender, archetype, balance
                    ),
                    reasoning_factors=[
                        f"75% success rate for tenders in your category",
                        f"Improves your tender ratio from {balance.current_tender_ratio:.1f}% to target 40%",
                        f"High-value opportunity - R{tender.get('estimated_value', 0):,.0f}",
                        "Matches your expertise and location preferences"
                    ]
                )
                suggestions.append(suggestion)
                
        except Exception as e:
            logger.error(f"Error generating tender deficit suggestions: {e}")
            
        return suggestions

    async def _generate_critical_imbalance_suggestions(
        self,
        user_id: str,
        balance: PortfolioBalance,
        archetype: str,
        limit: int
    ) -> List[RFQSuggestion]:
        """Generate aggressive suggestions for critical imbalance"""
        
        suggestions = []
        
        # Combine both RFQ and tender suggestions with high urgency
        rfq_suggestions = await self._generate_rfq_deficit_suggestions(
            user_id, balance, archetype, limit // 2
        )
        
        tender_suggestions = await self._generate_tender_deficit_suggestions(
            user_id, balance, archetype, limit // 2
        )
        
        # Increase urgency and psychological pressure
        for suggestion in rfq_suggestions + tender_suggestions:
            suggestion.urgency_level = UrgencyLevel.CRITICAL
            suggestion.psychological_trigger = PsychologicalTrigger.FINANCIAL_PRESSURE
            suggestion.trigger_message = f"🚨 CRITICAL: {suggestion.trigger_message}"
            
        suggestions.extend(rfq_suggestions + tender_suggestions)
        
        return suggestions

    def _select_psychological_trigger(self, archetype: str, deficit_type: str) -> PsychologicalTrigger:
        """Select appropriate psychological trigger based on user archetype"""
        
        profile = self.psychological_profiles.get(archetype, self.psychological_profiles["analyst"])
        
        if deficit_type == "rfq_deficit":
            # For RFQ deficit, prefer instant gratification and FOMO
            if archetype == "hunter":
                return PsychologicalTrigger.INSTANT_GRATIFICATION
            elif archetype == "achiever":
                return PsychologicalTrigger.ACHIEVEMENT
            else:
                return PsychologicalTrigger.BALANCE_OPTIMIZATION
        else:
            # For tender deficit, prefer competition and financial pressure
            if archetype == "achiever":
                return PsychologicalTrigger.COMPETITION
            elif archetype == "hunter":
                return PsychologicalTrigger.FINANCIAL_PRESSURE
            else:
                return PsychologicalTrigger.BALANCE_OPTIMIZATION

    def _generate_trigger_message(
        self,
        suggestion_type: str,
        opportunity: Dict,
        archetype: str,
        balance: PortfolioBalance
    ) -> str:
        """Generate psychological trigger message"""
        
        messages = {
            "bid_government_rfq": {
                "hunter": f"🔥 QUICK WIN: {opportunity.get('title', 'Government RFQ')} - 88% success rate, complete in 2 days!",
                "achiever": f"🏆 BEAT THE COMPETITION: Only 3 bidders on {opportunity.get('title', 'this RFQ')} - claim your victory!",
                "analyst": f"📊 OPTIMIZE BALANCE: Bid on {opportunity.get('title', 'this RFQ')} to reach optimal 60% RFQ ratio",
                "relationship_builder": f"🤝 PERFECT MATCH: {opportunity.get('title', 'Government RFQ')} aligns with your expertise"
            },
            "bid_tender": {
                "hunter": f"💰 HIGH VALUE: {opportunity.get('title', 'Tender')} worth R{opportunity.get('estimated_value', 0):,.0f} - act fast!",
                "achiever": f"🎯 CHALLENGE ACCEPTED: {opportunity.get('title', 'Premium tender')} - prove your expertise!",
                "analyst": f"📈 BALANCE CORRECTION: Bid on {opportunity.get('title', 'this tender')} to reach 40% tender ratio",
                "relationship_builder": f"🌟 RELATIONSHIP OPPORTUNITY: {opportunity.get('title', 'Tender')} with trusted organization"
            }
        }
        
        return messages.get(suggestion_type, {}).get(archetype, f"Opportunity: {opportunity.get('title', 'Unknown')}")

    def _generate_create_rfq_trigger_message(self, archetype: str, balance: PortfolioBalance) -> str:
        """Generate trigger message for RFQ creation"""
        
        messages = {
            "hunter": f"⚡ 90-SECOND WIN: Create RFQ now, get quotes in 2 hours - 92% success rate!",
            "achiever": f"🚀 SPEED DEMON: Beat your 90-second RFQ creation record - unlock achievement!",
            "analyst": f"🎯 BALANCE FIX: Create RFQ to optimize your {balance.current_rfq_ratio:.1f}% → 60% ratio",
            "relationship_builder": f"🤝 SUPPLIER NETWORK: Create RFQ to engage your supplier relationships"
        }
        
        return messages.get(archetype, "Create RFQ to improve your portfolio balance")

    async def _get_user_activities(self, user_id: str) -> Dict:
        """Get user's current activity counts"""
        
        try:
            # Count bidder RFQs
            bidder_rfqs = supabase.table("bidder_rfqs").select("id").eq("bidder_id", user_id).execute()
            
            # Count government RFQ responses
            gov_rfq_responses = supabase.table("government_rfq_responses").select("id").eq("bidder_id", user_id).execute()
            
            # Count tender bids
            tender_bids = supabase.table("tender_bids").select("id").eq("bidder_id", user_id).execute()
            
            rfq_activities = len(bidder_rfqs.data or []) + len(gov_rfq_responses.data or [])
            tender_activities = len(tender_bids.data or [])
            total_activities = rfq_activities + tender_activities
            
            return {
                "total": total_activities,
                "rfq": rfq_activities,
                "tender": tender_activities
            }
            
        except Exception as e:
            logger.error(f"Error getting user activities: {e}")
            return {"total": 0, "rfq": 0, "tender": 0}

    def _determine_balance_status(
        self,
        current_rfq_ratio: float,
        current_tender_ratio: float,
        deviation_score: float
    ) -> BalanceStatus:
        """Determine portfolio balance status"""
        
        if deviation_score > 30:
            return BalanceStatus.CRITICAL_IMBALANCE
        elif current_rfq_ratio < 50:  # Target is 60%
            return BalanceStatus.RFQ_DEFICIT
        elif current_tender_ratio < 30:  # Target is 40%
            return BalanceStatus.TENDER_DEFICIT
        else:
            return BalanceStatus.BALANCED

    def _calculate_urgency_level(self, deviation_score: float, total_activities: int) -> UrgencyLevel:
        """Calculate urgency level for balance correction"""
        
        if deviation_score > 30 or total_activities < 3:
            return UrgencyLevel.CRITICAL
        elif deviation_score > 20:
            return UrgencyLevel.HIGH
        elif deviation_score > 10:
            return UrgencyLevel.MEDIUM
        else:
            return UrgencyLevel.LOW

    async def _calculate_financial_impact(
        self,
        user_id: str,
        current_rfq_ratio: float,
        current_tender_ratio: float
    ) -> Dict:
        """Calculate financial impact of portfolio imbalance"""
        
        # Simplified calculation - could be enhanced with real data
        optimal_earnings = 5000000  # R5M target
        
        # Calculate efficiency loss due to imbalance
        rfq_efficiency = min(1.0, current_rfq_ratio / 60.0)
        tender_efficiency = min(1.0, current_tender_ratio / 40.0)
        overall_efficiency = (rfq_efficiency + tender_efficiency) / 2
        
        missed_earnings = optimal_earnings * (1 - overall_efficiency)
        potential_earnings = optimal_earnings * overall_efficiency
        
        return {
            "missed_earnings": missed_earnings,
            "potential_earnings": potential_earnings
        }

    async def _save_portfolio_balance(self, balance: PortfolioBalance):
        """Save portfolio balance to database"""
        
        try:
            balance_record = {
                "user_id": balance.user_id,
                "total_activities": balance.total_activities,
                "rfq_activities": balance.rfq_activities,
                "tender_activities": balance.tender_activities,
                "current_rfq_ratio": balance.current_rfq_ratio,
                "current_tender_ratio": balance.current_tender_ratio,
                "target_rfq_ratio": balance.target_rfq_ratio,
                "target_tender_ratio": balance.target_tender_ratio,
                "balance_status": balance.balance_status.value,
                "deviation_score": balance.deviation_score,
                "urgency_level": balance.urgency_level.value,
                "missed_earnings": balance.missed_earnings,
                "potential_earnings": balance.potential_earnings,
                "last_calculated_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }
            
            # Upsert (insert or update)
            result = supabase.table("portfolio_balance_tracking").upsert(
                balance_record, on_conflict="user_id"
            ).execute()
            
            if result.data:
                logger.info(f"Saved portfolio balance for user {balance.user_id}")
            
        except Exception as e:
            logger.error(f"Error saving portfolio balance: {e}")

    async def _save_rfq_suggestion(self, suggestion: RFQSuggestion):
        """Save RFQ suggestion to database"""
        
        try:
            suggestion_record = {
                "user_id": suggestion.user_id if hasattr(suggestion, 'user_id') else "",
                "suggestion_type": suggestion.suggestion_type,
                "opportunity_id": suggestion.opportunity_id,
                "opportunity_type": suggestion.opportunity_type,
                "ai_confidence": suggestion.ai_confidence,
                "success_probability": suggestion.success_probability,
                "potential_earnings": suggestion.potential_earnings,
                "time_to_complete": suggestion.time_to_complete,
                "urgency_level": suggestion.urgency_level.value,
                "psychological_trigger": suggestion.psychological_trigger.value,
                "trigger_message": suggestion.trigger_message,
                "reasoning_factors": suggestion.reasoning_factors,
                "suggestion_status": "pending",
                "created_at": datetime.now().isoformat(),
                "expires_at": (datetime.now() + timedelta(hours=24)).isoformat()
            }
            
            result = supabase.table("rfq_suggestions").insert(suggestion_record).execute()
            
            if result.data:
                logger.info(f"Saved RFQ suggestion: {suggestion.suggestion_id}")
                
        except Exception as e:
            logger.error(f"Error saving RFQ suggestion: {e}")

    async def _get_user_psychological_profile(self, user_id: str) -> Dict:
        """Get user's psychological profile"""
        
        try:
            result = supabase.table("users").select("psychological_profile").eq("id", user_id).execute()
            
            if result.data and result.data[0].get("psychological_profile"):
                return result.data[0]["psychological_profile"]
            else:
                return {"archetype": "analyst"}  # Default
                
        except Exception as e:
            logger.error(f"Error getting psychological profile: {e}")
            return {"archetype": "analyst"}

    async def _get_available_government_rfqs(self, user_id: str, limit: int = 5) -> List[Dict]:
        """Get available government RFQs for suggestions"""
        
        try:
            result = supabase.table("government_rfqs").select("*").eq("status", "active").limit(limit).execute()
            return result.data or []
            
        except Exception as e:
            logger.error(f"Error getting government RFQs: {e}")
            return []

    async def _get_available_tenders(self, user_id: str, limit: int = 5) -> List[Dict]:
        """Get available tenders for suggestions"""
        
        try:
            result = supabase.table("tenders").select("*").eq("status", "active").limit(limit).execute()
            return result.data or []
            
        except Exception as e:
            logger.error(f"Error getting tenders: {e}")
            return []

# Service instance
portfolio_balance_ai = PortfolioBalanceAI()
