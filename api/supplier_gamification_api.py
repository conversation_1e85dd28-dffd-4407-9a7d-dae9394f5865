"""
Supplier Gamification API
Leaderboards, badges, achievements, and engagement features for supplier retention
"""

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Union
import uuid
import json
import logging
from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
import asyncio

# Supabase client
from supabase import create_client, Client
import os

# Initialize Supabase
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_ANON_KEY")
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# Logging setup
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="BidBeez Supplier Gamification API",
    description="Leaderboards, badges, and engagement features",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# =====================================================
# PYDANTIC MODELS
# =====================================================

class SupplierRanking(BaseModel):
    supplier_id: str
    supplier_name: str
    rank: int
    score: float
    category_rank: Optional[int] = None
    period: str  # monthly, quarterly, yearly, all_time
    points: int
    badges_count: int
    streak_days: int
    level: str
    next_level_points: int
    achievements: List[str]

class Badge(BaseModel):
    badge_id: str
    name: str
    description: str
    icon: str
    tier: str  # bronze, silver, gold, platinum, diamond
    category: str
    requirements: Dict
    points_value: int
    rarity: str  # common, uncommon, rare, epic, legendary

class Achievement(BaseModel):
    achievement_id: str
    supplier_id: str
    badge_id: str
    achieved_at: datetime
    points_earned: int
    milestone_data: Dict

class LeaderboardEntry(BaseModel):
    rank: int
    supplier_id: str
    supplier_name: str
    company_name: str
    score: float
    points: int
    badges: List[Badge]
    streak: int
    level: str
    bee_level: int
    location: str
    change_from_last_period: int  # +/- rank change

class GamificationStats(BaseModel):
    total_points: int
    current_level: str
    next_level: str
    points_to_next_level: int
    total_badges: int
    current_streak: int
    longest_streak: int
    rank_overall: int
    rank_category: int
    achievements_unlocked: int
    completion_percentage: float

# =====================================================
# GAMIFICATION ENGINE
# =====================================================

class GamificationEngine:
    def __init__(self):
        self.levels = {
            "Rookie": {"min_points": 0, "max_points": 999, "color": "#8B5CF6"},
            "Explorer": {"min_points": 1000, "max_points": 2499, "color": "#06B6D4"},
            "Achiever": {"min_points": 2500, "max_points": 4999, "color": "#10B981"},
            "Expert": {"min_points": 5000, "max_points": 9999, "color": "#F59E0B"},
            "Master": {"min_points": 10000, "max_points": 19999, "color": "#EF4444"},
            "Legend": {"min_points": 20000, "max_points": 49999, "color": "#8B5CF6"},
            "Champion": {"min_points": 50000, "max_points": 99999, "color": "#DC2626"},
            "Elite": {"min_points": 100000, "max_points": float('inf'), "color": "#FFD700"}
        }
        
        self.point_activities = {
            "quote_submitted": 50,
            "quote_awarded": 500,
            "contract_completed": 1000,
            "perfect_delivery": 200,
            "early_delivery": 100,
            "compliance_maintained": 150,
            "bee_level_improved": 300,
            "certificate_renewed": 100,
            "profile_completed": 75,
            "document_uploaded": 25,
            "review_received": 50,
            "referral_successful": 250,
            "training_completed": 100,
            "streak_milestone": 200
        }
        
        self.badges = [
            {
                "name": "First Quote",
                "description": "Submit your first quote",
                "icon": "🎯",
                "tier": "bronze",
                "category": "milestone",
                "requirements": {"quotes_submitted": 1},
                "points_value": 100,
                "rarity": "common"
            },
            {
                "name": "Quote Master",
                "description": "Submit 100 quotes",
                "icon": "📋",
                "tier": "gold",
                "category": "milestone",
                "requirements": {"quotes_submitted": 100},
                "points_value": 1000,
                "rarity": "rare"
            },
            {
                "name": "Winner",
                "description": "Win your first tender",
                "icon": "🏆",
                "tier": "silver",
                "category": "achievement",
                "requirements": {"quotes_awarded": 1},
                "points_value": 500,
                "rarity": "uncommon"
            },
            {
                "name": "Champion",
                "description": "Win 50 tenders",
                "icon": "👑",
                "tier": "platinum",
                "category": "achievement",
                "requirements": {"quotes_awarded": 50},
                "points_value": 5000,
                "rarity": "epic"
            },
            {
                "name": "B-BBEE Elite",
                "description": "Achieve Level 1 B-BBEE status",
                "icon": "⭐",
                "tier": "diamond",
                "category": "compliance",
                "requirements": {"bee_level": 1},
                "points_value": 2000,
                "rarity": "legendary"
            },
            {
                "name": "Streak Warrior",
                "description": "Maintain 30-day activity streak",
                "icon": "🔥",
                "tier": "gold",
                "category": "engagement",
                "requirements": {"streak_days": 30},
                "points_value": 1500,
                "rarity": "rare"
            },
            {
                "name": "Perfect Performer",
                "description": "Complete 10 contracts with perfect ratings",
                "icon": "💎",
                "tier": "platinum",
                "category": "performance",
                "requirements": {"perfect_contracts": 10},
                "points_value": 3000,
                "rarity": "epic"
            },
            {
                "name": "Speed Demon",
                "description": "Complete 5 early deliveries",
                "icon": "⚡",
                "tier": "silver",
                "category": "performance",
                "requirements": {"early_deliveries": 5},
                "points_value": 750,
                "rarity": "uncommon"
            },
            {
                "name": "Compliance King",
                "description": "Maintain compliance for 365 days",
                "icon": "🛡️",
                "tier": "diamond",
                "category": "compliance",
                "requirements": {"compliance_days": 365},
                "points_value": 4000,
                "rarity": "legendary"
            },
            {
                "name": "Mentor",
                "description": "Refer 10 successful suppliers",
                "icon": "🤝",
                "tier": "gold",
                "category": "community",
                "requirements": {"successful_referrals": 10},
                "points_value": 2500,
                "rarity": "rare"
            }
        ]
    
    async def calculate_supplier_score(self, supplier_id: str) -> float:
        """Calculate comprehensive supplier score for leaderboard"""
        try:
            # Get supplier data
            supplier_result = supabase.table("users").select("""
                id,
                supplier_blockchain_profiles(*),
                supplier_compliance_record(*)
            """).eq("id", supplier_id).execute()
            
            if not supplier_result.data:
                return 0.0
            
            supplier = supplier_result.data[0]
            profile = supplier.get("supplier_blockchain_profiles", [{}])[0]
            compliance = supplier.get("supplier_compliance_record", [{}])[0]
            
            # Get quotes data
            quotes_result = supabase.table("supplier_quotes").select("*").eq("supplier_id", supplier_id).execute()
            quotes = quotes_result.data
            
            # Get gamification data
            gamification_result = supabase.table("gamification").select("*").eq("supplier_id", supplier_id).execute()
            gamification = gamification_result.data[0] if gamification_result.data else {}
            
            # Calculate score components
            base_score = 0.0
            
            # Performance score (40%)
            performance_score = profile.get("performance_score", 0) / 100 * 0.4
            base_score += performance_score
            
            # B-BBEE bonus (20%)
            bee_level = compliance.get("bbbee_level", 8)
            bee_score = max(0, (9 - bee_level) / 8) * 0.2
            base_score += bee_score
            
            # Quote success rate (20%)
            if quotes:
                awarded_quotes = len([q for q in quotes if q.get("status") == "awarded"])
                success_rate = awarded_quotes / len(quotes) * 0.2
                base_score += success_rate
            
            # Activity points (10%)
            points = gamification.get("points", 0)
            points_score = min(points / 10000, 1.0) * 0.1  # Cap at 10k points
            base_score += points_score
            
            # Streak bonus (5%)
            streak = gamification.get("current_streak", 0)
            streak_score = min(streak / 100, 1.0) * 0.05  # Cap at 100 days
            base_score += streak_score
            
            # Verification bonus (5%)
            if profile.get("is_verified"):
                base_score += 0.05
            
            return min(base_score * 100, 100.0)  # Convert to 0-100 scale
            
        except Exception as e:
            logger.error(f"Error calculating supplier score: {e}")
            return 0.0
    
    async def award_points(self, supplier_id: str, activity: str, metadata: Dict = None) -> int:
        """Award points for supplier activity"""
        try:
            points = self.point_activities.get(activity, 0)
            
            if points == 0:
                return 0
            
            # Get current gamification record
            result = supabase.table("gamification").select("*").eq("supplier_id", supplier_id).execute()
            
            if result.data:
                # Update existing record
                current = result.data[0]
                new_points = current.get("points", 0) + points
                new_level = self.get_level_from_points(new_points)
                
                update_data = {
                    "points": new_points,
                    "level": new_level,
                    "last_activity": datetime.now().isoformat(),
                    "activities": current.get("activities", []) + [{
                        "activity": activity,
                        "points": points,
                        "timestamp": datetime.now().isoformat(),
                        "metadata": metadata or {}
                    }]
                }
                
                supabase.table("gamification").update(update_data).eq("supplier_id", supplier_id).execute()
            else:
                # Create new record
                new_level = self.get_level_from_points(points)
                
                gamification_record = {
                    "supplier_id": supplier_id,
                    "points": points,
                    "level": new_level,
                    "current_streak": 1,
                    "longest_streak": 1,
                    "last_activity": datetime.now().isoformat(),
                    "activities": [{
                        "activity": activity,
                        "points": points,
                        "timestamp": datetime.now().isoformat(),
                        "metadata": metadata or {}
                    }]
                }
                
                supabase.table("gamification").insert(gamification_record).execute()
            
            # Check for badge achievements
            await self.check_badge_achievements(supplier_id)
            
            return points
            
        except Exception as e:
            logger.error(f"Error awarding points: {e}")
            return 0
    
    def get_level_from_points(self, points: int) -> str:
        """Get level name from points"""
        for level, data in self.levels.items():
            if data["min_points"] <= points <= data["max_points"]:
                return level
        return "Rookie"
    
    async def check_badge_achievements(self, supplier_id: str):
        """Check and award badges based on supplier activity"""
        try:
            # Get supplier stats
            stats = await self.get_supplier_stats(supplier_id)
            
            # Get existing badges
            existing_badges = supabase.table("supplier_badge").select("*").eq("supplier_id", supplier_id).execute()
            existing_badge_names = [b["name"] for b in existing_badges.data]
            
            # Check each badge
            for badge_config in self.badges:
                if badge_config["name"] in existing_badge_names:
                    continue  # Already has this badge
                
                # Check requirements
                requirements_met = True
                for req_key, req_value in badge_config["requirements"].items():
                    if stats.get(req_key, 0) < req_value:
                        requirements_met = False
                        break
                
                if requirements_met:
                    # Award badge
                    badge_record = {
                        "supplier_id": supplier_id,
                        "name": badge_config["name"],
                        "description": badge_config["description"],
                        "icon": badge_config["icon"],
                        "tier": badge_config["tier"],
                        "achieved_at": datetime.now().isoformat(),
                        "points_earned": badge_config["points_value"]
                    }
                    
                    supabase.table("supplier_badge").insert(badge_record).execute()
                    
                    # Award points for badge
                    await self.award_points(supplier_id, "badge_earned", {
                        "badge_name": badge_config["name"],
                        "points": badge_config["points_value"]
                    })
                    
                    logger.info(f"Badge '{badge_config['name']}' awarded to supplier {supplier_id}")
                    
        except Exception as e:
            logger.error(f"Error checking badge achievements: {e}")
    
    async def get_supplier_stats(self, supplier_id: str) -> Dict:
        """Get supplier statistics for badge checking"""
        try:
            stats = {}
            
            # Quote statistics
            quotes_result = supabase.table("supplier_quotes").select("*").eq("supplier_id", supplier_id).execute()
            quotes = quotes_result.data
            
            stats["quotes_submitted"] = len(quotes)
            stats["quotes_awarded"] = len([q for q in quotes if q.get("status") == "awarded"])
            
            # B-BBEE level
            compliance_result = supabase.table("supplier_compliance_record").select("*").eq("supplier_id", supplier_id).execute()
            if compliance_result.data:
                stats["bee_level"] = compliance_result.data[0].get("bbbee_level", 8)
            
            # Gamification data
            gamification_result = supabase.table("gamification").select("*").eq("supplier_id", supplier_id).execute()
            if gamification_result.data:
                gamification = gamification_result.data[0]
                stats["streak_days"] = gamification.get("current_streak", 0)
                stats["points"] = gamification.get("points", 0)
            
            # Additional stats would be calculated from other tables
            stats["perfect_contracts"] = 0  # Would calculate from contract completion data
            stats["early_deliveries"] = 0  # Would calculate from delivery data
            stats["compliance_days"] = 0  # Would calculate from compliance history
            stats["successful_referrals"] = 0  # Would calculate from referral data
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting supplier stats: {e}")
            return {}

# =====================================================
# API ENDPOINTS
# =====================================================

gamification_engine = GamificationEngine()

@app.get("/leaderboard")
async def get_leaderboard(
    period: str = "monthly",
    category: Optional[str] = None,
    limit: int = 50,
    offset: int = 0
):
    """Get supplier leaderboard"""
    try:
        # Get all suppliers with their data
        suppliers_result = supabase.table("users").select("""
            id, email,
            supplier_blockchain_profiles(*),
            supplier_compliance_record(*),
            gamification(*)
        """).not_.is_("supplier_blockchain_profiles", "null").execute()
        
        leaderboard_entries = []
        
        for supplier in suppliers_result.data:
            supplier_id = supplier["id"]
            profile = supplier.get("supplier_blockchain_profiles", [{}])[0]
            compliance = supplier.get("supplier_compliance_record", [{}])[0]
            gamification = supplier.get("gamification", [{}])[0]
            
            # Calculate score
            score = await gamification_engine.calculate_supplier_score(supplier_id)
            
            # Get badges
            badges_result = supabase.table("supplier_badge").select("*").eq("supplier_id", supplier_id).execute()
            badges = badges_result.data

            # Create badge objects with proper structure
            badge_objects = []
            for badge in badges:
                badge_obj = Badge(
                    badge_id=badge.get("id", ""),
                    name=badge.get("name", ""),
                    description=badge.get("description", ""),
                    icon=badge.get("icon", ""),
                    tier=badge.get("tier", "bronze"),
                    category="achievement",
                    requirements={},
                    points_value=badge.get("points_earned", 0),
                    rarity="common"
                )
                badge_objects.append(badge_obj)

            entry = LeaderboardEntry(
                rank=0,  # Will be set after sorting
                supplier_id=supplier_id,
                supplier_name=supplier["email"],
                company_name=profile.get("company_name", ""),
                score=score,
                points=gamification.get("points", 0),
                badges=badge_objects,
                streak=gamification.get("current_streak", 0),
                level=gamification.get("level", "Rookie"),
                bee_level=compliance.get("bbbee_level", 8),
                location=profile.get("location", {}).get("province", "") if isinstance(profile.get("location"), dict) else "",
                change_from_last_period=0  # Would calculate from historical data
            )
            
            leaderboard_entries.append(entry)
        
        # Sort by score
        leaderboard_entries.sort(key=lambda x: x.score, reverse=True)
        
        # Assign ranks
        for i, entry in enumerate(leaderboard_entries):
            entry.rank = i + 1
        
        # Apply pagination
        paginated_entries = leaderboard_entries[offset:offset + limit]
        
        return {
            "leaderboard": [entry.dict() for entry in paginated_entries],
            "total_suppliers": len(leaderboard_entries),
            "period": period,
            "category": category
        }
        
    except Exception as e:
        logger.error(f"Error getting leaderboard: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/supplier/{supplier_id}/gamification")
async def get_supplier_gamification(supplier_id: str):
    """Get gamification stats for a supplier"""
    try:
        # Get gamification record
        result = supabase.table("gamification").select("*").eq("supplier_id", supplier_id).execute()
        
        if not result.data:
            return GamificationStats(
                total_points=0,
                current_level="Rookie",
                next_level="Explorer",
                points_to_next_level=1000,
                total_badges=0,
                current_streak=0,
                longest_streak=0,
                rank_overall=0,
                rank_category=0,
                achievements_unlocked=0,
                completion_percentage=0.0
            )
        
        gamification = result.data[0]
        current_points = gamification.get("points", 0)
        current_level = gamification.get("level", "Rookie")
        
        # Calculate next level
        next_level = None
        points_to_next = 0
        
        level_names = list(gamification_engine.levels.keys())
        current_index = level_names.index(current_level) if current_level in level_names else 0
        
        if current_index < len(level_names) - 1:
            next_level = level_names[current_index + 1]
            points_to_next = gamification_engine.levels[next_level]["min_points"] - current_points
        else:
            next_level = current_level
            points_to_next = 0
        
        # Get badges
        badges_result = supabase.table("supplier_badge").select("*").eq("supplier_id", supplier_id).execute()
        total_badges = len(badges_result.data)
        
        # Calculate rank (simplified)
        score = await gamification_engine.calculate_supplier_score(supplier_id)
        # Would calculate actual rank from leaderboard
        
        return GamificationStats(
            total_points=current_points,
            current_level=current_level,
            next_level=next_level,
            points_to_next_level=max(0, points_to_next),
            total_badges=total_badges,
            current_streak=gamification.get("current_streak", 0),
            longest_streak=gamification.get("longest_streak", 0),
            rank_overall=1,  # Would calculate from leaderboard
            rank_category=1,  # Would calculate from category leaderboard
            achievements_unlocked=total_badges,
            completion_percentage=min(100.0, (total_badges / len(gamification_engine.badges)) * 100)
        )
        
    except Exception as e:
        logger.error(f"Error getting supplier gamification: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/supplier/{supplier_id}/award-points")
async def award_points_to_supplier(
    supplier_id: str,
    activity: str,
    metadata: Optional[Dict] = None
):
    """Award points to supplier for activity"""
    points_awarded = await gamification_engine.award_points(supplier_id, activity, metadata)
    
    return {
        "points_awarded": points_awarded,
        "activity": activity,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/supplier/{supplier_id}/badges")
async def get_supplier_badges(supplier_id: str):
    """Get all badges for a supplier"""
    try:
        result = supabase.table("supplier_badge").select("*").eq("supplier_id", supplier_id).order("achieved_at", desc=True).execute()
        
        return {"badges": result.data}
        
    except Exception as e:
        logger.error(f"Error getting supplier badges: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/badges/available")
async def get_available_badges():
    """Get all available badges and their requirements"""
    return {"badges": gamification_engine.badges}

@app.get("/levels")
async def get_levels():
    """Get all available levels and their requirements"""
    return {"levels": gamification_engine.levels}

@app.get("/analytics/gamification")
async def get_gamification_analytics():
    """Get gamification analytics"""
    try:
        # Get all gamification records
        result = supabase.table("gamification").select("*").execute()
        records = result.data
        
        if not records:
            return {
                "total_active_suppliers": 0,
                "total_points_awarded": 0,
                "average_level": "Rookie",
                "level_distribution": {},
                "badge_distribution": {},
                "engagement_metrics": {}
            }
        
        total_points = sum(r.get("points", 0) for r in records)
        level_distribution = {}
        
        for record in records:
            level = record.get("level", "Rookie")
            level_distribution[level] = level_distribution.get(level, 0) + 1
        
        # Get badge statistics
        badges_result = supabase.table("supplier_badge").select("*").execute()
        badge_distribution = {}
        
        for badge in badges_result.data:
            name = badge.get("name", "Unknown")
            badge_distribution[name] = badge_distribution.get(name, 0) + 1
        
        return {
            "total_active_suppliers": len(records),
            "total_points_awarded": total_points,
            "average_points": total_points / len(records) if records else 0,
            "level_distribution": level_distribution,
            "badge_distribution": badge_distribution,
            "engagement_metrics": {
                "active_streaks": len([r for r in records if r.get("current_streak", 0) > 0]),
                "total_badges_awarded": len(badges_result.data)
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting gamification analytics: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8004)
