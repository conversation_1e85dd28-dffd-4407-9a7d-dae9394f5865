"""
Bidder Profile Configuration API
Handles bidder profile settings, opportunity preferences, and notification configuration
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from supabase import create_client, Client
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FastAPI app
app = FastAPI(title="Bidder Profile API", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Supabase client
supabase: Client = create_client(
    os.getenv("SUPABASE_URL", ""),
    os.getenv("SUPABASE_SERVICE_KEY", "")
)

# Request/Response Models
class PersonalInfo(BaseModel):
    fullName: str
    email: str
    phone: str
    company: str
    registrationNumber: str
    bbbeeLevel: int
    taxNumber: str

class BusinessPreferences(BaseModel):
    preferredCategories: List[str]
    preferredProvinces: List[str]
    minimumValue: int
    maximumValue: int
    riskTolerance: str
    targetTurnover: int
    currentCapacity: int

class OpportunityConfig(BaseModel):
    tenderNotifications: bool
    governmentRfqNotifications: bool
    bidderRfqNotifications: bool
    urgentOpportunities: bool
    portfolioBalanceAlerts: bool
    competitionAlerts: bool
    deadlineReminders: int

class NotificationPreferences(BaseModel):
    email: bool
    sms: bool
    whatsapp: bool
    push: bool
    frequency: str
    quietHours: Dict

class PortfolioSettings(BaseModel):
    targetRfqRatio: int
    targetTenderRatio: int
    autoOptimization: bool
    balanceThreshold: int
    psychologicalProfile: str

class BidderProfileRequest(BaseModel):
    personalInfo: PersonalInfo
    businessPreferences: BusinessPreferences
    opportunityConfig: OpportunityConfig
    notificationPreferences: NotificationPreferences
    portfolioSettings: PortfolioSettings

class BidderProfileResponse(BaseModel):
    profile: Dict
    portfolioImpact: Dict
    recommendedSettings: Dict

# Dependency to get current user
async def get_current_user():
    # Placeholder - implement actual authentication
    return {"id": "user-123", "email": "<EMAIL>"}

# =====================================================
# PROFILE MANAGEMENT ENDPOINTS
# =====================================================

@app.get("/api/profile", response_model=BidderProfileResponse)
async def get_bidder_profile(user: Dict = Depends(get_current_user)):
    """Get bidder profile configuration"""
    
    try:
        user_id = user["id"]
        
        # Get profile from database
        result = supabase.table("bidder_profiles").select("*").eq("user_id", user_id).execute()
        
        if result.data:
            profile = result.data[0]
        else:
            # Return default profile
            profile = await _create_default_profile(user_id)
        
        # Calculate portfolio impact
        portfolio_impact = await _calculate_portfolio_impact(user_id, profile)
        
        # Generate recommended settings
        recommended_settings = await _generate_recommended_settings(user_id, profile)
        
        return BidderProfileResponse(
            profile=profile,
            portfolioImpact=portfolio_impact,
            recommendedSettings=recommended_settings
        )
        
    except Exception as e:
        logger.error(f"Error getting bidder profile: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/profile/update")
async def update_bidder_profile(
    profile_data: BidderProfileRequest,
    user: Dict = Depends(get_current_user)
):
    """Update bidder profile configuration"""
    
    try:
        user_id = user["id"]
        
        # Prepare profile record
        profile_record = {
            "user_id": user_id,
            "personal_info": profile_data.personalInfo.dict(),
            "business_preferences": profile_data.businessPreferences.dict(),
            "opportunity_config": profile_data.opportunityConfig.dict(),
            "notification_preferences": profile_data.notificationPreferences.dict(),
            "portfolio_settings": profile_data.portfolioSettings.dict(),
            "updated_at": datetime.now().isoformat()
        }
        
        # Upsert profile
        result = supabase.table("bidder_profiles").upsert(
            profile_record, on_conflict="user_id"
        ).execute()
        
        if not result.data:
            raise HTTPException(status_code=500, detail="Failed to update profile")
        
        # Update portfolio balance tracking with new targets
        await _update_portfolio_targets(user_id, profile_data.portfolioSettings)
        
        # Update notification subscriptions
        await _update_notification_subscriptions(user_id, profile_data)
        
        # Trigger AI re-analysis with new preferences
        await _trigger_ai_reanalysis(user_id, profile_data)
        
        return {
            "status": "success",
            "message": "Profile updated successfully",
            "profile_id": result.data[0]["id"]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating bidder profile: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/profile/recommendations")
async def get_profile_recommendations(user: Dict = Depends(get_current_user)):
    """Get AI-powered profile configuration recommendations"""
    
    try:
        user_id = user["id"]
        
        # Analyze user's current activity
        activity_analysis = await _analyze_user_activity(user_id)
        
        # Generate category recommendations
        category_recommendations = await _recommend_categories(user_id, activity_analysis)
        
        # Generate portfolio recommendations
        portfolio_recommendations = await _recommend_portfolio_settings(user_id, activity_analysis)
        
        # Generate notification recommendations
        notification_recommendations = await _recommend_notification_settings(user_id, activity_analysis)
        
        return {
            "activityAnalysis": activity_analysis,
            "categoryRecommendations": category_recommendations,
            "portfolioRecommendations": portfolio_recommendations,
            "notificationRecommendations": notification_recommendations,
            "confidence": 0.85
        }
        
    except Exception as e:
        logger.error(f"Error getting profile recommendations: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/profile/onboarding")
async def complete_onboarding(
    onboarding_data: Dict,
    user: Dict = Depends(get_current_user)
):
    """Complete bidder onboarding with profile configuration"""
    
    try:
        user_id = user["id"]
        
        # Create comprehensive profile from onboarding data
        profile_record = {
            "user_id": user_id,
            "personal_info": {
                "fullName": onboarding_data.get("fullName", ""),
                "email": user["email"],
                "company": onboarding_data.get("company", ""),
                "bbbeeLevel": onboarding_data.get("bbbeeLevel", 4)
            },
            "business_preferences": {
                "preferredCategories": onboarding_data.get("preferredCategories", []),
                "preferredProvinces": onboarding_data.get("preferredProvinces", ["Gauteng"]),
                "minimumValue": onboarding_data.get("minimumValue", 100000),
                "maximumValue": onboarding_data.get("maximumValue", 10000000),
                "riskTolerance": onboarding_data.get("riskTolerance", "medium"),
                "targetTurnover": onboarding_data.get("targetTurnover", 5000000),
                "currentCapacity": onboarding_data.get("currentCapacity", 80)
            },
            "opportunity_config": {
                "tenderNotifications": onboarding_data.get("tenderNotifications", True),
                "governmentRfqNotifications": onboarding_data.get("governmentRfqNotifications", True),
                "bidderRfqNotifications": onboarding_data.get("bidderRfqNotifications", True),
                "urgentOpportunities": True,
                "portfolioBalanceAlerts": True,
                "competitionAlerts": False,
                "deadlineReminders": 7
            },
            "notification_preferences": {
                "email": onboarding_data.get("emailNotifications", True),
                "sms": onboarding_data.get("smsNotifications", True),
                "whatsapp": onboarding_data.get("whatsappNotifications", False),
                "push": True,
                "frequency": "immediate",
                "quietHours": {"enabled": True, "start": "18:00", "end": "08:00"}
            },
            "portfolio_settings": {
                "targetRfqRatio": onboarding_data.get("recommendedMix", {}).get("rfqPercentage", 60),
                "targetTenderRatio": onboarding_data.get("recommendedMix", {}).get("tenderPercentage", 40),
                "autoOptimization": True,
                "balanceThreshold": 10,
                "psychologicalProfile": onboarding_data.get("psychologicalProfile", {}).get("archetype", "achiever")
            },
            "onboarding_completed": True,
            "onboarding_completed_at": datetime.now().isoformat(),
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        # Save profile
        result = supabase.table("bidder_profiles").insert(profile_record).execute()
        
        if not result.data:
            raise HTTPException(status_code=500, detail="Failed to complete onboarding")
        
        # Initialize portfolio balance tracking
        await _initialize_portfolio_tracking(user_id, profile_record["portfolio_settings"])
        
        # Set up initial notification subscriptions
        await _setup_notification_subscriptions(user_id, profile_record)
        
        # Trigger initial AI analysis
        await _trigger_initial_ai_analysis(user_id, profile_record)
        
        return {
            "status": "onboarding_complete",
            "message": "Profile created successfully",
            "profile_id": result.data[0]["id"],
            "redirectTo": "/dashboard"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error completing onboarding: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# =====================================================
# HELPER FUNCTIONS
# =====================================================

async def _create_default_profile(user_id: str) -> Dict:
    """Create default profile for new user"""
    
    return {
        "user_id": user_id,
        "personal_info": {
            "fullName": "",
            "email": "",
            "phone": "",
            "company": "",
            "registrationNumber": "",
            "bbbeeLevel": 4,
            "taxNumber": ""
        },
        "business_preferences": {
            "preferredCategories": [],
            "preferredProvinces": ["Gauteng"],
            "minimumValue": 100000,
            "maximumValue": 10000000,
            "riskTolerance": "medium",
            "targetTurnover": 5000000,
            "currentCapacity": 80
        },
        "opportunity_config": {
            "tenderNotifications": True,
            "governmentRfqNotifications": True,
            "bidderRfqNotifications": True,
            "urgentOpportunities": True,
            "portfolioBalanceAlerts": True,
            "competitionAlerts": False,
            "deadlineReminders": 7
        },
        "notification_preferences": {
            "email": True,
            "sms": True,
            "whatsapp": False,
            "push": True,
            "frequency": "immediate",
            "quietHours": {"enabled": True, "start": "18:00", "end": "08:00"}
        },
        "portfolio_settings": {
            "targetRfqRatio": 60,
            "targetTenderRatio": 40,
            "autoOptimization": True,
            "balanceThreshold": 10,
            "psychologicalProfile": "achiever"
        }
    }

async def _calculate_portfolio_impact(user_id: str, profile: Dict) -> Dict:
    """Calculate impact of profile settings on portfolio"""
    
    try:
        # Get current portfolio balance
        balance_result = supabase.table("portfolio_balance_tracking").select("*").eq("user_id", user_id).execute()
        
        if balance_result.data:
            current_balance = balance_result.data[0]
            
            target_rfq_ratio = profile["portfolio_settings"]["targetRfqRatio"]
            current_rfq_ratio = current_balance.get("current_rfq_ratio", 0)
            
            return {
                "currentRatio": current_rfq_ratio,
                "targetRatio": target_rfq_ratio,
                "deviation": abs(current_rfq_ratio - target_rfq_ratio),
                "needsOptimization": abs(current_rfq_ratio - target_rfq_ratio) > profile["portfolio_settings"]["balanceThreshold"],
                "recommendedActions": []
            }
        else:
            return {
                "currentRatio": 0,
                "targetRatio": profile["portfolio_settings"]["targetRfqRatio"],
                "deviation": profile["portfolio_settings"]["targetRfqRatio"],
                "needsOptimization": True,
                "recommendedActions": ["Start bidding on opportunities to build portfolio"]
            }
            
    except Exception as e:
        logger.error(f"Error calculating portfolio impact: {e}")
        return {"error": str(e)}

async def _generate_recommended_settings(user_id: str, profile: Dict) -> Dict:
    """Generate AI-powered recommended settings"""
    
    # Simplified recommendations - could be enhanced with ML
    return {
        "categories": {
            "recommended": ["Construction", "IT Services", "Professional Services"],
            "reasoning": "Based on high success rates in South African market"
        },
        "portfolioRatio": {
            "recommended": {"rfq": 60, "tender": 40},
            "reasoning": "Optimal balance for maximum success rate"
        },
        "notifications": {
            "recommended": {
                "frequency": "immediate",
                "channels": ["email", "sms"],
                "urgentOnly": False
            },
            "reasoning": "Immediate notifications ensure you don't miss opportunities"
        }
    }

async def _update_portfolio_targets(user_id: str, portfolio_settings: PortfolioSettings):
    """Update portfolio balance targets"""
    
    try:
        update_data = {
            "target_rfq_ratio": portfolio_settings.targetRfqRatio,
            "target_tender_ratio": portfolio_settings.targetTenderRatio,
            "balance_threshold": portfolio_settings.balanceThreshold,
            "auto_optimization": portfolio_settings.autoOptimization,
            "updated_at": datetime.now().isoformat()
        }
        
        supabase.table("portfolio_balance_tracking").upsert(
            {**update_data, "user_id": user_id}, on_conflict="user_id"
        ).execute()
        
    except Exception as e:
        logger.error(f"Error updating portfolio targets: {e}")

async def _update_notification_subscriptions(user_id: str, profile_data: BidderProfileRequest):
    """Update notification subscriptions based on preferences"""
    
    try:
        # This would integrate with notification service
        logger.info(f"Updated notification subscriptions for user {user_id}")
        
    except Exception as e:
        logger.error(f"Error updating notification subscriptions: {e}")

async def _trigger_ai_reanalysis(user_id: str, profile_data: BidderProfileRequest):
    """Trigger AI re-analysis with new profile preferences"""
    
    try:
        # This would trigger the portfolio balance AI to recalculate
        logger.info(f"Triggered AI re-analysis for user {user_id}")
        
    except Exception as e:
        logger.error(f"Error triggering AI re-analysis: {e}")

async def _analyze_user_activity(user_id: str) -> Dict:
    """Analyze user's bidding activity for recommendations"""
    
    # Simplified analysis - could be enhanced with real data
    return {
        "totalBids": 15,
        "successRate": 78,
        "preferredCategories": ["Construction", "IT Services"],
        "averageValue": 2500000,
        "activityLevel": "medium"
    }

async def _recommend_categories(user_id: str, activity_analysis: Dict) -> List[str]:
    """Recommend categories based on user activity"""
    
    # Simplified recommendations
    return ["Construction", "IT Services", "Professional Services"]

async def _recommend_portfolio_settings(user_id: str, activity_analysis: Dict) -> Dict:
    """Recommend portfolio settings based on activity"""
    
    return {
        "targetRfqRatio": 60,
        "targetTenderRatio": 40,
        "reasoning": "Optimal for your activity level and success rate"
    }

async def _recommend_notification_settings(user_id: str, activity_analysis: Dict) -> Dict:
    """Recommend notification settings based on activity"""
    
    return {
        "frequency": "immediate",
        "channels": ["email", "sms"],
        "reasoning": "Based on your high activity level"
    }

async def _initialize_portfolio_tracking(user_id: str, portfolio_settings: Dict):
    """Initialize portfolio balance tracking for new user"""
    
    try:
        tracking_record = {
            "user_id": user_id,
            "total_activities": 0,
            "rfq_activities": 0,
            "tender_activities": 0,
            "current_rfq_ratio": 0,
            "current_tender_ratio": 0,
            "target_rfq_ratio": portfolio_settings["targetRfqRatio"],
            "target_tender_ratio": portfolio_settings["targetTenderRatio"],
            "balance_status": "balanced",
            "urgency_level": "low",
            "created_at": datetime.now().isoformat()
        }
        
        supabase.table("portfolio_balance_tracking").insert(tracking_record).execute()
        
    except Exception as e:
        logger.error(f"Error initializing portfolio tracking: {e}")

async def _setup_notification_subscriptions(user_id: str, profile_record: Dict):
    """Set up initial notification subscriptions"""
    
    try:
        # This would set up notification subscriptions
        logger.info(f"Set up notification subscriptions for user {user_id}")
        
    except Exception as e:
        logger.error(f"Error setting up notification subscriptions: {e}")

async def _trigger_initial_ai_analysis(user_id: str, profile_record: Dict):
    """Trigger initial AI analysis for new user"""
    
    try:
        # This would trigger initial AI analysis
        logger.info(f"Triggered initial AI analysis for user {user_id}")
        
    except Exception as e:
        logger.error(f"Error triggering initial AI analysis: {e}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
