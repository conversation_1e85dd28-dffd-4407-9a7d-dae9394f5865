"""
Privacy-Aware Location Tracking Service
Respects bee worker privacy settings and consent preferences
Implements POPIA compliance and granular permission controls
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import json
from dataclasses import dataclass
from enum import Enum
from supabase import create_client, Client
import os
import math

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Supabase client
supabase: Client = create_client(
    os.getenv("SUPABASE_URL", ""),
    os.getenv("SUPABASE_SERVICE_KEY", "")
)

class TrackingMode(Enum):
    TASKS_ONLY = "tasks_only"
    WORKING_HOURS = "working_hours"
    ALWAYS = "always"
    EMERGENCY_ONLY = "emergency_only"

class AccuracyLevel(Enum):
    EXACT = "exact"
    APPROXIMATE = "approximate"
    CITY_ONLY = "city_only"

class AccessType(Enum):
    CLIENT_VIEW = "client_view"
    QUEEN_BEE_VIEW = "queen_bee_view"
    EMERGENCY_ACCESS = "emergency_access"
    SYSTEM_ACCESS = "system_access"
    ANALYTICS = "analytics"

@dataclass
class LocationPrivacySettings:
    """Bee worker location privacy preferences"""
    bee_worker_id: str
    tracking_enabled: bool
    tracking_mode: TrackingMode
    accuracy_level: AccuracyLevel
    share_with_clients: bool
    share_with_queen_bee: bool
    share_location_history: bool
    data_retention_days: int
    emergency_tracking_enabled: bool = True

@dataclass
class LocationData:
    """Location data with privacy controls"""
    bee_worker_id: str
    latitude: float
    longitude: float
    accuracy_meters: Optional[int]
    address: Optional[str]
    city: Optional[str]
    province: Optional[str]
    timestamp: datetime
    task_id: Optional[str] = None
    is_during_task: bool = False
    is_during_working_hours: bool = False

@dataclass
class LocationAccessRequest:
    """Request to access bee worker location"""
    requester_id: str
    requester_type: str  # 'client', 'queen_bee', 'emergency', 'system'
    bee_worker_id: str
    access_purpose: str
    legal_basis: str

class PrivacyAwareLocationService:
    """Service for privacy-compliant location tracking"""
    
    def __init__(self):
        self.active_tracking_sessions = {}
        self.location_cache = {}
        
    async def get_privacy_settings(self, bee_worker_id: str) -> Optional[LocationPrivacySettings]:
        """Get bee worker's location privacy settings"""
        
        try:
            result = supabase.table("bee_location_privacy_settings").select("*").eq("bee_worker_id", bee_worker_id).execute()
            
            if not result.data:
                return None
                
            settings_data = result.data[0]
            
            return LocationPrivacySettings(
                bee_worker_id=settings_data['bee_worker_id'],
                tracking_enabled=settings_data['tracking_enabled'],
                tracking_mode=TrackingMode(settings_data['tracking_mode']),
                accuracy_level=AccuracyLevel(settings_data['accuracy_level']),
                share_with_clients=settings_data['share_with_clients'],
                share_with_queen_bee=settings_data['share_with_queen_bee'],
                share_location_history=settings_data['share_location_history'],
                data_retention_days=settings_data['data_retention_days'],
                emergency_tracking_enabled=settings_data.get('emergency_tracking_enabled', True)
            )
            
        except Exception as e:
            logger.error(f"Error getting privacy settings for {bee_worker_id}: {e}")
            return None

    async def can_track_location(self, bee_worker_id: str, context: Dict = None) -> bool:
        """Check if location tracking is allowed based on privacy settings"""
        
        settings = await self.get_privacy_settings(bee_worker_id)
        
        if not settings or not settings.tracking_enabled:
            return False
            
        # Check tracking mode
        if settings.tracking_mode == TrackingMode.EMERGENCY_ONLY:
            return context and context.get('is_emergency', False)
            
        elif settings.tracking_mode == TrackingMode.TASKS_ONLY:
            return context and context.get('is_during_task', False)
            
        elif settings.tracking_mode == TrackingMode.WORKING_HOURS:
            return context and context.get('is_during_working_hours', False)
            
        elif settings.tracking_mode == TrackingMode.ALWAYS:
            return True
            
        return False

    async def record_location(self, location_data: LocationData) -> bool:
        """Record location data with privacy controls"""
        
        try:
            # Check if tracking is allowed
            context = {
                'is_during_task': location_data.is_during_task,
                'is_during_working_hours': location_data.is_during_working_hours,
                'is_emergency': False
            }
            
            if not await self.can_track_location(location_data.bee_worker_id, context):
                logger.info(f"Location tracking not allowed for {location_data.bee_worker_id}")
                return False
                
            # Get privacy settings for data processing
            settings = await self.get_privacy_settings(location_data.bee_worker_id)
            if not settings:
                return False
                
            # Apply accuracy level
            processed_location = await self._apply_accuracy_level(location_data, settings.accuracy_level)
            
            # Calculate expiration date
            expires_at = location_data.timestamp + timedelta(days=settings.data_retention_days)
            
            # Store location data
            location_record = {
                "bee_worker_id": processed_location.bee_worker_id,
                "latitude": processed_location.latitude,
                "longitude": processed_location.longitude,
                "accuracy_meters": processed_location.accuracy_meters,
                "address": processed_location.address,
                "city": processed_location.city,
                "province": processed_location.province,
                "timestamp": processed_location.timestamp.isoformat(),
                "task_id": processed_location.task_id,
                "is_during_task": processed_location.is_during_task,
                "is_during_working_hours": processed_location.is_during_working_hours,
                "shared_with_clients": settings.share_with_clients,
                "shared_with_queen_bee": settings.share_with_queen_bee,
                "expires_at": expires_at.isoformat()
            }
            
            result = supabase.table("bee_location_history").insert(location_record).execute()
            
            if result.data:
                logger.info(f"Location recorded for {location_data.bee_worker_id}")
                return True
                
            return False
            
        except Exception as e:
            logger.error(f"Error recording location for {location_data.bee_worker_id}: {e}")
            return False

    async def get_location(self, access_request: LocationAccessRequest) -> Optional[LocationData]:
        """Get bee worker location with privacy checks"""
        
        try:
            # Check if access is allowed
            if not await self._is_access_allowed(access_request):
                await self._log_access_denial(access_request)
                return None
                
            # Get latest location
            result = supabase.table("bee_location_history").select("*").eq("bee_worker_id", access_request.bee_worker_id).order("timestamp", desc=True).limit(1).execute()
            
            if not result.data:
                return None
                
            location_record = result.data[0]
            
            # Log access
            await self._log_location_access(access_request, location_record['id'])
            
            # Return location data
            return LocationData(
                bee_worker_id=location_record['bee_worker_id'],
                latitude=location_record['latitude'],
                longitude=location_record['longitude'],
                accuracy_meters=location_record['accuracy_meters'],
                address=location_record['address'],
                city=location_record['city'],
                province=location_record['province'],
                timestamp=datetime.fromisoformat(location_record['timestamp']),
                task_id=location_record['task_id'],
                is_during_task=location_record['is_during_task'],
                is_during_working_hours=location_record['is_during_working_hours']
            )
            
        except Exception as e:
            logger.error(f"Error getting location for {access_request.bee_worker_id}: {e}")
            return None

    async def _is_access_allowed(self, access_request: LocationAccessRequest) -> bool:
        """Check if location access is allowed based on privacy settings"""
        
        settings = await self.get_privacy_settings(access_request.bee_worker_id)
        
        if not settings or not settings.tracking_enabled:
            return False
            
        # Check sharing permissions
        if access_request.requester_type == 'client':
            return settings.share_with_clients
        elif access_request.requester_type == 'queen_bee':
            return settings.share_with_queen_bee
        elif access_request.requester_type == 'emergency':
            return settings.emergency_tracking_enabled
        elif access_request.requester_type == 'system':
            return True  # System access for platform operations
        else:
            return False

    async def _apply_accuracy_level(self, location_data: LocationData, accuracy_level: AccuracyLevel) -> LocationData:
        """Apply accuracy level to location data"""
        
        if accuracy_level == AccuracyLevel.EXACT:
            return location_data
            
        elif accuracy_level == AccuracyLevel.APPROXIMATE:
            # Reduce precision to ~100m accuracy
            location_data.latitude = round(location_data.latitude, 3)
            location_data.longitude = round(location_data.longitude, 3)
            location_data.accuracy_meters = max(location_data.accuracy_meters or 0, 100)
            
        elif accuracy_level == AccuracyLevel.CITY_ONLY:
            # Only keep city-level information
            location_data.latitude = round(location_data.latitude, 1)
            location_data.longitude = round(location_data.longitude, 1)
            location_data.address = f"{location_data.city}, {location_data.province}"
            location_data.accuracy_meters = 5000  # ~5km accuracy
            
        return location_data

    async def _log_location_access(self, access_request: LocationAccessRequest, location_id: str):
        """Log location data access for audit trail"""
        
        try:
            access_log = {
                "bee_worker_id": access_request.bee_worker_id,
                "accessed_by": access_request.requester_id,
                "access_type": access_request.requester_type,
                "location_id": location_id,
                "access_purpose": access_request.access_purpose,
                "legal_basis": access_request.legal_basis,
                "access_timestamp": datetime.now().isoformat()
            }
            
            supabase.table("bee_location_sharing_logs").insert(access_log).execute()
            
        except Exception as e:
            logger.error(f"Error logging location access: {e}")

    async def _log_access_denial(self, access_request: LocationAccessRequest):
        """Log denied location access attempts"""
        
        try:
            denial_log = {
                "bee_worker_id": access_request.bee_worker_id,
                "accessed_by": access_request.requester_id,
                "access_type": f"denied_{access_request.requester_type}",
                "access_purpose": f"DENIED: {access_request.access_purpose}",
                "legal_basis": "Privacy settings denial",
                "access_timestamp": datetime.now().isoformat()
            }
            
            supabase.table("bee_location_sharing_logs").insert(denial_log).execute()
            
        except Exception as e:
            logger.error(f"Error logging access denial: {e}")

    async def update_privacy_settings(self, bee_worker_id: str, new_settings: LocationPrivacySettings) -> bool:
        """Update bee worker privacy settings with consent logging"""
        
        try:
            # Update privacy settings
            settings_data = {
                "tracking_enabled": new_settings.tracking_enabled,
                "tracking_mode": new_settings.tracking_mode.value,
                "accuracy_level": new_settings.accuracy_level.value,
                "share_with_clients": new_settings.share_with_clients,
                "share_with_queen_bee": new_settings.share_with_queen_bee,
                "share_location_history": new_settings.share_location_history,
                "data_retention_days": new_settings.data_retention_days,
                "emergency_tracking_enabled": new_settings.emergency_tracking_enabled,
                "last_updated": datetime.now().isoformat(),
                "updated_by": bee_worker_id
            }
            
            result = supabase.table("bee_location_privacy_settings").upsert(settings_data).eq("bee_worker_id", bee_worker_id).execute()
            
            # Log consent change
            consent_log = {
                "bee_worker_id": bee_worker_id,
                "consent_type": "settings_update",
                "consent_given": new_settings.tracking_enabled,
                "settings_snapshot": json.dumps(settings_data),
                "consent_timestamp": datetime.now().isoformat()
            }
            
            supabase.table("bee_location_consent_history").insert(consent_log).execute()
            
            logger.info(f"Privacy settings updated for {bee_worker_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating privacy settings for {bee_worker_id}: {e}")
            return False

    async def cleanup_expired_data(self):
        """Clean up expired location data based on retention settings"""
        
        try:
            # Mark expired data
            supabase.rpc("expire_location_data").execute()
            
            # Anonymize expired data
            supabase.rpc("anonymize_expired_location_data").execute()
            
            logger.info("Location data cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during location data cleanup: {e}")

    async def get_privacy_compliance_report(self, bee_worker_id: str) -> Dict:
        """Generate privacy compliance report for bee worker"""
        
        try:
            # Get current settings
            settings = await self.get_privacy_settings(bee_worker_id)
            
            # Get consent history
            consent_result = supabase.table("bee_location_consent_history").select("*").eq("bee_worker_id", bee_worker_id).order("consent_timestamp", desc=True).execute()
            
            # Get access logs
            access_result = supabase.table("bee_location_sharing_logs").select("*").eq("bee_worker_id", bee_worker_id).order("access_timestamp", desc=True).limit(10).execute()
            
            # Get location data count
            location_result = supabase.table("bee_location_history").select("id").eq("bee_worker_id", bee_worker_id).execute()
            
            return {
                "bee_worker_id": bee_worker_id,
                "current_settings": settings.__dict__ if settings else None,
                "consent_history": consent_result.data or [],
                "recent_access_logs": access_result.data or [],
                "total_location_records": len(location_result.data or []),
                "compliance_status": "compliant" if settings and settings.tracking_enabled else "tracking_disabled",
                "report_generated_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error generating compliance report for {bee_worker_id}: {e}")
            return {"error": str(e)}

# Service instance
privacy_location_service = PrivacyAwareLocationService()

# Helper functions for easy integration
async def track_bee_location(bee_worker_id: str, latitude: float, longitude: float, 
                           task_id: str = None, is_during_task: bool = False) -> bool:
    """Simple function to track bee worker location"""
    
    location_data = LocationData(
        bee_worker_id=bee_worker_id,
        latitude=latitude,
        longitude=longitude,
        accuracy_meters=None,
        address=None,
        city=None,
        province=None,
        timestamp=datetime.now(),
        task_id=task_id,
        is_during_task=is_during_task,
        is_during_working_hours=True  # TODO: Calculate based on working hours
    )
    
    return await privacy_location_service.record_location(location_data)

async def get_bee_location_for_client(client_id: str, bee_worker_id: str) -> Optional[LocationData]:
    """Get bee worker location for client with privacy checks"""
    
    access_request = LocationAccessRequest(
        requester_id=client_id,
        requester_type="client",
        bee_worker_id=bee_worker_id,
        access_purpose="Client tracking during assigned task",
        legal_basis="Legitimate interest - service delivery"
    )
    
    return await privacy_location_service.get_location(access_request)

if __name__ == "__main__":
    # Example usage
    async def main():
        # Test location tracking
        success = await track_bee_location("WB-JHB-001", -26.1951, 28.0568, "task-123", True)
        print(f"Location tracking: {success}")
        
        # Test location access
        location = await get_bee_location_for_client("client-456", "WB-JHB-001")
        print(f"Location access: {location}")
    
    asyncio.run(main())
