"""
Product Specifications Parser API
Handles document parsing, specification extraction, and structured data storage
"""

from fastapi import FastAPI, HTTPException, UploadFile, File, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, <PERSON>
from typing import Dict, List, Optional, Union
import uuid
import json
import logging
from datetime import datetime
import asyncio
import aiofiles
import tempfile
import os

# Import the existing parser components
from supplier_matcher_compliance_engine.parser.construction_parser import ConstructionSpecParser
from supplier_matcher_compliance_engine.models.parsed_spec import ParsedSpec
from supplier_matcher_compliance_engine.utils.spec_normalizer import normalize_units

# Supabase client
from supabase import create_client, Client
import os

# Initialize Supabase
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_ANON_KEY")
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# Logging setup
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="BidBeez Specification Parser API",
    description="AI-powered tender document parsing and specification extraction",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# =====================================================
# PYDANTIC MODELS
# =====================================================

class ParseRequest(BaseModel):
    tender_id: Optional[str] = None
    document_type: str = Field(default="pdf", description="Document type: pdf, docx, or text")
    extract_boq: bool = Field(default=True, description="Extract Bill of Quantities")
    extract_sow: bool = Field(default=True, description="Extract Scope of Work")
    extract_standards: bool = Field(default=True, description="Extract Standards and Certifications")
    province_hint: Optional[str] = None

class ParseResponse(BaseModel):
    spec_id: str
    parsing_status: str
    confidence_score: float
    project_metadata: Dict
    province: str
    boq_items_count: int
    sow_requirements_count: int
    standards_count: int
    processing_time_ms: int

class BOQItem(BaseModel):
    line_number: int
    quantity: Optional[float]
    unit: str
    description: str
    unit_price: Optional[float]
    total_price: Optional[float]
    category: Optional[str]
    cpv_code: Optional[str]
    confidence_score: float

class SOWRequirement(BaseModel):
    category: str
    requirement_type: str
    description: str
    priority: str = "normal"
    compliance_level: str = "mandatory"
    technical_specs: Dict = {}

class SpecificationStandard(BaseModel):
    standard_type: str
    standard_code: str
    standard_title: Optional[str]
    compliance_level: str = "mandatory"
    equivalents_allowed: bool = False

# =====================================================
# HELPER FUNCTIONS
# =====================================================

async def save_parsed_specification(spec_data: Dict, tender_id: Optional[str] = None) -> str:
    """Save parsed specification to database"""
    try:
        # Insert main specification record
        spec_record = {
            "tender_id": tender_id,
            "project_name": spec_data.get("project_metadata", {}).get("project_name"),
            "tender_number": spec_data.get("project_metadata", {}).get("tender_number"),
            "province": spec_data.get("province", "Unknown"),
            "parsing_status": "completed",
            "confidence_score": spec_data.get("confidence_score", 0.0),
            "metadata": spec_data.get("project_metadata", {})
        }
        
        result = supabase.table("parsed_specifications").insert(spec_record).execute()
        spec_id = result.data[0]["id"]
        
        # Insert BOQ line items
        boq_items = []
        for i, item in enumerate(spec_data.get("boq_specs", [])):
            boq_item = {
                "spec_id": spec_id,
                "line_number": i + 1,
                "quantity": item.get("quantity"),
                "unit": item.get("unit", "N/A"),
                "description": item.get("description", ""),
                "category": item.get("category"),
                "confidence_score": item.get("confidence_score", 0.8),
                "metadata": item
            }
            boq_items.append(boq_item)
        
        if boq_items:
            supabase.table("boq_line_items").insert(boq_items).execute()
        
        # Insert SOW requirements
        sow_requirements = []
        for item in spec_data.get("sow_specs", []):
            sow_req = {
                "spec_id": spec_id,
                "category": item.get("category", "General"),
                "requirement_type": "specification",
                "description": item.get("description", ""),
                "priority": "normal",
                "compliance_level": "mandatory"
            }
            sow_requirements.append(sow_req)
        
        if sow_requirements:
            supabase.table("sow_requirements").insert(sow_requirements).execute()
        
        # Insert standards
        standards = []
        for standard in spec_data.get("standards", []):
            std_record = {
                "spec_id": spec_id,
                "standard_type": standard.get("type", ""),
                "standard_code": standard.get("code", ""),
                "compliance_level": "mandatory",
                "equivalents_allowed": False
            }
            standards.append(std_record)
        
        if standards:
            supabase.table("specification_standards").insert(standards).execute()
        
        return spec_id
        
    except Exception as e:
        logger.error(f"Error saving parsed specification: {e}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

async def process_document_async(file_content: bytes, file_type: str, spec_id: str, parse_options: Dict):
    """Background task for document processing"""
    try:
        # Update status to processing
        supabase.table("parsed_specifications").update({
            "parsing_status": "processing"
        }).eq("id", spec_id).execute()
        
        # Parse document
        parser = ConstructionSpecParser(file_content, input_type=file_type)
        parsed_data = parser.parse()
        
        # Calculate confidence score based on extracted data
        confidence_score = calculate_confidence_score(parsed_data)
        
        # Update database with parsed results
        await update_parsed_specification(spec_id, parsed_data, confidence_score)
        
        logger.info(f"Successfully processed specification {spec_id}")
        
    except Exception as e:
        logger.error(f"Error processing document for spec {spec_id}: {e}")
        # Update status to failed
        supabase.table("parsed_specifications").update({
            "parsing_status": "failed",
            "metadata": {"error": str(e)}
        }).eq("id", spec_id).execute()

def calculate_confidence_score(parsed_data: Dict) -> float:
    """Calculate confidence score based on extracted data quality"""
    score = 0.0
    
    # Project metadata presence (20%)
    if parsed_data.get("project_metadata"):
        metadata = parsed_data["project_metadata"]
        if metadata.get("project_name"):
            score += 0.1
        if metadata.get("tender_number"):
            score += 0.1
    
    # Province detection (10%)
    if parsed_data.get("province") and parsed_data["province"] != "Unknown":
        score += 0.1
    
    # BOQ extraction (30%)
    boq_specs = parsed_data.get("boq_specs", [])
    if boq_specs:
        score += 0.2
        # Bonus for quantity and unit extraction
        valid_items = sum(1 for item in boq_specs if item.get("quantity") and item.get("unit"))
        if valid_items > 0:
            score += 0.1 * min(valid_items / len(boq_specs), 1.0)
    
    # SOW extraction (20%)
    sow_specs = parsed_data.get("sow_specs", [])
    if sow_specs:
        score += 0.2
    
    # Standards extraction (20%)
    standards = parsed_data.get("standards", [])
    if standards:
        score += 0.2
    
    return min(score, 1.0)

async def update_parsed_specification(spec_id: str, parsed_data: Dict, confidence_score: float):
    """Update parsed specification with results"""
    try:
        # Update main record
        supabase.table("parsed_specifications").update({
            "parsing_status": "completed",
            "confidence_score": confidence_score,
            "metadata": parsed_data.get("project_metadata", {})
        }).eq("id", spec_id).execute()
        
        # Insert/update BOQ items, SOW requirements, and standards
        # (Implementation similar to save_parsed_specification)
        
    except Exception as e:
        logger.error(f"Error updating specification {spec_id}: {e}")
        raise

# =====================================================
# API ENDPOINTS
# =====================================================

@app.post("/parse", response_model=ParseResponse)
async def parse_document(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    tender_id: Optional[str] = None,
    document_type: str = "pdf",
    extract_boq: bool = True,
    extract_sow: bool = True,
    extract_standards: bool = True
):
    """
    Parse tender document and extract specifications
    """
    start_time = datetime.now()
    
    try:
        # Validate file type
        if document_type not in ["pdf", "docx", "text"]:
            raise HTTPException(status_code=400, detail="Unsupported document type")
        
        # Read file content
        file_content = await file.read()
        
        if len(file_content) == 0:
            raise HTTPException(status_code=400, detail="Empty file uploaded")
        
        # Create initial specification record
        spec_record = {
            "tender_id": tender_id,
            "parsing_status": "pending",
            "confidence_score": 0.0,
            "metadata": {"filename": file.filename, "file_size": len(file_content)}
        }
        
        result = supabase.table("parsed_specifications").insert(spec_record).execute()
        spec_id = result.data[0]["id"]
        
        # Start background processing
        parse_options = {
            "extract_boq": extract_boq,
            "extract_sow": extract_sow,
            "extract_standards": extract_standards
        }
        
        background_tasks.add_task(
            process_document_async, 
            file_content, 
            document_type, 
            spec_id, 
            parse_options
        )
        
        processing_time = int((datetime.now() - start_time).total_seconds() * 1000)
        
        return ParseResponse(
            spec_id=spec_id,
            parsing_status="processing",
            confidence_score=0.0,
            project_metadata={},
            province="Unknown",
            boq_items_count=0,
            sow_requirements_count=0,
            standards_count=0,
            processing_time_ms=processing_time
        )
        
    except Exception as e:
        logger.error(f"Error in parse_document: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/specifications/{spec_id}")
async def get_specification(spec_id: str):
    """Get parsed specification details"""
    try:
        # Get main specification
        spec_result = supabase.table("parsed_specifications").select("*").eq("id", spec_id).execute()
        
        if not spec_result.data:
            raise HTTPException(status_code=404, detail="Specification not found")
        
        spec = spec_result.data[0]
        
        # Get BOQ items
        boq_result = supabase.table("boq_line_items").select("*").eq("spec_id", spec_id).execute()
        
        # Get SOW requirements
        sow_result = supabase.table("sow_requirements").select("*").eq("spec_id", spec_id).execute()
        
        # Get standards
        standards_result = supabase.table("specification_standards").select("*").eq("spec_id", spec_id).execute()
        
        return {
            "specification": spec,
            "boq_items": boq_result.data,
            "sow_requirements": sow_result.data,
            "standards": standards_result.data
        }
        
    except Exception as e:
        logger.error(f"Error getting specification {spec_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/specifications/{spec_id}/status")
async def get_parsing_status(spec_id: str):
    """Get parsing status for a specification"""
    try:
        result = supabase.table("parsed_specifications").select("parsing_status, confidence_score, metadata").eq("id", spec_id).execute()
        
        if not result.data:
            raise HTTPException(status_code=404, detail="Specification not found")
        
        return result.data[0]
        
    except Exception as e:
        logger.error(f"Error getting status for {spec_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/specifications")
async def list_specifications(
    tender_id: Optional[str] = None,
    province: Optional[str] = None,
    status: Optional[str] = None,
    limit: int = 50,
    offset: int = 0
):
    """List parsed specifications with filtering"""
    try:
        query = supabase.table("parsed_specifications").select("*")
        
        if tender_id:
            query = query.eq("tender_id", tender_id)
        if province:
            query = query.eq("province", province)
        if status:
            query = query.eq("parsing_status", status)
        
        result = query.range(offset, offset + limit - 1).execute()
        
        return {
            "specifications": result.data,
            "count": len(result.data)
        }
        
    except Exception as e:
        logger.error(f"Error listing specifications: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/specifications/{spec_id}")
async def delete_specification(spec_id: str):
    """Delete a parsed specification and all related data"""
    try:
        # Delete will cascade to related tables due to foreign key constraints
        result = supabase.table("parsed_specifications").delete().eq("id", spec_id).execute()
        
        if not result.data:
            raise HTTPException(status_code=404, detail="Specification not found")
        
        return {"message": "Specification deleted successfully"}
        
    except Exception as e:
        logger.error(f"Error deleting specification {spec_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
