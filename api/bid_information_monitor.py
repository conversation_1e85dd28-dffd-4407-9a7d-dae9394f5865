"""
Bid Information Monitor Service
Continuously monitors bid developments and sends multi-channel alerts
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import aiohttp
import json
from bs4 import BeautifulSoup
import re
from dataclasses import dataclass
from supabase import create_client, Client
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Supabase client
supabase: Client = create_client(
    os.getenv("SUPABASE_URL", ""),
    os.getenv("SUPABASE_SERVICE_KEY", "")
)

@dataclass
class BidUpdate:
    """Data class for bid updates"""
    bid_tracking_id: str
    update_type: str
    priority: str
    title: str
    description: str
    source_url: str
    source_type: str
    previous_status: Optional[str] = None
    new_status: Optional[str] = None
    document_urls: List[str] = None
    document_names: List[str] = None
    competitor_info: Dict = None
    confidence_score: float = 1.0

class BidInformationMonitor:
    """Service for monitoring bid information and sending alerts"""
    
    def __init__(self):
        self.session = None
        self.monitoring_sources = []
        self.alert_dispatcher = AlertDispatcher()
        
        # Update type priorities
        self.priority_mapping = {
            'award_announcement': 'critical',
            'rejection_notice': 'critical',
            'cancellation': 'critical',
            'addendum': 'high',
            'clarification': 'high',
            'deadline_extension': 'high',
            'status_change': 'medium',
            'competitor_activity': 'medium',
            'document_update': 'low'
        }

    async def start_monitoring(self):
        """Start the continuous monitoring process"""
        
        logger.info("Starting bid information monitoring service...")
        
        # Initialize HTTP session
        self.session = aiohttp.ClientSession()
        
        # Load monitoring sources
        await self._load_monitoring_sources()
        
        # Start monitoring loop
        while True:
            try:
                await self._monitor_all_bids()
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying

    async def _load_monitoring_sources(self):
        """Load active monitoring sources from database"""
        
        try:
            result = supabase.table("bid_monitoring_sources").select("*").eq("is_active", True).execute()
            self.monitoring_sources = result.data or []
            logger.info(f"Loaded {len(self.monitoring_sources)} monitoring sources")
            
        except Exception as e:
            logger.error(f"Error loading monitoring sources: {e}")

    async def _monitor_all_bids(self):
        """Monitor all active bids for updates"""
        
        try:
            # Get all active bid tracking records
            result = supabase.table("bid_tracking").select("*").eq("tracking_status", "active").execute()
            active_bids = result.data or []
            
            logger.info(f"Monitoring {len(active_bids)} active bids")
            
            # Monitor each bid
            for bid in active_bids:
                try:
                    await self._monitor_single_bid(bid)
                except Exception as e:
                    logger.error(f"Error monitoring bid {bid['id']}: {e}")
                    
        except Exception as e:
            logger.error(f"Error getting active bids: {e}")

    async def _monitor_single_bid(self, bid: Dict):
        """Monitor a single bid for updates"""
        
        bid_id = bid['id']
        bid_reference = bid['bid_reference']
        organization = bid['organization_name']
        
        # Check if enough time has passed since last check
        last_checked = datetime.fromisoformat(bid['last_checked'].replace('Z', '+00:00'))
        if datetime.now() - last_checked < timedelta(minutes=30):
            return
        
        logger.info(f"Monitoring bid: {bid_reference} - {organization}")
        
        # Monitor different sources
        updates = []
        
        # 1. Monitor official government portals
        if bid['opportunity_type'] in ['tender', 'government_rfq']:
            gov_updates = await self._monitor_government_portals(bid)
            updates.extend(gov_updates)
        
        # 2. Monitor organization websites
        org_updates = await self._monitor_organization_website(bid)
        updates.extend(org_updates)
        
        # 3. Monitor tender portals
        portal_updates = await self._monitor_tender_portals(bid)
        updates.extend(portal_updates)
        
        # 4. Check for bee worker reports
        bee_updates = await self._check_bee_worker_reports(bid)
        updates.extend(bee_updates)
        
        # Process and save updates
        for update in updates:
            await self._process_bid_update(update)
        
        # Update last checked timestamp
        await self._update_last_checked(bid_id)

    async def _monitor_government_portals(self, bid: Dict) -> List[BidUpdate]:
        """Monitor government portals for bid updates"""
        
        updates = []
        
        try:
            # South African Government Tender Portal
            if 'etenders.gov.za' in bid.get('source_url', ''):
                updates.extend(await self._check_etenders_portal(bid))
            
            # Provincial portals
            if 'gauteng' in bid.get('organization_name', '').lower():
                updates.extend(await self._check_gauteng_portal(bid))
            
            # Municipal portals
            if any(city in bid.get('organization_name', '').lower() for city in ['johannesburg', 'cape town', 'durban']):
                updates.extend(await self._check_municipal_portals(bid))
                
        except Exception as e:
            logger.error(f"Error monitoring government portals for bid {bid['id']}: {e}")
        
        return updates

    async def _check_etenders_portal(self, bid: Dict) -> List[BidUpdate]:
        """Check eTenders portal for updates"""
        
        updates = []
        bid_reference = bid['bid_reference']
        
        try:
            # Search for bid by reference number
            search_url = f"https://etenders.gov.za/content/search-results?search={bid_reference}"
            
            async with self.session.get(search_url) as response:
                if response.status == 200:
                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')
                    
                    # Look for status updates
                    status_elements = soup.find_all('div', class_='tender-status')
                    for element in status_elements:
                        status_text = element.get_text().strip()
                        
                        # Check for award announcements
                        if 'awarded' in status_text.lower():
                            updates.append(BidUpdate(
                                bid_tracking_id=bid['id'],
                                update_type='award_announcement',
                                priority='critical',
                                title=f"Award Announcement - {bid['bid_title']}",
                                description=f"Bid has been awarded. Status: {status_text}",
                                source_url=search_url,
                                source_type='official_portal',
                                new_status='awarded',
                                confidence_score=0.95
                            ))
                        
                        # Check for cancellations
                        elif 'cancelled' in status_text.lower():
                            updates.append(BidUpdate(
                                bid_tracking_id=bid['id'],
                                update_type='cancellation',
                                priority='critical',
                                title=f"Bid Cancelled - {bid['bid_title']}",
                                description=f"Bid has been cancelled. Status: {status_text}",
                                source_url=search_url,
                                source_type='official_portal',
                                new_status='cancelled',
                                confidence_score=0.95
                            ))
                    
                    # Look for addendums
                    addendum_links = soup.find_all('a', href=re.compile(r'addendum|amendment'))
                    for link in addendum_links:
                        updates.append(BidUpdate(
                            bid_tracking_id=bid['id'],
                            update_type='addendum',
                            priority='high',
                            title=f"New Addendum Available - {bid['bid_title']}",
                            description=f"New addendum document: {link.get_text().strip()}",
                            source_url=link.get('href'),
                            source_type='official_portal',
                            document_urls=[link.get('href')],
                            document_names=[link.get_text().strip()],
                            confidence_score=0.90
                        ))
                        
        except Exception as e:
            logger.error(f"Error checking eTenders portal: {e}")
        
        return updates

    async def _monitor_organization_website(self, bid: Dict) -> List[BidUpdate]:
        """Monitor organization website for updates"""
        
        updates = []
        
        try:
            # Extract organization domain from bid details
            org_domain = self._extract_organization_domain(bid['organization_name'])
            
            if org_domain:
                # Check organization's tender/procurement page
                tender_urls = [
                    f"https://{org_domain}/tenders",
                    f"https://{org_domain}/procurement",
                    f"https://{org_domain}/suppliers",
                    f"https://www.{org_domain}/tenders"
                ]
                
                for url in tender_urls:
                    try:
                        async with self.session.get(url, timeout=10) as response:
                            if response.status == 200:
                                html = await response.text()
                                
                                # Look for bid reference in content
                                if bid['bid_reference'] in html:
                                    soup = BeautifulSoup(html, 'html.parser')
                                    
                                    # Check for award announcements
                                    if any(keyword in html.lower() for keyword in ['awarded', 'successful bidder', 'contract awarded']):
                                        updates.append(BidUpdate(
                                            bid_tracking_id=bid['id'],
                                            update_type='award_announcement',
                                            priority='critical',
                                            title=f"Award Notice Found - {bid['bid_title']}",
                                            description="Award announcement found on organization website",
                                            source_url=url,
                                            source_type='organization_website',
                                            confidence_score=0.85
                                        ))
                                    
                                    break  # Found relevant page
                                    
                    except Exception as e:
                        logger.debug(f"Could not access {url}: {e}")
                        continue
                        
        except Exception as e:
            logger.error(f"Error monitoring organization website: {e}")
        
        return updates

    async def _check_bee_worker_reports(self, bid: Dict) -> List[BidUpdate]:
        """Check for bee worker reports and observations"""
        
        updates = []
        
        try:
            # Get recent bee worker activities for this bid
            result = supabase.table("bee_worker_activities").select("*").eq("tender_id", bid['opportunity_id']).gte("created_at", (datetime.now() - timedelta(days=7)).isoformat()).execute()
            
            activities = result.data or []
            
            for activity in activities:
                if activity['activity_type'] == 'site_visit' and activity.get('observations'):
                    # Check for competitive intelligence
                    observations = activity['observations']
                    
                    if 'competitor' in observations.lower():
                        updates.append(BidUpdate(
                            bid_tracking_id=bid['id'],
                            update_type='competitor_activity',
                            priority='medium',
                            title=f"Competitor Activity Observed - {bid['bid_title']}",
                            description=f"Bee worker observed: {observations}",
                            source_url=f"/bee-activities/{activity['id']}",
                            source_type='bee_worker',
                            competitor_info={'observation': observations, 'date': activity['created_at']},
                            confidence_score=0.80
                        ))
                
                elif activity['activity_type'] == 'document_collection' and activity.get('status') == 'completed':
                    # Check for new documents
                    if activity.get('documents_collected'):
                        updates.append(BidUpdate(
                            bid_tracking_id=bid['id'],
                            update_type='document_update',
                            priority='low',
                            title=f"New Documents Collected - {bid['bid_title']}",
                            description=f"Bee worker collected: {activity['documents_collected']}",
                            source_url=f"/bee-activities/{activity['id']}",
                            source_type='bee_worker',
                            confidence_score=1.0
                        ))
                        
        except Exception as e:
            logger.error(f"Error checking bee worker reports: {e}")
        
        return updates

    async def _process_bid_update(self, update: BidUpdate):
        """Process and save a bid update"""
        
        try:
            # Check if this update already exists
            existing = supabase.table("bid_updates").select("id").eq("bid_tracking_id", update.bid_tracking_id).eq("title", update.title).eq("detected_at", datetime.now().date().isoformat()).execute()
            
            if existing.data:
                logger.debug(f"Update already exists: {update.title}")
                return
            
            # Save the update
            update_data = {
                "bid_tracking_id": update.bid_tracking_id,
                "update_type": update.update_type,
                "update_priority": update.priority,
                "title": update.title,
                "description": update.description,
                "source_url": update.source_url,
                "source_type": update.source_type,
                "previous_status": update.previous_status,
                "new_status": update.new_status,
                "document_urls": update.document_urls or [],
                "document_names": update.document_names or [],
                "competitor_info": update.competitor_info or {},
                "confidence_score": update.confidence_score,
                "detected_at": datetime.now().isoformat()
            }
            
            result = supabase.table("bid_updates").insert(update_data).execute()
            
            if result.data:
                update_id = result.data[0]['id']
                logger.info(f"Saved bid update: {update.title}")
                
                # Send alerts
                await self.alert_dispatcher.send_alerts(update_id, update)
                
                # Update bid status if changed
                if update.new_status:
                    await self._update_bid_status(update.bid_tracking_id, update.new_status)
                    
        except Exception as e:
            logger.error(f"Error processing bid update: {e}")

    async def _update_bid_status(self, bid_tracking_id: str, new_status: str):
        """Update bid tracking status"""
        
        try:
            supabase.table("bid_tracking").update({
                "current_status": new_status,
                "updated_at": datetime.now().isoformat()
            }).eq("id", bid_tracking_id).execute()
            
        except Exception as e:
            logger.error(f"Error updating bid status: {e}")

    async def _update_last_checked(self, bid_tracking_id: str):
        """Update last checked timestamp"""
        
        try:
            supabase.table("bid_tracking").update({
                "last_checked": datetime.now().isoformat()
            }).eq("id", bid_tracking_id).execute()
            
        except Exception as e:
            logger.error(f"Error updating last checked: {e}")

    def _extract_organization_domain(self, organization_name: str) -> Optional[str]:
        """Extract likely domain from organization name"""
        
        # Simple domain extraction logic
        name = organization_name.lower().replace(' ', '').replace('municipality', '').replace('city of', '').replace('department of', '')
        
        # Common South African organization domains
        domain_mappings = {
            'johannesburg': 'joburg.org.za',
            'capetown': 'capetown.gov.za',
            'durban': 'durban.gov.za',
            'gauteng': 'gauteng.gov.za',
            'westerncape': 'westerncape.gov.za'
        }
        
        for key, domain in domain_mappings.items():
            if key in name:
                return domain
        
        return None

    async def close(self):
        """Close the monitoring service"""
        
        if self.session:
            await self.session.close()

class AlertDispatcher:
    """Handles multi-channel alert dispatch"""
    
    def __init__(self):
        self.whatsapp_service = WhatsAppService()
        self.email_service = EmailService()
        self.sms_service = SMSService()
        self.push_service = PushNotificationService()

    async def send_alerts(self, update_id: str, update: BidUpdate):
        """Send alerts through appropriate channels"""
        
        try:
            # Get bid tracking info and user preferences
            bid_result = supabase.table("bid_tracking").select("user_id").eq("id", update.bid_tracking_id).execute()
            
            if not bid_result.data:
                return
            
            user_id = bid_result.data[0]['user_id']
            
            # Get user alert preferences
            prefs_result = supabase.table("alert_preferences").select("*").eq("user_id", user_id).execute()
            
            if not prefs_result.data:
                return
            
            preferences = prefs_result.data[0]
            
            # Determine channels based on priority and preferences
            channels = self._get_alert_channels(update.priority, preferences)
            
            # Send alerts through each channel
            for channel in channels:
                try:
                    await self._send_channel_alert(channel, user_id, update_id, update)
                except Exception as e:
                    logger.error(f"Error sending {channel} alert: {e}")
                    
        except Exception as e:
            logger.error(f"Error dispatching alerts: {e}")

    def _get_alert_channels(self, priority: str, preferences: Dict) -> List[str]:
        """Get appropriate channels based on priority and preferences"""
        
        channel_map = {
            'critical': preferences.get('critical_channels', []),
            'high': preferences.get('high_channels', []),
            'medium': preferences.get('medium_channels', []),
            'low': preferences.get('low_channels', [])
        }
        
        return channel_map.get(priority, ['push'])

    async def _send_channel_alert(self, channel: str, user_id: str, update_id: str, update: BidUpdate):
        """Send alert through specific channel"""
        
        # Create alert message
        message = self._create_alert_message(update)
        
        # Send through appropriate service
        if channel == 'whatsapp' and hasattr(self, 'whatsapp_service'):
            await self.whatsapp_service.send_alert(user_id, message)
        elif channel == 'email' and hasattr(self, 'email_service'):
            await self.email_service.send_alert(user_id, update.title, message)
        elif channel == 'sms' and hasattr(self, 'sms_service'):
            await self.sms_service.send_alert(user_id, message)
        elif channel == 'push' and hasattr(self, 'push_service'):
            await self.push_service.send_alert(user_id, update.title, message)
        
        # Log alert
        await self._log_alert(update_id, user_id, channel, update.title, message)

    def _create_alert_message(self, update: BidUpdate) -> str:
        """Create alert message content"""
        
        priority_emoji = {
            'critical': '🚨',
            'high': '⚠️',
            'medium': 'ℹ️',
            'low': '📝'
        }
        
        emoji = priority_emoji.get(update.priority, 'ℹ️')
        
        message = f"{emoji} {update.title}\n\n{update.description}"
        
        if update.source_url:
            message += f"\n\nSource: {update.source_url}"
        
        return message

    async def _log_alert(self, update_id: str, user_id: str, channel: str, subject: str, message: str):
        """Log sent alert"""
        
        try:
            alert_data = {
                "bid_update_id": update_id,
                "user_id": user_id,
                "channel": channel,
                "alert_type": "bid_update",
                "priority": "medium",
                "subject": subject,
                "message": message,
                "status": "sent",
                "sent_at": datetime.now().isoformat()
            }
            
            supabase.table("alert_history").insert(alert_data).execute()
            
        except Exception as e:
            logger.error(f"Error logging alert: {e}")

# Placeholder services for different alert channels
class WhatsAppService:
    async def send_alert(self, user_id: str, message: str):
        logger.info(f"WhatsApp alert sent to user {user_id}")

class EmailService:
    async def send_alert(self, user_id: str, subject: str, message: str):
        logger.info(f"Email alert sent to user {user_id}: {subject}")

class SMSService:
    async def send_alert(self, user_id: str, message: str):
        logger.info(f"SMS alert sent to user {user_id}")

class PushNotificationService:
    async def send_alert(self, user_id: str, title: str, message: str):
        logger.info(f"Push notification sent to user {user_id}: {title}")

# Service instance
bid_monitor = BidInformationMonitor()

# Main function to start monitoring
async def start_bid_monitoring():
    """Start the bid monitoring service"""
    await bid_monitor.start_monitoring()

if __name__ == "__main__":
    asyncio.run(start_bid_monitoring())
