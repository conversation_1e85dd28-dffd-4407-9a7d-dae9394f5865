"""
BidBeez API Main Application - Development Version
Simplified version without Supabase dependencies for local development
"""

from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import os
import logging
from datetime import datetime

# Logging setup
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Main application
app = FastAPI(
    title="BidBeez Development API",
    description="Development version of BidBeez API without external dependencies",
    version="1.0.0-dev",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# =====================================================
# HEALTH CHECK ENDPOINTS
# =====================================================

@app.get("/health")
async def health_check():
    """Main health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "environment": "development",
        "services": {
            "api": "active",
            "database": "mock"
        }
    }

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "BidBeez Development API",
        "version": "1.0.0-dev",
        "documentation": "/docs",
        "health": "/health",
        "environment": "development"
    }

# =====================================================
# MOCK API ENDPOINTS
# =====================================================

@app.get("/api/status")
async def api_status():
    """Get overall API status"""
    try:
        return {
            "api_status": "operational",
            "database_status": "mock",
            "services": {
                "behavioral_engine": "mock",
                "onboarding_engine": "mock",
                "contractor_access": "mock"
            },
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Status check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={"status": "error", "message": str(e)}
        )

@app.get("/api/metrics")
async def get_metrics():
    """Get API metrics and usage statistics"""
    try:
        return {
            "total_requests": 0,
            "active_users": 0,
            "response_time_avg": "150ms",
            "uptime": "99.9%",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Metrics retrieval failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Mock endpoints for frontend development
@app.get("/api/tenders")
async def get_tenders():
    """Mock tenders endpoint"""
    return {
        "tenders": [
            {
                "id": 1,
                "title": "Sample Tender 1",
                "description": "This is a sample tender for development",
                "status": "open",
                "deadline": "2024-12-31"
            },
            {
                "id": 2,
                "title": "Sample Tender 2", 
                "description": "Another sample tender for development",
                "status": "open",
                "deadline": "2024-11-30"
            }
        ]
    }

@app.get("/api/dashboard")
async def get_dashboard():
    """Mock dashboard data"""
    return {
        "stats": {
            "total_tenders": 25,
            "active_bids": 8,
            "success_rate": "94.7%",
            "revenue": "R2.4M"
        },
        "recent_activity": [
            {"action": "New tender available", "time": "2 hours ago"},
            {"action": "Bid submitted successfully", "time": "4 hours ago"}
        ]
    }

# =====================================================
# ERROR HANDLERS
# =====================================================

@app.exception_handler(404)
async def not_found_handler(request, exc):
    return JSONResponse(
        status_code=404,
        content={
            "error": "Not Found",
            "message": "The requested endpoint was not found",
            "documentation": "/docs"
        }
    )

@app.exception_handler(500)
async def internal_error_handler(request, exc):
    logger.error(f"Internal server error: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal Server Error", 
            "message": "An unexpected error occurred",
            "timestamp": datetime.now().isoformat()
        }
    )

# =====================================================
# STARTUP/SHUTDOWN EVENTS
# =====================================================

@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    logger.info("🚀 BidBeez Development API starting up...")
    logger.info("✅ Development mode enabled")
    logger.info("✅ Mock services loaded")
    logger.info("🎯 Development API operational!")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    logger.info("🛑 BidBeez Development API shutting down...")
    logger.info("✅ Cleanup completed")

if __name__ == "__main__":
    import uvicorn
    
    port = int(os.getenv("PORT", 8000))
    host = os.getenv("HOST", "0.0.0.0")
    
    uvicorn.run(
        "main_dev:app",
        host=host,
        port=port,
        reload=True,  # Enable reload for development
        log_level="info"
    )
