"""
Tender Service Layer for BidBeez Platform
Handles all tender and quote database operations
"""

import asyncio
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone
import asyncpg
from fastapi import HTTPException
import logging

from ..models.tender_models import (
    Tender, SupplierQuote, TenderBid,
    TenderCreateRequest, QuoteCreateRequest, BidCreateRequest,
    TenderStatus, QuoteStatus, BidStatus
)
from ..database.connection import get_database_connection

logger = logging.getLogger(__name__)

class TenderService:
    """Service class for tender operations"""
    
    def __init__(self):
        self.db = None
    
    async def get_connection(self):
        """Get database connection"""
        if not self.db:
            self.db = await get_database_connection()
        return self.db
    
    # =====================================================
    # TENDER OPERATIONS
    # =====================================================
    
    async def create_tender(self, tender_data: TenderCreateRequest, user_id: str) -> Tender:
        """Create a new tender"""
        try:
            conn = await self.get_connection()
            
            # Generate tender ID
            tender_id = f"BID-{datetime.now().strftime('%Y%m%d')}-{user_id[:8]}"
            
            query = """
                INSERT INTO tenders (
                    tender_id, title, description, organization, estimated_value,
                    closing_date, location, province, category, requirements,
                    bbbee_required, bbbee_minimum_level, status, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
                RETURNING *
            """
            
            now = datetime.now(timezone.utc)
            
            result = await conn.fetchrow(
                query,
                tender_id,
                tender_data.title,
                tender_data.description,
                tender_data.organization,
                tender_data.estimated_value,
                tender_data.closing_date,
                tender_data.location,
                tender_data.province,
                tender_data.category,
                tender_data.requirements,
                tender_data.bbbee_required,
                tender_data.bbbee_minimum_level,
                TenderStatus.ACTIVE.value,
                now,
                now
            )
            
            return Tender(**dict(result))
            
        except Exception as e:
            logger.error(f"Error creating tender: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to create tender")
    
    async def get_tender_by_id(self, tender_id: str) -> Optional[Tender]:
        """Get tender by ID"""
        try:
            conn = await self.get_connection()
            
            query = "SELECT * FROM tenders WHERE id = $1 OR tender_id = $1"
            result = await conn.fetchrow(query, tender_id)
            
            if result:
                return Tender(**dict(result))
            return None
            
        except Exception as e:
            logger.error(f"Error fetching tender {tender_id}: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to fetch tender")
    
    async def get_tenders(
        self,
        page: int = 1,
        limit: int = 20,
        status: Optional[str] = None,
        category: Optional[str] = None,
        province: Optional[str] = None,
        search: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get paginated list of tenders with filters"""
        try:
            conn = await self.get_connection()
            
            # Build WHERE clause
            where_conditions = ["status != 'draft'"]
            params = []
            param_count = 0
            
            if status:
                param_count += 1
                where_conditions.append(f"status = ${param_count}")
                params.append(status)
            
            if category:
                param_count += 1
                where_conditions.append(f"category = ${param_count}")
                params.append(category)
            
            if province:
                param_count += 1
                where_conditions.append(f"province = ${param_count}")
                params.append(province)
            
            if search:
                param_count += 1
                where_conditions.append(f"(title ILIKE ${param_count} OR description ILIKE ${param_count} OR organization ILIKE ${param_count})")
                params.append(f"%{search}%")
            
            where_clause = " AND ".join(where_conditions)
            
            # Count total records
            count_query = f"SELECT COUNT(*) FROM tenders WHERE {where_clause}"
            total = await conn.fetchval(count_query, *params)
            
            # Get paginated results
            offset = (page - 1) * limit
            param_count += 1
            params.append(limit)
            param_count += 1
            params.append(offset)
            
            query = f"""
                SELECT * FROM tenders 
                WHERE {where_clause}
                ORDER BY created_at DESC
                LIMIT ${param_count - 1} OFFSET ${param_count}
            """
            
            results = await conn.fetch(query, *params)
            tenders = [Tender(**dict(row)) for row in results]
            
            return {
                "tenders": tenders,
                "total": total,
                "page": page,
                "limit": limit,
                "has_more": (page * limit) < total
            }
            
        except Exception as e:
            logger.error(f"Error fetching tenders: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to fetch tenders")
    
    async def update_tender_metrics(self, tender_id: str, metric: str):
        """Update tender metrics (views, downloads, bids)"""
        try:
            conn = await self.get_connection()
            
            if metric == "view":
                query = "UPDATE tenders SET view_count = view_count + 1, updated_at = $1 WHERE id = $2 OR tender_id = $2"
            elif metric == "download":
                query = "UPDATE tenders SET download_count = download_count + 1, updated_at = $1 WHERE id = $2 OR tender_id = $2"
            elif metric == "bid":
                query = "UPDATE tenders SET bid_count = bid_count + 1, updated_at = $1 WHERE id = $2 OR tender_id = $2"
            else:
                return
            
            await conn.execute(query, datetime.now(timezone.utc), tender_id)
            
        except Exception as e:
            logger.error(f"Error updating tender metrics: {str(e)}")
    
    # =====================================================
    # QUOTE OPERATIONS
    # =====================================================
    
    async def create_quote(self, quote_data: QuoteCreateRequest, supplier_id: str) -> SupplierQuote:
        """Create a new supplier quote"""
        try:
            conn = await self.get_connection()
            
            # Generate quote ID
            quote_id = f"QUO-{datetime.now().strftime('%Y%m%d')}-{supplier_id[:8]}"
            
            query = """
                INSERT INTO supplier_quotes (
                    quote_id, tender_id, supplier_id, total_amount, currency,
                    delivery_timeframe, validity_period_days, line_items,
                    payment_terms, warranty_terms, smart_contract_enabled,
                    status, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
                RETURNING *
            """
            
            now = datetime.now(timezone.utc)
            
            result = await conn.fetchrow(
                query,
                quote_id,
                quote_data.tender_id,
                supplier_id,
                quote_data.total_amount,
                quote_data.currency,
                quote_data.delivery_timeframe,
                quote_data.validity_period_days,
                [item.dict() for item in quote_data.line_items],
                quote_data.payment_terms,
                quote_data.warranty_terms,
                quote_data.smart_contract_enabled,
                QuoteStatus.DRAFT.value,
                now,
                now
            )
            
            return SupplierQuote(**dict(result))
            
        except Exception as e:
            logger.error(f"Error creating quote: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to create quote")
    
    async def get_quote_by_id(self, quote_id: str, user_id: str) -> Optional[SupplierQuote]:
        """Get quote by ID (with user access control)"""
        try:
            conn = await self.get_connection()
            
            query = """
                SELECT * FROM supplier_quotes 
                WHERE (id = $1 OR quote_id = $1) AND supplier_id = $2
            """
            result = await conn.fetchrow(query, quote_id, user_id)
            
            if result:
                return SupplierQuote(**dict(result))
            return None
            
        except Exception as e:
            logger.error(f"Error fetching quote {quote_id}: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to fetch quote")
    
    async def get_quotes_by_supplier(
        self,
        supplier_id: str,
        page: int = 1,
        limit: int = 20,
        status: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get quotes by supplier with pagination"""
        try:
            conn = await self.get_connection()
            
            # Build WHERE clause
            where_conditions = ["supplier_id = $1"]
            params = [supplier_id]
            param_count = 1
            
            if status:
                param_count += 1
                where_conditions.append(f"status = ${param_count}")
                params.append(status)
            
            where_clause = " AND ".join(where_conditions)
            
            # Count total records
            count_query = f"SELECT COUNT(*) FROM supplier_quotes WHERE {where_clause}"
            total = await conn.fetchval(count_query, *params)
            
            # Get paginated results
            offset = (page - 1) * limit
            param_count += 1
            params.append(limit)
            param_count += 1
            params.append(offset)
            
            query = f"""
                SELECT sq.*, t.title as tender_title, t.organization as tender_organization
                FROM supplier_quotes sq
                LEFT JOIN tenders t ON sq.tender_id = t.id
                WHERE {where_clause}
                ORDER BY sq.created_at DESC
                LIMIT ${param_count - 1} OFFSET ${param_count}
            """
            
            results = await conn.fetch(query, *params)
            quotes = [SupplierQuote(**dict(row)) for row in results]
            
            return {
                "quotes": quotes,
                "total": total,
                "page": page,
                "limit": limit,
                "has_more": (page * limit) < total
            }
            
        except Exception as e:
            logger.error(f"Error fetching supplier quotes: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to fetch quotes")
    
    async def submit_quote(self, quote_id: str, supplier_id: str) -> SupplierQuote:
        """Submit a quote (change status from draft to submitted)"""
        try:
            conn = await self.get_connection()
            
            query = """
                UPDATE supplier_quotes 
                SET status = $1, submission_date = $2, updated_at = $2
                WHERE (id = $3 OR quote_id = $3) AND supplier_id = $4 AND status = 'draft'
                RETURNING *
            """
            
            now = datetime.now(timezone.utc)
            result = await conn.fetchrow(query, QuoteStatus.SUBMITTED.value, now, quote_id, supplier_id)
            
            if result:
                return SupplierQuote(**dict(result))
            else:
                raise HTTPException(status_code=404, detail="Quote not found or already submitted")
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error submitting quote: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to submit quote")
    
    # =====================================================
    # BID OPERATIONS
    # =====================================================
    
    async def create_bid(self, bid_data: BidCreateRequest, bidder_id: str) -> TenderBid:
        """Create a new tender bid"""
        try:
            conn = await self.get_connection()
            
            # Generate bid ID
            bid_id = f"BID-{datetime.now().strftime('%Y%m%d')}-{bidder_id[:8]}"
            
            query = """
                INSERT INTO tender_bids (
                    bid_id, tender_id, bidder_id, bid_amount, currency,
                    submission_method, status, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                RETURNING *
            """
            
            now = datetime.now(timezone.utc)
            
            result = await conn.fetchrow(
                query,
                bid_id,
                bid_data.tender_id,
                bidder_id,
                bid_data.bid_amount,
                bid_data.currency,
                bid_data.submission_method.value,
                BidStatus.SUBMITTED.value,
                now,
                now
            )
            
            # Update tender bid count
            await self.update_tender_metrics(bid_data.tender_id, "bid")
            
            return TenderBid(**dict(result))
            
        except Exception as e:
            logger.error(f"Error creating bid: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to create bid")
    
    async def get_bids_by_bidder(
        self,
        bidder_id: str,
        page: int = 1,
        limit: int = 20,
        status: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get bids by bidder with pagination"""
        try:
            conn = await self.get_connection()
            
            # Build WHERE clause
            where_conditions = ["bidder_id = $1"]
            params = [bidder_id]
            param_count = 1
            
            if status:
                param_count += 1
                where_conditions.append(f"status = ${param_count}")
                params.append(status)
            
            where_clause = " AND ".join(where_conditions)
            
            # Count total records
            count_query = f"SELECT COUNT(*) FROM tender_bids WHERE {where_clause}"
            total = await conn.fetchval(count_query, *params)
            
            # Get paginated results
            offset = (page - 1) * limit
            param_count += 1
            params.append(limit)
            param_count += 1
            params.append(offset)
            
            query = f"""
                SELECT tb.*, t.title as tender_title, t.organization as tender_organization
                FROM tender_bids tb
                LEFT JOIN tenders t ON tb.tender_id = t.id
                WHERE {where_clause}
                ORDER BY tb.created_at DESC
                LIMIT ${param_count - 1} OFFSET ${param_count}
            """
            
            results = await conn.fetch(query, *params)
            bids = [TenderBid(**dict(row)) for row in results]
            
            return {
                "bids": bids,
                "total": total,
                "page": page,
                "limit": limit,
                "has_more": (page * limit) < total
            }
            
        except Exception as e:
            logger.error(f"Error fetching bidder bids: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to fetch bids")

# Global service instance
tender_service = TenderService()
