"""
Background AI Compliance Service
Silent compliance monitoring that only surfaces when action is needed
- Runs continuously in background
- Only alerts when compliance issues detected
- Automatically assigns bee workers when evidence needed
- No frontend interface - pure AI-driven service
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json
from dataclasses import dataclass
from supabase import create_client, Client
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Supabase client
supabase: Client = create_client(
    os.getenv("SUPABASE_URL", ""),
    os.getenv("SUPABASE_SERVICE_KEY", "")
)

@dataclass
class ComplianceIssue:
    """Data class for compliance issues"""
    tender_id: str
    issue_type: str
    severity: str
    description: str
    protest_viability: float
    deadline: datetime
    evidence_required: List[str]
    legal_framework: List[str]

@dataclass
class ProtestCase:
    """Data class for protest cases"""
    id: str
    tender_id: str
    user_id: str
    issue_type: str
    protest_letter: str
    evidence_collected: List[str]
    status: str
    success_probability: float
    assigned_bee_workers: List[str]

class BackgroundComplianceAI:
    """Background AI service for silent compliance monitoring and automated response"""
    
    def __init__(self):
        self.protest_templates = {}
        self.legal_frameworks = {}
        self.compliance_checkers = {}
        
    async def start_background_monitoring(self):
        """Start silent background compliance monitoring"""

        logger.info("Starting background AI compliance service...")

        while True:
            try:
                # Silent background operations
                await self._auto_check_bid_submissions()
                await self._monitor_award_announcements()
                await self._auto_assign_compliance_tasks()
                await self._check_compliance_deadlines()
                await asyncio.sleep(300)  # Check every 5 minutes

            except Exception as e:
                logger.error(f"Error in background compliance monitoring: {e}")
                await asyncio.sleep(60)

    async def _auto_check_bid_submissions(self):
        """Automatically check all new bid submissions for compliance"""

        try:
            # Get recent bid submissions that haven't been compliance checked
            result = supabase.table("bid_tracking").select("*").eq("compliance_checked", False).execute()

            unchecked_bids = result.data or []

            for bid in unchecked_bids:
                try:
                    await self._silent_compliance_check(bid)
                except Exception as e:
                    logger.error(f"Error checking bid compliance: {e}")

        except Exception as e:
            logger.error(f"Error in auto bid compliance checking: {e}")

    async def _silent_compliance_check(self, bid: Dict):
        """Silently check bid compliance and only alert if issues found"""

        try:
            compliance_issues = []

            # AI checks bid documents automatically
            document_issues = await self._ai_check_bid_documents(bid)
            compliance_issues.extend(document_issues)

            # Check submission completeness
            completeness_issues = await self._check_submission_completeness(bid)
            compliance_issues.extend(completeness_issues)

            # Mark as checked
            supabase.table("bid_tracking").update({
                "compliance_checked": True,
                "compliance_check_date": datetime.now().isoformat()
            }).eq("id", bid['id']).execute()

            # Only surface if issues found
            if compliance_issues:
                await self._surface_compliance_issues(bid, compliance_issues)
            else:
                # Silent success - no user notification needed
                logger.info(f"Bid {bid['bid_reference']} passed compliance check silently")

        except Exception as e:
            logger.error(f"Error in silent compliance check: {e}")

    async def _ai_check_bid_documents(self, bid: Dict) -> List[ComplianceIssue]:
        """AI automatically checks bid documents for compliance"""

        issues = []

        try:
            # Simulate AI document analysis
            # In real implementation, this would use document AI/OCR

            # Check for required documents
            required_docs = ['bbbee_certificate', 'cidb_certificate', 'tax_clearance', 'company_registration']
            submitted_docs = bid.get('submitted_documents', [])

            for doc_type in required_docs:
                if doc_type not in submitted_docs:
                    issues.append(ComplianceIssue(
                        tender_id=bid['opportunity_id'],
                        issue_type=f'missing_{doc_type}',
                        severity='high',
                        description=f"Required document missing: {doc_type.replace('_', ' ').title()}",
                        protest_viability=0.0,  # Not a protest issue, submission issue
                        deadline=datetime.fromisoformat(bid['closing_date']),
                        evidence_required=[],
                        legal_framework=[]
                    ))

        except Exception as e:
            logger.error(f"Error in AI document checking: {e}")

        return issues

    async def _surface_compliance_issues(self, bid: Dict, issues: List[ComplianceIssue]):
        """Surface compliance issues through existing alert system"""

        try:
            for issue in issues:
                # Create alert through existing bid update system
                update_data = {
                    "bid_tracking_id": bid['id'],
                    "update_type": "compliance_issue",
                    "update_priority": "high" if issue.severity == 'critical' else "medium",
                    "title": f"⚠️ Compliance Issue: {issue.description}",
                    "description": f"AI detected compliance issue with your bid submission. {issue.description}",
                    "source_url": "",
                    "source_type": "ai_compliance_check",
                    "confidence_score": 0.95,
                    "detected_at": datetime.now().isoformat()
                }

                supabase.table("bid_updates").insert(update_data).execute()

                # Auto-assign bee worker if document collection needed
                if 'missing_' in issue.issue_type:
                    await self._auto_assign_document_collection(bid, issue)

        except Exception as e:
            logger.error(f"Error surfacing compliance issues: {e}")

    async def _auto_assign_document_collection(self, bid: Dict, issue: ComplianceIssue):
        """Automatically assign bee worker for missing document collection"""

        try:
            # Create bee worker task automatically
            task_data = {
                "user_id": bid['user_id'],
                "tender_id": bid['opportunity_id'],
                "task_type": "compliance_verification",
                "title": f"Collect Missing Document: {issue.issue_type.replace('missing_', '').replace('_', ' ').title()}",
                "description": f"AI detected missing compliance document. Collect and verify {issue.description}",
                "status": "auto_assigned",
                "priority": "high",
                "deadline": issue.deadline.isoformat(),
                "auto_assigned": True,
                "created_at": datetime.now().isoformat()
            }

            result = supabase.table("bee_worker_tasks").insert(task_data).execute()

            if result.data:
                # Send alert about auto-assignment
                update_data = {
                    "bid_tracking_id": bid['id'],
                    "update_type": "auto_task_assigned",
                    "update_priority": "medium",
                    "title": "🐝 Bee Worker Auto-Assigned",
                    "description": f"AI automatically assigned bee worker to collect missing document: {issue.description}",
                    "source_url": f"/bee-tasks/{result.data[0]['id']}",
                    "source_type": "ai_auto_assignment",
                    "detected_at": datetime.now().isoformat()
                }

                supabase.table("bid_updates").insert(update_data).execute()

        except Exception as e:
            logger.error(f"Error auto-assigning document collection: {e}")

    async def _auto_assign_compliance_tasks(self):
        """Automatically assign compliance-related bee worker tasks"""

        try:
            # Get protest cases that need evidence collection
            result = supabase.table("protest_cases").select("*").eq("status", "evidence_needed").execute()

            evidence_needed = result.data or []

            for case in evidence_needed:
                await self._auto_assign_evidence_collection(case)

        except Exception as e:
            logger.error(f"Error in auto compliance task assignment: {e}")

    async def _auto_assign_evidence_collection(self, protest_case: Dict):
        """Automatically assign evidence collection for protest cases"""

        try:
            # Create evidence collection task automatically
            task_data = {
                "user_id": protest_case['user_id'],
                "tender_id": protest_case['tender_id'],
                "task_type": "protest_evidence",
                "title": f"Evidence Collection: {protest_case['issue_type']}",
                "description": f"AI detected protest opportunity. Collect evidence for: {protest_case['description']}",
                "status": "auto_assigned",
                "priority": "high",
                "deadline": protest_case['deadline'],
                "auto_assigned": True,
                "protest_case_id": protest_case['id'],
                "created_at": datetime.now().isoformat()
            }

            result = supabase.table("bee_worker_tasks").insert(task_data).execute()

            if result.data:
                # Update protest case status
                supabase.table("protest_cases").update({
                    "status": "evidence_collection_assigned"
                }).eq("id", protest_case['id']).execute()

                # Send alert about evidence collection
                update_data = {
                    "bid_tracking_id": protest_case['tender_id'],
                    "update_type": "evidence_collection_started",
                    "update_priority": "medium",
                    "title": "🕵️ Evidence Collection Started",
                    "description": f"AI assigned bee worker to collect evidence for {protest_case['issue_type']} protest",
                    "source_url": f"/bee-tasks/{result.data[0]['id']}",
                    "source_type": "ai_auto_assignment",
                    "detected_at": datetime.now().isoformat()
                }

                supabase.table("bid_updates").insert(update_data).execute()

        except Exception as e:
            logger.error(f"Error auto-assigning evidence collection: {e}")

    async def _monitor_award_announcements(self):
        """Monitor award announcements for compliance issues"""
        
        try:
            # Get recent bid updates for awards
            result = supabase.table("bid_updates").select("*").eq("update_type", "award_announcement").gte("detected_at", (datetime.now() - timedelta(hours=24)).isoformat()).execute()
            
            recent_awards = result.data or []
            
            for award in recent_awards:
                try:
                    await self._analyze_award_compliance(award)
                except Exception as e:
                    logger.error(f"Error analyzing award compliance: {e}")
                    
        except Exception as e:
            logger.error(f"Error monitoring award announcements: {e}")

    async def _analyze_award_compliance(self, award: Dict):
        """Analyze award for compliance issues"""
        
        bid_tracking_id = award['bid_tracking_id']
        
        try:
            # Get bid tracking info
            bid_result = supabase.table("bid_tracking").select("*").eq("id", bid_tracking_id).execute()
            
            if not bid_result.data:
                return
            
            bid_info = bid_result.data[0]
            
            # Analyze for various compliance issues
            compliance_issues = []
            
            # 1. B-BBEE Compliance Analysis
            bbbee_issues = await self._check_bbbee_compliance(bid_info, award)
            compliance_issues.extend(bbbee_issues)
            
            # 2. CIDB Compliance Analysis
            cidb_issues = await self._check_cidb_compliance(bid_info, award)
            compliance_issues.extend(cidb_issues)
            
            # 3. Procedural Compliance Analysis
            procedural_issues = await self._check_procedural_compliance(bid_info, award)
            compliance_issues.extend(procedural_issues)
            
            # 4. SME Set-aside Analysis
            sme_issues = await self._check_sme_compliance(bid_info, award)
            compliance_issues.extend(sme_issues)
            
            # Process identified issues
            for issue in compliance_issues:
                if issue.protest_viability >= 0.6:  # 60% threshold for viable protests
                    await self._create_protest_opportunity(issue, bid_info)
                    
        except Exception as e:
            logger.error(f"Error analyzing award compliance: {e}")

    async def _check_bbbee_compliance(self, bid_info: Dict, award: Dict) -> List[ComplianceIssue]:
        """Check B-BBEE compliance issues"""
        
        issues = []
        
        try:
            # Simulate B-BBEE compliance checking
            # In real implementation, this would integrate with B-BBEE verification APIs
            
            # Example: Check if winner's B-BBEE level matches claimed points
            if 'winner_bbbee_level' in award and 'winner_bbbee_points' in award:
                claimed_points = award.get('winner_bbbee_points', 0)
                actual_level = award.get('winner_bbbee_level', 8)
                
                # B-BBEE point allocation (simplified)
                level_points = {1: 20, 2: 18, 3: 14, 4: 12, 5: 8, 6: 6, 7: 4, 8: 2}
                expected_points = level_points.get(actual_level, 0)
                
                if claimed_points > expected_points:
                    issues.append(ComplianceIssue(
                        tender_id=bid_info['opportunity_id'],
                        issue_type='bbbee_scoring_error',
                        severity='high',
                        description=f"Winner claimed {claimed_points} B-BBEE points but Level {actual_level} certificate only allows {expected_points} points",
                        protest_viability=0.85,
                        deadline=datetime.now() + timedelta(days=14),
                        evidence_required=['bbbee_certificate', 'scoring_sheet', 'evaluation_report'],
                        legal_framework=['PPPFA', 'B-BBEE_Act']
                    ))
                    
        except Exception as e:
            logger.error(f"Error checking B-BBEE compliance: {e}")
        
        return issues

    async def _check_cidb_compliance(self, bid_info: Dict, award: Dict) -> List[ComplianceIssue]:
        """Check CIDB compliance issues"""
        
        issues = []
        
        try:
            # Check CIDB grade requirements vs winner's grade
            if 'required_cidb_grade' in bid_info and 'winner_cidb_grade' in award:
                required_grade = bid_info.get('required_cidb_grade', 9)
                winner_grade = award.get('winner_cidb_grade', 9)
                
                if winner_grade > required_grade:  # Higher grade number = lower grade
                    issues.append(ComplianceIssue(
                        tender_id=bid_info['opportunity_id'],
                        issue_type='cidb_grade_mismatch',
                        severity='critical',
                        description=f"Winner has CIDB Grade {winner_grade} but tender required Grade {required_grade} or higher",
                        protest_viability=0.95,
                        deadline=datetime.now() + timedelta(days=14),
                        evidence_required=['cidb_certificate', 'tender_specification', 'evaluation_criteria'],
                        legal_framework=['CIDB_Act', 'PPPFA']
                    ))
                    
        except Exception as e:
            logger.error(f"Error checking CIDB compliance: {e}")
        
        return issues

    async def _check_procedural_compliance(self, bid_info: Dict, award: Dict) -> List[ComplianceIssue]:
        """Check procedural compliance issues"""
        
        issues = []
        
        try:
            # Check evaluation timeline
            submission_date = datetime.fromisoformat(bid_info['closing_date'])
            award_date = datetime.fromisoformat(award['detected_at'])
            evaluation_days = (award_date - submission_date).days
            
            # Check if evaluation was too fast (potential red flag)
            if evaluation_days < 7:
                issues.append(ComplianceIssue(
                    tender_id=bid_info['opportunity_id'],
                    issue_type='rushed_evaluation',
                    severity='medium',
                    description=f"Evaluation completed in {evaluation_days} days, which may indicate inadequate evaluation process",
                    protest_viability=0.65,
                    deadline=datetime.now() + timedelta(days=14),
                    evidence_required=['evaluation_timeline', 'evaluation_committee_minutes'],
                    legal_framework=['MFMA', 'PFMA', 'PPPFA']
                ))
                
        except Exception as e:
            logger.error(f"Error checking procedural compliance: {e}")
        
        return issues

    async def _check_sme_compliance(self, bid_info: Dict, award: Dict) -> List[ComplianceIssue]:
        """Check SME set-aside compliance"""
        
        issues = []
        
        try:
            # Check if tender was SME set-aside but awarded to large enterprise
            if bid_info.get('sme_set_aside', False) and award.get('winner_enterprise_size') == 'large':
                issues.append(ComplianceIssue(
                    tender_id=bid_info['opportunity_id'],
                    issue_type='sme_set_aside_violation',
                    severity='critical',
                    description="Tender was designated for SME set-aside but awarded to large enterprise",
                    protest_viability=0.90,
                    deadline=datetime.now() + timedelta(days=14),
                    evidence_required=['tender_advertisement', 'winner_company_registration', 'sme_declaration'],
                    legal_framework=['PPPFA', 'SME_Development_Act']
                ))
                
        except Exception as e:
            logger.error(f"Error checking SME compliance: {e}")
        
        return issues

    async def _create_protest_opportunity(self, issue: ComplianceIssue, bid_info: Dict):
        """Create protest opportunity and assign bee workers"""
        
        try:
            # Generate automated protest letter
            protest_letter = await self._generate_protest_letter(issue, bid_info)
            
            # Create protest case record
            protest_data = {
                "tender_id": issue.tender_id,
                "user_id": bid_info['user_id'],
                "issue_type": issue.issue_type,
                "severity": issue.severity,
                "description": issue.description,
                "protest_viability": issue.protest_viability,
                "deadline": issue.deadline.isoformat(),
                "evidence_required": issue.evidence_required,
                "legal_framework": issue.legal_framework,
                "protest_letter": protest_letter,
                "status": "opportunity_identified",
                "created_at": datetime.now().isoformat()
            }
            
            result = supabase.table("protest_cases").insert(protest_data).execute()
            
            if result.data:
                protest_case_id = result.data[0]['id']
                
                # Create bee worker tasks for evidence collection
                await self._create_evidence_collection_tasks(protest_case_id, issue)
                
                # Send alert to user about protest opportunity
                await self._send_protest_opportunity_alert(bid_info['user_id'], issue, protest_case_id)
                
                logger.info(f"Created protest opportunity for {issue.issue_type} with {issue.protest_viability*100}% viability")
                
        except Exception as e:
            logger.error(f"Error creating protest opportunity: {e}")

    async def _generate_protest_letter(self, issue: ComplianceIssue, bid_info: Dict) -> str:
        """Generate automated protest letter"""
        
        try:
            # Load protest letter template based on issue type
            template = await self._get_protest_template(issue.issue_type)
            
            # Fill template with case-specific information
            protest_letter = template.format(
                tender_reference=bid_info['bid_reference'],
                organization=bid_info['organization_name'],
                issue_description=issue.description,
                legal_framework=', '.join(issue.legal_framework),
                deadline=issue.deadline.strftime('%Y-%m-%d'),
                evidence_list=', '.join(issue.evidence_required)
            )
            
            return protest_letter
            
        except Exception as e:
            logger.error(f"Error generating protest letter: {e}")
            return "Protest letter generation failed"

    async def _get_protest_template(self, issue_type: str) -> str:
        """Get protest letter template for issue type"""
        
        templates = {
            'bbbee_scoring_error': """
FORMAL PROTEST: B-BBEE SCORING IRREGULARITY

Tender Reference: {tender_reference}
Procuring Entity: {organization}

Dear Sir/Madam,

We hereby lodge a formal protest regarding the above-mentioned tender award based on B-BBEE scoring irregularities.

GROUNDS FOR PROTEST:
{issue_description}

LEGAL FRAMEWORK:
This protest is lodged in terms of {legal_framework} and the applicable procurement regulations.

EVIDENCE REQUIRED:
We request access to the following documents: {evidence_list}

We request that this matter be investigated and the award be reviewed accordingly.

Yours faithfully,
[Bidder Name]
Date: {deadline}
            """,
            
            'cidb_grade_mismatch': """
FORMAL PROTEST: CIDB GRADE NON-COMPLIANCE

Tender Reference: {tender_reference}
Procuring Entity: {organization}

Dear Sir/Madam,

We hereby lodge a formal protest regarding CIDB grade non-compliance in the above tender award.

GROUNDS FOR PROTEST:
{issue_description}

LEGAL FRAMEWORK:
This protest is lodged in terms of {legal_framework}.

EVIDENCE REQUIRED:
{evidence_list}

We request immediate review of the award decision.

Yours faithfully,
[Bidder Name]
Date: {deadline}
            """
        }
        
        return templates.get(issue_type, templates['bbbee_scoring_error'])

    async def _create_evidence_collection_tasks(self, protest_case_id: str, issue: ComplianceIssue):
        """Create bee worker tasks for evidence collection"""
        
        try:
            # Create evidence collection task
            task_data = {
                "protest_case_id": protest_case_id,
                "task_type": "protest_evidence",
                "title": f"Evidence Collection: {issue.issue_type}",
                "description": f"Collect evidence for {issue.description}",
                "evidence_required": issue.evidence_required,
                "deadline": issue.deadline.isoformat(),
                "status": "pending_assignment",
                "created_at": datetime.now().isoformat()
            }
            
            supabase.table("compliance_tasks").insert(task_data).execute()
            
            # Create legal document delivery task
            delivery_task_data = {
                "protest_case_id": protest_case_id,
                "task_type": "legal_document_delivery",
                "title": f"Protest Letter Delivery: {issue.issue_type}",
                "description": "Deliver formal protest letter to procuring entity",
                "deadline": (issue.deadline - timedelta(days=2)).isoformat(),  # 2 days before deadline
                "status": "pending_assignment",
                "created_at": datetime.now().isoformat()
            }
            
            supabase.table("compliance_tasks").insert(delivery_task_data).execute()
            
        except Exception as e:
            logger.error(f"Error creating evidence collection tasks: {e}")

    async def _send_protest_opportunity_alert(self, user_id: str, issue: ComplianceIssue, protest_case_id: str):
        """Send alert about protest opportunity"""
        
        try:
            # Create bid update for protest opportunity
            update_data = {
                "bid_tracking_id": issue.tender_id,
                "update_type": "protest_opportunity",
                "update_priority": "high" if issue.protest_viability >= 0.8 else "medium",
                "title": f"Protest Opportunity Detected: {issue.issue_type}",
                "description": f"{issue.description} - {int(issue.protest_viability*100)}% success probability",
                "source_url": f"/compliance/protests/{protest_case_id}",
                "source_type": "compliance_analysis",
                "competitor_info": {
                    "issue_type": issue.issue_type,
                    "severity": issue.severity,
                    "protest_viability": issue.protest_viability,
                    "deadline": issue.deadline.isoformat()
                },
                "confidence_score": 0.95,
                "detected_at": datetime.now().isoformat()
            }
            
            supabase.table("bid_updates").insert(update_data).execute()
            
            logger.info(f"Sent protest opportunity alert to user {user_id}")
            
        except Exception as e:
            logger.error(f"Error sending protest opportunity alert: {e}")

    async def _check_compliance_deadlines(self):
        """Check for approaching compliance deadlines"""
        
        try:
            # Get protest cases with approaching deadlines
            deadline_threshold = datetime.now() + timedelta(days=3)
            
            result = supabase.table("protest_cases").select("*").eq("status", "active").lte("deadline", deadline_threshold.isoformat()).execute()
            
            approaching_deadlines = result.data or []
            
            for case in approaching_deadlines:
                await self._send_deadline_reminder(case)
                
        except Exception as e:
            logger.error(f"Error checking compliance deadlines: {e}")

    async def _send_deadline_reminder(self, protest_case: Dict):
        """Send deadline reminder for protest case"""
        
        try:
            days_remaining = (datetime.fromisoformat(protest_case['deadline']) - datetime.now()).days
            
            # Create urgent alert
            update_data = {
                "bid_tracking_id": protest_case['tender_id'],
                "update_type": "protest_deadline",
                "update_priority": "critical" if days_remaining <= 1 else "high",
                "title": f"Protest Deadline Approaching: {days_remaining} days remaining",
                "description": f"Protest for {protest_case['issue_type']} must be submitted by {protest_case['deadline']}",
                "source_url": f"/compliance/protests/{protest_case['id']}",
                "source_type": "deadline_monitoring",
                "detected_at": datetime.now().isoformat()
            }
            
            supabase.table("bid_updates").insert(update_data).execute()
            
        except Exception as e:
            logger.error(f"Error sending deadline reminder: {e}")

    async def _process_pending_protests(self):
        """Process pending protest cases"""
        
        try:
            # Get protest cases that need processing
            result = supabase.table("protest_cases").select("*").eq("status", "pending_submission").execute()
            
            pending_protests = result.data or []
            
            for protest in pending_protests:
                await self._process_protest_submission(protest)
                
        except Exception as e:
            logger.error(f"Error processing pending protests: {e}")

    async def _process_protest_submission(self, protest_case: Dict):
        """Process protest submission"""
        
        try:
            # Check if all evidence has been collected
            evidence_result = supabase.table("compliance_tasks").select("*").eq("protest_case_id", protest_case['id']).eq("task_type", "protest_evidence").execute()
            
            evidence_tasks = evidence_result.data or []
            completed_evidence = [task for task in evidence_tasks if task['status'] == 'completed']
            
            if len(completed_evidence) >= len(evidence_tasks) * 0.8:  # 80% evidence collected
                # Mark ready for submission
                supabase.table("protest_cases").update({
                    "status": "ready_for_submission",
                    "updated_at": datetime.now().isoformat()
                }).eq("id", protest_case['id']).execute()
                
                # Send submission ready alert
                await self._send_submission_ready_alert(protest_case)
                
        except Exception as e:
            logger.error(f"Error processing protest submission: {e}")

    async def _send_submission_ready_alert(self, protest_case: Dict):
        """Send alert that protest is ready for submission"""
        
        try:
            update_data = {
                "bid_tracking_id": protest_case['tender_id'],
                "update_type": "protest_ready",
                "update_priority": "high",
                "title": "Protest Ready for Submission",
                "description": f"Evidence collection complete for {protest_case['issue_type']} protest. Ready to submit.",
                "source_url": f"/compliance/protests/{protest_case['id']}",
                "source_type": "protest_management",
                "detected_at": datetime.now().isoformat()
            }
            
            supabase.table("bid_updates").insert(update_data).execute()
            
        except Exception as e:
            logger.error(f"Error sending submission ready alert: {e}")

# Service instance
background_compliance_ai = BackgroundComplianceAI()

# Main function to start background compliance AI
async def start_background_compliance():
    """Start the background AI compliance service"""
    await background_compliance_ai.start_background_monitoring()

if __name__ == "__main__":
    asyncio.run(start_background_compliance())
