"""
WhatsApp Auto-Bidding Engine
Receives bid notifications via WhatsApp and triggers automated bidding
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, <PERSON>
from typing import Dict, List, Optional, Union
import uuid
import json
import logging
import re
from datetime import datetime, timedelta
import asyncio
import requests
from enum import Enum

# Supabase client
from supabase import create_client, Client
import os

# Initialize Supabase
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_ANON_KEY")
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# WhatsApp Business API configuration
WHATSAPP_TOKEN = os.getenv("WHATSAPP_ACCESS_TOKEN")
WHATSAPP_PHONE_ID = os.getenv("WHATSAPP_PHONE_NUMBER_ID")
WHATSAPP_VERIFY_TOKEN = os.getenv("WHATSAPP_VERIFY_TOKEN")

# Logging setup
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="BidBeez WhatsApp Auto-Bidding Engine",
    description="Automated bidding triggered by WhatsApp notifications",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# =====================================================
# MODELS
# =====================================================

class MessageType(str, Enum):
    BID_NOTIFICATION = "bid_notification"
    TENDER_ALERT = "tender_alert"
    DEADLINE_REMINDER = "deadline_reminder"
    DOCUMENT_AVAILABLE = "document_available"
    UNKNOWN = "unknown"

class AutoBidPreference(str, Enum):
    ALWAYS = "always"
    ASK_FIRST = "ask_first"
    NEVER = "never"
    CONDITIONS_BASED = "conditions_based"

class WhatsAppMessage(BaseModel):
    message_id: str
    from_number: str
    to_number: str
    message_body: str
    timestamp: datetime
    message_type: MessageType = MessageType.UNKNOWN
    extracted_data: Dict = {}

class BidNotificationData(BaseModel):
    tender_id: Optional[str] = None
    tender_title: Optional[str] = None
    organization: Optional[str] = None
    closing_date: Optional[datetime] = None
    estimated_value: Optional[float] = None
    location: Optional[str] = None
    category: Optional[str] = None
    requirements: List[str] = []
    document_url: Optional[str] = None
    briefing_date: Optional[datetime] = None
    confidence_score: float = 0.0

class AutoBidSettings(BaseModel):
    user_id: str
    whatsapp_number: str
    auto_bid_preference: AutoBidPreference = AutoBidPreference.ASK_FIRST
    max_bid_value: Optional[float] = None
    preferred_categories: List[str] = []
    excluded_categories: List[str] = []
    minimum_confidence_score: float = 0.7
    require_feasibility_check: bool = True
    notify_on_auto_bid: bool = True

# =====================================================
# WHATSAPP MESSAGE PROCESSING ENGINE
# =====================================================

class WhatsAppMessageProcessor:
    """Processes incoming WhatsApp messages and extracts bid information"""
    
    def __init__(self):
        self.tender_patterns = [
            # Common tender notification patterns
            r'tender\s+(?:no\.?|number)?\s*:?\s*([A-Z0-9\-/]+)',
            r'rfq\s+(?:no\.?|number)?\s*:?\s*([A-Z0-9\-/]+)',
            r'quotation\s+(?:no\.?|number)?\s*:?\s*([A-Z0-9\-/]+)',
        ]
        
        self.value_patterns = [
            r'value\s*:?\s*r?\s*([0-9,]+(?:\.[0-9]{2})?)',
            r'amount\s*:?\s*r?\s*([0-9,]+(?:\.[0-9]{2})?)',
            r'budget\s*:?\s*r?\s*([0-9,]+(?:\.[0-9]{2})?)',
        ]
        
        self.date_patterns = [
            r'closing\s+date\s*:?\s*([0-9]{1,2}[/-][0-9]{1,2}[/-][0-9]{2,4})',
            r'deadline\s*:?\s*([0-9]{1,2}[/-][0-9]{1,2}[/-][0-9]{2,4})',
            r'due\s+date\s*:?\s*([0-9]{1,2}[/-][0-9]{1,2}[/-][0-9]{2,4})',
        ]
    
    async def process_message(self, message: WhatsAppMessage) -> BidNotificationData:
        """Process WhatsApp message and extract bid information"""
        try:
            text = message.message_body.lower()
            extracted_data = BidNotificationData()
            
            # Extract tender ID/number
            tender_id = self._extract_tender_id(text)
            if tender_id:
                extracted_data.tender_id = tender_id
                extracted_data.confidence_score += 0.3
            
            # Extract tender title (usually first line or after "subject:")
            title = self._extract_title(message.message_body)
            if title:
                extracted_data.tender_title = title
                extracted_data.confidence_score += 0.2
            
            # Extract organization
            organization = self._extract_organization(text)
            if organization:
                extracted_data.organization = organization
                extracted_data.confidence_score += 0.2
            
            # Extract value
            value = self._extract_value(text)
            if value:
                extracted_data.estimated_value = value
                extracted_data.confidence_score += 0.1
            
            # Extract closing date
            closing_date = self._extract_closing_date(text)
            if closing_date:
                extracted_data.closing_date = closing_date
                extracted_data.confidence_score += 0.2
            
            # Extract location
            location = self._extract_location(text)
            if location:
                extracted_data.location = location
                extracted_data.confidence_score += 0.1
            
            # Classify message type
            message.message_type = self._classify_message_type(text)
            
            logger.info(f"Processed message with confidence: {extracted_data.confidence_score}")
            return extracted_data
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            return BidNotificationData()
    
    def _extract_tender_id(self, text: str) -> Optional[str]:
        """Extract tender ID from message text"""
        for pattern in self.tender_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        return None
    
    def _extract_title(self, text: str) -> Optional[str]:
        """Extract tender title from message"""
        lines = text.split('\n')
        for line in lines:
            line = line.strip()
            if len(line) > 20 and not any(keyword in line.lower() for keyword in ['tender', 'rfq', 'closing', 'value']):
                return line[:100]  # Limit title length
        return None
    
    def _extract_organization(self, text: str) -> Optional[str]:
        """Extract organization name"""
        org_patterns = [
            r'(?:from|by|issued by)\s*:?\s*([A-Za-z\s&]+(?:municipality|council|department|ministry|agency))',
            r'((?:city|municipality|department|ministry)\s+of\s+[A-Za-z\s]+)',
        ]
        
        for pattern in org_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        return None
    
    def _extract_value(self, text: str) -> Optional[float]:
        """Extract tender value"""
        for pattern in self.value_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                value_str = match.group(1).replace(',', '')
                try:
                    return float(value_str)
                except ValueError:
                    continue
        return None
    
    def _extract_closing_date(self, text: str) -> Optional[datetime]:
        """Extract closing date"""
        for pattern in self.date_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                date_str = match.group(1)
                try:
                    # Try different date formats
                    for fmt in ['%d/%m/%Y', '%d-%m-%Y', '%d/%m/%y', '%d-%m-%y']:
                        try:
                            return datetime.strptime(date_str, fmt)
                        except ValueError:
                            continue
                except Exception:
                    continue
        return None
    
    def _extract_location(self, text: str) -> Optional[str]:
        """Extract location information"""
        location_patterns = [
            r'(?:location|venue|address)\s*:?\s*([A-Za-z\s,]+)',
            r'(?:in|at)\s+([A-Za-z\s]+(?:province|city|town))',
        ]
        
        for pattern in location_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        return None
    
    def _classify_message_type(self, text: str) -> MessageType:
        """Classify the type of message"""
        if any(keyword in text for keyword in ['tender', 'rfq', 'quotation', 'bid']):
            return MessageType.BID_NOTIFICATION
        elif any(keyword in text for keyword in ['deadline', 'closing', 'reminder']):
            return MessageType.DEADLINE_REMINDER
        elif any(keyword in text for keyword in ['document', 'available', 'download']):
            return MessageType.DOCUMENT_AVAILABLE
        else:
            return MessageType.UNKNOWN

# =====================================================
# AUTO-BIDDING ENGINE
# =====================================================

class WhatsAppAutoBidEngine:
    """Handles automatic bidding triggered by WhatsApp notifications"""
    
    def __init__(self):
        self.message_processor = WhatsAppMessageProcessor()
    
    async def process_whatsapp_notification(
        self, 
        message: WhatsAppMessage, 
        user_settings: AutoBidSettings
    ) -> Dict:
        """Process WhatsApp notification and potentially trigger auto-bid"""
        try:
            # Extract bid information from message
            bid_data = await self.message_processor.process_message(message)
            
            # Check if message meets auto-bid criteria
            should_auto_bid = await self._should_auto_bid(bid_data, user_settings)
            
            if should_auto_bid:
                # Trigger auto-bid process
                result = await self._trigger_auto_bid(bid_data, user_settings)
                
                # Send confirmation via WhatsApp
                await self._send_whatsapp_confirmation(
                    user_settings.whatsapp_number, 
                    result
                )
                
                return result
            else:
                # Send notification asking for permission
                await self._send_permission_request(
                    user_settings.whatsapp_number,
                    bid_data
                )
                
                return {
                    "status": "permission_requested",
                    "message": "Auto-bid permission requested via WhatsApp",
                    "bid_data": bid_data.dict()
                }
                
        except Exception as e:
            logger.error(f"Error processing WhatsApp notification: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def _should_auto_bid(
        self, 
        bid_data: BidNotificationData, 
        settings: AutoBidSettings
    ) -> bool:
        """Determine if auto-bid should be triggered"""
        
        # Check auto-bid preference
        if settings.auto_bid_preference == AutoBidPreference.NEVER:
            return False
        
        if settings.auto_bid_preference == AutoBidPreference.ASK_FIRST:
            return False
        
        # Check confidence score
        if bid_data.confidence_score < settings.minimum_confidence_score:
            return False
        
        # Check value limits
        if settings.max_bid_value and bid_data.estimated_value:
            if bid_data.estimated_value > settings.max_bid_value:
                return False
        
        # Check category preferences
        if settings.preferred_categories and bid_data.category:
            if bid_data.category not in settings.preferred_categories:
                return False
        
        if settings.excluded_categories and bid_data.category:
            if bid_data.category in settings.excluded_categories:
                return False
        
        return True
    
    async def _trigger_auto_bid(
        self, 
        bid_data: BidNotificationData, 
        settings: AutoBidSettings
    ) -> Dict:
        """Trigger the actual auto-bidding process"""
        try:
            # Create tender record if it doesn't exist
            tender_record = await self._create_or_update_tender(bid_data)
            
            # Run feasibility check if required
            if settings.require_feasibility_check:
                feasibility = await self._run_feasibility_check(tender_record)
                if not feasibility.get("can_autobid", False):
                    return {
                        "status": "feasibility_failed",
                        "message": "Auto-bid blocked by feasibility check",
                        "feasibility": feasibility
                    }
            
            # Trigger AI bidding engine
            ai_result = await self._trigger_ai_bidding(tender_record, settings.user_id)
            
            # Record auto-bid activity
            await self._record_auto_bid_activity(
                settings.user_id,
                tender_record["id"],
                ai_result
            )
            
            return {
                "status": "auto_bid_triggered",
                "tender_id": tender_record["id"],
                "ai_result": ai_result,
                "message": "Auto-bid successfully initiated"
            }
            
        except Exception as e:
            logger.error(f"Error triggering auto-bid: {e}")
            return {
                "status": "error",
                "message": f"Auto-bid failed: {str(e)}"
            }
    
    async def _create_or_update_tender(self, bid_data: BidNotificationData) -> Dict:
        """Create or update tender record in database"""
        tender_record = {
            "tender_id": bid_data.tender_id or f"WA-{uuid.uuid4().hex[:8]}",
            "title": bid_data.tender_title or "WhatsApp Notification",
            "organization": bid_data.organization or "Unknown",
            "estimated_value": bid_data.estimated_value,
            "closing_date": bid_data.closing_date.isoformat() if bid_data.closing_date else None,
            "location": bid_data.location,
            "category": bid_data.category,
            "requirements": bid_data.requirements,
            "source": "whatsapp_notification",
            "status": "active",
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        result = supabase.table("tenders").insert(tender_record).execute()
        return result.data[0]
    
    async def _run_feasibility_check(self, tender: Dict) -> Dict:
        """Run feasibility check via existing autobid engine"""
        try:
            # This would integrate with the existing AutobidFeasibilityEngine
            # For now, simulate the check
            return {
                "can_autobid": True,
                "overall_score": 85,
                "missing_resources": [],
                "success_probability": 75
            }
        except Exception as e:
            logger.error(f"Feasibility check failed: {e}")
            return {"can_autobid": False, "error": str(e)}
    
    async def _trigger_ai_bidding(self, tender: Dict, user_id: str) -> Dict:
        """Trigger AI bidding engine"""
        try:
            # This would integrate with the existing BidBeezAIEngine
            # For now, simulate the process
            return {
                "bid_id": f"BID-{uuid.uuid4().hex[:8]}",
                "status": "generated",
                "submission_readiness": 85,
                "estimated_completion": "2-4 hours"
            }
        except Exception as e:
            logger.error(f"AI bidding failed: {e}")
            return {"status": "failed", "error": str(e)}
    
    async def _record_auto_bid_activity(
        self, 
        user_id: str, 
        tender_id: str, 
        ai_result: Dict
    ):
        """Record auto-bid activity for tracking"""
        activity_record = {
            "user_id": user_id,
            "tender_id": tender_id,
            "trigger_source": "whatsapp",
            "ai_result": ai_result,
            "timestamp": datetime.now().isoformat()
        }
        
        supabase.table("auto_bid_activities").insert(activity_record).execute()
    
    async def _send_whatsapp_confirmation(self, phone_number: str, result: Dict):
        """Send WhatsApp confirmation message"""
        if result["status"] == "auto_bid_triggered":
            message = f"🤖 Auto-bid initiated successfully!\n\n" \
                     f"Tender: {result.get('tender_id', 'Unknown')}\n" \
                     f"Status: {result['ai_result']['status']}\n" \
                     f"Completion: {result['ai_result']['estimated_completion']}\n\n" \
                     f"Track progress in your BidBeez dashboard."
        else:
            message = f"❌ Auto-bid failed: {result.get('message', 'Unknown error')}"
        
        await self._send_whatsapp_message(phone_number, message)
    
    async def _send_permission_request(self, phone_number: str, bid_data: BidNotificationData):
        """Send WhatsApp message requesting permission to auto-bid"""
        message = f"🔔 New bid opportunity detected!\n\n" \
                 f"Title: {bid_data.tender_title or 'Unknown'}\n" \
                 f"Organization: {bid_data.organization or 'Unknown'}\n" \
                 f"Value: R{bid_data.estimated_value:,.2f}" if bid_data.estimated_value else "Value: Unknown" + "\n" \
                 f"Closing: {bid_data.closing_date.strftime('%d/%m/%Y')}" if bid_data.closing_date else "Closing: Unknown" + "\n\n" \
                 f"Reply 'YES' to auto-bid or 'NO' to skip.\n" \
                 f"Or visit your BidBeez dashboard for details."
        
        await self._send_whatsapp_message(phone_number, message)
    
    async def _send_whatsapp_message(self, phone_number: str, message: str):
        """Send WhatsApp message via Business API"""
        try:
            url = f"https://graph.facebook.com/v17.0/{WHATSAPP_PHONE_ID}/messages"
            
            headers = {
                "Authorization": f"Bearer {WHATSAPP_TOKEN}",
                "Content-Type": "application/json"
            }
            
            data = {
                "messaging_product": "whatsapp",
                "to": phone_number,
                "type": "text",
                "text": {"body": message}
            }
            
            response = requests.post(url, headers=headers, json=data)
            
            if response.status_code == 200:
                logger.info(f"WhatsApp message sent successfully to {phone_number}")
            else:
                logger.error(f"Failed to send WhatsApp message: {response.text}")
                
        except Exception as e:
            logger.error(f"Error sending WhatsApp message: {e}")

# =====================================================
# API ENDPOINTS
# =====================================================

auto_bid_engine = WhatsAppAutoBidEngine()

@app.get("/webhook")
async def verify_webhook(request: Request):
    """Verify WhatsApp webhook"""
    mode = request.query_params.get("hub.mode")
    token = request.query_params.get("hub.verify_token")
    challenge = request.query_params.get("hub.challenge")
    
    if mode == "subscribe" and token == WHATSAPP_VERIFY_TOKEN:
        logger.info("WhatsApp webhook verified successfully")
        return int(challenge)
    else:
        logger.warning("WhatsApp webhook verification failed")
        raise HTTPException(status_code=403, detail="Verification failed")

@app.post("/webhook")
async def handle_whatsapp_webhook(request: Request, background_tasks: BackgroundTasks):
    """Handle incoming WhatsApp messages"""
    try:
        data = await request.json()
        
        # Extract message data from WhatsApp webhook
        if "messages" in data.get("entry", [{}])[0].get("changes", [{}])[0].get("value", {}):
            messages = data["entry"][0]["changes"][0]["value"]["messages"]
            
            for msg in messages:
                # Create WhatsApp message object
                whatsapp_msg = WhatsAppMessage(
                    message_id=msg["id"],
                    from_number=msg["from"],
                    to_number=msg.get("to", WHATSAPP_PHONE_ID),
                    message_body=msg["text"]["body"],
                    timestamp=datetime.fromtimestamp(int(msg["timestamp"]))
                )
                
                # Process message in background
                background_tasks.add_task(
                    process_message_background,
                    whatsapp_msg
                )
        
        return {"status": "received"}
        
    except Exception as e:
        logger.error(f"Error handling WhatsApp webhook: {e}")
        return {"status": "error", "message": str(e)}

async def process_message_background(message: WhatsAppMessage):
    """Background task to process WhatsApp message"""
    try:
        # Get user settings based on phone number
        user_settings = await get_user_auto_bid_settings(message.from_number)
        
        if user_settings:
            # Process the notification
            result = await auto_bid_engine.process_whatsapp_notification(
                message, 
                user_settings
            )
            logger.info(f"Processed WhatsApp notification: {result}")
        else:
            logger.warning(f"No auto-bid settings found for {message.from_number}")
            
    except Exception as e:
        logger.error(f"Error in background message processing: {e}")

async def get_user_auto_bid_settings(phone_number: str) -> Optional[AutoBidSettings]:
    """Get user auto-bid settings from database"""
    try:
        result = supabase.table("auto_bid_settings").select("*").eq("whatsapp_number", phone_number).execute()
        
        if result.data:
            return AutoBidSettings(**result.data[0])
        return None
        
    except Exception as e:
        logger.error(f"Error fetching user settings: {e}")
        return None

@app.post("/settings/auto-bid")
async def update_auto_bid_settings(settings: AutoBidSettings):
    """Update user auto-bid settings"""
    try:
        result = supabase.table("auto_bid_settings").upsert(settings.dict()).execute()
        return {"status": "updated", "settings": result.data[0]}
        
    except Exception as e:
        logger.error(f"Error updating settings: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy", 
        "timestamp": datetime.now().isoformat(),
        "whatsapp_configured": bool(WHATSAPP_TOKEN and WHATSAPP_PHONE_ID)
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8007)
