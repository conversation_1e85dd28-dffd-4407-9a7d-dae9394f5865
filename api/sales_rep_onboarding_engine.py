"""
Sales Rep Self-Onboarding Engine with Psychological Company Pathway
Implements freemium psychology model with non-frustrating company onboarding
"""

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, <PERSON>
from typing import Dict, List, Optional, Union
import uuid
import json
import logging
from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decimal
from enum import Enum
import asyncio

# Supabase client
from supabase import create_client, Client
import os

# Initialize Supabase
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_ANON_KEY")
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# Logging setup
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="BidBeez Sales Rep Self-Onboarding Engine",
    description="Psychological onboarding system with company pathway",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# =====================================================
# ONBOARDING MODELS
# =====================================================

class RepOnboardingState(str, Enum):
    DISCOVERY = "discovery"                    # Landing page visitor
    HOOKED = "hooked"                         # Completed psychological quiz
    ENGAGED = "engaged"                       # Set first target, using features
    INVESTED = "invested"                     # 1+ weeks usage, achievements unlocked
    COMPANY_CURIOUS = "company_curious"       # Shown company benefits
    COMPANY_MOTIVATED = "company_motivated"   # Actively considering company onboarding
    COMPANY_ONBOARDING = "company_onboarding" # In company verification process
    FULLY_INTEGRATED = "fully_integrated"     # Complete company access

class FeatureTier(str, Enum):
    SOLO_REP = "solo_rep"
    TEAM_REP = "team_rep"
    ENTERPRISE_REP = "enterprise_rep"

class OnboardingQuizResponse(BaseModel):
    question_id: str
    response: Union[str, int, List[str]]
    response_time_ms: int
    confidence_level: Optional[int] = None

class InstantOnboardingData(BaseModel):
    email: str
    first_name: str
    last_name: str
    phone: Optional[str] = None
    quiz_responses: List[OnboardingQuizResponse]
    referral_source: Optional[str] = None
    utm_data: Optional[Dict] = None

class CompanyOnboardingData(BaseModel):
    company_name: str
    industry: str
    company_size: str  # "1-10", "11-50", "51-200", "201-1000", "1000+"
    annual_revenue: Optional[str] = None
    rep_role: str  # "sales_rep", "sales_manager", "sales_director", "vp_sales"
    authorization_level: str  # "can_authorize", "need_approval", "not_sure"

# =====================================================
# ONBOARDING PROGRESSION ENGINE
# =====================================================

class OnboardingProgressionEngine:
    def __init__(self):
        self.feature_tiers = {
            FeatureTier.SOLO_REP: {
                "target_tracking": True,
                "basic_achievements": True,
                "supplier_quotes_per_month": 5,
                "leaderboard_access": "anonymous_only",
                "analytics": "basic",
                "team_features": False,
                "api_access": False,
                "custom_branding": False
            },
            FeatureTier.TEAM_REP: {
                "target_tracking": True,
                "basic_achievements": True,
                "team_achievements": True,
                "supplier_quotes_per_month": 25,
                "leaderboard_access": "team_comparison",
                "analytics": "advanced",
                "team_features": "limited",
                "api_access": False,
                "custom_branding": False
            },
            FeatureTier.ENTERPRISE_REP: {
                "target_tracking": True,
                "basic_achievements": True,
                "team_achievements": True,
                "custom_achievements": True,
                "supplier_quotes_per_month": -1,  # Unlimited
                "leaderboard_access": "full",
                "analytics": "enterprise",
                "team_features": True,
                "api_access": True,
                "custom_branding": True,
                "white_label": True
            }
        }

    async def instant_onboard_rep(self, onboarding_data: InstantOnboardingData) -> Dict:
        """Complete instant onboarding with psychological profiling"""
        try:
            rep_id = str(uuid.uuid4())
            user_id = str(uuid.uuid4())  # Would integrate with auth system

            # Analyze psychological profile from quiz
            psychological_profile = await self.analyze_onboarding_quiz(onboarding_data.quiz_responses)

            # Create rep profile
            rep_profile = {
                "rep_id": rep_id,
                "user_id": user_id,
                "email": onboarding_data.email,
                "first_name": onboarding_data.first_name,
                "last_name": onboarding_data.last_name,
                "phone": onboarding_data.phone,
                "onboarding_state": RepOnboardingState.HOOKED,
                "feature_tier": FeatureTier.SOLO_REP,
                "psychological_profile": psychological_profile,
                "onboarding_completed_at": datetime.now().isoformat(),
                "referral_source": onboarding_data.referral_source,
                "utm_data": onboarding_data.utm_data or {},
                "created_at": datetime.now().isoformat()
            }

            # Save to database
            supabase.table("sales_rep_onboarding").insert(rep_profile).execute()

            # Grant first achievement
            first_achievement = await self.grant_first_achievement(rep_id, psychological_profile["archetype"])

            # Create initial target suggestion
            target_suggestion = await self.suggest_first_target(rep_id, psychological_profile)

            return {
                "rep_id": rep_id,
                "onboarding_state": RepOnboardingState.HOOKED,
                "psychological_profile": psychological_profile,
                "first_achievement": first_achievement,
                "target_suggestion": target_suggestion,
                "feature_access": self.feature_tiers[FeatureTier.SOLO_REP],
                "next_steps": [
                    "Set your first monthly target",
                    "Explore your personalized dashboard",
                    "Request your first supplier quote"
                ]
            }

        except Exception as e:
            logger.error(f"Error in instant onboarding: {e}")
            raise HTTPException(status_code=500, detail=str(e))

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Union
import uuid
import json
import logging
from datetime import datetime, timedelta
from decimal import Decimal
from enum import Enum
import asyncio

# Supabase client
from supabase import create_client, Client
import os

# Initialize Supabase
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_ANON_KEY")
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# Logging setup
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="BidBeez Sales Rep Self-Onboarding Engine",
    description="Psychological onboarding system with company pathway",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# =====================================================
# ONBOARDING MODELS
# =====================================================

class RepOnboardingState(str, Enum):
    DISCOVERY = "discovery"                    # Landing page visitor
    HOOKED = "hooked"                         # Completed psychological quiz
    ENGAGED = "engaged"                       # Set first target, using features
    INVESTED = "invested"                     # 1+ weeks usage, achievements unlocked
    COMPANY_CURIOUS = "company_curious"       # Shown company benefits
    COMPANY_MOTIVATED = "company_motivated"   # Actively considering company onboarding
    COMPANY_ONBOARDING = "company_onboarding" # In company verification process
    FULLY_INTEGRATED = "fully_integrated"     # Complete company access

class OnboardingTrigger(str, Enum):
    TIME_BASED = "time_based"
    ACHIEVEMENT_BASED = "achievement_based"
    USAGE_BASED = "usage_based"
    SOCIAL_BASED = "social_based"
    LIMITATION_HIT = "limitation_hit"

class FeatureTier(str, Enum):
    SOLO_REP = "solo_rep"
    TEAM_REP = "team_rep"
    ENTERPRISE_REP = "enterprise_rep"

class OnboardingQuizResponse(BaseModel):
    question_id: str
    response: Union[str, int, List[str]]
    response_time_ms: int
    confidence_level: Optional[int] = None

class InstantOnboardingData(BaseModel):
    email: str
    first_name: str
    last_name: str
    phone: Optional[str] = None
    quiz_responses: List[OnboardingQuizResponse]
    referral_source: Optional[str] = None
    utm_data: Optional[Dict] = None

class CompanyOnboardingData(BaseModel):
    company_name: str
    industry: str
    company_size: str  # "1-10", "11-50", "51-200", "201-1000", "1000+"
    annual_revenue: Optional[str] = None
    rep_role: str  # "sales_rep", "sales_manager", "sales_director", "vp_sales"
    authorization_level: str  # "can_authorize", "need_approval", "not_sure"

class OnboardingProgress(BaseModel):
    rep_id: str
    current_state: RepOnboardingState
    feature_tier: FeatureTier
    progress_percentage: int
    next_milestone: str
    days_since_signup: int
    achievements_unlocked: int
    features_used: List[str]
    limitations_hit: List[str]

# =====================================================
# ONBOARDING PROGRESSION ENGINE
# =====================================================

class OnboardingProgressionEngine:
    def __init__(self):
        self.progression_rules = {
            RepOnboardingState.DISCOVERY: {
                "next_state": RepOnboardingState.HOOKED,
                "triggers": ["psychological_quiz_completed"],
                "psychology": "curiosity_satisfaction",
                "reward": "archetype_reveal + first_achievement",
                "time_limit_hours": 1
            },
            RepOnboardingState.HOOKED: {
                "next_state": RepOnboardingState.ENGAGED,
                "triggers": ["first_target_set", "dashboard_explored"],
                "psychology": "goal_commitment + ownership",
                "reward": "personalized_dashboard + target_tracking",
                "time_limit_hours": 24
            },
            RepOnboardingState.ENGAGED: {
                "next_state": RepOnboardingState.INVESTED,
                "triggers": ["7_days_usage", "3_achievements_unlocked", "supplier_quote_used"],
                "psychology": "habit_formation + competence",
                "reward": "advanced_features + peer_comparison",
                "time_limit_hours": 168  # 7 days
            },
            RepOnboardingState.INVESTED: {
                "next_state": RepOnboardingState.COMPANY_CURIOUS,
                "triggers": ["14_days_usage", "supplier_limit_hit", "leaderboard_viewed"],
                "psychology": "limitation_awareness + expansion_desire",
                "reward": "company_benefits_preview",
                "time_limit_hours": 336  # 14 days
            },
            RepOnboardingState.COMPANY_CURIOUS: {
                "next_state": RepOnboardingState.COMPANY_MOTIVATED,
                "triggers": ["company_benefits_viewed", "team_features_previewed"],
                "psychology": "FOMO + social_proof",
                "reward": "company_onboarding_incentive",
                "time_limit_hours": 72  # 3 days
            },
            RepOnboardingState.COMPANY_MOTIVATED: {
                "next_state": RepOnboardingState.COMPANY_ONBOARDING,
                "triggers": ["company_onboarding_started"],
                "psychology": "commitment + immediate_gratification",
                "reward": "temporary_team_access",
                "time_limit_hours": 168  # 7 days
            }
        }
        
        self.feature_tiers = {
            FeatureTier.SOLO_REP: {
                "target_tracking": True,
                "basic_achievements": True,
                "supplier_quotes_per_month": 5,
                "leaderboard_access": "anonymous_only",
                "analytics": "basic",
                "team_features": False,
                "api_access": False,
                "custom_branding": False
            },
            FeatureTier.TEAM_REP: {
                "target_tracking": True,
                "basic_achievements": True,
                "team_achievements": True,
                "supplier_quotes_per_month": 25,
                "leaderboard_access": "team_comparison",
                "analytics": "advanced",
                "team_features": "limited",
                "api_access": False,
                "custom_branding": False
            },
            FeatureTier.ENTERPRISE_REP: {
                "target_tracking": True,
                "basic_achievements": True,
                "team_achievements": True,
                "custom_achievements": True,
                "supplier_quotes_per_month": -1,  # Unlimited
                "leaderboard_access": "full",
                "analytics": "enterprise",
                "team_features": True,
                "api_access": True,
                "custom_branding": True,
                "white_label": True
            }
        }
    
    async def instant_onboard_rep(self, onboarding_data: InstantOnboardingData) -> Dict:
        """Complete instant onboarding with psychological profiling"""
        try:
            rep_id = str(uuid.uuid4())
            user_id = str(uuid.uuid4())  # Would integrate with auth system
            
            # Analyze psychological profile from quiz
            psychological_profile = await self.analyze_onboarding_quiz(onboarding_data.quiz_responses)
            
            # Create rep profile
            rep_profile = {
                "rep_id": rep_id,
                "user_id": user_id,
                "email": onboarding_data.email,
                "first_name": onboarding_data.first_name,
                "last_name": onboarding_data.last_name,
                "phone": onboarding_data.phone,
                "onboarding_state": RepOnboardingState.HOOKED,
                "feature_tier": FeatureTier.SOLO_REP,
                "psychological_profile": psychological_profile,
                "onboarding_completed_at": datetime.now().isoformat(),
                "referral_source": onboarding_data.referral_source,
                "utm_data": onboarding_data.utm_data or {},
                "created_at": datetime.now().isoformat()
            }
            
            # Save to database
            supabase.table("sales_rep_onboarding").insert(rep_profile).execute()
            
            # Grant first achievement
            first_achievement = await self.grant_first_achievement(rep_id, psychological_profile["archetype"])
            
            # Create initial target suggestion
            target_suggestion = await self.suggest_first_target(rep_id, psychological_profile)
            
            return {
                "rep_id": rep_id,
                "onboarding_state": RepOnboardingState.HOOKED,
                "psychological_profile": psychological_profile,
                "first_achievement": first_achievement,
                "target_suggestion": target_suggestion,
                "feature_access": self.feature_tiers[FeatureTier.SOLO_REP],
                "next_steps": [
                    "Set your first monthly target",
                    "Explore your personalized dashboard",
                    "Request your first supplier quote"
                ]
            }
            
        except Exception as e:
            logger.error(f"Error in instant onboarding: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def analyze_onboarding_quiz(self, quiz_responses: List[OnboardingQuizResponse]) -> Dict:
        """Analyze psychological profile from onboarding quiz"""
        try:
            # Initialize scoring
            archetype_scores = {
                "achiever": 0,
                "hunter": 0,
                "relationship_builder": 0,
                "analyst": 0
            }
            
            motivation_factors = []
            psychological_traits = {}
            
            # Analyze each response
            for response in quiz_responses:
                question_analysis = await self.analyze_quiz_response(response)
                
                # Update archetype scores
                for archetype, score in question_analysis["archetype_scores"].items():
                    archetype_scores[archetype] += score
                
                # Collect motivation factors
                motivation_factors.extend(question_analysis.get("motivation_factors", []))
                
                # Update psychological traits
                psychological_traits.update(question_analysis.get("psychological_traits", {}))
            
            # Determine primary archetype
            primary_archetype = max(archetype_scores, key=archetype_scores.get)
            
            # Calculate confidence scores
            confidence_scores = {
                "stress_tolerance": psychological_traits.get("stress_tolerance", 0.5),
                "competition_comfort": psychological_traits.get("competition_comfort", 0.5),
                "financial_motivation": psychological_traits.get("financial_motivation", 0.5),
                "relationship_focus": psychological_traits.get("relationship_focus", 0.5)
            }
            
            return {
                "archetype": primary_archetype,
                "archetype_scores": archetype_scores,
                "motivation_factors": list(set(motivation_factors)),
                "confidence_scores": confidence_scores,
                "psychological_traits": psychological_traits,
                "quiz_completion_time": sum(r.response_time_ms for r in quiz_responses),
                "analysis_confidence": 0.85  # Would calculate based on response consistency
            }
            
        except Exception as e:
            logger.error(f"Error analyzing onboarding quiz: {e}")
            return {"archetype": "achiever", "analysis_confidence": 0.5}
    
    async def analyze_quiz_response(self, response: OnboardingQuizResponse) -> Dict:
        """Analyze individual quiz response for psychological insights"""
        # This would contain the actual quiz analysis logic
        # For now, returning sample analysis
        
        question_mappings = {
            "motivation_primary": {
                "recognition": {"archetype_scores": {"achiever": 3}, "motivation_factors": ["recognition"]},
                "money": {"archetype_scores": {"hunter": 3}, "motivation_factors": ["financial"]},
                "relationships": {"archetype_scores": {"relationship_builder": 3}, "motivation_factors": ["relationship"]},
                "data": {"archetype_scores": {"analyst": 3}, "motivation_factors": ["growth"]}
            },
            "work_style": {
                "competitive": {"archetype_scores": {"achiever": 2, "hunter": 2}},
                "collaborative": {"archetype_scores": {"relationship_builder": 2}},
                "analytical": {"archetype_scores": {"analyst": 2}},
                "independent": {"archetype_scores": {"hunter": 1, "analyst": 1}}
            },
            "stress_response": {
                "thrive": {"psychological_traits": {"stress_tolerance": 0.8}},
                "manage": {"psychological_traits": {"stress_tolerance": 0.6}},
                "struggle": {"psychological_traits": {"stress_tolerance": 0.3}}
            }
        }
        
        # Default analysis
        return {
            "archetype_scores": {"achiever": 1},
            "motivation_factors": ["achievement"],
            "psychological_traits": {"stress_tolerance": 0.5}
        }
    
    async def check_progression_triggers(self, rep_id: str) -> Optional[str]:
        """Check if rep should progress to next onboarding state"""
        try:
            # Get current state
            current_state = await self.get_rep_onboarding_state(rep_id)
            rules = self.progression_rules.get(current_state)
            
            if not rules:
                return None
            
            # Check if all triggers are met
            triggers_met = await self.evaluate_triggers(rep_id, rules["triggers"])
            
            if triggers_met:
                next_state = rules["next_state"]
                await self.progress_rep_state(rep_id, next_state)
                await self.deliver_progression_reward(rep_id, rules["reward"])
                
                logger.info(f"Rep {rep_id} progressed from {current_state} to {next_state}")
                return next_state
            
            return None
            
        except Exception as e:
            logger.error(f"Error checking progression triggers: {e}")
            return None
    
    async def evaluate_triggers(self, rep_id: str, triggers: List[str]) -> bool:
        """Evaluate if progression triggers are met"""
        try:
            rep_data = await self.get_rep_activity_data(rep_id)
            
            trigger_results = {}
            
            for trigger in triggers:
                if trigger == "psychological_quiz_completed":
                    trigger_results[trigger] = rep_data.get("quiz_completed", False)
                elif trigger == "first_target_set":
                    trigger_results[trigger] = rep_data.get("targets_set", 0) > 0
                elif trigger == "dashboard_explored":
                    trigger_results[trigger] = rep_data.get("dashboard_sessions", 0) > 2
                elif trigger == "7_days_usage":
                    trigger_results[trigger] = rep_data.get("days_since_signup", 0) >= 7
                elif trigger == "3_achievements_unlocked":
                    trigger_results[trigger] = rep_data.get("achievements_count", 0) >= 3
                elif trigger == "supplier_quote_used":
                    trigger_results[trigger] = rep_data.get("quotes_requested", 0) > 0
                elif trigger == "14_days_usage":
                    trigger_results[trigger] = rep_data.get("days_since_signup", 0) >= 14
                elif trigger == "supplier_limit_hit":
                    trigger_results[trigger] = rep_data.get("monthly_quotes_used", 0) >= 5
                elif trigger == "leaderboard_viewed":
                    trigger_results[trigger] = rep_data.get("leaderboard_views", 0) > 0
                elif trigger == "company_benefits_viewed":
                    trigger_results[trigger] = rep_data.get("company_benefits_page_views", 0) > 0
                elif trigger == "team_features_previewed":
                    trigger_results[trigger] = rep_data.get("team_features_previewed", False)
                elif trigger == "company_onboarding_started":
                    trigger_results[trigger] = rep_data.get("company_onboarding_initiated", False)
                else:
                    trigger_results[trigger] = False
            
            # All triggers must be met
            return all(trigger_results.values())
            
        except Exception as e:
            logger.error(f"Error evaluating triggers: {e}")
            return False
    
    async def get_rep_activity_data(self, rep_id: str) -> Dict:
        """Get comprehensive rep activity data for trigger evaluation"""
        try:
            # Get rep profile
            profile_result = supabase.table("sales_rep_onboarding").select("*").eq("rep_id", rep_id).execute()
            
            if not profile_result.data:
                return {}
            
            profile = profile_result.data[0]
            created_at = datetime.fromisoformat(profile["created_at"].replace('Z', '+00:00'))
            days_since_signup = (datetime.now() - created_at).days
            
            # Get activity data from various tables
            targets_result = supabase.table("sales_targets").select("*").eq("rep_id", rep_id).execute()
            achievements_result = supabase.table("sales_rep_achievements").select("*").eq("rep_id", rep_id).execute()
            
            # Calculate activity metrics
            activity_data = {
                "days_since_signup": days_since_signup,
                "quiz_completed": profile.get("psychological_profile") is not None,
                "targets_set": len(targets_result.data) if targets_result.data else 0,
                "achievements_count": len(achievements_result.data) if achievements_result.data else 0,
                "dashboard_sessions": profile.get("dashboard_sessions", 0),
                "quotes_requested": profile.get("quotes_requested", 0),
                "monthly_quotes_used": profile.get("monthly_quotes_used", 0),
                "leaderboard_views": profile.get("leaderboard_views", 0),
                "company_benefits_page_views": profile.get("company_benefits_page_views", 0),
                "team_features_previewed": profile.get("team_features_previewed", False),
                "company_onboarding_initiated": profile.get("company_onboarding_initiated", False)
            }
            
            return activity_data
            
        except Exception as e:
            logger.error(f"Error getting rep activity data: {e}")
            return {}
    
    async def grant_first_achievement(self, rep_id: str, archetype: str) -> Dict:
        """Grant first achievement based on archetype"""
        try:
            achievement_id = str(uuid.uuid4())
            
            achievement = {
                "achievement_id": achievement_id,
                "rep_id": rep_id,
                "name": "Welcome Aboard!",
                "description": f"Discovered your {archetype} sales archetype",
                "icon": "🎯",
                "tier": "bronze",
                "category": "onboarding",
                "xp_reward": 100,
                "psychological_benefit": "Confidence building and platform familiarity",
                "unlocked_at": datetime.now().isoformat()
            }
            
            supabase.table("sales_rep_achievements").insert(achievement).execute()
            
            return achievement
            
        except Exception as e:
            logger.error(f"Error granting first achievement: {e}")
            return {}
    
    async def suggest_first_target(self, rep_id: str, psychological_profile: Dict) -> Dict:
        """Suggest first target based on psychological profile"""
        try:
            archetype = psychological_profile.get("archetype", "achiever")
            
            # Archetype-based target suggestions
            target_suggestions = {
                "achiever": {
                    "target_type": "deals",
                    "suggested_value": 5,
                    "unit": "deals",
                    "period": "monthly",
                    "motivation": "Start with a challenging but achievable goal to build momentum"
                },
                "hunter": {
                    "target_type": "revenue",
                    "suggested_value": 50000,
                    "unit": "ZAR",
                    "period": "monthly",
                    "motivation": "Focus on revenue generation to maximize your earning potential"
                },
                "relationship_builder": {
                    "target_type": "clients",
                    "suggested_value": 3,
                    "unit": "new_clients",
                    "period": "monthly",
                    "motivation": "Build lasting relationships with new clients for long-term success"
                },
                "analyst": {
                    "target_type": "efficiency",
                    "suggested_value": 20,
                    "unit": "percentage_improvement",
                    "period": "monthly",
                    "motivation": "Optimize your sales process for maximum efficiency"
                }
            }
            
            suggestion = target_suggestions.get(archetype, target_suggestions["achiever"])
            suggestion["rep_id"] = rep_id
            suggestion["archetype_optimized"] = True
            
            return suggestion
            
        except Exception as e:
            logger.error(f"Error suggesting first target: {e}")
            return {}
    
    async def get_rep_onboarding_state(self, rep_id: str) -> RepOnboardingState:
        """Get current onboarding state for rep"""
        try:
            result = supabase.table("sales_rep_onboarding").select("onboarding_state").eq("rep_id", rep_id).execute()
            
            if result.data:
                return RepOnboardingState(result.data[0]["onboarding_state"])
            
            return RepOnboardingState.DISCOVERY
            
        except Exception as e:
            logger.error(f"Error getting rep onboarding state: {e}")
            return RepOnboardingState.DISCOVERY
    
    async def progress_rep_state(self, rep_id: str, new_state: RepOnboardingState):
        """Progress rep to new onboarding state"""
        try:
            update_data = {
                "onboarding_state": new_state,
                "state_updated_at": datetime.now().isoformat()
            }
            
            # Update feature tier based on state
            if new_state in [RepOnboardingState.COMPANY_ONBOARDING, RepOnboardingState.FULLY_INTEGRATED]:
                update_data["feature_tier"] = FeatureTier.TEAM_REP
            
            supabase.table("sales_rep_onboarding").update(update_data).eq("rep_id", rep_id).execute()
            
        except Exception as e:
            logger.error(f"Error progressing rep state: {e}")
    
    async def deliver_progression_reward(self, rep_id: str, reward: str):
        """Deliver progression reward to rep"""
        try:
            # This would trigger notifications, unlock features, etc.
            logger.info(f"Delivering reward '{reward}' to rep {rep_id}")
            
            # Create notification record
            notification = {
                "notification_id": str(uuid.uuid4()),
                "rep_id": rep_id,
                "type": "progression_reward",
                "title": "Congratulations! You've unlocked new features!",
                "message": f"Reward: {reward}",
                "created_at": datetime.now().isoformat(),
                "read": False
            }
            
            supabase.table("sales_rep_notifications").insert(notification).execute()
            
        except Exception as e:
            logger.error(f"Error delivering progression reward: {e}")

# =====================================================
# GLOBAL INSTANCE
# =====================================================

onboarding_engine = OnboardingProgressionEngine()

# =====================================================
# API ENDPOINTS
# =====================================================

@app.post("/sales-rep/instant-onboard")
async def instant_onboard(onboarding_data: InstantOnboardingData):
    """Complete instant onboarding with psychological profiling"""
    return await onboarding_engine.instant_onboard_rep(onboarding_data)

@app.get("/sales-rep/{rep_id}/onboarding-progress")
async def get_onboarding_progress(rep_id: str):
    """Get current onboarding progress for rep"""
    try:
        current_state = await onboarding_engine.get_rep_onboarding_state(rep_id)
        activity_data = await onboarding_engine.get_rep_activity_data(rep_id)
        
        # Calculate progress percentage
        state_progress = {
            RepOnboardingState.DISCOVERY: 0,
            RepOnboardingState.HOOKED: 15,
            RepOnboardingState.ENGAGED: 30,
            RepOnboardingState.INVESTED: 50,
            RepOnboardingState.COMPANY_CURIOUS: 65,
            RepOnboardingState.COMPANY_MOTIVATED: 80,
            RepOnboardingState.COMPANY_ONBOARDING: 90,
            RepOnboardingState.FULLY_INTEGRATED: 100
        }
        
        progress = OnboardingProgress(
            rep_id=rep_id,
            current_state=current_state,
            feature_tier=FeatureTier.SOLO_REP,  # Would determine from database
            progress_percentage=state_progress.get(current_state, 0),
            next_milestone="Complete psychological quiz" if current_state == RepOnboardingState.DISCOVERY else "Set first target",
            days_since_signup=activity_data.get("days_since_signup", 0),
            achievements_unlocked=activity_data.get("achievements_count", 0),
            features_used=[],  # Would calculate from usage data
            limitations_hit=[]  # Would calculate from usage limits
        )
        
        return progress.dict()
        
    except Exception as e:
        logger.error(f"Error getting onboarding progress: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/sales-rep/{rep_id}/check-progression")
async def check_progression(rep_id: str):
    """Check and trigger progression if conditions are met"""
    try:
        new_state = await onboarding_engine.check_progression_triggers(rep_id)
        
        if new_state:
            return {"progressed": True, "new_state": new_state}
        else:
            return {"progressed": False, "current_state": await onboarding_engine.get_rep_onboarding_state(rep_id)}
            
    except Exception as e:
        logger.error(f"Error checking progression: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/sales-rep/{rep_id}/feature-access")
async def get_feature_access(rep_id: str):
    """Get current feature access for rep"""
    try:
        # Get rep's current tier
        result = supabase.table("sales_rep_onboarding").select("feature_tier").eq("rep_id", rep_id).execute()
        
        if result.data:
            tier = FeatureTier(result.data[0]["feature_tier"])
            return onboarding_engine.feature_tiers[tier]
        
        return onboarding_engine.feature_tiers[FeatureTier.SOLO_REP]
        
    except Exception as e:
        logger.error(f"Error getting feature access: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8009)
