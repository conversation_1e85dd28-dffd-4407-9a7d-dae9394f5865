"""
Supplier Quote Management API
Core revenue generator - handles quote submission, management, and commission tracking
"""

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, <PERSON>
from typing import Dict, List, Optional, Union
import uuid
import json
import logging
from datetime import datetime, timedelta
from decimal import Decimal
import asyncio

# Supabase client
from supabase import create_client, Client
import os

# Initialize Supabase
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_ANON_KEY")
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# Logging setup
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="BidBeez Supplier Quote Management API",
    description="Quote submission, management, and commission tracking system",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# =====================================================
# PYDANTIC MODELS
# =====================================================

class QuoteSubmission(BaseModel):
    tender_id: str
    supplier_id: str
    total_amount: Decimal
    currency: str = "ZAR"
    delivery_timeframe: str
    validity_period_days: int = 30
    line_items: List[Dict] = []
    terms_and_conditions: Optional[str] = None
    compliance_documents: List[str] = []
    smart_contract_enabled: bool = False
    payment_terms: Optional[str] = None
    warranty_terms: Optional[str] = None

class QuoteUpdate(BaseModel):
    amount: Optional[Decimal] = None
    delivery_timeframe: Optional[str] = None
    validity_period_days: Optional[int] = None
    line_items: Optional[List[Dict]] = None
    terms_and_conditions: Optional[str] = None
    status: Optional[str] = None

class QuoteResponse(BaseModel):
    quote_id: str
    tender_id: str
    supplier_id: str
    supplier_name: str
    total_amount: Decimal
    currency: str
    delivery_timeframe: str
    validity_period_days: int
    status: str
    trust_score: float
    compliance_status: str
    submission_date: datetime
    expiry_date: datetime
    commission_rate: float
    estimated_commission: Decimal
    smart_contract_address: Optional[str] = None
    blockchain_hash: Optional[str] = None

class CommissionCalculation(BaseModel):
    quote_amount: Decimal
    commission_rate: float
    commission_amount: Decimal
    platform_fee: Decimal
    supplier_net: Decimal
    payment_schedule: Dict

class QuoteAnalytics(BaseModel):
    total_quotes: int
    total_value: Decimal
    avg_quote_value: Decimal
    commission_earned: Decimal
    conversion_rate: float
    top_categories: List[Dict]
    monthly_trends: List[Dict]

# =====================================================
# COMMISSION CALCULATION ENGINE
# =====================================================

class CommissionEngine:
    def __init__(self):
        self.base_commission_rate = 0.025  # 2.5% base commission
        self.tier_rates = {
            "bronze": 0.025,    # 2.5% for new suppliers
            "silver": 0.020,    # 2.0% for verified suppliers
            "gold": 0.015,      # 1.5% for premium suppliers
            "platinum": 0.010   # 1.0% for enterprise suppliers
        }
        self.volume_discounts = {
            100000: 0.002,   # 0.2% discount for quotes > R100k
            500000: 0.005,   # 0.5% discount for quotes > R500k
            1000000: 0.010,  # 1.0% discount for quotes > R1M
        }
    
    def calculate_commission(self, quote_amount: Decimal, supplier_tier: str = "bronze", 
                           quote_volume_ytd: Decimal = 0) -> CommissionCalculation:
        """Calculate commission with tier and volume discounts"""
        
        # Base rate from supplier tier
        base_rate = self.tier_rates.get(supplier_tier, self.base_commission_rate)
        
        # Apply volume discounts
        volume_discount = 0
        for threshold, discount in sorted(self.volume_discounts.items(), reverse=True):
            if quote_amount >= threshold:
                volume_discount = discount
                break
        
        # Final commission rate
        final_rate = max(base_rate - volume_discount, 0.005)  # Minimum 0.5%
        
        # Calculate amounts
        commission_amount = quote_amount * Decimal(str(final_rate))
        platform_fee = commission_amount * Decimal("0.1")  # 10% platform fee
        supplier_net = quote_amount - commission_amount
        
        # Payment schedule (commission paid on milestones)
        payment_schedule = {
            "on_award": commission_amount * Decimal("0.3"),      # 30% on contract award
            "on_delivery": commission_amount * Decimal("0.5"),    # 50% on delivery
            "on_completion": commission_amount * Decimal("0.2")   # 20% on completion
        }
        
        return CommissionCalculation(
            quote_amount=quote_amount,
            commission_rate=final_rate,
            commission_amount=commission_amount,
            platform_fee=platform_fee,
            supplier_net=supplier_net,
            payment_schedule=payment_schedule
        )

# =====================================================
# QUOTE MANAGEMENT SERVICE
# =====================================================

class QuoteManagementService:
    def __init__(self):
        self.commission_engine = CommissionEngine()
    
    async def submit_quote(self, quote_data: QuoteSubmission) -> QuoteResponse:
        """Submit a new supplier quote"""
        try:
            # Get supplier information
            supplier_result = supabase.table("users").select("""
                id, email,
                supplier_blockchain_profiles(*),
                supplier_compliance_record(*)
            """).eq("id", quote_data.supplier_id).execute()
            
            if not supplier_result.data:
                raise HTTPException(status_code=404, detail="Supplier not found")
            
            supplier = supplier_result.data[0]
            supplier_profile = supplier.get("supplier_blockchain_profiles", [{}])[0]
            compliance = supplier.get("supplier_compliance_record", [{}])[0]
            
            # Calculate trust score
            trust_score = await self.calculate_trust_score(supplier)
            
            # Calculate commission
            supplier_tier = self.get_supplier_tier(compliance, supplier_profile)
            commission_calc = self.commission_engine.calculate_commission(
                quote_data.total_amount, 
                supplier_tier
            )
            
            # Generate quote ID
            quote_id = str(uuid.uuid4())
            
            # Calculate expiry date
            expiry_date = datetime.now() + timedelta(days=quote_data.validity_period_days)
            
            # Prepare quote record
            quote_record = {
                "id": quote_id,
                "tender_id": quote_data.tender_id,
                "supplier_id": quote_data.supplier_id,
                "amount": float(quote_data.total_amount),
                "currency": quote_data.currency,
                "delivery_time": quote_data.delivery_timeframe,
                "validity_period": quote_data.validity_period_days,
                "trust_score": trust_score,
                "status": "submitted",
                "is_smart_contract": quote_data.smart_contract_enabled,
                "compliance_doc_url": json.dumps(quote_data.compliance_documents),
                "terms_conditions": quote_data.terms_and_conditions,
                "commission_rate": float(commission_calc.commission_rate),
                "commission_amount": float(commission_calc.commission_amount),
                "expiry_date": expiry_date.isoformat(),
                "metadata": {
                    "line_items": quote_data.line_items,
                    "payment_terms": quote_data.payment_terms,
                    "warranty_terms": quote_data.warranty_terms,
                    "supplier_tier": supplier_tier,
                    "commission_breakdown": commission_calc.dict()
                }
            }
            
            # Insert quote
            result = supabase.table("supplier_quotes").insert(quote_record).execute()
            
            if not result.data:
                raise HTTPException(status_code=500, detail="Failed to submit quote")
            
            # Create commission tracking record
            await self.create_commission_record(quote_id, commission_calc)
            
            # Generate smart contract if enabled
            smart_contract_address = None
            blockchain_hash = None
            if quote_data.smart_contract_enabled:
                smart_contract_address, blockchain_hash = await self.create_smart_contract(quote_record)
            
            return QuoteResponse(
                quote_id=quote_id,
                tender_id=quote_data.tender_id,
                supplier_id=quote_data.supplier_id,
                supplier_name=supplier_profile.get("company_name", supplier["email"]),
                total_amount=quote_data.total_amount,
                currency=quote_data.currency,
                delivery_timeframe=quote_data.delivery_timeframe,
                validity_period_days=quote_data.validity_period_days,
                status="submitted",
                trust_score=trust_score,
                compliance_status=self.get_compliance_status(compliance),
                submission_date=datetime.now(),
                expiry_date=expiry_date,
                commission_rate=commission_calc.commission_rate,
                estimated_commission=commission_calc.commission_amount,
                smart_contract_address=smart_contract_address,
                blockchain_hash=blockchain_hash
            )
            
        except Exception as e:
            logger.error(f"Error submitting quote: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def calculate_trust_score(self, supplier: Dict) -> float:
        """Calculate supplier trust score"""
        score = 0.5  # Base score
        
        profile = supplier.get("supplier_blockchain_profiles", [{}])[0]
        compliance = supplier.get("supplier_compliance_record", [{}])[0]
        
        # Verification bonus
        if profile.get("is_verified"):
            score += 0.2
        
        # B-BBEE level bonus
        bee_level = compliance.get("bbbee_level", 8)
        if bee_level <= 4:
            score += 0.1
        
        # Experience bonus
        experience = profile.get("industry_experience", 0)
        if experience > 5:
            score += 0.15
        elif experience > 2:
            score += 0.1
        
        # Performance score
        performance = profile.get("performance_score", 0)
        if performance > 0:
            score += (performance / 100) * 0.2
        
        return min(score, 1.0)
    
    def get_supplier_tier(self, compliance: Dict, profile: Dict) -> str:
        """Determine supplier tier for commission calculation"""
        if not compliance or not profile:
            return "bronze"
        
        bee_level = compliance.get("bbbee_level", 8)
        is_verified = profile.get("is_verified", False)
        performance = profile.get("performance_score", 0)
        
        if bee_level <= 2 and is_verified and performance >= 90:
            return "platinum"
        elif bee_level <= 4 and is_verified and performance >= 80:
            return "gold"
        elif is_verified and performance >= 70:
            return "silver"
        else:
            return "bronze"
    
    def get_compliance_status(self, compliance: Dict) -> str:
        """Get compliance status"""
        if not compliance:
            return "pending"
        
        bee_level = compliance.get("bbbee_level")
        expiry_date = compliance.get("expiry_date")
        
        if bee_level and expiry_date:
            expiry = datetime.fromisoformat(expiry_date.replace('Z', '+00:00'))
            if expiry > datetime.now():
                return "compliant"
            else:
                return "expired"
        
        return "pending"
    
    async def create_commission_record(self, quote_id: str, commission_calc: CommissionCalculation):
        """Create commission tracking record"""
        try:
            commission_record = {
                "quote_id": quote_id,
                "commission_rate": float(commission_calc.commission_rate),
                "commission_amount": float(commission_calc.commission_amount),
                "platform_fee": float(commission_calc.platform_fee),
                "payment_schedule": commission_calc.payment_schedule,
                "status": "pending",
                "created_at": datetime.now().isoformat()
            }
            
            # Note: This would go to a commission_tracking table
            # For now, we'll store in metadata
            logger.info(f"Commission record created for quote {quote_id}: {commission_record}")
            
        except Exception as e:
            logger.error(f"Error creating commission record: {e}")
    
    async def create_smart_contract(self, quote_record: Dict) -> tuple:
        """Create smart contract for quote (placeholder)"""
        try:
            # This would integrate with actual blockchain service
            # For now, return mock values
            contract_address = f"0x{uuid.uuid4().hex[:40]}"
            blockchain_hash = f"0x{uuid.uuid4().hex}"
            
            logger.info(f"Smart contract created: {contract_address}")
            return contract_address, blockchain_hash
            
        except Exception as e:
            logger.error(f"Error creating smart contract: {e}")
            return None, None

# =====================================================
# API ENDPOINTS
# =====================================================

quote_service = QuoteManagementService()

@app.post("/quotes", response_model=QuoteResponse)
async def submit_quote(quote_data: QuoteSubmission):
    """Submit a new supplier quote"""
    return await quote_service.submit_quote(quote_data)

@app.get("/quotes/{quote_id}")
async def get_quote(quote_id: str):
    """Get quote details"""
    try:
        result = supabase.table("supplier_quotes").select("*").eq("id", quote_id).execute()
        
        if not result.data:
            raise HTTPException(status_code=404, detail="Quote not found")
        
        return result.data[0]
        
    except Exception as e:
        logger.error(f"Error getting quote: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.put("/quotes/{quote_id}")
async def update_quote(quote_id: str, update_data: QuoteUpdate):
    """Update an existing quote"""
    try:
        # Prepare update data
        update_fields = {}
        if update_data.amount is not None:
            update_fields["amount"] = float(update_data.amount)
        if update_data.delivery_timeframe:
            update_fields["delivery_time"] = update_data.delivery_timeframe
        if update_data.validity_period_days:
            update_fields["validity_period"] = update_data.validity_period_days
        if update_data.terms_and_conditions:
            update_fields["terms_conditions"] = update_data.terms_and_conditions
        if update_data.status:
            update_fields["status"] = update_data.status
        
        update_fields["updated_at"] = datetime.now().isoformat()
        
        result = supabase.table("supplier_quotes").update(update_fields).eq("id", quote_id).execute()
        
        if not result.data:
            raise HTTPException(status_code=404, detail="Quote not found")
        
        return result.data[0]
        
    except Exception as e:
        logger.error(f"Error updating quote: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/quotes/tender/{tender_id}")
async def get_tender_quotes(tender_id: str, status: Optional[str] = None):
    """Get all quotes for a tender"""
    try:
        query = supabase.table("supplier_quotes").select("""
            *,
            users!supplier_id(email),
            supplier_blockchain_profiles!supplier_id(company_name, performance_score)
        """).eq("tender_id", tender_id)
        
        if status:
            query = query.eq("status", status)
        
        result = query.order("amount", desc=False).execute()
        
        return {"quotes": result.data}
        
    except Exception as e:
        logger.error(f"Error getting tender quotes: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/quotes/supplier/{supplier_id}")
async def get_supplier_quotes(supplier_id: str, status: Optional[str] = None):
    """Get all quotes from a supplier"""
    try:
        query = supabase.table("supplier_quotes").select("*").eq("supplier_id", supplier_id)
        
        if status:
            query = query.eq("status", status)
        
        result = query.order("created_at", desc=True).execute()
        
        return {"quotes": result.data}
        
    except Exception as e:
        logger.error(f"Error getting supplier quotes: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/quotes/{quote_id}/calculate-commission")
async def calculate_quote_commission(quote_id: str):
    """Calculate commission for a specific quote"""
    try:
        # Get quote details
        quote_result = supabase.table("supplier_quotes").select("*").eq("id", quote_id).execute()
        
        if not quote_result.data:
            raise HTTPException(status_code=404, detail="Quote not found")
        
        quote = quote_result.data[0]
        
        # Get supplier tier
        supplier_result = supabase.table("supplier_compliance_record").select("*").eq("supplier_id", quote["supplier_id"]).execute()
        compliance = supplier_result.data[0] if supplier_result.data else {}
        
        profile_result = supabase.table("supplier_blockchain_profiles").select("*").eq("supplier_id", quote["supplier_id"]).execute()
        profile = profile_result.data[0] if profile_result.data else {}
        
        supplier_tier = quote_service.get_supplier_tier(compliance, profile)
        
        # Calculate commission
        commission_calc = quote_service.commission_engine.calculate_commission(
            Decimal(str(quote["amount"])),
            supplier_tier
        )
        
        return commission_calc
        
    except Exception as e:
        logger.error(f"Error calculating commission: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/analytics/quotes")
async def get_quote_analytics(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    supplier_id: Optional[str] = None
):
    """Get quote analytics and metrics"""
    try:
        # Build query
        query = supabase.table("supplier_quotes").select("*")
        
        if supplier_id:
            query = query.eq("supplier_id", supplier_id)
        
        if start_date:
            query = query.gte("created_at", start_date)
        
        if end_date:
            query = query.lte("created_at", end_date)
        
        result = query.execute()
        quotes = result.data
        
        if not quotes:
            return QuoteAnalytics(
                total_quotes=0,
                total_value=Decimal("0"),
                avg_quote_value=Decimal("0"),
                commission_earned=Decimal("0"),
                conversion_rate=0.0,
                top_categories=[],
                monthly_trends=[]
            )
        
        # Calculate metrics
        total_quotes = len(quotes)
        total_value = sum(Decimal(str(q["amount"])) for q in quotes)
        avg_quote_value = total_value / total_quotes if total_quotes > 0 else Decimal("0")
        commission_earned = sum(Decimal(str(q.get("commission_amount", 0))) for q in quotes)
        
        # Conversion rate (awarded / submitted)
        awarded_quotes = len([q for q in quotes if q.get("status") == "awarded"])
        conversion_rate = (awarded_quotes / total_quotes * 100) if total_quotes > 0 else 0.0
        
        return QuoteAnalytics(
            total_quotes=total_quotes,
            total_value=total_value,
            avg_quote_value=avg_quote_value,
            commission_earned=commission_earned,
            conversion_rate=conversion_rate,
            top_categories=[],  # Would calculate from tender categories
            monthly_trends=[]   # Would calculate monthly aggregations
        )
        
    except Exception as e:
        logger.error(f"Error getting analytics: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)
