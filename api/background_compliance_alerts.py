"""
Background Compliance Alerts API
Provides real-time compliance alerts through Server-Sent Events
Only surfaces when AI detects issues or opportunities
"""

from fastapi import FastAP<PERSON>, Request
from fastapi.responses import StreamingResponse
import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional
from supabase import create_client, Client
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Supabase client
supabase: Client = create_client(
    os.getenv("SUPABASE_URL", ""),
    os.getenv("SUPABASE_SERVICE_KEY", "")
)

app = FastAPI()

class ComplianceAlertStream:
    """Manages real-time compliance alert streams for users"""
    
    def __init__(self):
        self.active_streams = {}
        
    async def add_stream(self, user_id: str, queue: asyncio.Queue):
        """Add a new alert stream for user"""
        self.active_streams[user_id] = queue
        
    async def remove_stream(self, user_id: str):
        """Remove alert stream for user"""
        if user_id in self.active_streams:
            del self.active_streams[user_id]
            
    async def send_alert(self, user_id: str, alert: Dict):
        """Send alert to user's stream if active"""
        if user_id in self.active_streams:
            try:
                await self.active_streams[user_id].put(alert)
            except Exception as e:
                logger.error(f"Error sending alert to user {user_id}: {e}")

# Global alert stream manager
alert_stream = ComplianceAlertStream()

@app.get("/api/compliance/alerts/stream")
async def compliance_alert_stream(request: Request, userId: str):
    """Server-Sent Events stream for real-time compliance alerts"""
    
    async def event_generator():
        queue = asyncio.Queue()
        await alert_stream.add_stream(userId, queue)
        
        try:
            # Send initial connection confirmation
            yield f"data: {json.dumps({'type': 'connected', 'message': 'Compliance monitoring active'})}\n\n"
            
            while True:
                # Check if client disconnected
                if await request.is_disconnected():
                    break
                    
                try:
                    # Wait for alert with timeout
                    alert = await asyncio.wait_for(queue.get(), timeout=30.0)
                    yield f"data: {json.dumps(alert)}\n\n"
                except asyncio.TimeoutError:
                    # Send keepalive
                    yield f"data: {json.dumps({'type': 'keepalive'})}\n\n"
                    
        except Exception as e:
            logger.error(f"Error in compliance alert stream: {e}")
        finally:
            await alert_stream.remove_stream(userId)
    
    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control"
        }
    )

@app.get("/api/compliance/status")
async def get_compliance_status(userId: str):
    """Get current compliance status for user"""
    
    try:
        # Get active compliance issues
        issues_result = supabase.table("bid_updates").select("*").eq("user_id", userId).eq("update_type", "compliance_issue").eq("resolved", False).execute()
        
        active_issues = len(issues_result.data or [])
        
        # Get protest opportunities
        protest_result = supabase.table("bid_updates").select("*").eq("user_id", userId).eq("update_type", "protest_opportunity").eq("resolved", False).execute()
        
        protest_opportunities = len(protest_result.data or [])
        
        # Get auto-assigned tasks
        tasks_result = supabase.table("bee_worker_tasks").select("*").eq("user_id", userId).eq("auto_assigned", True).eq("status", "active").execute()
        
        auto_assigned_tasks = len(tasks_result.data or [])
        
        return {
            "activeIssues": active_issues,
            "protestOpportunities": protest_opportunities,
            "autoAssignedTasks": auto_assigned_tasks,
            "lastCheck": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting compliance status: {e}")
        return {
            "activeIssues": 0,
            "protestOpportunities": 0,
            "autoAssignedTasks": 0,
            "lastCheck": datetime.now().isoformat()
        }

@app.post("/api/compliance/alert")
async def send_compliance_alert(alert_data: Dict):
    """Send compliance alert to user (called by background AI service)"""
    
    try:
        user_id = alert_data.get("user_id")
        alert_type = alert_data.get("type")
        title = alert_data.get("title")
        description = alert_data.get("description")
        severity = alert_data.get("severity", "info")
        auto_action = alert_data.get("auto_action")
        task_id = alert_data.get("task_id")
        
        alert = {
            "id": f"{user_id}_{datetime.now().timestamp()}",
            "type": alert_type,
            "title": title,
            "description": description,
            "severity": severity,
            "autoAction": auto_action,
            "taskId": task_id,
            "timestamp": datetime.now().isoformat()
        }
        
        # Send to user's stream
        await alert_stream.send_alert(user_id, alert)
        
        # Log alert
        logger.info(f"Sent compliance alert to user {user_id}: {title}")
        
        return {"status": "sent", "alert_id": alert["id"]}
        
    except Exception as e:
        logger.error(f"Error sending compliance alert: {e}")
        return {"status": "error", "message": str(e)}

# Background service integration
async def trigger_compliance_alert(user_id: str, alert_type: str, title: str, description: str, 
                                 severity: str = "info", auto_action: str = None, task_id: str = None):
    """Helper function for background AI service to trigger alerts"""
    
    alert_data = {
        "user_id": user_id,
        "type": alert_type,
        "title": title,
        "description": description,
        "severity": severity,
        "auto_action": auto_action,
        "task_id": task_id
    }
    
    # Send alert through stream
    await alert_stream.send_alert(user_id, {
        "id": f"{user_id}_{datetime.now().timestamp()}",
        "type": alert_type,
        "title": title,
        "description": description,
        "severity": severity,
        "autoAction": auto_action,
        "taskId": task_id,
        "timestamp": datetime.now().isoformat()
    })

# Example alert types that AI service can trigger:

async def alert_compliance_issue(user_id: str, issue_description: str, missing_document: str = None):
    """Alert user about compliance issue detected by AI"""
    
    title = "⚠️ Compliance Issue Detected"
    description = f"AI detected compliance issue: {issue_description}"
    auto_action = f"Auto-assigned bee worker to collect {missing_document}" if missing_document else None
    
    await trigger_compliance_alert(
        user_id=user_id,
        alert_type="compliance_issue",
        title=title,
        description=description,
        severity="warning",
        auto_action=auto_action
    )

async def alert_protest_opportunity(user_id: str, issue_type: str, success_probability: float, contract_value: float):
    """Alert user about protest opportunity detected by AI"""
    
    title = "🚨 Protest Opportunity Detected"
    description = f"AI detected {issue_type} with {int(success_probability*100)}% success probability. Potential recovery: R{contract_value:,.0f}"
    auto_action = "Auto-assigned bee worker for evidence collection"
    
    await trigger_compliance_alert(
        user_id=user_id,
        alert_type="protest_opportunity", 
        title=title,
        description=description,
        severity="error",
        auto_action=auto_action
    )

async def alert_auto_task_assigned(user_id: str, task_type: str, task_description: str, task_id: str):
    """Alert user about auto-assigned bee worker task"""
    
    title = "🐝 AI Auto-Assigned Bee Worker"
    description = f"AI automatically assigned bee worker for {task_type}: {task_description}"
    
    await trigger_compliance_alert(
        user_id=user_id,
        alert_type="auto_task_assigned",
        title=title,
        description=description,
        severity="info",
        auto_action=f"View task details",
        task_id=task_id
    )

async def alert_evidence_collection_started(user_id: str, protest_type: str, evidence_type: str):
    """Alert user that evidence collection has started"""
    
    title = "🕵️ Evidence Collection Started"
    description = f"Bee worker started collecting {evidence_type} evidence for {protest_type} protest"
    
    await trigger_compliance_alert(
        user_id=user_id,
        alert_type="evidence_collection_started",
        title=title,
        description=description,
        severity="success"
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
