"""
Contractor-Supplier Access Integration API
Enables ContractorSync users to access supplier networks for bidding
"""

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, <PERSON>
from typing import Dict, List, Optional, Union
import uuid
import json
import logging
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum

# Supabase client
from supabase import create_client, Client
import os

# Initialize Supabase
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_ANON_KEY")
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# Logging setup
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="BidBeez Contractor-Supplier Access API",
    description="Integration system for ContractorSync users to access supplier networks",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# =====================================================
# ENUMS AND MODELS
# =====================================================

class UserRole(str, Enum):
    BIDDER = "bidder"
    CONTRACTOR = "contractor"
    SUPPLIER = "supplier"
    ADMIN = "admin"
    BEE = "bee"

class UserType(str, Enum):
    CORPORATE = "corporate"
    SME = "sme"
    FREELANCER = "freelancer"

class EcosystemService(str, Enum):
    SKILLSYNC = "skillsync"
    TOOLSYNC = "toolsync"
    CONTRACTORSYNC = "contractorsync"
    SUPPLIERSYNC = "suppliersync"

class AccessLevel(str, Enum):
    BASIC = "basic"
    PREMIUM = "premium"
    ENTERPRISE = "enterprise"

class ContractorProfile(BaseModel):
    contractor_id: str
    user_id: str
    company_name: str
    registration_number: str
    contractor_type: str  # general, specialist, subcontractor
    specializations: List[str]
    cidb_grade: Optional[str] = None
    bbbee_level: Optional[int] = None
    location: Dict
    service_areas: List[str]
    capacity: Dict  # project capacity, team size, etc.
    certifications: List[Dict]
    insurance_details: Dict
    performance_rating: float = 0.0
    completed_projects: int = 0
    active_projects: int = 0
    supplier_access_level: AccessLevel = AccessLevel.BASIC

class SupplierAccess(BaseModel):
    access_id: str
    contractor_id: str
    supplier_id: str
    access_level: AccessLevel
    granted_at: datetime
    expires_at: Optional[datetime] = None
    permissions: List[str]
    usage_limits: Dict
    current_usage: Dict
    status: str = "active"

class SupplierRequest(BaseModel):
    request_id: str
    contractor_id: str
    tender_id: Optional[str] = None
    category: str
    specifications: Dict
    quantity_required: Optional[int] = None
    delivery_location: str
    delivery_deadline: datetime
    budget_range: Dict
    quality_requirements: List[str]
    compliance_requirements: List[str]
    preferred_suppliers: List[str] = []
    urgency: str = "normal"  # low, normal, high, urgent

class SupplierMatch(BaseModel):
    supplier_id: str
    supplier_name: str
    company_name: str
    match_score: float
    category_match: float
    location_match: float
    capacity_match: float
    compliance_match: float
    price_competitiveness: float
    delivery_capability: float
    quality_score: float
    bee_level: Optional[int] = None
    certifications: List[str]
    estimated_cost: Optional[float] = None
    estimated_delivery: Optional[str] = None
    availability: str

# =====================================================
# CONTRACTOR-SUPPLIER ACCESS SERVICE
# =====================================================

class ContractorSupplierAccessService:
    def __init__(self):
        self.access_permissions = {
            AccessLevel.BASIC: [
                "view_supplier_profiles",
                "search_suppliers",
                "request_quotes",
                "view_basic_pricing"
            ],
            AccessLevel.PREMIUM: [
                "view_supplier_profiles",
                "search_suppliers", 
                "request_quotes",
                "view_detailed_pricing",
                "access_supplier_network",
                "bulk_quote_requests",
                "priority_support",
                "advanced_matching"
            ],
            AccessLevel.ENTERPRISE: [
                "view_supplier_profiles",
                "search_suppliers",
                "request_quotes", 
                "view_detailed_pricing",
                "access_supplier_network",
                "bulk_quote_requests",
                "priority_support",
                "advanced_matching",
                "custom_integrations",
                "dedicated_account_manager",
                "white_label_access",
                "api_access"
            ]
        }
    
    async def register_contractor(self, contractor_data: Dict) -> ContractorProfile:
        """Register a contractor and set up supplier access"""
        try:
            contractor_id = str(uuid.uuid4())
            
            # Determine access level based on contractor profile
            access_level = await self.determine_access_level(contractor_data)
            
            contractor_profile = ContractorProfile(
                contractor_id=contractor_id,
                user_id=contractor_data["user_id"],
                company_name=contractor_data["company_name"],
                registration_number=contractor_data.get("registration_number", ""),
                contractor_type=contractor_data.get("contractor_type", "general"),
                specializations=contractor_data.get("specializations", []),
                cidb_grade=contractor_data.get("cidb_grade"),
                bbbee_level=contractor_data.get("bbbee_level"),
                location=contractor_data.get("location", {}),
                service_areas=contractor_data.get("service_areas", []),
                capacity=contractor_data.get("capacity", {}),
                certifications=contractor_data.get("certifications", []),
                insurance_details=contractor_data.get("insurance_details", {}),
                supplier_access_level=access_level
            )
            
            # Save contractor profile
            contractor_record = contractor_profile.dict()
            contractor_record["created_at"] = datetime.now().isoformat()
            contractor_record["updated_at"] = datetime.now().isoformat()
            
            result = supabase.table("contractor_profiles").insert(contractor_record).execute()
            
            # Set up supplier access permissions
            await self.setup_supplier_access(contractor_id, access_level)
            
            logger.info(f"Contractor registered: {contractor_id} with {access_level} access")
            return contractor_profile
            
        except Exception as e:
            logger.error(f"Error registering contractor: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def determine_access_level(self, contractor_data: Dict) -> AccessLevel:
        """Determine supplier access level based on contractor profile"""
        try:
            # Basic scoring system
            score = 0
            
            # CIDB Grade scoring
            cidb_grade = contractor_data.get("cidb_grade", "")
            if cidb_grade:
                grade_scores = {"9": 10, "8": 9, "7": 8, "6": 7, "5": 6, "4": 5, "3": 4, "2": 3, "1": 2}
                score += grade_scores.get(cidb_grade, 0)
            
            # B-BBEE Level scoring
            bee_level = contractor_data.get("bbbee_level", 8)
            if bee_level <= 4:
                score += 5
            elif bee_level <= 6:
                score += 3
            
            # Company size/capacity scoring
            capacity = contractor_data.get("capacity", {})
            team_size = capacity.get("team_size", 0)
            if team_size >= 50:
                score += 5
            elif team_size >= 20:
                score += 3
            elif team_size >= 10:
                score += 2
            
            # Certifications scoring
            certifications = contractor_data.get("certifications", [])
            score += min(len(certifications), 5)
            
            # Determine access level
            if score >= 20:
                return AccessLevel.ENTERPRISE
            elif score >= 12:
                return AccessLevel.PREMIUM
            else:
                return AccessLevel.BASIC
                
        except Exception as e:
            logger.error(f"Error determining access level: {e}")
            return AccessLevel.BASIC
    
    async def setup_supplier_access(self, contractor_id: str, access_level: AccessLevel):
        """Set up supplier access permissions for contractor"""
        try:
            permissions = self.access_permissions[access_level]
            
            # Usage limits based on access level
            usage_limits = {
                AccessLevel.BASIC: {
                    "quote_requests_per_month": 10,
                    "supplier_searches_per_day": 20,
                    "bulk_requests_per_month": 0
                },
                AccessLevel.PREMIUM: {
                    "quote_requests_per_month": 100,
                    "supplier_searches_per_day": 200,
                    "bulk_requests_per_month": 10
                },
                AccessLevel.ENTERPRISE: {
                    "quote_requests_per_month": -1,  # Unlimited
                    "supplier_searches_per_day": -1,  # Unlimited
                    "bulk_requests_per_month": -1   # Unlimited
                }
            }
            
            access_record = {
                "access_id": str(uuid.uuid4()),
                "contractor_id": contractor_id,
                "access_level": access_level,
                "permissions": permissions,
                "usage_limits": usage_limits[access_level],
                "current_usage": {
                    "quote_requests_this_month": 0,
                    "supplier_searches_today": 0,
                    "bulk_requests_this_month": 0
                },
                "granted_at": datetime.now().isoformat(),
                "status": "active"
            }
            
            supabase.table("contractor_supplier_access").insert(access_record).execute()
            
        except Exception as e:
            logger.error(f"Error setting up supplier access: {e}")
    
    async def find_suppliers_for_contractor(self, request: SupplierRequest) -> List[SupplierMatch]:
        """Find matching suppliers for contractor requirements"""
        try:
            # Get contractor access level
            contractor_access = await self.get_contractor_access(request.contractor_id)
            
            if not contractor_access or contractor_access["status"] != "active":
                raise HTTPException(status_code=403, detail="No active supplier access")
            
            # Check usage limits
            await self.check_usage_limits(request.contractor_id, "supplier_search")
            
            # Get suppliers matching category
            suppliers_result = supabase.table("supplier_blockchain_profiles").select("""
                *,
                users!supplier_id(email),
                supplier_compliance_record!supplier_id(*)
            """).ilike("specializations", f"%{request.category}%").execute()
            
            suppliers = suppliers_result.data
            matches = []
            
            for supplier in suppliers:
                match_score = await self.calculate_supplier_match_score(supplier, request)
                
                if match_score >= 0.3:  # Minimum match threshold
                    supplier_match = SupplierMatch(
                        supplier_id=supplier["supplier_id"],
                        supplier_name=supplier["users"]["email"],
                        company_name=supplier.get("company_name", ""),
                        match_score=match_score,
                        category_match=await self.calculate_category_match(supplier, request),
                        location_match=await self.calculate_location_match(supplier, request),
                        capacity_match=await self.calculate_capacity_match(supplier, request),
                        compliance_match=await self.calculate_compliance_match(supplier, request),
                        price_competitiveness=0.8,  # Would calculate from historical data
                        delivery_capability=0.9,    # Would calculate from location/capacity
                        quality_score=supplier.get("performance_score", 0) / 100,
                        bee_level=supplier.get("supplier_compliance_record", [{}])[0].get("bbbee_level"),
                        certifications=supplier.get("certifications", []),
                        availability="available"
                    )
                    matches.append(supplier_match)
            
            # Sort by match score
            matches.sort(key=lambda x: x.match_score, reverse=True)
            
            # Update usage tracking
            await self.update_usage_tracking(request.contractor_id, "supplier_search")
            
            return matches[:20]  # Return top 20 matches
            
        except Exception as e:
            logger.error(f"Error finding suppliers: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def get_contractor_access(self, contractor_id: str) -> Optional[Dict]:
        """Get contractor's supplier access details"""
        try:
            result = supabase.table("contractor_supplier_access").select("*").eq("contractor_id", contractor_id).execute()
            
            return result.data[0] if result.data else None
            
        except Exception as e:
            logger.error(f"Error getting contractor access: {e}")
            return None
    
    async def check_usage_limits(self, contractor_id: str, action: str):
        """Check if contractor has exceeded usage limits"""
        try:
            access = await self.get_contractor_access(contractor_id)
            
            if not access:
                raise HTTPException(status_code=403, detail="No supplier access found")
            
            usage_limits = access["usage_limits"]
            current_usage = access["current_usage"]
            
            # Check specific action limits
            if action == "supplier_search":
                daily_limit = usage_limits.get("supplier_searches_per_day", 0)
                current_searches = current_usage.get("supplier_searches_today", 0)
                
                if daily_limit > 0 and current_searches >= daily_limit:
                    raise HTTPException(status_code=429, detail="Daily supplier search limit exceeded")
            
            elif action == "quote_request":
                monthly_limit = usage_limits.get("quote_requests_per_month", 0)
                current_requests = current_usage.get("quote_requests_this_month", 0)
                
                if monthly_limit > 0 and current_requests >= monthly_limit:
                    raise HTTPException(status_code=429, detail="Monthly quote request limit exceeded")
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error checking usage limits: {e}")
    
    async def update_usage_tracking(self, contractor_id: str, action: str):
        """Update usage tracking for contractor"""
        try:
            access = await self.get_contractor_access(contractor_id)
            
            if access:
                current_usage = access["current_usage"]
                
                if action == "supplier_search":
                    current_usage["supplier_searches_today"] = current_usage.get("supplier_searches_today", 0) + 1
                elif action == "quote_request":
                    current_usage["quote_requests_this_month"] = current_usage.get("quote_requests_this_month", 0) + 1
                
                supabase.table("contractor_supplier_access").update({
                    "current_usage": current_usage,
                    "last_used": datetime.now().isoformat()
                }).eq("contractor_id", contractor_id).execute()
                
        except Exception as e:
            logger.error(f"Error updating usage tracking: {e}")
    
    async def calculate_supplier_match_score(self, supplier: Dict, request: SupplierRequest) -> float:
        """Calculate overall match score between supplier and request"""
        try:
            category_score = await self.calculate_category_match(supplier, request)
            location_score = await self.calculate_location_match(supplier, request)
            capacity_score = await self.calculate_capacity_match(supplier, request)
            compliance_score = await self.calculate_compliance_match(supplier, request)
            
            # Weighted average
            overall_score = (
                category_score * 0.3 +
                location_score * 0.2 +
                capacity_score * 0.2 +
                compliance_score * 0.3
            )
            
            return min(overall_score, 1.0)
            
        except Exception as e:
            logger.error(f"Error calculating match score: {e}")
            return 0.0
    
    async def calculate_category_match(self, supplier: Dict, request: SupplierRequest) -> float:
        """Calculate category/specialization match"""
        try:
            supplier_specializations = supplier.get("specializations", [])
            request_category = request.category.lower()
            
            # Simple keyword matching
            for spec in supplier_specializations:
                if request_category in spec.lower() or spec.lower() in request_category:
                    return 1.0
            
            return 0.3  # Partial match
            
        except Exception:
            return 0.0
    
    async def calculate_location_match(self, supplier: Dict, request: SupplierRequest) -> float:
        """Calculate location proximity match"""
        try:
            supplier_location = supplier.get("location", {})
            request_location = request.delivery_location.lower()
            
            supplier_province = supplier_location.get("province", "").lower()
            supplier_city = supplier_location.get("city", "").lower()
            
            # Simple location matching
            if supplier_province in request_location or supplier_city in request_location:
                return 1.0
            
            return 0.5  # Different location but still serviceable
            
        except Exception:
            return 0.5
    
    async def calculate_capacity_match(self, supplier: Dict, request: SupplierRequest) -> float:
        """Calculate capacity/availability match"""
        try:
            # Simple capacity check based on supplier status
            if supplier.get("status") == "active":
                return 0.9
            
            return 0.5
            
        except Exception:
            return 0.5
    
    async def calculate_compliance_match(self, supplier: Dict, request: SupplierRequest) -> float:
        """Calculate compliance requirements match"""
        try:
            compliance_records = supplier.get("supplier_compliance_record", [])
            
            if not compliance_records:
                return 0.3
            
            compliance = compliance_records[0]
            bee_level = compliance.get("bbbee_level", 8)
            
            # Higher score for better B-BBEE levels
            if bee_level <= 4:
                return 1.0
            elif bee_level <= 6:
                return 0.8
            else:
                return 0.6
                
        except Exception:
            return 0.5

    async def request_supplier_quote(self, contractor_id: str, supplier_id: str, quote_request: Dict) -> Dict:
        """Request quote from supplier on behalf of contractor"""
        try:
            # Check contractor access
            await self.check_usage_limits(contractor_id, "quote_request")

            # Create quote request
            quote_request_record = {
                "request_id": str(uuid.uuid4()),
                "contractor_id": contractor_id,
                "supplier_id": supplier_id,
                "tender_id": quote_request.get("tender_id"),
                "specifications": quote_request.get("specifications", {}),
                "quantity": quote_request.get("quantity"),
                "delivery_location": quote_request.get("delivery_location"),
                "delivery_deadline": quote_request.get("delivery_deadline"),
                "budget_range": quote_request.get("budget_range", {}),
                "special_requirements": quote_request.get("special_requirements", []),
                "status": "pending",
                "requested_at": datetime.now().isoformat()
            }

            result = supabase.table("contractor_quote_requests").insert(quote_request_record).execute()

            # Update usage tracking
            await self.update_usage_tracking(contractor_id, "quote_request")

            # Notify supplier (would integrate with notification system)
            await self.notify_supplier_of_quote_request(supplier_id, quote_request_record)

            return {
                "request_id": quote_request_record["request_id"],
                "status": "submitted",
                "estimated_response_time": "24-48 hours"
            }

        except Exception as e:
            logger.error(f"Error requesting supplier quote: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    async def notify_supplier_of_quote_request(self, supplier_id: str, quote_request: Dict):
        """Notify supplier of new quote request"""
        try:
            # This would integrate with the notification system
            notification_record = {
                "notification_id": str(uuid.uuid4()),
                "user_id": supplier_id,
                "type": "quote_request",
                "title": "New Quote Request from Contractor",
                "message": f"You have received a new quote request for {quote_request.get('specifications', {}).get('category', 'services')}",
                "data": quote_request,
                "created_at": datetime.now().isoformat(),
                "read": False
            }

            supabase.table("notifications").insert(notification_record).execute()

        except Exception as e:
            logger.error(f"Error notifying supplier: {e}")

# =====================================================
# API ENDPOINTS
# =====================================================

contractor_service = ContractorSupplierAccessService()

@app.post("/contractor/register")
async def register_contractor(contractor_data: Dict):
    """Register a contractor and set up supplier access"""
    return await contractor_service.register_contractor(contractor_data)

@app.get("/contractor/{contractor_id}/access")
async def get_contractor_access(contractor_id: str):
    """Get contractor's supplier access details"""
    access = await contractor_service.get_contractor_access(contractor_id)
    
    if not access:
        raise HTTPException(status_code=404, detail="Contractor access not found")
    
    return access

@app.post("/contractor/{contractor_id}/find-suppliers")
async def find_suppliers(contractor_id: str, request_data: Dict):
    """Find matching suppliers for contractor requirements"""
    try:
        supplier_request = SupplierRequest(
            request_id=str(uuid.uuid4()),
            contractor_id=contractor_id,
            **request_data
        )
        
        matches = await contractor_service.find_suppliers_for_contractor(supplier_request)
        
        return {
            "request_id": supplier_request.request_id,
            "matches_found": len(matches),
            "suppliers": [match.dict() for match in matches]
        }
        
    except Exception as e:
        logger.error(f"Error finding suppliers: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/contractor/{contractor_id}/usage")
async def get_contractor_usage(contractor_id: str):
    """Get contractor's current usage statistics"""
    try:
        access = await contractor_service.get_contractor_access(contractor_id)
        
        if not access:
            raise HTTPException(status_code=404, detail="Contractor access not found")
        
        return {
            "access_level": access["access_level"],
            "usage_limits": access["usage_limits"],
            "current_usage": access["current_usage"],
            "permissions": access["permissions"]
        }
        
    except Exception as e:
        logger.error(f"Error getting contractor usage: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/contractor/{contractor_id}/upgrade-access")
async def upgrade_contractor_access(contractor_id: str, new_level: AccessLevel):
    """Upgrade contractor's supplier access level"""
    try:
        # Update access level
        update_data = {
            "access_level": new_level,
            "permissions": contractor_service.access_permissions[new_level],
            "updated_at": datetime.now().isoformat()
        }
        
        result = supabase.table("contractor_supplier_access").update(update_data).eq("contractor_id", contractor_id).execute()
        
        if not result.data:
            raise HTTPException(status_code=404, detail="Contractor access not found")
        
        return {"status": "upgraded", "new_level": new_level}
        
    except Exception as e:
        logger.error(f"Error upgrading contractor access: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/contractor/{contractor_id}/request-quote")
async def request_supplier_quote(contractor_id: str, quote_request_data: Dict):
    """Request quote from supplier on behalf of contractor"""
    try:
        supplier_id = quote_request_data.pop("supplier_id")
        result = await contractor_service.request_supplier_quote(contractor_id, supplier_id, quote_request_data)

        return result

    except Exception as e:
        logger.error(f"Error requesting quote: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/contractor/{contractor_id}/quote-requests")
async def get_contractor_quote_requests(contractor_id: str, status: Optional[str] = None):
    """Get contractor's quote requests"""
    try:
        query = supabase.table("contractor_quote_requests").select("*").eq("contractor_id", contractor_id)

        if status:
            query = query.eq("status", status)

        result = query.order("requested_at", desc=True).execute()

        return {"quote_requests": result.data}

    except Exception as e:
        logger.error(f"Error getting quote requests: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/ecosystem/integration-status")
async def get_ecosystem_integration_status():
    """Get status of ecosystem service integrations"""
    return {
        "services": {
            "skillsync": {"status": "active", "integration": "complete"},
            "toolsync": {"status": "active", "integration": "complete"},
            "contractorsync": {"status": "active", "integration": "complete"},
            "suppliersync": {"status": "active", "integration": "complete"}
        },
        "contractor_supplier_bridge": {
            "status": "active",
            "features": ["supplier_search", "quote_requests", "access_control", "usage_tracking"]
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8007)
