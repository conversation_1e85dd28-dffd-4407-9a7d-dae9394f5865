"""
Unified Opportunity Service
Combines tenders, government RFQs, and bidder RFQs into a single opportunity interface
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union
from dataclasses import dataclass
from enum import Enum

from fastapi import HTTPException
from supabase import create_client, Client
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Supabase client
supabase: Client = create_client(
    os.getenv("SUPABASE_URL", ""),
    os.getenv("SUPABASE_SERVICE_KEY", "")
)

class OpportunityType(str, Enum):
    TENDER = "tender"
    GOVERNMENT_RFQ = "government_rfq"
    BIDDER_RFQ = "bidder_rfq"

class OpportunityStatus(str, Enum):
    ACTIVE = "active"
    CLOSING_SOON = "closing_soon"
    CLOSED = "closed"
    AWARDED = "awarded"

@dataclass
class UnifiedOpportunity:
    id: str
    opportunity_type: OpportunityType
    title: str
    description: str
    organization: str
    estimated_value: Optional[float]
    closing_date: datetime
    category: str
    province: str
    status: OpportunityStatus
    
    # Submission details
    submission_type: str  # "bid", "quote", "response"
    submission_url: str
    
    # AI analysis
    match_score: float
    success_probability: float
    competition_level: str
    ai_recommendation: str  # "bid", "create_rfq", "watch", "skip"
    
    # Portfolio impact
    portfolio_category: str  # "rfq_activity", "tender_activity"
    balance_impact: Dict
    
    # Metadata
    source_url: Optional[str]
    documents: List[str]
    requirements: Dict

class UnifiedOpportunityService:
    """Service to provide unified access to all bidding opportunities"""
    
    def __init__(self):
        self.opportunity_types = {
            OpportunityType.TENDER: {
                "table": "tenders",
                "portfolio_category": "tender_activity",
                "submission_type": "bid",
                "success_rate_base": 75
            },
            OpportunityType.GOVERNMENT_RFQ: {
                "table": "government_rfqs",
                "portfolio_category": "rfq_activity",
                "submission_type": "quote",
                "success_rate_base": 88
            },
            OpportunityType.BIDDER_RFQ: {
                "table": "bidder_rfqs",
                "portfolio_category": "rfq_activity",
                "submission_type": "response",
                "success_rate_base": 92
            }
        }

    async def get_all_opportunities(
        self,
        user_id: str,
        filters: Optional[Dict] = None,
        limit: int = 20,
        offset: int = 0
    ) -> List[UnifiedOpportunity]:
        """Get all bidding opportunities (tenders + government RFQs + bidder RFQs)"""
        
        try:
            filters = filters or {}
            opportunities = []
            
            # Get user profile for personalization
            user_profile = await self._get_user_profile(user_id)
            
            # Fetch from each opportunity type
            for opp_type, config in self.opportunity_types.items():
                opp_list = await self._fetch_opportunities_by_type(
                    opp_type, config, filters, user_profile
                )
                opportunities.extend(opp_list)
            
            # Sort by relevance and apply limit/offset
            opportunities = await self._rank_opportunities(opportunities, user_profile)
            
            return opportunities[offset:offset + limit]
            
        except Exception as e:
            logger.error(f"Error getting unified opportunities: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    async def _fetch_opportunities_by_type(
        self,
        opp_type: OpportunityType,
        config: Dict,
        filters: Dict,
        user_profile: Dict
    ) -> List[UnifiedOpportunity]:
        """Fetch opportunities from specific table"""
        
        opportunities = []
        
        try:
            # Build query
            query = supabase.table(config["table"]).select("*")
            
            # Apply filters
            if filters.get("status"):
                query = query.eq("status", filters["status"])
            else:
                query = query.eq("status", "active")
                
            if filters.get("category"):
                query = query.eq("category", filters["category"])
                
            if filters.get("province"):
                query = query.eq("province", filters["province"])
                
            if filters.get("max_value"):
                query = query.lte("estimated_value", filters["max_value"])
                
            # Execute query
            result = query.execute()
            
            if not result.data:
                return opportunities
            
            # Convert to unified format
            for item in result.data:
                unified_opp = await self._convert_to_unified_opportunity(
                    item, opp_type, config, user_profile
                )
                if unified_opp:
                    opportunities.append(unified_opp)
                    
        except Exception as e:
            logger.error(f"Error fetching {opp_type} opportunities: {e}")
            
        return opportunities

    async def _convert_to_unified_opportunity(
        self,
        item: Dict,
        opp_type: OpportunityType,
        config: Dict,
        user_profile: Dict
    ) -> Optional[UnifiedOpportunity]:
        """Convert database record to unified opportunity format"""
        
        try:
            # Extract common fields
            if opp_type == OpportunityType.TENDER:
                id_field = item.get("id")
                title = item.get("title", "")
                organization = item.get("organization", "")
                closing_date_str = item.get("closing_date", "")
                estimated_value = item.get("estimated_value")
                
            elif opp_type == OpportunityType.GOVERNMENT_RFQ:
                id_field = item.get("id")
                title = item.get("title", "")
                organization = item.get("organization", "")
                closing_date_str = item.get("closing_date", "")
                estimated_value = item.get("estimated_value")
                
            elif opp_type == OpportunityType.BIDDER_RFQ:
                id_field = item.get("id")
                title = item.get("title", "")
                organization = f"RFQ by {item.get('bidder_name', 'Bidder')}"
                closing_date_str = item.get("submission_deadline", "")
                estimated_value = item.get("estimated_value")
            
            # Parse closing date
            try:
                closing_date = datetime.fromisoformat(closing_date_str.replace('Z', '+00:00'))
            except:
                closing_date = datetime.now() + timedelta(days=30)
            
            # Determine status
            status = self._determine_opportunity_status(closing_date, item.get("status", "active"))
            
            # Calculate AI metrics
            ai_metrics = await self._calculate_ai_metrics(item, opp_type, user_profile)
            
            # Determine portfolio impact
            balance_impact = await self._calculate_balance_impact(opp_type, user_profile)
            
            return UnifiedOpportunity(
                id=str(id_field),
                opportunity_type=opp_type,
                title=title,
                description=item.get("description", ""),
                organization=organization,
                estimated_value=estimated_value,
                closing_date=closing_date,
                category=item.get("category", "General"),
                province=item.get("province", "National"),
                status=status,
                submission_type=config["submission_type"],
                submission_url=f"/api/{config['submission_type']}/{id_field}",
                match_score=ai_metrics["match_score"],
                success_probability=ai_metrics["success_probability"],
                competition_level=ai_metrics["competition_level"],
                ai_recommendation=ai_metrics["recommendation"],
                portfolio_category=config["portfolio_category"],
                balance_impact=balance_impact,
                source_url=item.get("source_url"),
                documents=item.get("specification_documents", []) or item.get("documents", []),
                requirements=item.get("technical_requirements", {})
            )
            
        except Exception as e:
            logger.error(f"Error converting opportunity: {e}")
            return None

    async def _calculate_ai_metrics(
        self,
        item: Dict,
        opp_type: OpportunityType,
        user_profile: Dict
    ) -> Dict:
        """Calculate AI-driven metrics for opportunity"""
        
        try:
            # Base success rates (psychological manipulation)
            base_rates = {
                OpportunityType.TENDER: 75,
                OpportunityType.GOVERNMENT_RFQ: 88,  # Higher to encourage RFQ activities
                OpportunityType.BIDDER_RFQ: 92       # Highest to encourage RFQ creation
            }
            
            base_success_rate = base_rates[opp_type]
            
            # Calculate match score based on user profile
            match_score = await self._calculate_match_score(item, user_profile)
            
            # Adjust success probability based on match
            success_probability = min(95, base_success_rate + (match_score - 70) * 0.3)
            
            # Determine competition level
            competition_level = self._determine_competition_level(item, opp_type)
            
            # Generate AI recommendation
            recommendation = await self._generate_ai_recommendation(
                item, opp_type, match_score, success_probability, user_profile
            )
            
            return {
                "match_score": round(match_score, 1),
                "success_probability": round(success_probability, 1),
                "competition_level": competition_level,
                "recommendation": recommendation
            }
            
        except Exception as e:
            logger.error(f"Error calculating AI metrics: {e}")
            return {
                "match_score": 70.0,
                "success_probability": 75.0,
                "competition_level": "medium",
                "recommendation": "watch"
            }

    async def _calculate_match_score(self, item: Dict, user_profile: Dict) -> float:
        """Calculate how well opportunity matches user profile"""
        
        score = 50.0  # Base score
        
        try:
            # Category match
            user_categories = user_profile.get("preferred_categories", [])
            if item.get("category") in user_categories:
                score += 20
            
            # Location match
            user_provinces = user_profile.get("preferred_provinces", [])
            if item.get("province") in user_provinces:
                score += 15
            
            # Value range match
            estimated_value = item.get("estimated_value", 0)
            user_min_value = user_profile.get("min_project_value", 0)
            user_max_value = user_profile.get("max_project_value", float('inf'))
            
            if user_min_value <= estimated_value <= user_max_value:
                score += 15
            
            # Experience match (simplified)
            score += min(20, len(user_profile.get("skills", [])) * 2)
            
        except Exception as e:
            logger.error(f"Error calculating match score: {e}")
            
        return min(100.0, max(0.0, score))

    async def _generate_ai_recommendation(
        self,
        item: Dict,
        opp_type: OpportunityType,
        match_score: float,
        success_probability: float,
        user_profile: Dict
    ) -> str:
        """Generate AI recommendation for opportunity"""
        
        try:
            # Get user's current portfolio balance
            balance = await self._get_portfolio_balance(user_profile.get("user_id"))
            
            # Recommendation logic based on portfolio balance and opportunity type
            if opp_type == OpportunityType.TENDER:
                if balance.get("tender_deficit", False) and match_score > 70:
                    return "bid"  # Encourage tender bidding if deficit
                elif match_score > 80:
                    return "bid"
                else:
                    return "watch"
                    
            elif opp_type == OpportunityType.GOVERNMENT_RFQ:
                if balance.get("rfq_deficit", False):
                    return "bid"  # Encourage RFQ bidding if deficit
                elif success_probability > 85:
                    return "bid"
                else:
                    return "watch"
                    
            elif opp_type == OpportunityType.BIDDER_RFQ:
                # Always encourage bidder RFQ responses (highest success rate)
                if success_probability > 90:
                    return "bid"
                else:
                    return "watch"
            
            return "watch"
            
        except Exception as e:
            logger.error(f"Error generating AI recommendation: {e}")
            return "watch"

    async def _calculate_balance_impact(self, opp_type: OpportunityType, user_profile: Dict) -> Dict:
        """Calculate how opportunity affects portfolio balance"""
        
        try:
            balance = await self._get_portfolio_balance(user_profile.get("user_id"))
            
            current_rfq_ratio = balance.get("current_rfq_ratio", 0)
            current_tender_ratio = balance.get("current_tender_ratio", 0)
            
            # Calculate impact of adding this opportunity
            if opp_type in [OpportunityType.GOVERNMENT_RFQ, OpportunityType.BIDDER_RFQ]:
                # RFQ activity
                new_rfq_ratio = (current_rfq_ratio * balance.get("total_activities", 0) + 1) / (balance.get("total_activities", 0) + 1) * 100
                impact = {
                    "category": "rfq_activity",
                    "current_ratio": current_rfq_ratio,
                    "new_ratio": new_rfq_ratio,
                    "improvement": new_rfq_ratio - current_rfq_ratio,
                    "moves_toward_target": new_rfq_ratio < 60  # Target is 60% RFQ
                }
            else:
                # Tender activity
                new_tender_ratio = (current_tender_ratio * balance.get("total_activities", 0) + 1) / (balance.get("total_activities", 0) + 1) * 100
                impact = {
                    "category": "tender_activity",
                    "current_ratio": current_tender_ratio,
                    "new_ratio": new_tender_ratio,
                    "improvement": new_tender_ratio - current_tender_ratio,
                    "moves_toward_target": new_tender_ratio < 40  # Target is 40% Tender
                }
            
            return impact
            
        except Exception as e:
            logger.error(f"Error calculating balance impact: {e}")
            return {"category": "unknown", "improvement": 0}

    async def _get_user_profile(self, user_id: str) -> Dict:
        """Get user profile for personalization"""
        
        try:
            result = supabase.table("users").select("*").eq("id", user_id).execute()
            
            if result.data:
                return result.data[0]
            else:
                return {"user_id": user_id}
                
        except Exception as e:
            logger.error(f"Error getting user profile: {e}")
            return {"user_id": user_id}

    async def _get_portfolio_balance(self, user_id: str) -> Dict:
        """Get user's current portfolio balance"""
        
        try:
            result = supabase.table("portfolio_balance_tracking").select("*").eq("user_id", user_id).execute()
            
            if result.data:
                return result.data[0]
            else:
                return {
                    "total_activities": 0,
                    "current_rfq_ratio": 0,
                    "current_tender_ratio": 0,
                    "rfq_deficit": True,
                    "tender_deficit": False
                }
                
        except Exception as e:
            logger.error(f"Error getting portfolio balance: {e}")
            return {"total_activities": 0}

    async def _rank_opportunities(self, opportunities: List[UnifiedOpportunity], user_profile: Dict) -> List[UnifiedOpportunity]:
        """Rank opportunities by relevance and psychological factors"""
        
        def ranking_score(opp: UnifiedOpportunity) -> float:
            score = 0
            
            # Base score from match and success probability
            score += opp.match_score * 0.3
            score += opp.success_probability * 0.2
            
            # Portfolio balance bonus (psychological manipulation)
            if opp.balance_impact.get("moves_toward_target", False):
                score += 20  # Bonus for opportunities that improve balance
            
            # Urgency bonus
            days_to_close = (opp.closing_date - datetime.now()).days
            if days_to_close <= 3:
                score += 15  # Urgent opportunities get priority
            elif days_to_close <= 7:
                score += 10
            
            # AI recommendation bonus
            if opp.ai_recommendation == "bid":
                score += 25
            elif opp.ai_recommendation == "watch":
                score += 5
            
            return score
        
        return sorted(opportunities, key=ranking_score, reverse=True)

    def _determine_opportunity_status(self, closing_date: datetime, db_status: str) -> OpportunityStatus:
        """Determine opportunity status based on closing date and database status"""
        
        if db_status in ["closed", "awarded"]:
            return OpportunityStatus.AWARDED if db_status == "awarded" else OpportunityStatus.CLOSED
        
        days_to_close = (closing_date - datetime.now()).days
        
        if days_to_close <= 3:
            return OpportunityStatus.CLOSING_SOON
        else:
            return OpportunityStatus.ACTIVE

    def _determine_competition_level(self, item: Dict, opp_type: OpportunityType) -> str:
        """Determine competition level for opportunity"""
        
        # Simplified logic - could be enhanced with real data
        estimated_value = item.get("estimated_value", 0)
        
        if estimated_value > 10000000:  # R10M+
            return "high"
        elif estimated_value > 1000000:  # R1M+
            return "medium"
        else:
            return "low"

# Service instance
unified_opportunity_service = UnifiedOpportunityService()
