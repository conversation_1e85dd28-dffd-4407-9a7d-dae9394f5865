"""
Competitive Intelligence Service
Tracks competitor activity and provides strategic insights
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json
from dataclasses import dataclass
from supabase import create_client, Client
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Supabase client
supabase: Client = create_client(
    os.getenv("SUPABASE_URL", ""),
    os.getenv("SUPABASE_SERVICE_KEY", "")
)

@dataclass
class CompetitorActivity:
    """Data class for competitor activities"""
    bid_tracking_id: str
    competitor_name: str
    competitor_type: str
    activity_type: str
    activity_description: str
    activity_date: datetime
    source_type: str
    source_details: str
    confidence_level: str
    threat_level: str
    impact_description: str

class CompetitiveIntelligenceService:
    """Service for tracking and analyzing competitor activity"""
    
    def __init__(self):
        self.known_competitors = {}
        self.activity_patterns = {}
        
    async def start_intelligence_gathering(self):
        """Start continuous competitive intelligence gathering"""
        
        logger.info("Starting competitive intelligence service...")
        
        while True:
            try:
                await self._gather_intelligence()
                await asyncio.sleep(1800)  # Check every 30 minutes
                
            except Exception as e:
                logger.error(f"Error in intelligence gathering: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes before retrying

    async def _gather_intelligence(self):
        """Gather competitive intelligence from various sources"""
        
        try:
            # Get all active bid tracking records
            result = supabase.table("bid_tracking").select("*").eq("tracking_status", "active").eq("monitor_competitors", True).execute()
            active_bids = result.data or []
            
            logger.info(f"Gathering intelligence for {len(active_bids)} bids")
            
            for bid in active_bids:
                try:
                    await self._analyze_bid_competition(bid)
                except Exception as e:
                    logger.error(f"Error analyzing competition for bid {bid['id']}: {e}")
                    
        except Exception as e:
            logger.error(f"Error gathering intelligence: {e}")

    async def _analyze_bid_competition(self, bid: Dict):
        """Analyze competition for a specific bid"""
        
        bid_id = bid['id']
        
        # 1. Analyze bee worker observations
        await self._analyze_bee_worker_observations(bid)
        
        # 2. Monitor public announcements
        await self._monitor_public_announcements(bid)
        
        # 3. Track historical competitor patterns
        await self._analyze_historical_patterns(bid)
        
        # 4. Monitor social media and news
        await self._monitor_media_mentions(bid)

    async def _analyze_bee_worker_observations(self, bid: Dict):
        """Analyze bee worker observations for competitor intelligence"""
        
        try:
            # Get recent bee worker activities
            result = supabase.table("bee_worker_activities").select("*").eq("tender_id", bid['opportunity_id']).gte("created_at", (datetime.now() - timedelta(days=30)).isoformat()).execute()
            
            activities = result.data or []
            
            for activity in activities:
                if activity['activity_type'] in ['site_visit', 'document_collection', 'briefing_meeting']:
                    observations = activity.get('observations', '')
                    
                    # Extract competitor information from observations
                    competitors = self._extract_competitor_info(observations)
                    
                    for competitor in competitors:
                        await self._record_competitor_activity(
                            bid['id'],
                            competitor['name'],
                            'known_company',
                            'site_visit',
                            f"Observed during {activity['activity_type']}: {competitor['details']}",
                            datetime.fromisoformat(activity['created_at']),
                            'bee_worker_observation',
                            f"Bee worker {activity['bee_worker_id']} observation",
                            'high',
                            self._assess_threat_level(competitor, bid),
                            f"Competitor active in same opportunity category"
                        )
                        
        except Exception as e:
            logger.error(f"Error analyzing bee worker observations: {e}")

    def _extract_competitor_info(self, observations: str) -> List[Dict]:
        """Extract competitor information from observation text"""
        
        competitors = []
        
        # Common competitor indicators
        competitor_keywords = [
            'competitor', 'rival', 'other bidder', 'another company',
            'competing firm', 'other supplier', 'alternative bidder'
        ]
        
        # Company name patterns (simplified)
        company_patterns = [
            r'(\w+\s+(?:Pty|Ltd|Inc|Corp|Company))',
            r'(\w+\s+\w+\s+(?:Pty|Ltd|Inc|Corp))',
            r'([A-Z][a-z]+\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)'
        ]
        
        observations_lower = observations.lower()
        
        # Look for competitor mentions
        for keyword in competitor_keywords:
            if keyword in observations_lower:
                # Try to extract company names near the keyword
                import re
                for pattern in company_patterns:
                    matches = re.findall(pattern, observations, re.IGNORECASE)
                    for match in matches:
                        competitors.append({
                            'name': match.strip(),
                            'details': f"Mentioned in context: {keyword}",
                            'confidence': 'medium'
                        })
        
        return competitors

    async def _monitor_public_announcements(self, bid: Dict):
        """Monitor public announcements for competitor activity"""
        
        try:
            # Check for public tender announcements that might reveal competitors
            # This would integrate with various announcement sources
            
            # Example: Monitor government gazette for competitor awards
            recent_awards = await self._check_government_gazette(bid)
            
            for award in recent_awards:
                if award['organization'] == bid['organization_name']:
                    await self._record_competitor_activity(
                        bid['id'],
                        award['winner'],
                        'frequent_competitor',
                        'award_received',
                        f"Won similar tender: {award['description']}",
                        datetime.fromisoformat(award['date']),
                        'public_announcement',
                        'Government Gazette',
                        'high',
                        'high',
                        f"Proven track record with {bid['organization_name']}"
                    )
                    
        except Exception as e:
            logger.error(f"Error monitoring public announcements: {e}")

    async def _check_government_gazette(self, bid: Dict) -> List[Dict]:
        """Check government gazette for recent awards"""
        
        # This would be implemented to scrape or API call government gazette
        # For now, return mock data
        return [
            {
                'winner': 'ABC Construction Pty Ltd',
                'organization': bid['organization_name'],
                'description': 'Infrastructure Development Project',
                'date': (datetime.now() - timedelta(days=30)).isoformat(),
                'value': 15000000
            }
        ]

    async def _analyze_historical_patterns(self, bid: Dict):
        """Analyze historical competitor patterns"""
        
        try:
            # Get historical competitive intelligence for similar bids
            result = supabase.table("competitive_intelligence").select("*").eq("bid_tracking_id", bid['id']).execute()
            
            historical_data = result.data or []
            
            # Analyze patterns
            competitor_frequency = {}
            for record in historical_data:
                competitor = record['competitor_name']
                competitor_frequency[competitor] = competitor_frequency.get(competitor, 0) + 1
            
            # Identify frequent competitors
            for competitor, frequency in competitor_frequency.items():
                if frequency >= 3:  # Frequent competitor threshold
                    await self._update_competitor_type(competitor, 'frequent_competitor')
                    
                    # Predict likely participation in current bid
                    await self._record_competitor_activity(
                        bid['id'],
                        competitor,
                        'frequent_competitor',
                        'predicted_participation',
                        f"Historically active in {frequency} similar opportunities",
                        datetime.now(),
                        'pattern_analysis',
                        'Historical data analysis',
                        'medium',
                        'medium',
                        f"High likelihood of participation based on {frequency} previous activities"
                    )
                    
        except Exception as e:
            logger.error(f"Error analyzing historical patterns: {e}")

    async def _monitor_media_mentions(self, bid: Dict):
        """Monitor media mentions for competitor intelligence"""
        
        try:
            # This would integrate with news APIs and social media monitoring
            # For now, simulate basic monitoring
            
            # Check for news about the organization or sector
            news_items = await self._search_news(bid['organization_name'], bid['bid_title'])
            
            for item in news_items:
                if any(keyword in item['content'].lower() for keyword in ['tender', 'bid', 'contract', 'award']):
                    # Extract potential competitor mentions
                    competitors = self._extract_competitor_info(item['content'])
                    
                    for competitor in competitors:
                        await self._record_competitor_activity(
                            bid['id'],
                            competitor['name'],
                            'new_entrant',
                            'media_mention',
                            f"Mentioned in news: {item['title']}",
                            datetime.fromisoformat(item['date']),
                            'media_monitoring',
                            item['source'],
                            'low',
                            'low',
                            f"Media coverage suggests potential interest"
                        )
                        
        except Exception as e:
            logger.error(f"Error monitoring media mentions: {e}")

    async def _search_news(self, organization: str, title: str) -> List[Dict]:
        """Search news for relevant mentions"""
        
        # This would integrate with news APIs
        # For now, return mock data
        return [
            {
                'title': f'{organization} announces new procurement initiative',
                'content': f'The {organization} has announced plans for {title} with several companies expressing interest...',
                'source': 'Business Day',
                'date': (datetime.now() - timedelta(days=5)).isoformat(),
                'url': 'https://example.com/news'
            }
        ]

    async def _record_competitor_activity(self, bid_tracking_id: str, competitor_name: str, 
                                        competitor_type: str, activity_type: str, 
                                        activity_description: str, activity_date: datetime,
                                        source_type: str, source_details: str,
                                        confidence_level: str, threat_level: str,
                                        impact_description: str):
        """Record competitor activity in database"""
        
        try:
            activity_data = {
                "bid_tracking_id": bid_tracking_id,
                "competitor_name": competitor_name,
                "competitor_type": competitor_type,
                "activity_type": activity_type,
                "activity_description": activity_description,
                "activity_date": activity_date.isoformat(),
                "source_type": source_type,
                "source_details": source_details,
                "confidence_level": confidence_level,
                "threat_level": threat_level,
                "impact_description": impact_description
            }
            
            # Check if similar activity already recorded
            existing = supabase.table("competitive_intelligence").select("id").eq("bid_tracking_id", bid_tracking_id).eq("competitor_name", competitor_name).eq("activity_type", activity_type).eq("activity_date", activity_date.date().isoformat()).execute()
            
            if not existing.data:
                result = supabase.table("competitive_intelligence").insert(activity_data).execute()
                
                if result.data:
                    logger.info(f"Recorded competitor activity: {competitor_name} - {activity_type}")
                    
                    # Create bid update for high-threat activities
                    if threat_level in ['high', 'medium']:
                        await self._create_competitor_update(bid_tracking_id, activity_data)
                        
        except Exception as e:
            logger.error(f"Error recording competitor activity: {e}")

    async def _create_competitor_update(self, bid_tracking_id: str, activity_data: Dict):
        """Create a bid update for significant competitor activity"""
        
        try:
            update_data = {
                "bid_tracking_id": bid_tracking_id,
                "update_type": "competitor_activity",
                "update_priority": "medium" if activity_data['threat_level'] == 'high' else "low",
                "title": f"Competitor Activity: {activity_data['competitor_name']}",
                "description": f"{activity_data['activity_description']} - {activity_data['impact_description']}",
                "source_url": "",
                "source_type": activity_data['source_type'],
                "competitor_info": {
                    "competitor_name": activity_data['competitor_name'],
                    "activity_type": activity_data['activity_type'],
                    "threat_level": activity_data['threat_level']
                },
                "confidence_score": 0.8 if activity_data['confidence_level'] == 'high' else 0.6,
                "detected_at": datetime.now().isoformat()
            }
            
            supabase.table("bid_updates").insert(update_data).execute()
            
        except Exception as e:
            logger.error(f"Error creating competitor update: {e}")

    def _assess_threat_level(self, competitor: Dict, bid: Dict) -> str:
        """Assess threat level of competitor"""
        
        # Simple threat assessment logic
        if competitor.get('confidence') == 'high':
            return 'high'
        elif 'pty' in competitor['name'].lower() or 'ltd' in competitor['name'].lower():
            return 'medium'
        else:
            return 'low'

    async def _update_competitor_type(self, competitor_name: str, new_type: str):
        """Update competitor type based on frequency"""
        
        try:
            supabase.table("competitive_intelligence").update({
                "competitor_type": new_type
            }).eq("competitor_name", competitor_name).execute()
            
        except Exception as e:
            logger.error(f"Error updating competitor type: {e}")

    async def get_competitor_insights(self, bid_tracking_id: str) -> Dict:
        """Get comprehensive competitor insights for a bid"""
        
        try:
            # Get all competitive intelligence for the bid
            result = supabase.table("competitive_intelligence").select("*").eq("bid_tracking_id", bid_tracking_id).execute()
            
            intelligence_data = result.data or []
            
            # Analyze and summarize
            insights = {
                "total_competitors": len(set([item['competitor_name'] for item in intelligence_data])),
                "threat_levels": {
                    "high": len([item for item in intelligence_data if item['threat_level'] == 'high']),
                    "medium": len([item for item in intelligence_data if item['threat_level'] == 'medium']),
                    "low": len([item for item in intelligence_data if item['threat_level'] == 'low'])
                },
                "activity_types": {},
                "top_competitors": [],
                "recent_activities": intelligence_data[-5:] if intelligence_data else []
            }
            
            # Count activity types
            for item in intelligence_data:
                activity_type = item['activity_type']
                insights["activity_types"][activity_type] = insights["activity_types"].get(activity_type, 0) + 1
            
            # Identify top competitors
            competitor_scores = {}
            for item in intelligence_data:
                competitor = item['competitor_name']
                threat_score = {'high': 3, 'medium': 2, 'low': 1}[item['threat_level']]
                competitor_scores[competitor] = competitor_scores.get(competitor, 0) + threat_score
            
            insights["top_competitors"] = sorted(competitor_scores.items(), key=lambda x: x[1], reverse=True)[:5]
            
            return insights
            
        except Exception as e:
            logger.error(f"Error getting competitor insights: {e}")
            return {}

# Service instance
competitive_intelligence = CompetitiveIntelligenceService()

# Main function to start intelligence gathering
async def start_competitive_intelligence():
    """Start the competitive intelligence service"""
    await competitive_intelligence.start_intelligence_gathering()

if __name__ == "__main__":
    asyncio.run(start_competitive_intelligence())
