"""
B-BBEE Compliance API
Automated B-BBEE tracking, verification, and compliance management for SA market
"""

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Union
import uuid
import json
import logging
from datetime import datetime, timedelta, date
from decimal import Decimal
import asyncio
import requests

# Supabase client
from supabase import create_client, Client
import os

# Initialize Supabase
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_ANON_KEY")
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# Logging setup
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="BidBeez B-BBEE Compliance API",
    description="Automated B-BBEE tracking and verification system",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# =====================================================
# PYDANTIC MODELS
# =====================================================

class BBEECertificate(BaseModel):
    supplier_id: str
    bbbee_level: int = Field(ge=1, le=8, description="B-BBEE level from 1-8")
    certificate_number: str
    issuing_authority: str
    issue_date: date
    expiry_date: date
    certificate_url: Optional[str] = None
    verification_agency: str
    annual_turnover: Optional[Decimal] = None
    ownership_score: Optional[float] = None
    management_control_score: Optional[float] = None
    skills_development_score: Optional[float] = None
    enterprise_development_score: Optional[float] = None
    socioeconomic_development_score: Optional[float] = None

class BBEEStatus(BaseModel):
    supplier_id: str
    current_level: int
    status: str  # active, expired, pending, suspended
    compliance_score: float
    risk_level: str  # low, medium, high, critical
    days_to_expiry: int
    renewal_required: bool
    verification_status: str
    last_updated: datetime

class BBEEVerificationRequest(BaseModel):
    supplier_id: str
    certificate_number: str
    verification_type: str = "manual"  # manual, automated, third_party
    priority: str = "normal"  # low, normal, high, urgent

class BBEEAnalytics(BaseModel):
    total_suppliers: int
    compliant_suppliers: int
    compliance_rate: float
    level_distribution: Dict[int, int]
    expiring_certificates: int
    risk_distribution: Dict[str, int]
    verification_backlog: int
    monthly_trends: List[Dict]

class BBEEAlert(BaseModel):
    alert_id: str
    supplier_id: str
    alert_type: str  # expiry_warning, compliance_risk, verification_required
    severity: str  # info, warning, critical
    message: str
    created_at: datetime
    resolved: bool = False

# =====================================================
# B-BBEE COMPLIANCE ENGINE
# =====================================================

class BBEEComplianceEngine:
    def __init__(self):
        self.level_thresholds = {
            1: {"min_score": 90, "procurement_recognition": 135},
            2: {"min_score": 80, "procurement_recognition": 125},
            3: {"min_score": 70, "procurement_recognition": 110},
            4: {"min_score": 60, "procurement_recognition": 100},
            5: {"min_score": 50, "procurement_recognition": 80},
            6: {"min_score": 40, "procurement_recognition": 60},
            7: {"min_score": 30, "procurement_recognition": 50},
            8: {"min_score": 0, "procurement_recognition": 10}
        }
        
        self.verification_agencies = [
            "SANAS Accredited Verification Agency",
            "Independent Regulatory Board for Auditors (IRBA)",
            "Chartered Accountants South Africa (SAICA)",
            "Association for the Advancement of Black Accountants (AABA)"
        ]
        
        self.risk_factors = {
            "expiry_within_30_days": 0.3,
            "expiry_within_60_days": 0.2,
            "level_6_or_higher": 0.2,
            "unverified_certificate": 0.4,
            "missing_documentation": 0.3,
            "compliance_issues": 0.5
        }
    
    async def register_certificate(self, cert_data: BBEECertificate) -> Dict:
        """Register a new B-BBEE certificate"""
        try:
            # Validate certificate data
            validation_result = await self.validate_certificate(cert_data)
            
            if not validation_result["valid"]:
                raise HTTPException(status_code=400, detail=validation_result["errors"])
            
            # Check for existing certificates
            existing = supabase.table("supplier_compliance_record").select("*").eq("supplier_id", cert_data.supplier_id).execute()
            
            certificate_record = {
                "supplier_id": cert_data.supplier_id,
                "bbbee_level": cert_data.bbbee_level,
                "certificate_number": cert_data.certificate_number,
                "issuing_authority": cert_data.issuing_authority,
                "issue_date": cert_data.issue_date.isoformat(),
                "expiry_date": cert_data.expiry_date.isoformat(),
                "certificate_url": cert_data.certificate_url,
                "verification_agency": cert_data.verification_agency,
                "verified_by": "system",
                "verification_date": datetime.now().isoformat(),
                "metadata": {
                    "annual_turnover": float(cert_data.annual_turnover) if cert_data.annual_turnover else None,
                    "ownership_score": cert_data.ownership_score,
                    "management_control_score": cert_data.management_control_score,
                    "skills_development_score": cert_data.skills_development_score,
                    "enterprise_development_score": cert_data.enterprise_development_score,
                    "socioeconomic_development_score": cert_data.socioeconomic_development_score,
                    "validation_result": validation_result
                }
            }
            
            if existing.data:
                # Update existing record
                result = supabase.table("supplier_compliance_record").update(certificate_record).eq("supplier_id", cert_data.supplier_id).execute()
            else:
                # Insert new record
                result = supabase.table("supplier_compliance_record").insert(certificate_record).execute()
            
            # Create compliance monitoring record
            await self.create_monitoring_record(cert_data.supplier_id, cert_data.expiry_date)
            
            # Generate alerts if needed
            await self.check_compliance_alerts(cert_data.supplier_id)
            
            return {
                "success": True,
                "certificate_id": result.data[0]["id"] if result.data else None,
                "status": "registered",
                "compliance_level": cert_data.bbbee_level,
                "procurement_recognition": self.level_thresholds[cert_data.bbbee_level]["procurement_recognition"]
            }
            
        except Exception as e:
            logger.error(f"Error registering certificate: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def validate_certificate(self, cert_data: BBEECertificate) -> Dict:
        """Validate B-BBEE certificate data"""
        errors = []
        warnings = []
        
        # Check certificate number format
        if not cert_data.certificate_number or len(cert_data.certificate_number) < 8:
            errors.append("Invalid certificate number format")
        
        # Check B-BBEE level validity
        if cert_data.bbbee_level < 1 or cert_data.bbbee_level > 8:
            errors.append("B-BBEE level must be between 1 and 8")
        
        # Check date validity
        if cert_data.expiry_date <= cert_data.issue_date:
            errors.append("Expiry date must be after issue date")
        
        if cert_data.expiry_date <= date.today():
            warnings.append("Certificate has expired")
        
        # Check issuing authority
        if cert_data.verification_agency not in self.verification_agencies:
            warnings.append("Verification agency not in approved list")
        
        # Check certificate validity period (typically 1 year)
        validity_period = (cert_data.expiry_date - cert_data.issue_date).days
        if validity_period > 400:  # More than ~13 months
            warnings.append("Certificate validity period seems unusually long")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings,
            "validation_score": max(0, 100 - len(errors) * 25 - len(warnings) * 10)
        }
    
    async def get_compliance_status(self, supplier_id: str) -> BBEEStatus:
        """Get current B-BBEE compliance status for supplier"""
        try:
            # Get compliance record
            result = supabase.table("supplier_compliance_record").select("*").eq("supplier_id", supplier_id).execute()
            
            if not result.data:
                return BBEEStatus(
                    supplier_id=supplier_id,
                    current_level=8,  # Non-compliant
                    status="not_registered",
                    compliance_score=0.0,
                    risk_level="critical",
                    days_to_expiry=0,
                    renewal_required=True,
                    verification_status="not_verified",
                    last_updated=datetime.now()
                )
            
            compliance = result.data[0]
            expiry_date = datetime.fromisoformat(compliance["expiry_date"].replace('Z', '+00:00')).date()
            days_to_expiry = (expiry_date - date.today()).days
            
            # Calculate compliance score
            compliance_score = await self.calculate_compliance_score(compliance, days_to_expiry)
            
            # Determine status
            if days_to_expiry < 0:
                status = "expired"
            elif days_to_expiry <= 30:
                status = "expiring_soon"
            else:
                status = "active"
            
            # Calculate risk level
            risk_level = self.calculate_risk_level(compliance, days_to_expiry)
            
            return BBEEStatus(
                supplier_id=supplier_id,
                current_level=compliance["bbbee_level"],
                status=status,
                compliance_score=compliance_score,
                risk_level=risk_level,
                days_to_expiry=days_to_expiry,
                renewal_required=days_to_expiry <= 60,
                verification_status=compliance.get("verification_status", "verified"),
                last_updated=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error getting compliance status: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    async def calculate_compliance_score(self, compliance: Dict, days_to_expiry: int) -> float:
        """Calculate overall compliance score"""
        base_score = 100.0
        
        # Deduct for B-BBEE level (higher level = lower score)
        level_penalty = (compliance["bbbee_level"] - 1) * 10
        base_score -= level_penalty
        
        # Deduct for expiry proximity
        if days_to_expiry < 0:
            base_score -= 50  # Expired
        elif days_to_expiry <= 30:
            base_score -= 20  # Expiring soon
        elif days_to_expiry <= 60:
            base_score -= 10  # Renewal needed
        
        # Bonus for verification
        if compliance.get("verified_by"):
            base_score += 10
        
        return max(0.0, min(100.0, base_score))
    
    def calculate_risk_level(self, compliance: Dict, days_to_expiry: int) -> str:
        """Calculate compliance risk level"""
        risk_score = 0.0
        
        # Add risk factors
        if days_to_expiry < 0:
            risk_score += 0.5  # Expired
        elif days_to_expiry <= 30:
            risk_score += 0.3  # Expiring soon
        elif days_to_expiry <= 60:
            risk_score += 0.2  # Renewal needed
        
        if compliance["bbbee_level"] >= 6:
            risk_score += 0.2  # High B-BBEE level
        
        if not compliance.get("verified_by"):
            risk_score += 0.4  # Unverified
        
        # Determine risk level
        if risk_score >= 0.7:
            return "critical"
        elif risk_score >= 0.4:
            return "high"
        elif risk_score >= 0.2:
            return "medium"
        else:
            return "low"
    
    async def create_monitoring_record(self, supplier_id: str, expiry_date: date):
        """Create certificate monitoring record"""
        try:
            monitoring_record = {
                "supplier_id": supplier_id,
                "certificate_type": "B-BBEE Certificate",
                "expiry_date": expiry_date.isoformat(),
                "renewal_reminder_date": (expiry_date - timedelta(days=60)).isoformat(),
                "status": "active",
                "monitoring_enabled": True
            }
            
            # Check if monitoring record exists
            existing = supabase.table("certificate_monitoring").select("*").eq("supplier_id", supplier_id).eq("certificate_type", "B-BBEE Certificate").execute()
            
            if existing.data:
                supabase.table("certificate_monitoring").update(monitoring_record).eq("id", existing.data[0]["id"]).execute()
            else:
                supabase.table("certificate_monitoring").insert(monitoring_record).execute()
                
        except Exception as e:
            logger.error(f"Error creating monitoring record: {e}")
    
    async def check_compliance_alerts(self, supplier_id: str):
        """Check and generate compliance alerts"""
        try:
            status = await self.get_compliance_status(supplier_id)
            
            alerts = []
            
            # Expiry alerts
            if status.days_to_expiry < 0:
                alerts.append({
                    "alert_type": "certificate_expired",
                    "severity": "critical",
                    "message": f"B-BBEE certificate has expired {abs(status.days_to_expiry)} days ago"
                })
            elif status.days_to_expiry <= 30:
                alerts.append({
                    "alert_type": "expiry_warning",
                    "severity": "warning",
                    "message": f"B-BBEE certificate expires in {status.days_to_expiry} days"
                })
            elif status.days_to_expiry <= 60:
                alerts.append({
                    "alert_type": "renewal_reminder",
                    "severity": "info",
                    "message": f"B-BBEE certificate renewal required in {status.days_to_expiry} days"
                })
            
            # Risk level alerts
            if status.risk_level == "critical":
                alerts.append({
                    "alert_type": "compliance_risk",
                    "severity": "critical",
                    "message": "Critical compliance risk detected - immediate attention required"
                })
            elif status.risk_level == "high":
                alerts.append({
                    "alert_type": "compliance_risk",
                    "severity": "warning",
                    "message": "High compliance risk - review required"
                })
            
            # Save alerts
            for alert in alerts:
                alert_record = {
                    "alert_id": str(uuid.uuid4()),
                    "supplier_id": supplier_id,
                    "alert_type": alert["alert_type"],
                    "severity": alert["severity"],
                    "message": alert["message"],
                    "resolved": False
                }
                
                # Check if similar alert already exists
                existing_alert = supabase.table("compliance_alerts").select("*").eq("supplier_id", supplier_id).eq("alert_type", alert["alert_type"]).eq("resolved", False).execute()
                
                if not existing_alert.data:
                    supabase.table("compliance_alerts").insert(alert_record).execute()
                    
        except Exception as e:
            logger.error(f"Error checking compliance alerts: {e}")

# =====================================================
# API ENDPOINTS
# =====================================================

compliance_engine = BBEEComplianceEngine()

@app.post("/bbee/register")
async def register_bbee_certificate(cert_data: BBEECertificate):
    """Register a new B-BBEE certificate"""
    return await compliance_engine.register_certificate(cert_data)

@app.get("/bbee/status/{supplier_id}", response_model=BBEEStatus)
async def get_bbee_status(supplier_id: str):
    """Get B-BBEE compliance status for supplier"""
    return await compliance_engine.get_compliance_status(supplier_id)

@app.get("/bbee/certificate/{supplier_id}")
async def get_bbee_certificate(supplier_id: str):
    """Get B-BBEE certificate details"""
    try:
        result = supabase.table("supplier_compliance_record").select("*").eq("supplier_id", supplier_id).execute()
        
        if not result.data:
            raise HTTPException(status_code=404, detail="Certificate not found")
        
        return result.data[0]
        
    except Exception as e:
        logger.error(f"Error getting certificate: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.put("/bbee/certificate/{supplier_id}")
async def update_bbee_certificate(supplier_id: str, cert_data: BBEECertificate):
    """Update B-BBEE certificate"""
    cert_data.supplier_id = supplier_id
    return await compliance_engine.register_certificate(cert_data)

@app.post("/bbee/verify/{supplier_id}")
async def verify_bbee_certificate(supplier_id: str, verification_request: BBEEVerificationRequest):
    """Request B-BBEE certificate verification"""
    try:
        verification_record = {
            "supplier_id": supplier_id,
            "certificate_number": verification_request.certificate_number,
            "verification_type": verification_request.verification_type,
            "priority": verification_request.priority,
            "status": "pending",
            "requested_at": datetime.now().isoformat()
        }
        
        result = supabase.table("bbee_verifications").insert(verification_record).execute()
        
        return {
            "verification_id": result.data[0]["id"],
            "status": "verification_requested",
            "estimated_completion": "2-5 business days"
        }
        
    except Exception as e:
        logger.error(f"Error requesting verification: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/bbee/analytics")
async def get_bbee_analytics():
    """Get B-BBEE compliance analytics"""
    try:
        # Get all compliance records
        result = supabase.table("supplier_compliance_record").select("*").execute()
        records = result.data
        
        if not records:
            return BBEEAnalytics(
                total_suppliers=0,
                compliant_suppliers=0,
                compliance_rate=0.0,
                level_distribution={},
                expiring_certificates=0,
                risk_distribution={},
                verification_backlog=0,
                monthly_trends=[]
            )
        
        total_suppliers = len(records)
        
        # Calculate compliance rate (non-expired certificates)
        today = date.today()
        compliant_suppliers = 0
        expiring_certificates = 0
        level_distribution = {}
        
        for record in records:
            expiry_date = datetime.fromisoformat(record["expiry_date"].replace('Z', '+00:00')).date()
            level = record["bbbee_level"]
            
            # Count by level
            level_distribution[level] = level_distribution.get(level, 0) + 1
            
            # Check compliance
            if expiry_date > today:
                compliant_suppliers += 1
                
                # Check if expiring soon
                days_to_expiry = (expiry_date - today).days
                if days_to_expiry <= 60:
                    expiring_certificates += 1
        
        compliance_rate = (compliant_suppliers / total_suppliers * 100) if total_suppliers > 0 else 0.0
        
        # Get verification backlog
        verification_result = supabase.table("bbee_verifications").select("*").eq("status", "pending").execute()
        verification_backlog = len(verification_result.data)
        
        return BBEEAnalytics(
            total_suppliers=total_suppliers,
            compliant_suppliers=compliant_suppliers,
            compliance_rate=compliance_rate,
            level_distribution=level_distribution,
            expiring_certificates=expiring_certificates,
            risk_distribution={"low": 0, "medium": 0, "high": 0, "critical": 0},  # Would calculate
            verification_backlog=verification_backlog,
            monthly_trends=[]  # Would calculate monthly trends
        )
        
    except Exception as e:
        logger.error(f"Error getting analytics: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/bbee/alerts/{supplier_id}")
async def get_bbee_alerts(supplier_id: str):
    """Get B-BBEE compliance alerts for supplier"""
    try:
        result = supabase.table("compliance_alerts").select("*").eq("supplier_id", supplier_id).eq("resolved", False).order("created_at", desc=True).execute()
        
        return {"alerts": result.data}
        
    except Exception as e:
        logger.error(f"Error getting alerts: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.put("/bbee/alerts/{alert_id}/resolve")
async def resolve_bbee_alert(alert_id: str):
    """Resolve a B-BBEE compliance alert"""
    try:
        result = supabase.table("compliance_alerts").update({
            "resolved": True,
            "resolved_at": datetime.now().isoformat()
        }).eq("alert_id", alert_id).execute()
        
        if not result.data:
            raise HTTPException(status_code=404, detail="Alert not found")
        
        return {"status": "resolved"}
        
    except Exception as e:
        logger.error(f"Error resolving alert: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/bbee/expiring")
async def get_expiring_certificates(days: int = 60):
    """Get certificates expiring within specified days"""
    try:
        cutoff_date = (date.today() + timedelta(days=days)).isoformat()
        
        result = supabase.table("supplier_compliance_record").select("""
            *,
            users!supplier_id(email),
            supplier_blockchain_profiles!supplier_id(company_name)
        """).lte("expiry_date", cutoff_date).order("expiry_date").execute()
        
        return {"expiring_certificates": result.data}
        
    except Exception as e:
        logger.error(f"Error getting expiring certificates: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8003)
