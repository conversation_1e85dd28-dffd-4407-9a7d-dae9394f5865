"""
Unified Opportunities API
Provides endpoints for accessing all bidding opportunities (tenders + government RFQs + bidder RFQs)
with portfolio balance optimization and psychological triggers
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional

from fastapi import FastAPI, HTTPException, Query, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from supabase import create_client, Client
import os

from unified_opportunity_service import unified_opportunity_service, UnifiedOpportunity
from portfolio_balance_ai import portfolio_balance_ai, PortfolioBalance, RFQSuggestion
from government_rfq_detection import GovernmentRFQDetectionService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FastAPI app
app = FastAPI(title="Unified Opportunities API", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Supabase client
supabase: Client = create_client(
    os.getenv("SUPABASE_URL", ""),
    os.getenv("SUPABASE_SERVICE_KEY", "")
)

# Request/Response Models
class OpportunityFilters(BaseModel):
    status: Optional[str] = None
    category: Optional[str] = None
    province: Optional[str] = None
    min_value: Optional[float] = None
    max_value: Optional[float] = None
    opportunity_types: Optional[List[str]] = None  # ["tender", "government_rfq", "bidder_rfq"]

class OpportunityResponse(BaseModel):
    opportunities: List[Dict]
    total_count: int
    portfolio_balance: Dict
    ai_suggestions: List[Dict]
    psychological_triggers: List[Dict]

class PortfolioBalanceResponse(BaseModel):
    balance: Dict
    suggestions: List[Dict]
    urgency_level: str
    financial_impact: Dict

class RFQSuggestionResponse(BaseModel):
    suggestions: List[Dict]
    trigger_messages: List[str]
    psychological_profile: Dict

# Dependency to get current user
async def get_current_user():
    # Placeholder - implement actual authentication
    return {"id": "user-123", "email": "<EMAIL>"}

# =====================================================
# UNIFIED OPPORTUNITIES ENDPOINTS
# =====================================================

@app.get("/api/opportunities", response_model=OpportunityResponse)
async def get_unified_opportunities(
    user: Dict = Depends(get_current_user),
    status: Optional[str] = Query(None, description="Filter by status"),
    category: Optional[str] = Query(None, description="Filter by category"),
    province: Optional[str] = Query(None, description="Filter by province"),
    min_value: Optional[float] = Query(None, description="Minimum estimated value"),
    max_value: Optional[float] = Query(None, description="Maximum estimated value"),
    opportunity_types: Optional[str] = Query(None, description="Comma-separated opportunity types"),
    limit: int = Query(20, description="Number of opportunities to return"),
    offset: int = Query(0, description="Offset for pagination")
):
    """Get all unified opportunities (tenders + government RFQs + bidder RFQs)"""
    
    try:
        user_id = user["id"]
        
        # Parse filters
        filters = {
            "status": status,
            "category": category,
            "province": province,
            "min_value": min_value,
            "max_value": max_value
        }
        
        if opportunity_types:
            filters["opportunity_types"] = opportunity_types.split(",")
        
        # Get opportunities
        opportunities = await unified_opportunity_service.get_all_opportunities(
            user_id, filters, limit, offset
        )
        
        # Get portfolio balance
        balance = await portfolio_balance_ai.analyze_portfolio_balance(user_id)
        
        # Generate AI suggestions
        suggestions = await portfolio_balance_ai.generate_rfq_suggestions(
            user_id, balance, limit=5
        )
        
        # Generate psychological triggers
        triggers = await _generate_psychological_triggers(user_id, balance, opportunities)
        
        return OpportunityResponse(
            opportunities=[opp.__dict__ for opp in opportunities],
            total_count=len(opportunities),
            portfolio_balance=balance.__dict__,
            ai_suggestions=[sugg.__dict__ for sugg in suggestions],
            psychological_triggers=triggers
        )
        
    except Exception as e:
        logger.error(f"Error getting unified opportunities: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/opportunities/{opportunity_id}")
async def get_opportunity_details(
    opportunity_id: str,
    opportunity_type: str = Query(..., description="Type: tender, government_rfq, or bidder_rfq"),
    user: Dict = Depends(get_current_user)
):
    """Get detailed information about a specific opportunity"""
    
    try:
        user_id = user["id"]
        
        # Get opportunity details based on type
        if opportunity_type == "tender":
            result = supabase.table("tenders").select("*").eq("id", opportunity_id).execute()
        elif opportunity_type == "government_rfq":
            result = supabase.table("government_rfqs").select("*").eq("id", opportunity_id).execute()
        elif opportunity_type == "bidder_rfq":
            result = supabase.table("bidder_rfqs").select("*").eq("id", opportunity_id).execute()
        else:
            raise HTTPException(status_code=400, detail="Invalid opportunity type")
        
        if not result.data:
            raise HTTPException(status_code=404, detail="Opportunity not found")
        
        opportunity = result.data[0]
        
        # Get AI analysis for this opportunity
        ai_analysis = await _get_opportunity_ai_analysis(opportunity_id, opportunity_type, user_id)
        
        # Get portfolio impact
        balance = await portfolio_balance_ai.analyze_portfolio_balance(user_id)
        portfolio_impact = await _calculate_opportunity_portfolio_impact(
            opportunity_type, balance
        )
        
        return {
            "opportunity": opportunity,
            "ai_analysis": ai_analysis,
            "portfolio_impact": portfolio_impact,
            "submission_guidance": await _get_submission_guidance(opportunity_type, opportunity)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting opportunity details: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# =====================================================
# PORTFOLIO BALANCE ENDPOINTS
# =====================================================

@app.get("/api/portfolio/balance", response_model=PortfolioBalanceResponse)
async def get_portfolio_balance(user: Dict = Depends(get_current_user)):
    """Get user's current portfolio balance and optimization suggestions"""
    
    try:
        user_id = user["id"]
        
        # Analyze portfolio balance
        balance = await portfolio_balance_ai.analyze_portfolio_balance(user_id)
        
        # Generate suggestions
        suggestions = await portfolio_balance_ai.generate_rfq_suggestions(
            user_id, balance, limit=10
        )
        
        # Calculate financial impact
        financial_impact = {
            "missed_earnings": balance.missed_earnings,
            "potential_earnings": balance.potential_earnings,
            "optimization_opportunity": balance.missed_earnings,
            "target_annual_earnings": 5000000  # R5M target
        }
        
        return PortfolioBalanceResponse(
            balance=balance.__dict__,
            suggestions=[sugg.__dict__ for sugg in suggestions],
            urgency_level=balance.urgency_level.value,
            financial_impact=financial_impact
        )
        
    except Exception as e:
        logger.error(f"Error getting portfolio balance: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/portfolio/optimize")
async def optimize_portfolio_balance(user: Dict = Depends(get_current_user)):
    """Trigger portfolio balance optimization and generate new suggestions"""
    
    try:
        user_id = user["id"]
        
        # Re-analyze balance
        balance = await portfolio_balance_ai.analyze_portfolio_balance(user_id)
        
        # Generate fresh suggestions with higher urgency
        suggestions = await portfolio_balance_ai.generate_rfq_suggestions(
            user_id, balance, limit=15
        )
        
        # Mark as optimization triggered
        await _mark_optimization_triggered(user_id)
        
        return {
            "status": "optimization_triggered",
            "balance": balance.__dict__,
            "suggestions": [sugg.__dict__ for sugg in suggestions],
            "message": "Portfolio optimization complete - new suggestions generated"
        }
        
    except Exception as e:
        logger.error(f"Error optimizing portfolio: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# =====================================================
# RFQ SUGGESTION ENDPOINTS
# =====================================================

@app.get("/api/rfq/suggestions", response_model=RFQSuggestionResponse)
async def get_rfq_suggestions(
    user: Dict = Depends(get_current_user),
    limit: int = Query(10, description="Number of suggestions to return"),
    urgency_filter: Optional[str] = Query(None, description="Filter by urgency level")
):
    """Get AI-generated RFQ suggestions for portfolio optimization"""
    
    try:
        user_id = user["id"]
        
        # Get current balance
        balance = await portfolio_balance_ai.analyze_portfolio_balance(user_id)
        
        # Generate suggestions
        suggestions = await portfolio_balance_ai.generate_rfq_suggestions(
            user_id, balance, limit=limit
        )
        
        # Filter by urgency if specified
        if urgency_filter:
            suggestions = [s for s in suggestions if s.urgency_level.value == urgency_filter]
        
        # Get psychological profile
        psychological_profile = await _get_user_psychological_profile(user_id)
        
        # Extract trigger messages
        trigger_messages = [sugg.trigger_message for sugg in suggestions]
        
        return RFQSuggestionResponse(
            suggestions=[sugg.__dict__ for sugg in suggestions],
            trigger_messages=trigger_messages,
            psychological_profile=psychological_profile
        )
        
    except Exception as e:
        logger.error(f"Error getting RFQ suggestions: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/rfq/suggestions/{suggestion_id}/respond")
async def respond_to_rfq_suggestion(
    suggestion_id: str,
    response: str = Query(..., description="Response: interested, not_interested, acted, ignored"),
    user: Dict = Depends(get_current_user)
):
    """Record user response to RFQ suggestion for AI learning"""
    
    try:
        user_id = user["id"]
        
        # Update suggestion with user response
        result = supabase.table("rfq_suggestions").update({
            "user_response": response,
            "response_timestamp": datetime.now().isoformat(),
            "suggestion_status": "responded"
        }).eq("id", suggestion_id).execute()
        
        if not result.data:
            raise HTTPException(status_code=404, detail="Suggestion not found")
        
        # Log for AI learning
        await _log_suggestion_response(suggestion_id, response, user_id)
        
        return {
            "status": "response_recorded",
            "suggestion_id": suggestion_id,
            "response": response,
            "message": "Thank you for your feedback - this helps improve our AI suggestions"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error recording suggestion response: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# =====================================================
# GOVERNMENT RFQ ENDPOINTS
# =====================================================

@app.get("/api/government-rfqs")
async def get_government_rfqs(
    user: Dict = Depends(get_current_user),
    status: str = Query("active", description="Filter by status"),
    category: Optional[str] = Query(None, description="Filter by category"),
    province: Optional[str] = Query(None, description="Filter by province"),
    limit: int = Query(20, description="Number of RFQs to return"),
    offset: int = Query(0, description="Offset for pagination")
):
    """Get government-issued RFQs"""
    
    try:
        # Build query
        query = supabase.table("government_rfqs").select("*").eq("status", status)
        
        if category:
            query = query.eq("category", category)
        if province:
            query = query.eq("province", province)
            
        query = query.limit(limit).offset(offset).order("closing_date")
        
        result = query.execute()
        
        return {
            "government_rfqs": result.data or [],
            "total_count": len(result.data or []),
            "filters_applied": {
                "status": status,
                "category": category,
                "province": province
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting government RFQs: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/government-rfqs/{rfq_id}/respond")
async def submit_government_rfq_response(
    rfq_id: str,
    response_data: Dict,
    user: Dict = Depends(get_current_user)
):
    """Submit response to government RFQ"""
    
    try:
        user_id = user["id"]
        
        # Validate RFQ exists and is active
        rfq_result = supabase.table("government_rfqs").select("*").eq("id", rfq_id).execute()
        
        if not rfq_result.data:
            raise HTTPException(status_code=404, detail="Government RFQ not found")
        
        rfq = rfq_result.data[0]
        
        if rfq["status"] != "active":
            raise HTTPException(status_code=400, detail="RFQ is not active")
        
        # Create response record
        response_record = {
            "response_id": f"GRFQ-RESP-{datetime.now().strftime('%Y%m%d')}-{hash(user_id) % 1000000:06d}",
            "government_rfq_id": rfq_id,
            "bidder_id": user_id,
            "total_quote_amount": response_data.get("total_quote_amount"),
            "quote_breakdown": response_data.get("quote_breakdown", {}),
            "delivery_timeframe": response_data.get("delivery_timeframe"),
            "payment_terms": response_data.get("payment_terms"),
            "warranty_terms": response_data.get("warranty_terms"),
            "technical_proposal": response_data.get("technical_proposal", {}),
            "compliance_documents": response_data.get("compliance_documents", []),
            "response_status": "submitted",
            "submitted_at": datetime.now().isoformat()
        }
        
        result = supabase.table("government_rfq_responses").insert(response_record).execute()
        
        if not result.data:
            raise HTTPException(status_code=500, detail="Failed to submit response")
        
        # Update portfolio balance (this counts as RFQ activity)
        await _update_portfolio_after_rfq_activity(user_id, "government_rfq_response")
        
        return {
            "status": "response_submitted",
            "response_id": response_record["response_id"],
            "rfq_title": rfq["title"],
            "message": "Your response has been submitted successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error submitting government RFQ response: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# =====================================================
# HELPER FUNCTIONS
# =====================================================

async def _generate_psychological_triggers(
    user_id: str,
    balance: PortfolioBalance,
    opportunities: List[UnifiedOpportunity]
) -> List[Dict]:
    """Generate psychological triggers based on opportunities and balance"""
    
    triggers = []
    
    try:
        # Balance-based triggers
        if balance.balance_status.value == "rfq_deficit":
            triggers.append({
                "type": "balance_alert",
                "urgency": balance.urgency_level.value,
                "message": f"🎯 PORTFOLIO ALERT: Your RFQ ratio is {balance.current_rfq_ratio:.1f}% - target is 60%",
                "action": "create_rfq_or_bid_government_rfq",
                "psychological_principle": "loss_aversion"
            })
        
        # Opportunity-based triggers
        closing_soon = [opp for opp in opportunities if opp.status.value == "closing_soon"]
        if closing_soon:
            triggers.append({
                "type": "urgency_alert",
                "urgency": "high",
                "message": f"⏰ URGENT: {len(closing_soon)} opportunities closing in 3 days!",
                "action": "review_closing_opportunities",
                "psychological_principle": "scarcity"
            })
        
        # Success rate triggers
        high_success_rfqs = [opp for opp in opportunities if opp.success_probability > 85 and opp.opportunity_type.value in ["government_rfq", "bidder_rfq"]]
        if high_success_rfqs:
            triggers.append({
                "type": "success_opportunity",
                "urgency": "medium",
                "message": f"🚀 HIGH SUCCESS: {len(high_success_rfqs)} RFQs with 85%+ success rate available",
                "action": "bid_high_success_rfqs",
                "psychological_principle": "instant_gratification"
            })
        
    except Exception as e:
        logger.error(f"Error generating psychological triggers: {e}")
    
    return triggers

async def _get_opportunity_ai_analysis(opportunity_id: str, opportunity_type: str, user_id: str) -> Dict:
    """Get AI analysis for specific opportunity"""
    
    # Placeholder - implement detailed AI analysis
    return {
        "match_score": 85.0,
        "success_probability": 78.0,
        "competition_analysis": "Medium competition expected",
        "recommendation": "bid",
        "key_factors": [
            "Strong match with your expertise",
            "Favorable location",
            "Reasonable timeline"
        ]
    }

async def _calculate_opportunity_portfolio_impact(opportunity_type: str, balance: PortfolioBalance) -> Dict:
    """Calculate how opportunity affects portfolio balance"""
    
    if opportunity_type in ["government_rfq", "bidder_rfq"]:
        category = "rfq_activity"
        current_ratio = balance.current_rfq_ratio
        target_ratio = 60.0
    else:
        category = "tender_activity"
        current_ratio = balance.current_tender_ratio
        target_ratio = 40.0
    
    new_ratio = ((current_ratio * balance.total_activities) + 1) / (balance.total_activities + 1) * 100
    
    return {
        "category": category,
        "current_ratio": current_ratio,
        "new_ratio": new_ratio,
        "improvement": new_ratio - current_ratio,
        "moves_toward_target": abs(new_ratio - target_ratio) < abs(current_ratio - target_ratio)
    }

async def _get_submission_guidance(opportunity_type: str, opportunity: Dict) -> Dict:
    """Get submission guidance for opportunity"""
    
    guidance = {
        "tender": {
            "submission_type": "Formal Bid",
            "required_documents": ["Technical Proposal", "Commercial Proposal", "Compliance Documents"],
            "timeline": "3-4 weeks typical preparation time",
            "success_tips": ["Focus on compliance", "Competitive pricing", "Strong technical approach"]
        },
        "government_rfq": {
            "submission_type": "Quote Response",
            "required_documents": ["Price Quote", "Technical Specifications", "Delivery Terms"],
            "timeline": "2-3 days typical preparation time",
            "success_tips": ["Quick response", "Competitive pricing", "Clear delivery terms"]
        },
        "bidder_rfq": {
            "submission_type": "Supplier Response",
            "required_documents": ["Quote", "Product Specifications", "Terms & Conditions"],
            "timeline": "1-2 days typical preparation time",
            "success_tips": ["Fast response", "Detailed specifications", "Flexible terms"]
        }
    }
    
    return guidance.get(opportunity_type, guidance["tender"])

async def _mark_optimization_triggered(user_id: str):
    """Mark that portfolio optimization was triggered"""
    
    try:
        supabase.table("portfolio_balance_tracking").update({
            "last_optimization_triggered": datetime.now().isoformat()
        }).eq("user_id", user_id).execute()
        
    except Exception as e:
        logger.error(f"Error marking optimization triggered: {e}")

async def _get_user_psychological_profile(user_id: str) -> Dict:
    """Get user's psychological profile"""
    
    try:
        result = supabase.table("users").select("psychological_profile").eq("id", user_id).execute()
        
        if result.data and result.data[0].get("psychological_profile"):
            return result.data[0]["psychological_profile"]
        else:
            return {"archetype": "analyst", "confidence_level": 0.75}
            
    except Exception as e:
        logger.error(f"Error getting psychological profile: {e}")
        return {"archetype": "analyst"}

async def _log_suggestion_response(suggestion_id: str, response: str, user_id: str):
    """Log suggestion response for AI learning"""
    
    try:
        # This would feed into ML model for improving suggestions
        logger.info(f"Suggestion {suggestion_id} response: {response} from user {user_id}")
        
    except Exception as e:
        logger.error(f"Error logging suggestion response: {e}")

async def _update_portfolio_after_rfq_activity(user_id: str, activity_type: str):
    """Update portfolio balance after RFQ activity"""
    
    try:
        # Trigger portfolio balance recalculation
        await portfolio_balance_ai.analyze_portfolio_balance(user_id)
        
    except Exception as e:
        logger.error(f"Error updating portfolio after activity: {e}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
