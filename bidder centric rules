Of course. Here is the complete project blueprint with the technology stack updated to Java and Node.js, formatted as a single `.txt` file.

```text
Project Blueprint: Bidbeez Backend Microservices (Java/Node.js Stack)

Objective: Construct a robust microservices backend to support the Bidbeez bidder-centric frontend. The architecture must be scalable, secure, and containerized, with each service responsible for a distinct business domain.

---

### **Level 1: General Architectural Rules (System DNA)**

These rules apply to every microservice generated.

**1. Technology Stack Selection (Choose ONE per service):**
    *   **Option A: Java Stack**
        *   **Language:** Java 17+
        *   **Framework:** Spring Boot 3+ with Spring WebFlux for reactive, non-blocking APIs.
        *   **Data Modeling & Validation:** JPA (e.g., Hibernate) for entities. Use DTOs (Data Transfer Objects) with `jakarta.validation` annotations for API contracts.
        *   **Database Access:** Spring Data R2DBC for reactive access to the database.
        *   **Build Tool:** <PERSON>ven or Gradle.

    *   **Option B: Node.js Stack**
        *   **Language:** TypeScript
        *   **Framework:** NestJS (a progressive Node.js framework for building efficient, reliable, and scalable server-side applications).
        *   **Data Modeling & Validation:** Prisma or TypeORM for the ORM. Use DTOs (Data Transfer Objects) with `class-validator` and `class-transformer` for robust API contracts.
        *   **Asynchronous Support:** All I/O operations MUST be implemented asynchronously using native `async`/`await`.

**2. Architecture & Communication:**
    *   **API Gateway:** An API Gateway (e.g., Spring Cloud Gateway for the Java stack, or a separate NestJS gateway/custom Express server for the Node.js stack) will be the single entry point for all frontend requests. It will handle request routing, authentication, and rate limiting.
    *   **Microservices:** The system will be composed of the following distinct microservices: `Authentication`, `User & Onboarding`, `Tenders`, `Ecosystem`, `Courier`, `Bee`, `Gamification`, and `Analytics`.
    *   **Database:** Each microservice MUST have its own dedicated PostgreSQL database or schema to ensure loose coupling.
    *   **Inter-Service Communication:** Services will communicate via synchronous RESTful API calls for direct queries (using `WebClient` in Java or `HttpModule` in NestJS). For events and decoupling long-running tasks, a message broker (e.g., RabbitMQ) should be used.

**3. Authentication & Authorization:**
    *   **Mechanism:** Implement JWT-based authentication.
    *   **Flow:** The `Authentication Service` will issue JWTs upon successful login. The API Gateway is responsible for validating the JWT from the `Authorization: Bearer <token>` header on every incoming request to a protected route. Use Spring Security (Java) or Passport.js with `passport-jwt` (NestJS) for implementation.
    *   **Payload:** The validated token's payload (containing `user_id`, `user_type`, etc.) will be passed to the downstream microservices in a request header (e.g., `X-User-Info`).

**4. Configuration & Deployment:**
    *   **Configuration:** All configuration values (database connection strings, secret keys, API URLs) MUST be loaded from environment variables. Use `application.properties` or `application.yml` (Java) or a `.env` file with the `@nestjs/config` module (Node.js).
    *   **Containerization:** Every microservice, including the database and API Gateway, MUST be fully containerized using Docker and orchestrated with a `docker-compose.yml` file for local development. Provide a multi-stage `Dockerfile` for each service to create optimized production images.

**5. Error Handling & Logging:**
    *   **Error Responses:** All API errors MUST return a standardized JSON response: `{"message": "Error message", "statusCode": xxx, "timestamp": "..."}`. Use global exception handlers (`@ControllerAdvice` in Spring Boot, Exception Filters in NestJS).
    *   **Logging:** Implement structured logging (e.g., using Logback for Java, or Winston/Pino for Node.js). Logs must include a timestamp, log level, and relevant context.

---

### **Level 2: Microservice-Specific Implementation Rules**

#### **Service 1: Authentication Service**

*   **Purpose:** Manages user registration, login, and JWT lifecycle.
*   **Database Entity/Model:**
    *   `User`: `id` (UUID, primary key), `email` (string, unique), `username` (string), `passwordHash` (string), `userType` (enum: 'corporate', 'sme', 'freelancer'), `createdAt` (datetime).
*   **API Endpoints:**
    *   **`POST /auth/register`**
        *   **Request DTO:** `email`, `password`, `username`, `userType`.
        *   **Logic:** Hash the password (e.g., using BCrypt). Create a new `User` record.
        *   **Success Response (201 Created):** `{ "user": { "id": ..., "email": ... }, "token": "jwt_token" }`
    *   **`POST /auth/login`**
        *   **Request DTO:** `email`, `password`.
        *   **Logic:** Verify email and password hash. If valid, generate a JWT containing `userId`, `userType`, and an expiration date.
        *   **Success Response (200 OK):** `{ "user": { "id": ..., "email": ... }, "token": "jwt_token" }`
    *   **`GET /auth/me`**
        *   **Authorization:** Required.
        *   **Logic:** Fetches user details based on the `userId` from the validated JWT.
        *   **Success Response (200 OK):** `{ "user": { "id": ..., "email": ..., "username": ... } }`
    *   **`POST /auth/logout`**
        *   **Logic:** This endpoint is primarily for the frontend to clear its token. A stateful backend might implement token blacklisting.
        *   **Success Response (200 OK):** `{ "message": "Logout successful" }`

#### **Service 2: User & Onboarding Service**

*   **Purpose:** Manages user profile data, onboarding state, and intelligence analysis.
*   **Database Entity/Model:**
    *   `UserProfile`: `userId` (UUID, foreign key), `profileIntelligence` (JSONB), `onboardingState` (JSONB), `emotionalState` (JSONB).
*   **API Endpoints:**
    *   **`GET /onboarding/profile-analysis/{userId}`**
        *   **Logic:** Analyze user activity to compute `rfqFocus`, `missedOpportunities`, `avgOpportunityValue`, `competitivePosition`, and `recoveryPotential`.
        *   **Success Response (200 OK):** A `ProfileIntelligence` JSON object.
    *   **`POST /onboarding/emotional-tracking/{userId}`**
        *   **Request DTO:** An `EmotionalState` JSON object.
        *   **Logic:** Store the user's detected emotional state and behavior patterns.
        *   **Success Response (200 OK):** `{ "status": "tracked" }`
    *   **`POST /onboarding/recovery-plan/{userId}`**
        *   **Request DTO:** A `ProfileIntelligence` JSON object.
        *   **Logic:** Generate a strategic recovery plan.
        *   **Success Response (200 OK):** A JSON object detailing the recovery plan steps.
    *   **`GET /user/{userId}/stats`**
        *   **Logic:** Fetch user statistics like `activeBids` and `pendingTasks` by querying other relevant services.
        *   **Success Response (200 OK):** `{ "activeBids": int, "pendingTasks": int }`
    *   **`POST /user/{userId}/update`**
        *   **Request DTO:** `username`, `email`.
        *   **Logic:** Update user profile information in the database.
        *   **Success Response (200 OK):** `{ "success": true }`

#### **Service 3: Tenders Service**

*   **Purpose:** Manages all tender data, search, filtering, and bid submissions.
*   **Database Entities/Models:**
    *   `Tender`: `id` (string, primary key), `title`, `issuer`, `issuerAddress`, `estimatedValue`, `deadline`, `location`, `coordinates`, `bbbeeRequired`, `matchScore`, `competitorCount`, `urgencyLevel`, `category`, `riskScore`, `aiInsights`, `description`.
    *   `Bid`: `id` (UUID), `tenderId` (string, foreign key), `userId` (UUID), `amount`, `status` (enum: 'submitted', 'awarded', 'lost'), `bidData` (JSONB).
*   **API Endpoints:**
    *   **`GET /tenders`**
        *   **Query Params:** `limit`, `offset`, `userId`, `filters` (JSON string).
        *   **Logic:** Fetch a paginated list of tenders. Apply personalization if `userId` is present.
        *   **Success Response (200 OK):** `{ "tenders": [TenderObject, ...] }`
    *   **`POST /bids/submit`**
        *   **Authorization:** Required.
        *   **Request DTO:** `tenderId`, `bidAmount`, `team`, `equipment`, `compliance`, etc.
        *   **Logic:** Create a `Bid` record. Trigger a `bid_submitted` event.
        *   **Success Response (201 Created):** `{ "status": "success", "bidId": ... }`
    *   **`GET /bids`**
        *   **Authorization:** Required.
        *   **Query Params:** `userId`.
        *   **Logic:** Fetch all bids submitted by the specified user.
        *   **Success Response (200 OK):** `[BidObject, ...]`
    *   **`GET /tender/{id}/bid`**
        *   **Authorization:** Required.
        *   **Logic:** Retrieve a user's saved or submitted bid data for a specific tender.
        *   **Success Response (200 OK):** A bid data JSON object.

#### **Service 4: Ecosystem Service**

*   **Purpose:** Acts as a facade/aggregator for fetching documents and data.
*   **Logic:** This service does not have its own database. It makes downstream API calls to other services based on the mappings defined in the frontend's `EcosystemDocumentService.ts`.
*   **API Endpoints:**
    *   **`GET /ecosystem/connections/{bidderId}`**
        *   **Logic:** Fetches the bidder's connections to `skillsync`, `toolsync`, `contractorsync`, etc.
        *   **Success Response (200 OK):** `{ "skillsync": [...], "toolsync": [...] }`
    *   **`POST /skillsync/professionals/{profId}/documents`**
        *   **Logic:** Mocks fetching documents for a professional from the SkillSync system.
        *   **Success Response (200 OK):** `{ "documents": [...], "permissions": ... }`
    *   **(Implement similar mock endpoints for `toolsync`, `contractorsync`, `supplier` as defined in `EcosystemDocumentService.ts`)**

#### **Service 5: Courier Service**

*   **Purpose:** Manages courier requests for document pickup and delivery.
*   **Database Entity/Model:**
    *   `CourierRequest`: `id` (UUID), `tenderId`, `userId`, `pickupAddress`, `deliveryAddress`, `documentType`, `status`, `trackingHistory` (JSONB).
*   **API Endpoints:**
    *   **`POST /courier/requests`**
        *   **Logic:** Creates a standard delivery request (bidder to issuer).
        *   **Success Response (201 Created):** `{ "requestId": ..., "status": "created" }`
    *   **`POST /courier/pickup`**
        *   **Logic:** Creates a pickup request (issuer to bidder).
        *   **Success Response (201 Created):** `{ "requestId": ..., "status": "created" }`
    *   **`GET /courier/track/{tenderId}`**
        *   **Logic:** Retrieves all tracking events for a given tender.
        *   **Success Response (200 OK):** `[{ "id": ..., "status": ..., "timestamp": ..., "direction": "pickup/delivery" }, ...]`

#### **Service 6: Bee Service**

*   **Purpose:** Manages tasks assigned to human agents (Bees).
*   **Database Entity/Model:**
    *   `BeeTask`: `id` (UUID), `tenderId`, `userId`, `taskType` (enum), `details` (JSONB), `status`, `assignedBeeId`, `trackingHistory` (JSONB).
*   **API Endpoints:**
    *   **`GET /bee/available`**
        *   **Logic:** Returns the count of available Bees.
        *   **Success Response (200 OK):** `{ "count": int }`
    *   **`POST /bee/tasks`**
        *   **Request DTO:** `tenderId`, `taskType`, and other details from `BeeTaskForm`.
        *   **Logic:** Creates a new task and assigns it to an available Bee.
        *   **Success Response (201 Created):** `{ "taskId": ..., "status": "created" }`
    *   **`GET /bee/tasks/track/{tenderId}`**
        *   **Logic:** Retrieves all tracking events for Bee tasks related to a tender.
        *   **Success Response (200 OK):** `[{ "id": ..., "taskType": ..., "status": ..., "timestamp": ..., "beeName": ... }, ...]`

#### **Service 7 & 8: Gamification & Analytics Services**

*   **Purpose:** These services handle event-driven logic. They subscribe to events from other services (e.g., `bid_submitted`, `swipe_right`) via a message broker.
*   **Logic (Event-Driven):**
    *   **Gamification Service:**
        *   Listens for events to update user XP, streaks, and achievements. `NeuroMarketingEngine` logic should be implemented here.
    *   **Analytics Service:**
        *   Listens for events (`page_view`, `user_login`, etc.) to store in an analytics database for reporting.
*   **API Endpoints:**
    *   **`POST /analytics/event`** and **`POST /analytics/page-view`**: Simple endpoints to ingest events directly from the frontend if a message broker is not used initially.
    *   **`GET /tender/{id}/analytics`**: Fetches aggregated analytics for a tender.
``````text
Project Blueprint: Bee Module Backend Microservices

Objective: Construct a backend microservices architecture to power the Bee Module Bidder Portal. The system must be scalable, secure, and fully containerized, mirroring the domain-driven structure of the provided React frontend.

---

### **Level 1: General Architectural Rules (System DNA)**

These rules are mandatory for every microservice generated.

**1. Technology Stack (Choose ONE stack per service):**
    *   **Option A: Java Stack**
        *   **Language/Framework:** Java 17+ with Spring Boot 3+. Use Spring WebFlux for all API controllers to ensure a non-blocking, reactive architecture.
        *   **Data Access:** Spring Data R2DBC for reactive database communication.
        *   **Data Modeling:** JPA entities for database mapping, but use dedicated DTO classes with `jakarta.validation` annotations for all API request/response bodies.
        *   **Build Tool:** Maven.

    *   **Option B: Node.js Stack**
        *   **Language/Framework:** TypeScript with NestJS.
        *   **Data Access & ORM:** Prisma. Define the schema in `schema.prisma`.
        *   **Data Modeling:** Use DTO classes with `class-validator` and `class-transformer` for all API request/response bodies.
        *   **Asynchronous Model:** All I/O-bound operations MUST be `async`/`await`.

**2. Core Architecture:**
    *   **Microservice Boundaries:** The backend will consist of the following microservices, each with its own dedicated PostgreSQL database schema: `Auth`, `Dashboard`, `Tasks`, `Workflows`, `Bees`, `Payments`.
    *   **API Gateway:** A dedicated API Gateway (Spring Cloud Gateway or a NestJS Gateway) will be the single entry point. It is responsible for routing requests to the correct microservice, validating JWTs, and enforcing rate limits.
    *   **Inter-Service Communication:**
        *   **Synchronous:** Use a REST client (`WebClient` in Java, `HttpModule` in NestJS) for direct data retrieval between services (e.g., Dashboard service fetching data from Tasks service).
        *   **Asynchronous:** Use a message broker (RabbitMQ) for event-driven communication (e.g., when a task is created, publish a `TaskCreated` event).

**3. Authentication and Security:**
    *   **JWT Flow:** The `Auth Service` will generate JWTs upon login. The API Gateway MUST validate the `Authorization: Bearer <token>` header on all protected routes.
    *   **User Context:** The gateway will decode the validated JWT and pass the user's information (e.g., `userId`, `role`, `companyId`) to downstream services via a custom HTTP header (e.g., `X-User-Context`).
    *   **Password Hashing:** All user passwords MUST be hashed using BCrypt.

**4. Configuration and Deployment:**
    *   **Environment Variables:** All configuration (database URLs, JWT secrets, port numbers) MUST be managed through environment variables.
    *   **Containerization:** Every microservice and the PostgreSQL database MUST be containerized using Docker. A `docker-compose.yml` file must be provided to orchestrate the entire system for local development. Each service's `Dockerfile` must use a multi-stage build to create a small, optimized production image.

**5. API Standards and Error Handling:**
    *   **API Design:** All APIs MUST be RESTful.
    *   **Error Structure:** All API errors must return a standardized JSON object: `{"statusCode": number, "message": string, "timestamp": string}`. Implement global exception handlers (`@ControllerAdvice` in Spring, Exception Filters in NestJS) to ensure this consistency.
    *   **Logging:** Implement structured JSON logging (Logback for Java, Pino for Node.js).

---

### **Level 2: Microservice-Specific Implementation Rules**

#### **Service 1: Auth Service**

*   **Purpose:** Manages user identity, registration, and login.
*   **Database Entity (`User`):** `id` (UUID), `email` (string, unique), `passwordHash` (string), `fullName` (string), `role` (string, e.g., 'client', 'admin'), `companyId` (UUID, nullable).
*   **API Endpoints:**
    *   **`POST /auth/login`**
        *   **Request DTO:** `email`, `password`.
        *   **Logic:** Validate credentials. On success, issue a JWT.
        *   **Success Response (200 OK):** `{ "token": "jwt_string", "user": { "id": ..., "email": ... } }`
    *   **`POST /auth/register`**
        *   **Request DTO:** `email`, `password`, `fullName`, `companyName`.
        *   **Logic:** Create a `Company` and a `User`.
        *   **Success Response (201 Created):** `{ "message": "Registration successful" }`
    *   **`POST /auth/forgot-password`**
        *   **Request DTO:** `email`.
        *   **Logic:** Generate a password reset token and send a reset link (mock the email sending).
        *   **Success Response (200 OK):** `{ "message": "Password reset link sent" }`

#### **Service 2: Dashboard Service**

*   **Purpose:** Aggregates data for the main dashboard view.
*   **Database:** This service has **no database**. It fetches data from other services.
*   **API Endpoints:**
    *   **`GET /dashboard/stats`**
        *   **Logic:** Make inter-service calls to the `Tasks`, `Bees`, and `Payments` services to get their respective counts and totals.
        *   **Success Response (200 OK):** `{ "totalTasks": number, "activeBees": number, "totalPayments": number, "completionRate": number }`

#### **Service 3: Tasks Service**

*   **Purpose:** Manages the entire lifecycle of tasks.
*   **Database Entities:**
    *   `Task`: `id` (UUID), `title` (string), `description` (string), `taskType` (enum), `payment` (decimal), `deadline` (datetime), `estimatedDuration` (integer, in minutes), `priority` (enum: 'low', 'medium', 'high', 'urgent'), `status` (enum: 'open', 'assigned', 'in_progress', 'completed', 'cancelled'), `location` (JSONB: `{latitude, longitude, address}`), `clientId` (UUID), `assignedBeeId` (UUID, nullable).
    *   `TaskRequirement`: `id` (UUID), `taskId` (foreign key), `description` (string), `mandatory` (boolean).
*   **API Endpoints:**
    *   **`POST /tasks`**
        *   **Request DTO:** Matches the `TaskFormData` from `CreateTask.tsx`.
        *   **Logic:** Create `Task` and associated `TaskRequirement` records. Publish a `TaskCreated` event.
        *   **Success Response (201 Created):** The created `Task` object.
    *   **`GET /tasks`**
        *   **Logic:** Return a paginated list of tasks with filtering capabilities.
        *   **Success Response (200 OK):** `{ "tasks": [TaskObject, ...], "totalPages": number }`
    *   **`GET /tasks/{id}`**
        *   **Logic:** Return a single task by its ID.
        *   **Success Response (200 OK):** `Task` object.
    *   **`PUT /tasks/{id}`**
        *   **Logic:** Update a task's details.
        *   **Success Response (200 OK):** The updated `Task` object.
    *   **`POST /tasks/assign`**
        *   **Request DTO:** `taskId`, `beeId`.
        *   **Logic:** Update the `assignedBeeId` on the `Task` record.
        *   **Success Response (200 OK):** `{ "message": "Task assigned" }`
    *   **`POST /tasks/{taskId}/cancel`**
        *   **Request DTO:** `reason`.
        *   **Logic:** Update the task status to 'cancelled'.
        *   **Success Response (200 OK):** `{ "message": "Task cancelled" }`
    *   **`GET /tasks/analytics`**
        *   **Logic:** Provide data for `TaskAnalyticsChart`.
        *   **Success Response (200 OK):** `{ "labels": [...], "data": [...] }`

#### **Service 4: Bees Service**

*   **Purpose:** Manages bee profiles and real-time tracking.
*   **Database Entity (`Bee`):** `id` (UUID), `fullName` (string), `phoneNumber` (string), `email` (string, unique), `rating` (decimal), `isActive` (boolean), `location` (JSONB: `{latitude, longitude, lastUpdated}`).
*   **API Endpoints:**
    *   **`GET /bees/active`**
        *   **Logic:** Return a list of all bees where `isActive` is true.
        *   **Success Response (200 OK):** `[BeeObject, ...]`
    *   **`GET /bees/{id}`**
        *   **Logic:** Get a single bee's profile.
        *   **Success Response (200 OK):** `Bee` object.
*   **WebSocket Server:**
    *   Must host a WebSocket endpoint (e.g., at `/ws`).
    *   It must emit the following events:
        *   `bee_location_update`: Sent whenever a bee's location changes. Payload: `{ beeId, latitude, longitude }`.
        *   `bee_task_update`: Sent when a bee's task status changes. Payload: `{ beeId, taskId, status, progress }`.

#### **Service 5: Payments Service**

*   **Purpose:** Manages all financial transactions.
*   **Database Entity (`Payment`):** `id` (UUID), `reference` (string, unique), `amount` (decimal), `taskId` (foreign key), `beeId` (foreign key), `clientId` (foreign key), `type` (enum: 'task_payment', 'bonus', 'reimbursement'), `status` (enum: 'completed', 'processing', 'failed'), `method` (string), `createdAt` (datetime), `processedAt` (datetime, nullable).
*   **API Endpoints:**
    *   **`GET /payments/history`**
        *   **Query Params:** `status`, `type`, `dateFrom`, `dateTo`, `page`, `limit`.
        *   **Logic:** Return a paginated and filtered list of payment transactions. Include a summary object.
        *   **Success Response (200 OK):** `{ "payments": [PaymentObject, ...], "summary": { "totalAmount": ..., "completedCount": ... } }`
    *   **`GET /payments/overview`**
        *   **Logic:** Provide data for `PaymentChart`.
        *   **Success Response (200 OK):** `{ "labels": [...], "data": [...] }`

#### **Service 6: Workflows Service** (Optional, based on frontend)

*   **Purpose:** Manages multi-step task workflows.
*   **Database Entities:**
    *   `Workflow`: `id` (UUID), `name` (string), `description` (string).
    *   `WorkflowStep`: `id` (UUID), `workflowId` (foreign key), `stepNumber` (integer), `taskTypeId` (foreign key), `description` (string).
*   **API Endpoints:**
    *   **`POST /workflows`**
    *   **`GET /workflows`**
    *   **`GET /workflows/{id}`**
```