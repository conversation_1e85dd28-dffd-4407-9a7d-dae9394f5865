import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  <PERSON>,
  Card,
  CardContent,
  CardHeader,
  Typography,
  TextField,
  Button,
  Grid,
  Slider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  LinearProgress,
  Alert,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Paper,
  Divider,
  Stack
} from '@mui/material';
import {
  TrendingUp,
  GpsFixed as Target,
  Psychology,
  FlashOn,
  Assessment,
  EmojiEvents,
  MonetizationOn,
  Speed
} from '@mui/icons-material';
import { Pie<PERSON>hart, Pie, Cell, ResponsiveContainer, LineChart, Line, XAxis, YAxis, Tooltip } from 'recharts';

interface BidderOnboardingData {
  // Target Setting
  targetTurnover: number;
  currentCapacity: number;
  riskTolerance: 'low' | 'medium' | 'high';
  preferredCategories: string[];
  preferredProvinces: string[];

  // Value Preferences
  minimumValue: number;
  maximumValue: number;

  // Opportunity Configuration
  opportunityPreferences: {
    tenderNotifications: boolean;
    governmentRfqNotifications: boolean;
    bidderRfqNotifications: boolean;
    urgentOpportunities: boolean;
    portfolioBalanceAlerts: boolean;
  };

  // Notification Preferences
  notificationChannels: {
    email: boolean;
    sms: boolean;
    whatsapp: boolean;
    push: boolean;
  };

  // Calculated Mix
  recommendedMix: {
    rfqPercentage: number;
    tenderPercentage: number;
    monthlyRFQTarget: number;
    monthlyTenderTarget: number;
    projectedEarnings: number;
    quickWinPotential: number;
  };

  // Psychological Profile
  psychologicalProfile: {
    archetype: 'achiever' | 'hunter' | 'relationship_builder' | 'analyst';
    motivationFactors: string[];
    confidenceLevel: number;
  };
}

const categories = [
  'Construction & Infrastructure',
  'IT & Technology',
  'Professional Services',
  'Security Services',
  'Cleaning & Maintenance',
  'Catering & Events',
  'Transport & Logistics',
  'Medical & Healthcare',
  'Education & Training',
  'Engineering Services'
];

const BidderOnboarding: React.FC = () => {
  const navigate = useNavigate();
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [onboardingData, setOnboardingData] = useState<BidderOnboardingData>({
    targetTurnover: 5000000,
    currentCapacity: 80,
    riskTolerance: 'medium',
    preferredCategories: [],
    recommendedMix: {
      rfqPercentage: 60,
      tenderPercentage: 40,
      monthlyRFQTarget: 12,
      monthlyTenderTarget: 3,
      projectedEarnings: 0,
      quickWinPotential: 0
    },
    psychologicalProfile: {
      archetype: 'achiever',
      motivationFactors: [],
      confidenceLevel: 75
    }
  });

  // Calculate optimal mix based on target turnover
  useEffect(() => {
    calculateOptimalMix();
  }, [onboardingData.targetTurnover, onboardingData.riskTolerance, onboardingData.currentCapacity]);

  const calculateOptimalMix = () => {
    const { targetTurnover, riskTolerance, currentCapacity } = onboardingData;
    
    // Base mix: 60% RFQ, 40% Tender
    let rfqPercentage = 60;
    let tenderPercentage = 40;
    
    // Adjust based on risk tolerance
    if (riskTolerance === 'low') {
      rfqPercentage = 70; // More RFQs for safer wins
      tenderPercentage = 30;
    } else if (riskTolerance === 'high') {
      rfqPercentage = 50; // More tenders for bigger rewards
      tenderPercentage = 50;
    }
    
    // Calculate monthly targets
    const monthlyTarget = targetTurnover / 12;
    const avgRFQValue = 150000; // Average RFQ value
    const avgTenderValue = 2500000; // Average tender value
    
    const monthlyRFQTarget = Math.round((monthlyTarget * (rfqPercentage / 100)) / avgRFQValue);
    const monthlyTenderTarget = Math.round((monthlyTarget * (tenderPercentage / 100)) / avgTenderValue);
    
    const projectedEarnings = (monthlyRFQTarget * avgRFQValue + monthlyTenderTarget * avgTenderValue) * 12;
    const quickWinPotential = monthlyRFQTarget * avgRFQValue * 12;
    
    setOnboardingData(prev => ({
      ...prev,
      recommendedMix: {
        rfqPercentage,
        tenderPercentage,
        monthlyRFQTarget,
        monthlyTenderTarget,
        projectedEarnings,
        quickWinPotential
      }
    }));
  };

  const handleNext = () => {
    setActiveStep(prev => prev + 1);
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  const handleComplete = async () => {
    setLoading(true);
    try {
      // Save onboarding data to backend
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
      const response = await fetch(`${apiUrl}/api/bidder/onboarding`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken') || ''}`
        },
        body: JSON.stringify(onboardingData)
      });

      if (response.ok) {
        // Navigate to dashboard with new mix settings
        navigate('/dashboard', {
          state: { onboardingComplete: true, mixSettings: onboardingData.recommendedMix }
        });
      } else {
        console.error('Onboarding failed:', response.statusText);
      }
    } catch (error) {
      console.error('Onboarding failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const pieData = [
    { name: 'RFQ', value: onboardingData.recommendedMix.rfqPercentage, color: '#4CAF50' },
    { name: 'Tender', value: onboardingData.recommendedMix.tenderPercentage, color: '#2196F3' }
  ];

  const steps = [
    'Set Your Target',
    'Choose Categories',
    'Opportunity Preferences',
    'Notification Setup',
    'Review Your Mix',
    'Complete Setup'
  ];

  return (
    <Box sx={{ 
      minHeight: '100vh', 
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      py: 4,
      px: 2
    }}>
      <Box sx={{ maxWidth: 800, mx: 'auto' }}>
        {/* Header */}
        <Card sx={{ mb: 3, background: 'rgba(255,255,255,0.95)' }}>
          <CardContent sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant="h3" sx={{ fontWeight: 'bold', mb: 2, color: '#1976d2' }}>
              🚀 Welcome to Your Opportunity Journey!
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ mb: 3 }}>
              Let's optimize your RFQ/Tender mix for maximum success
            </Typography>
            
            <LinearProgress 
              variant="determinate" 
              value={(activeStep / (steps.length - 1)) * 100}
              sx={{ 
                height: 8, 
                borderRadius: 4,
                backgroundColor: '#e0e0e0',
                '& .MuiLinearProgress-bar': {
                  background: 'linear-gradient(90deg, #4CAF50, #2196F3)'
                }
              }}
            />
            <Typography variant="body2" sx={{ mt: 1 }}>
              Step {activeStep + 1} of {steps.length}
            </Typography>
          </CardContent>
        </Card>

        {/* Stepper Content */}
        <Card sx={{ background: 'rgba(255,255,255,0.95)' }}>
          <CardContent sx={{ p: 4 }}>
            <Stepper activeStep={activeStep} orientation="vertical">
              
              {/* Step 1: Target Setting */}
              <Step>
                <StepLabel>
                  <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Target color="primary" />
                    Set Your Annual Target
                  </Typography>
                </StepLabel>
                <StepContent>
                  <Box sx={{ py: 3 }}>
                    <Typography variant="body1" sx={{ mb: 3 }}>
                      What's your target turnover for this year? We'll create the perfect RFQ/Tender mix to achieve it.
                    </Typography>
                    
                    <Grid container spacing={3}>
                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="Annual Target Turnover (ZAR)"
                          type="number"
                          value={onboardingData.targetTurnover}
                          onChange={(e) => setOnboardingData(prev => ({
                            ...prev,
                            targetTurnover: Number(e.target.value)
                          }))}
                          InputProps={{
                            startAdornment: 'R',
                          }}
                        />
                      </Grid>
                      
                      <Grid item xs={12} md={6}>
                        <Typography gutterBottom>Current Capacity (%)</Typography>
                        <Slider
                          value={onboardingData.currentCapacity}
                          onChange={(_, value) => setOnboardingData(prev => ({
                            ...prev,
                            currentCapacity: value as number
                          }))}
                          valueLabelDisplay="auto"
                          step={10}
                          marks
                          min={20}
                          max={100}
                        />
                      </Grid>
                      
                      <Grid item xs={12}>
                        <FormControl fullWidth>
                          <InputLabel>Risk Tolerance</InputLabel>
                          <Select
                            value={onboardingData.riskTolerance}
                            onChange={(e) => setOnboardingData(prev => ({
                              ...prev,
                              riskTolerance: e.target.value as 'low' | 'medium' | 'high'
                            }))}
                          >
                            <MenuItem value="low">Low - Prefer quick wins (70% RFQ)</MenuItem>
                            <MenuItem value="medium">Medium - Balanced approach (60% RFQ)</MenuItem>
                            <MenuItem value="high">High - Go for big rewards (50% RFQ)</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>
                    </Grid>
                    
                    <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
                      <Button variant="contained" onClick={handleNext}>
                        Calculate My Mix
                      </Button>
                    </Box>
                  </Box>
                </StepContent>
              </Step>

              {/* Step 2: Category Selection */}
              <Step>
                <StepLabel>
                  <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Assessment color="primary" />
                    Choose Your Categories
                  </Typography>
                </StepLabel>
                <StepContent>
                  <Box sx={{ py: 3 }}>
                    <Typography variant="body1" sx={{ mb: 3 }}>
                      Select the categories you want to focus on. This helps us target the right opportunities.
                    </Typography>

                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                      {categories.map((category) => (
                        <Chip
                          key={category}
                          label={category}
                          clickable
                          color={onboardingData.preferredCategories.includes(category) ? 'primary' : 'default'}
                          onClick={() => {
                            setOnboardingData(prev => ({
                              ...prev,
                              preferredCategories: prev.preferredCategories.includes(category)
                                ? prev.preferredCategories.filter(c => c !== category)
                                : [...prev.preferredCategories, category]
                            }));
                          }}
                        />
                      ))}
                    </Box>

                    <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
                      <Button onClick={handleBack}>Back</Button>
                      <Button
                        variant="contained"
                        onClick={handleNext}
                        disabled={onboardingData.preferredCategories.length === 0}
                      >
                        Continue
                      </Button>
                    </Box>
                  </Box>
                </StepContent>
              </Step>

              {/* Step 3: Mix Review */}
              <Step>
                <StepLabel>
                  <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Psychology color="primary" />
                    Your Optimized Mix
                  </Typography>
                </StepLabel>
                <StepContent>
                  <Box sx={{ py: 3 }}>
                    <Typography variant="body1" sx={{ mb: 3 }}>
                      Based on your target and preferences, here's your personalized RFQ/Tender mix:
                    </Typography>

                    <Grid container spacing={3}>
                      {/* Mix Visualization */}
                      <Grid item xs={12} md={6}>
                        <Paper sx={{ p: 3, textAlign: 'center' }}>
                          <Typography variant="h6" gutterBottom>Your Optimal Mix</Typography>
                          <Box sx={{ height: 200, mb: 2 }}>
                            <ResponsiveContainer width="100%" height="100%">
                              <PieChart>
                                <Pie
                                  data={pieData}
                                  cx="50%"
                                  cy="50%"
                                  innerRadius={40}
                                  outerRadius={80}
                                  dataKey="value"
                                >
                                  {pieData.map((entry, index) => (
                                    <Cell key={`cell-${index}`} fill={entry.color} />
                                  ))}
                                </Pie>
                                <Tooltip />
                              </PieChart>
                            </ResponsiveContainer>
                          </Box>
                          <Typography variant="h4" color="primary" fontWeight="bold">
                            {onboardingData.recommendedMix.rfqPercentage}% RFQ
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Perfect for quick wins!
                          </Typography>
                        </Paper>
                      </Grid>

                      {/* Monthly Targets */}
                      <Grid item xs={12} md={6}>
                        <Paper sx={{ p: 3 }}>
                          <Typography variant="h6" gutterBottom>Monthly Targets</Typography>
                          <Stack spacing={2}>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                              <Typography>RFQs per month:</Typography>
                              <Typography fontWeight="bold" color="success.main">
                                {onboardingData.recommendedMix.monthlyRFQTarget}
                              </Typography>
                            </Box>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                              <Typography>Tenders per month:</Typography>
                              <Typography fontWeight="bold" color="primary.main">
                                {onboardingData.recommendedMix.monthlyTenderTarget}
                              </Typography>
                            </Box>
                            <Divider />
                            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                              <Typography>Projected Earnings:</Typography>
                              <Typography fontWeight="bold" color="success.main">
                                R{(onboardingData.recommendedMix.projectedEarnings / 1000000).toFixed(1)}M
                              </Typography>
                            </Box>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                              <Typography>Quick Win Potential:</Typography>
                              <Typography fontWeight="bold" color="warning.main">
                                R{(onboardingData.recommendedMix.quickWinPotential / 1000000).toFixed(1)}M
                              </Typography>
                            </Box>
                          </Stack>
                        </Paper>
                      </Grid>
                    </Grid>

                    <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
                      <Button onClick={handleBack}>Back</Button>
                      <Button variant="contained" onClick={handleNext}>
                        Looks Perfect!
                      </Button>
                    </Box>
                  </Box>
                </StepContent>
              </Step>

              {/* Step 4: Complete Setup */}
              <Step>
                <StepLabel>
                  <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <EmojiEvents color="primary" />
                    Ready to Dominate!
                  </Typography>
                </StepLabel>
                <StepContent>
                  <Box sx={{ py: 3 }}>
                    <Alert severity="success" sx={{ mb: 3 }}>
                      🎉 Your opportunity journey is configured! You're ready to start winning with your optimized mix.
                    </Alert>

                    <Typography variant="body1" sx={{ mb: 3 }}>
                      Your dashboard will now track your progress towards your R{(onboardingData.targetTurnover / 1000000).toFixed(1)}M target
                      with the perfect {onboardingData.recommendedMix.rfqPercentage}% RFQ / {onboardingData.recommendedMix.tenderPercentage}% Tender mix.
                    </Typography>

                    <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
                      <Button onClick={handleBack}>Back</Button>
                      <Button
                        variant="contained"
                        size="large"
                        onClick={handleComplete}
                        disabled={loading}
                        startIcon={<FlashOn />}
                        sx={{
                          background: 'linear-gradient(45deg, #4CAF50, #8BC34A)',
                          fontSize: '1.1rem',
                          fontWeight: 'bold'
                        }}
                      >
                        {loading ? 'Setting Up...' : '🚀 Start My Journey!'}
                      </Button>
                    </Box>
                  </Box>
                </StepContent>
              </Step>

            </Stepper>
          </CardContent>
        </Card>
      </Box>
    </Box>
  );
};

export default BidderOnboarding;
