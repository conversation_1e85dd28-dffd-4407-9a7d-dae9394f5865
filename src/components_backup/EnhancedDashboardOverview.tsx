import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Grid,
  LinearProgress,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>ack,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  TrendingUp,
  FlashOn,
  Psychology,
  EmojiEvents,
  Warning,
  VolumeUp,
  Refresh
} from '@mui/icons-material';
import { PieChart, Pie, Cell, ResponsiveContainer, LineChart, Line, XAxis, YAxis, Tooltip as RechartsTooltip } from 'recharts';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useNeuroMarketing } from '../../hooks/useNeuroMarketing';

interface DashboardMetrics {
  // Core Mix Tracking
  currentMix: {
    rfqPercentage: number;
    tenderPercentage: number;
    onTrack: boolean;
  };
  
  // Financial Psychology
  targetTurnover: number;
  currentEarnings: number;
  missedEarnings: number;
  projectedShortfall: number;
  
  // Achievement Tracking
  questProgress: {
    current: number;
    target: number;
    percentage: number;
  };
  
  // Momentum Psychology
  winStreak: {
    current: number;
    trend: 'up' | 'down' | 'flat';
    encouragement: string;
  };
  
  // Victory Reel
  victoryReel: {
    userWins: string;
    socialProof: string;
  };
}

const EnhancedDashboardOverview: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { psychologicalState, isStressed } = useNeuroMarketing();
  
  const [metrics, setMetrics] = useState<DashboardMetrics>({
    currentMix: {
      rfqPercentage: 60,
      tenderPercentage: 40,
      onTrack: true
    },
    targetTurnover: 5000000,
    currentEarnings: 1250000,
    missedEarnings: 350000,
    projectedShortfall: 500000,
    questProgress: {
      current: 173,
      target: 8650, // 173 is 2% of target
      percentage: 2
    },
    winStreak: {
      current: 2,
      trend: 'up',
      encouragement: 'Your 200% win streak suggests boldness!'
    },
    victoryReel: {
      userWins: '2 in a row!',
      socialProof: 'John from Gauteng won R500k!'
    }
  });

  const [winStreakData] = useState([
    { name: 'Week 1', value: 1 },
    { name: 'Week 2', value: 1.5 },
    { name: 'Week 3', value: 2.2 },
    { name: 'Week 4', value: 2.8 },
    { name: 'Week 5', value: 3.1 }
  ]);

  // Pie chart data for RFQ/Tender mix
  const pieData = [
    { name: 'RFQ', value: metrics.currentMix.rfqPercentage, color: '#4CAF50' },
    { name: 'Tender', value: metrics.currentMix.tenderPercentage, color: '#2196F3' }
  ];

  // Load user's actual metrics
  useEffect(() => {
    loadUserMetrics();
  }, [user]);

  const loadUserMetrics = async () => {
    try {
      // In real implementation, fetch from API
      // For now, using mock data that matches the image
      console.log('Loading user metrics...');
    } catch (error) {
      console.error('Failed to load metrics:', error);
    }
  };

  const handleClaimOpportunities = () => {
    // Navigate to unified opportunities with psychological urgency
    navigate('/opportunities', {
      state: {
        source: 'dashboard_claim',
        urgency: 'high',
        message: 'Fix your portfolio balance now!',
        filter: 'balance_optimization'
      }
    });
  };

  const handleCreateRFQ = () => {
    navigate('/rfq/create');
  };

  const handleRefreshMetrics = () => {
    loadUserMetrics();
  };

  return (
    <Grid container spacing={3}>
      {/* Main Dashboard Overview Card */}
      <Grid item xs={12} md={8}>
        <Card 
          sx={{ 
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            position: 'relative',
            overflow: 'visible'
          }}
        >
          <CardContent sx={{ p: 4 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 3 }}>
              <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                Dashboard Overview
              </Typography>
              <IconButton onClick={handleRefreshMetrics} sx={{ color: 'white' }}>
                <Refresh />
              </IconButton>
            </Box>

            <Grid container spacing={4}>
              {/* RFQ/Tender Mix Visualization */}
              <Grid item xs={12} md={6}>
                <Box sx={{ textAlign: 'center' }}>
                  <Box sx={{ height: 200, mb: 2 }}>
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={pieData}
                          cx="50%"
                          cy="50%"
                          innerRadius={50}
                          outerRadius={90}
                          dataKey="value"
                        >
                          {pieData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <RechartsTooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </Box>
                  
                  <Stack spacing={1} alignItems="center">
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box sx={{ width: 12, height: 12, backgroundColor: '#2196F3', borderRadius: '50%' }} />
                      <Typography variant="body2">RFQ/Tender Mix</Typography>
                    </Box>
                    <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#4CAF50' }}>
                      {metrics.currentMix.rfqPercentage}% RFQ
                    </Typography>
                  </Stack>
                </Box>
              </Grid>

              {/* Welcome Message & Financial Tracking */}
              <Grid item xs={12} md={6}>
                <Box>
                  <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 3 }}>
                    Welcome to Your Opportunity Journey!
                  </Typography>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Box>
                        <Typography variant="body2" sx={{ opacity: 0.9 }}>RFQ/Tender Mix</Typography>
                        <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                          {metrics.currentMix.rfqPercentage}% RFQ
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={6}>
                      <Box>
                        <Typography variant="body2" sx={{ opacity: 0.9 }}>Missed Earnings</Typography>
                        <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#f44336' }}>
                          R {(metrics.missedEarnings / 1000).toFixed(0)}k
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </Box>
              </Grid>
            </Grid>

            {/* Claim Opportunities Button */}
            <Button 
              variant="contained" 
              size="large" 
              fullWidth
              onClick={handleClaimOpportunities}
              startIcon={<FlashOn />}
              sx={{ 
                mt: 4, 
                py: 2,
                background: 'linear-gradient(45deg, #FF6B6B, #4ECDC4)',
                fontSize: '1.2rem',
                fontWeight: 'bold',
                '&:hover': {
                  background: 'linear-gradient(45deg, #FF5252, #26C6DA)',
                  transform: 'translateY(-2px)',
                  boxShadow: '0 8px 25px rgba(0,0,0,0.3)'
                },
                transition: 'all 0.3s ease'
              }}
            >
              🚀 Claim Your Opportunities
            </Button>
          </CardContent>
        </Card>
      </Grid>

      {/* Quest Progress & Win Streak */}
      <Grid item xs={12} md={4}>
        <Stack spacing={3}>
          {/* Quest Progress */}
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
                Quest Progress
              </Typography>
              
              <Box sx={{ mb: 2 }}>
                <LinearProgress 
                  variant="determinate" 
                  value={metrics.questProgress.percentage} 
                  sx={{ 
                    height: 12, 
                    borderRadius: 6,
                    backgroundColor: '#e0e0e0',
                    '& .MuiLinearProgress-bar': {
                      background: 'linear-gradient(90deg, #4CAF50, #8BC34A)'
                    }
                  }} 
                />
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                  <Typography variant="h4" fontWeight="bold">
                    {metrics.questProgress.current}
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    {metrics.questProgress.percentage}%
                  </Typography>
                </Box>
              </Box>
              
              {/* Win Streak Trend */}
              <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                Win Streak Trend
              </Typography>
              <Box sx={{ height: 100, mb: 2 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={winStreakData}>
                    <XAxis dataKey="name" hide />
                    <YAxis hide />
                    <Line 
                      type="monotone" 
                      dataKey="value" 
                      stroke="#2196F3" 
                      strokeWidth={3}
                      dot={{ fill: '#2196F3', strokeWidth: 2, r: 4 }}
                    />
                    <RechartsTooltip />
                  </LineChart>
                </ResponsiveContainer>
              </Box>
              
              {/* Victory Reel */}
              <Box sx={{ mt: 3 }}>
                <Typography variant="h6" gutterBottom>Victory Reel</Typography>
                <Typography variant="body1" fontWeight="bold" sx={{ mb: 1 }}>
                  Your Wins: {metrics.victoryReel.userWins} 🏆
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {metrics.victoryReel.socialProof}
                </Typography>
              </Box>
            </CardContent>
          </Card>

          {/* Confidence Coach */}
          <Card sx={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <Psychology sx={{ fontSize: 32 }} />
                <Typography variant="h6" fontWeight="bold">Confidence Coach</Typography>
                <VolumeUp />
              </Box>
              
              <Typography variant="body1" fontWeight="bold" sx={{ mb: 2 }}>
                {metrics.winStreak.encouragement}
              </Typography>
              
              <Typography variant="body2" sx={{ opacity: 0.9 }}>
                🎯 Your RFQ success rate is 89% - create 3 more this week to hit your target mix!
              </Typography>
            </CardContent>
          </Card>
        </Stack>
      </Grid>
    </Grid>
  );
};

export default EnhancedDashboardOverview;
