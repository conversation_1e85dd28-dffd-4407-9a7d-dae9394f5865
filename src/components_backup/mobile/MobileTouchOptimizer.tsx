/**
 * Mobile PWA with Touch Psychology
 * Optimizes mobile experience with touch-based behavioral triggers
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Fab,
  SpeedDial,
  SpeedDialAction,
  SpeedDialIcon,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  IconButton,
  Typography,
  Card,
  CardContent,
  Chip,
  Alert,
  Slide,
  Zoom,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Assignment as BidIcon,
  Security as ComplianceIcon,
  Psychology as PsychologyIcon,
  Notifications as NotificationIcon,
  Menu as MenuIcon,
  Close as CloseIcon,
  SwipeRight as SwipeIcon,
  TouchApp as TouchIcon,
  Vibration as HapticIcon,
  Speed as QuickIcon,
  LocalShipping as CourierIcon
} from '@mui/icons-material';
import { useSwipeable } from 'react-swipeable';
import { useNeuroMarketing } from '../../hooks/useNeuroMarketing';

interface TouchOptimizationSettings {
  hapticFeedback: boolean;
  gestureNavigation: boolean;
  quickActions: boolean;
  adaptiveButtonSize: boolean;
  touchTargetOptimization: boolean;
  swipeGestures: boolean;
}

interface MobileTouchOptimizerProps {
  children: React.ReactNode;
}

const MobileTouchOptimizer: React.FC<MobileTouchOptimizerProps> = ({ children }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  // Behavioral tracking
  const {
    psychologicalState,
    isStressed,
    isHighCognitiveLoad,
    needsSimplification
  } = useNeuroMarketing();

  // State management
  const [touchSettings, setTouchSettings] = useState<TouchOptimizationSettings>({
    hapticFeedback: true,
    gestureNavigation: true,
    quickActions: true,
    adaptiveButtonSize: true,
    touchTargetOptimization: true,
    swipeGestures: true
  });
  
  const [speedDialOpen, setSpeedDialOpen] = useState(false);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [showTouchTips, setShowTouchTips] = useState(false);
  const [adaptiveButtonSize, setAdaptiveButtonSize] = useState(48);
  const [quickActionVisible, setQuickActionVisible] = useState(false);
  
  const containerRef = useRef<HTMLDivElement>(null);

  // Adapt to psychological state
  useEffect(() => {
    if (psychologicalState && isMobile) {
      // Increase button size when stressed or high cognitive load
      if (isStressed || isHighCognitiveLoad) {
        setAdaptiveButtonSize(56); // Larger touch targets
        setShowTouchTips(true);
      } else {
        setAdaptiveButtonSize(48); // Normal size
      }

      // Show quick actions when engagement is low
      if (psychologicalState.engagementLevel < 0.5) {
        setQuickActionVisible(true);
      }
    }
  }, [psychologicalState, isMobile, isStressed, isHighCognitiveLoad]);

  // Haptic feedback function
  const triggerHaptic = (type: 'light' | 'medium' | 'heavy' = 'light') => {
    if (touchSettings.hapticFeedback && 'vibrate' in navigator) {
      const patterns = {
        light: [10],
        medium: [20],
        heavy: [30]
      };
      navigator.vibrate(patterns[type]);
    }
  };

  // Swipe gesture handlers
  const swipeHandlers = useSwipeable({
    onSwipedLeft: () => {
      if (touchSettings.swipeGestures) {
        triggerHaptic('light');
        console.log('Mobile swipe left:', { psychologicalState });
        // Navigate to next page or action
      }
    },
    onSwipedRight: () => {
      if (touchSettings.swipeGestures) {
        triggerHaptic('light');
        console.log('Mobile swipe right:', { psychologicalState });
        // Navigate to previous page or open drawer
        setDrawerOpen(true);
      }
    },
    onSwipedUp: () => {
      if (touchSettings.swipeGestures) {
        triggerHaptic('medium');
        console.log('Mobile swipe up:', { psychologicalState });
        // Show quick actions
        setQuickActionVisible(true);
      }
    },
    onSwipedDown: () => {
      if (touchSettings.swipeGestures) {
        triggerHaptic('light');
        console.log('Mobile swipe down:', { psychologicalState });
        // Hide quick actions or refresh
        setQuickActionVisible(false);
      }
    },
    trackMouse: false
  });

  // Quick action buttons
  const quickActions = [
    {
      icon: <SearchIcon />,
      name: 'Find Tenders',
      action: () => {
        triggerHaptic('medium');
        console.log('Mobile quick search');
        window.location.href = '/tenders/swipe';
      }
    },
    {
      icon: <BidIcon />,
      name: 'Create Bid',
      action: () => {
        triggerHaptic('medium');
        console.log('Mobile quick bid');
        window.location.href = '/bids/create';
      }
    },
    {
      icon: <ComplianceIcon />,
      name: 'Compliance',
      action: () => {
        triggerHaptic('medium');
        console.log('Mobile quick compliance');
        window.location.href = '/compliance';
      }
    },
    {
      icon: <PsychologyIcon />,
      name: 'Analytics',
      action: () => {
        triggerHaptic('medium');
        console.log('Mobile quick analytics');
        window.location.href = '/analytics/behavioral';
      }
    }
  ];

  // Navigation drawer items
  const drawerItems = [
    { text: 'Dashboard', icon: <MenuIcon />, path: '/dashboard' },
    { text: 'Discover Tenders', icon: <SwipeIcon />, path: '/tenders/swipe' },
    { text: 'My Bids', icon: <BidIcon />, path: '/bids' },
    { text: 'Compliance', icon: <ComplianceIcon />, path: '/compliance' },
    { text: 'Courier Dispatch', icon: <CourierIcon />, path: '/courier' },
    { text: 'Documents', icon: <TouchIcon />, path: '/documents' },
    { text: 'Analytics', icon: <PsychologyIcon />, path: '/analytics' }
  ];

  // Touch target optimization
  const getOptimizedTouchTarget = (baseSize: number) => {
    if (!touchSettings.touchTargetOptimization) return baseSize;
    
    // Minimum 44px for accessibility, larger when stressed
    const minSize = 44;
    const stressMultiplier = isStressed ? 1.3 : 1;
    const cognitiveMultiplier = isHighCognitiveLoad ? 1.2 : 1;
    
    return Math.max(minSize, baseSize * stressMultiplier * cognitiveMultiplier);
  };

  if (!isMobile) {
    return <>{children}</>;
  }

  return (
    <Box 
      {...swipeHandlers}
      sx={{ 
        minHeight: '100vh',
        position: 'relative',
        touchAction: 'pan-y' // Allow vertical scrolling but enable horizontal swipes
      }}
    >
      {/* Touch Tips Alert */}
      <Slide direction="down" in={showTouchTips}>
        <Alert 
          severity="info" 
          sx={{ 
            position: 'fixed', 
            top: 0, 
            left: 0, 
            right: 0, 
            zIndex: 1300,
            borderRadius: 0
          }}
          action={
            <IconButton size="small" onClick={() => setShowTouchTips(false)}>
              <CloseIcon />
            </IconButton>
          }
        >
          <Typography variant="body2">
            📱 <strong>Touch Optimized:</strong> Larger buttons and gestures enabled for easier interaction
          </Typography>
        </Alert>
      </Slide>

      {/* Main Content */}
      <Box sx={{ pb: 10 }}> {/* Bottom padding for FAB */}
        {children}
      </Box>

      {/* Quick Action Speed Dial */}
      {touchSettings.quickActions && (
        <SpeedDial
          ariaLabel="Quick Actions"
          sx={{ 
            position: 'fixed', 
            bottom: 16, 
            right: 16,
            '& .MuiFab-root': {
              width: getOptimizedTouchTarget(adaptiveButtonSize),
              height: getOptimizedTouchTarget(adaptiveButtonSize)
            }
          }}
          icon={<SpeedDialIcon />}
          onClose={() => setSpeedDialOpen(false)}
          onOpen={() => {
            setSpeedDialOpen(true);
            triggerHaptic('medium');
            console.log('Mobile speed dial open');
          }}
          open={speedDialOpen}
        >
          {quickActions.map((action) => (
            <SpeedDialAction
              key={action.name}
              icon={action.icon}
              tooltipTitle={action.name}
              onClick={() => {
                action.action();
                setSpeedDialOpen(false);
              }}
              sx={{
                '& .MuiFab-root': {
                  width: getOptimizedTouchTarget(40),
                  height: getOptimizedTouchTarget(40)
                }
              }}
            />
          ))}
        </SpeedDial>
      )}

      {/* Quick Action Bar (when visible) */}
      <Zoom in={quickActionVisible}>
        <Card
          sx={{
            position: 'fixed',
            bottom: 80,
            left: 16,
            right: 16,
            zIndex: 1200
          }}
        >
          <CardContent sx={{ py: 1 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-around', alignItems: 'center' }}>
              {quickActions.slice(0, 3).map((action, index) => (
                <IconButton
                  key={index}
                  onClick={action.action}
                  sx={{
                    width: getOptimizedTouchTarget(48),
                    height: getOptimizedTouchTarget(48),
                    flexDirection: 'column',
                    gap: 0.5
                  }}
                >
                  {action.icon}
                  <Typography variant="caption" sx={{ fontSize: '0.7rem' }}>
                    {action.name.split(' ')[0]}
                  </Typography>
                </IconButton>
              ))}
            </Box>
          </CardContent>
        </Card>
      </Zoom>

      {/* Navigation Drawer */}
      <Drawer
        anchor="left"
        open={drawerOpen}
        onClose={() => setDrawerOpen(false)}
        sx={{
          '& .MuiDrawer-paper': {
            width: 280,
            background: needsSimplification ? 
              'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)' : 
              'default'
          }
        }}
      >
        <Box sx={{ p: 2 }}>
          <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
            <TouchIcon sx={{ mr: 1 }} />
            BidBeez Mobile
          </Typography>
          
          {/* Psychological State Indicator */}
          {psychologicalState && (
            <Card sx={{ mb: 2, bgcolor: 'background.paper' }}>
              <CardContent sx={{ py: 1 }}>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  Current State:
                </Typography>
                <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                  <Chip 
                    label={`Stress: ${Math.round(psychologicalState.stressLevel * 100)}%`}
                    size="small"
                    color={psychologicalState.stressLevel > 0.6 ? 'error' : 'success'}
                  />
                  <Chip 
                    label={`Focus: ${Math.round(psychologicalState.engagementLevel * 100)}%`}
                    size="small"
                    color="info"
                  />
                </Box>
              </CardContent>
            </Card>
          )}
        </Box>

        <List>
          {drawerItems.map((item) => (
            <ListItem 
              key={item.text}
              onClick={() => {
                triggerHaptic('light');
                console.log('Mobile nav item:', item.text);
                window.location.href = item.path;
                setDrawerOpen(false);
              }}
              sx={{
                minHeight: getOptimizedTouchTarget(48),
                cursor: 'pointer',
                '&:hover': {
                  backgroundColor: 'action.hover'
                }
              }}
            >
              <ListItemIcon sx={{ minWidth: 40 }}>
                {item.icon}
              </ListItemIcon>
              <ListItemText 
                primary={item.text}
                primaryTypographyProps={{
                  fontSize: needsSimplification ? '1.1rem' : '1rem'
                }}
              />
            </ListItem>
          ))}
        </List>

        {/* Touch Settings */}
        <Box sx={{ p: 2, mt: 'auto' }}>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            Touch Features:
          </Typography>
          <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
            {touchSettings.hapticFeedback && (
              <Chip label="Haptic" size="small" icon={<HapticIcon />} />
            )}
            {touchSettings.swipeGestures && (
              <Chip label="Gestures" size="small" icon={<SwipeIcon />} />
            )}
            {touchSettings.quickActions && (
              <Chip label="Quick Actions" size="small" icon={<QuickIcon />} />
            )}
          </Box>
        </Box>
      </Drawer>

      {/* Floating Menu Button (alternative to swipe) */}
      {!speedDialOpen && (
        <Fab
          color="primary"
          sx={{
            position: 'fixed',
            top: 16,
            left: 16,
            width: getOptimizedTouchTarget(48),
            height: getOptimizedTouchTarget(48),
            zIndex: 1200
          }}
          onClick={() => {
            setDrawerOpen(true);
            triggerHaptic('medium');
            console.log('Mobile menu open');
          }}
        >
          <MenuIcon />
        </Fab>
      )}

      {/* Gesture Hints (for new users) */}
      {touchSettings.swipeGestures && showTouchTips && (
        <Box
          sx={{
            position: 'fixed',
            bottom: 140,
            left: '50%',
            transform: 'translateX(-50%)',
            zIndex: 1100,
            pointerEvents: 'none'
          }}
        >
          <Card sx={{ px: 2, py: 1, bgcolor: 'rgba(0,0,0,0.8)', color: 'white' }}>
            <Typography variant="caption">
              👆 Swipe up for quick actions • Swipe right for menu
            </Typography>
          </Card>
        </Box>
      )}
    </Box>
  );
};

export default MobileTouchOptimizer;
