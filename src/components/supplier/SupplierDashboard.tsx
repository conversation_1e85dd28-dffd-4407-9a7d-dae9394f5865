import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardContent,
  Typo<PERSON>,
  <PERSON>,
  Button,
  Chip,
  Alert,
  Stack,
  IconButton,
  Tooltip,
  LinearProgress,
  Grid,
  Avatar,
  Divider
} from '@mui/material';
import {
  Business,
  TrendingUp,
  MonetizationOn,
  Assignment,
  Star,
  Refresh,
  Dashboard,
  FormatQuote,
  Leaderboard,
  EmojiEvents
} from '@mui/icons-material';

interface SupplierDashboardProps {
  userId: string;
  compact?: boolean;
  onViewFullDashboard?: () => void;
}

interface SupplierMetrics {
  // Revenue Metrics
  totalRevenue: number;
  monthlyRevenue: number;
  revenueGrowth: number;
  
  // Quote Metrics
  activeQuotes: number;
  quotesWon: number;
  quoteSuccessRate: number;
  averageQuoteValue: number;
  
  // Performance Metrics
  leaderboardPosition: number;
  totalSuppliers: number;
  complianceScore: number;
  responseTime: number;
  
  // Recent Activity
  recentQuotes: QuoteActivity[];
  
  // Gamification
  badges: Badge[];
  level: number;
  experiencePoints: number;
  nextLevelPoints: number;
}

interface QuoteActivity {
  id: string;
  projectName: string;
  value: number;
  status: 'pending' | 'won' | 'lost' | 'submitted';
  submittedAt: string;
  client: string;
}

interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  earnedAt: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

const SupplierDashboard: React.FC<SupplierDashboardProps> = ({
  userId,
  compact = false,
  onViewFullDashboard
}) => {
  const [metrics, setMetrics] = useState<SupplierMetrics | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSupplierMetrics();
  }, [userId]);

  const loadSupplierMetrics = async () => {
    setLoading(true);
    try {
      // Simulate API call - replace with actual API
      const mockMetrics: SupplierMetrics = {
        totalRevenue: 2450000,
        monthlyRevenue: 385000,
        revenueGrowth: 23.5,
        activeQuotes: 8,
        quotesWon: 34,
        quoteSuccessRate: 68.0,
        averageQuoteValue: 72000,
        leaderboardPosition: 12,
        totalSuppliers: 1247,
        complianceScore: 94.5,
        responseTime: 2.3,
        recentQuotes: [
          {
            id: 'Q001',
            projectName: 'Office Furniture Supply',
            value: 125000,
            status: 'won',
            submittedAt: '2024-12-01',
            client: 'City of Cape Town'
          },
          {
            id: 'Q002',
            projectName: 'IT Equipment Procurement',
            value: 89000,
            status: 'pending',
            submittedAt: '2024-12-02',
            client: 'Department of Health'
          },
          {
            id: 'Q003',
            projectName: 'Construction Materials',
            value: 156000,
            status: 'submitted',
            submittedAt: '2024-12-03',
            client: 'Gauteng Province'
          }
        ],
        badges: [
          {
            id: 'fast_responder',
            name: 'Fast Responder',
            description: 'Responds to quotes within 2 hours',
            icon: '⚡',
            earnedAt: '2024-11-15',
            rarity: 'rare'
          },
          {
            id: 'compliance_master',
            name: 'Compliance Master',
            description: '95%+ compliance score for 3 months',
            icon: '🏆',
            earnedAt: '2024-11-28',
            rarity: 'epic'
          }
        ],
        level: 8,
        experiencePoints: 2340,
        nextLevelPoints: 2500
      };
      
      setMetrics(mockMetrics);
    } catch (error) {
      console.error('Failed to load supplier metrics:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'won': return 'success';
      case 'pending': return 'warning';
      case 'submitted': return 'info';
      case 'lost': return 'error';
      default: return 'default';
    }
  };

  const getBadgeColor = (rarity: string) => {
    switch (rarity) {
      case 'legendary': return '#FFD700';
      case 'epic': return '#9C27B0';
      case 'rare': return '#2196F3';
      default: return '#4CAF50';
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent>
          <LinearProgress />
          <Typography variant="body2" sx={{ mt: 1, textAlign: 'center' }}>
            Loading supplier metrics...
          </Typography>
        </CardContent>
      </Card>
    );
  }

  if (!metrics) {
    return (
      <Alert severity="error">
        Failed to load supplier metrics. Please try again.
      </Alert>
    );
  }

  return (
    <Card>
      <CardContent>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Business color="primary" />
            <Typography variant="h6" fontWeight="bold">
              Supplier Performance
            </Typography>
          </Box>
          <Stack direction="row" spacing={1}>
            <IconButton size="small" onClick={loadSupplierMetrics}>
              <Refresh />
            </IconButton>
            {onViewFullDashboard && (
              <IconButton size="small" onClick={onViewFullDashboard}>
                <Dashboard />
              </IconButton>
            )}
          </Stack>
        </Box>

        {/* Key Metrics */}
        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={6}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" fontWeight="bold" color="success.main">
                R{(metrics.monthlyRevenue / 1000).toFixed(0)}k
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Monthly Revenue
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 0.5 }}>
                <TrendingUp fontSize="small" color="success" />
                <Typography variant="caption" color="success.main">
                  +{metrics.revenueGrowth}%
                </Typography>
              </Box>
            </Box>
          </Grid>
          
          <Grid item xs={6}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" fontWeight="bold" color="primary">
                {metrics.quoteSuccessRate.toFixed(1)}%
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Success Rate
              </Typography>
              <Typography variant="caption" display="block" color="text.secondary">
                {metrics.quotesWon} wins
              </Typography>
            </Box>
          </Grid>
        </Grid>

        {/* Leaderboard Position */}
        <Box sx={{ mb: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Typography variant="body2" fontWeight="bold">
              Leaderboard Position
            </Typography>
            <Chip 
              label={`#${metrics.leaderboardPosition}`}
              color="primary"
              size="small"
              icon={<Leaderboard />}
            />
          </Box>
          <LinearProgress 
            variant="determinate" 
            value={((metrics.totalSuppliers - metrics.leaderboardPosition) / metrics.totalSuppliers) * 100}
            sx={{ height: 8, borderRadius: 4 }}
          />
          <Typography variant="caption" color="text.secondary">
            Top {Math.round(((metrics.totalSuppliers - metrics.leaderboardPosition) / metrics.totalSuppliers) * 100)}% of {metrics.totalSuppliers} suppliers
          </Typography>
        </Box>

        {/* Level Progress */}
        <Box sx={{ mb: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Typography variant="body2" fontWeight="bold">
              Level {metrics.level}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {metrics.experiencePoints}/{metrics.nextLevelPoints} XP
            </Typography>
          </Box>
          <LinearProgress 
            variant="determinate" 
            value={(metrics.experiencePoints / metrics.nextLevelPoints) * 100}
            sx={{ height: 6, borderRadius: 3 }}
          />
        </Box>

        {/* Recent Badges */}
        {metrics.badges.length > 0 && (
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" fontWeight="bold" sx={{ mb: 1 }}>
              Recent Achievements
            </Typography>
            <Stack direction="row" spacing={1}>
              {metrics.badges.slice(0, compact ? 2 : 3).map((badge) => (
                <Tooltip key={badge.id} title={badge.description}>
                  <Chip
                    avatar={<Avatar sx={{ bgcolor: getBadgeColor(badge.rarity) }}>{badge.icon}</Avatar>}
                    label={badge.name}
                    size="small"
                    variant="outlined"
                  />
                </Tooltip>
              ))}
            </Stack>
          </Box>
        )}

        {/* Recent Activity */}
        {!compact && (
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" fontWeight="bold" sx={{ mb: 1 }}>
              Recent Quotes
            </Typography>
            <Stack spacing={1}>
              {metrics.recentQuotes.slice(0, 3).map((quote) => (
                <Box key={quote.id} sx={{ p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="body2" fontWeight="bold">
                      {quote.projectName}
                    </Typography>
                    <Chip 
                      label={quote.status}
                      size="small"
                      color={getStatusColor(quote.status) as any}
                    />
                  </Box>
                  <Typography variant="caption" color="text.secondary">
                    R{quote.value.toLocaleString()} • {quote.client}
                  </Typography>
                </Box>
              ))}
            </Stack>
          </Box>
        )}

        {/* Quick Stats */}
        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={6}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h6" fontWeight="bold" color="info.main">
                {metrics.activeQuotes}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Active Quotes
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h6" fontWeight="bold" color="warning.main">
                {metrics.responseTime}h
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Avg Response
              </Typography>
            </Box>
          </Grid>
        </Grid>

        {/* Action Buttons */}
        <Stack direction="row" spacing={2}>
          <Button
            variant="outlined"
            startIcon={<Assignment />}
            size="small"
            sx={{ flex: 1 }}
          >
            New Quote
          </Button>
          {onViewFullDashboard && (
            <Button
              variant="contained"
              startIcon={<Dashboard />}
              onClick={onViewFullDashboard}
              size="small"
              sx={{ flex: 1 }}
            >
              Full Dashboard
            </Button>
          )}
        </Stack>
      </CardContent>
    </Card>
  );
};

export default SupplierDashboard;
