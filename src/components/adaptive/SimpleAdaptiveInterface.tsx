'use client';

import React, { ReactNode } from 'react';

interface SimpleAdaptiveInterfaceProps {
  children: ReactNode;
  enablePsychologicalOptimization?: boolean;
  enableAdaptiveAnimations?: boolean;
  enableStressReduction?: boolean;
  enableCognitiveLoadManagement?: boolean;
}

const SimpleAdaptiveInterface: React.FC<SimpleAdaptiveInterfaceProps> = ({ 
  children,
  enablePsychologicalOptimization = false,
  enableAdaptiveAnimations = false,
  enableStressReduction = false,
  enableCognitiveLoadManagement = false
}) => {
  // Simply pass through children for now - no complex processing
  return <>{children}</>;
};

export default SimpleAdaptiveInterface;