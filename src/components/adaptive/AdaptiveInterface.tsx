/**
 * Adaptive Interface Framework
 * Dynamically adjusts UI based on user's psychological state
 */

import React, { ReactNode, useEffect, useState } from 'react';
import { ThemeProvider, createTheme, Theme } from '@mui/material/styles';
import { CssBaseline, Box, Fade, Grow, Slide } from '@mui/material';
import { useNeuroMarketing } from '../../hooks/useNeuroMarketing';
import {
  UIComplexity,
  ColorScheme,
  AnimationLevel,
  InformationDensity,
  GamificationLevel
} from '../../services/NeuroMarketingEngine';

interface AdaptiveInterfaceProps {
  children: ReactNode;
  enablePsychologicalOptimization?: boolean;
  enableAdaptiveAnimations?: boolean;
  enableStressReduction?: boolean;
  enableCognitiveLoadManagement?: boolean;
}

interface AdaptiveThemeConfig {
  complexity: UIComplexity;
  colorScheme: ColorScheme;
  animationLevel: AnimationLevel;
  informationDensity: InformationDensity;
  gamificationLevel: GamificationLevel;
}

const AdaptiveInterface: React.FC<AdaptiveInterfaceProps> = ({
  children,
  enablePsychologicalOptimization = true,
  enableAdaptiveAnimations = true,
  enableStressReduction = true,
  enableCognitiveLoadManagement = true
}) => {
  const {
    psychologicalState,
    adaptiveSettings,
    isHighCognitiveLoad,
    isStressed,
    needsSimplification,
    shouldUseCalming,
    startTracking
  } = useNeuroMarketing();

  const [adaptiveTheme, setAdaptiveTheme] = useState<Theme>(createDefaultTheme());
  const [animationConfig, setAnimationConfig] = useState(getDefaultAnimationConfig());

  // Start tracking when component mounts
  useEffect(() => {
    if (enablePsychologicalOptimization) {
      startTracking();
    }
  }, [enablePsychologicalOptimization, startTracking]);

  // Update theme based on psychological state
  useEffect(() => {
    if (!enablePsychologicalOptimization) return;

    const themeConfig: AdaptiveThemeConfig = {
      complexity: adaptiveSettings.uiComplexity,
      colorScheme: adaptiveSettings.colorScheme,
      animationLevel: adaptiveSettings.animationLevel,
      informationDensity: adaptiveSettings.informationDensity,
      gamificationLevel: adaptiveSettings.gamificationLevel
    };

    const newTheme = createAdaptiveTheme(themeConfig, psychologicalState);
    setAdaptiveTheme(newTheme);

    if (enableAdaptiveAnimations) {
      const newAnimationConfig = createAnimationConfig(adaptiveSettings.animationLevel);
      setAnimationConfig(newAnimationConfig);
    }
  }, [
    adaptiveSettings,
    psychologicalState,
    enablePsychologicalOptimization,
    enableAdaptiveAnimations
  ]);

  return (
    <ThemeProvider theme={adaptiveTheme}>
      <CssBaseline />
      <AdaptiveContainer
        animationConfig={animationConfig}
        enableStressReduction={enableStressReduction}
        enableCognitiveLoadManagement={enableCognitiveLoadManagement}
        isStressed={isStressed}
        isHighCognitiveLoad={isHighCognitiveLoad}
        needsSimplification={needsSimplification}
        shouldUseCalming={shouldUseCalming}
      >
        {children}
      </AdaptiveContainer>
    </ThemeProvider>
  );
};

interface AdaptiveContainerProps {
  children: ReactNode;
  animationConfig: AnimationConfig;
  enableStressReduction: boolean;
  enableCognitiveLoadManagement: boolean;
  isStressed: boolean;
  isHighCognitiveLoad: boolean;
  needsSimplification: boolean;
  shouldUseCalming: boolean;
}

interface AnimationConfig {
  transitionDuration: number;
  easing: string;
  enableHover: boolean;
  enableFocus: boolean;
  enablePageTransitions: boolean;
}

const AdaptiveContainer: React.FC<AdaptiveContainerProps> = ({
  children,
  animationConfig,
  enableStressReduction,
  enableCognitiveLoadManagement,
  isStressed,
  isHighCognitiveLoad,
  needsSimplification,
  shouldUseCalming
}) => {
  const containerStyles = {
    minHeight: '100vh',
    transition: `all ${animationConfig.transitionDuration}ms ${animationConfig.easing}`,
    
    // Stress reduction styles
    ...(enableStressReduction && shouldUseCalming && {
      filter: 'brightness(0.95) contrast(0.9)',
      backgroundColor: 'rgba(240, 248, 255, 0.1)' // Light blue overlay
    }),
    
    // Cognitive load management
    ...(enableCognitiveLoadManagement && isHighCognitiveLoad && {
      '& .MuiButton-root': {
        fontSize: '1.1rem',
        padding: '12px 24px'
      },
      '& .MuiTypography-body1': {
        lineHeight: 1.8
      }
    }),
    
    // Simplification mode
    ...(needsSimplification && {
      '& .adaptive-hide-complex': {
        display: 'none'
      },
      '& .adaptive-simplify': {
        opacity: 0.7
      }
    })
  };

  return (
    <Box sx={containerStyles}>
      {animationConfig.enablePageTransitions ? (
        <Fade in timeout={animationConfig.transitionDuration}>
          <Box>{children}</Box>
        </Fade>
      ) : (
        children
      )}
    </Box>
  );
};

// Theme creation functions
function createDefaultTheme(): Theme {
  return createTheme({
    palette: {
      mode: 'light',
      primary: {
        main: '#1976d2'
      },
      secondary: {
        main: '#dc004e'
      }
    }
  });
}

function createAdaptiveTheme(config: AdaptiveThemeConfig, psychState: any): Theme {
  const baseTheme = createDefaultTheme();
  
  // Color scheme adaptation
  const paletteConfig = createAdaptivePalette(config.colorScheme, psychState);
  
  // Typography adaptation
  const typographyConfig = createAdaptiveTypography(config.complexity, config.informationDensity);
  
  // Component adaptations
  const componentsConfig = createAdaptiveComponents(config, psychState);
  
  return createTheme({
    ...baseTheme,
    palette: {
      ...baseTheme.palette,
      ...paletteConfig
    },
    typography: {
      ...baseTheme.typography,
      ...typographyConfig
    },
    components: {
      ...baseTheme.components,
      ...componentsConfig
    }
  });
}

function createAdaptivePalette(colorScheme: ColorScheme, psychState: any) {
  switch (colorScheme) {
    case ColorScheme.CALMING:
      return {
        primary: {
          main: '#4fc3f7', // Light blue
          light: '#8bf6ff',
          dark: '#0093c4'
        },
        secondary: {
          main: '#81c784', // Light green
          light: '#b2fab4',
          dark: '#519657'
        },
        background: {
          default: '#f8fffe',
          paper: '#ffffff'
        }
      };
      
    case ColorScheme.ENERGIZING:
      return {
        primary: {
          main: '#ff7043', // Orange
          light: '#ffb74d',
          dark: '#e64a19'
        },
        secondary: {
          main: '#ab47bc', // Purple
          light: '#ce93d8',
          dark: '#7b1fa2'
        },
        background: {
          default: '#fff8f5',
          paper: '#ffffff'
        }
      };
      
    case ColorScheme.HIGH_CONTRAST:
      return {
        primary: {
          main: '#000000',
          light: '#424242',
          dark: '#000000'
        },
        secondary: {
          main: '#ffffff',
          light: '#ffffff',
          dark: '#f5f5f5'
        },
        background: {
          default: '#ffffff',
          paper: '#f5f5f5'
        }
      };
      
    default: // NEUTRAL
      return {
        primary: {
          main: '#1976d2',
          light: '#42a5f5',
          dark: '#1565c0'
        },
        secondary: {
          main: '#dc004e',
          light: '#ff5983',
          dark: '#9a0036'
        }
      };
  }
}

function createAdaptiveTypography(complexity: UIComplexity, density: InformationDensity) {
  const baseSize = density === InformationDensity.SPARSE ? 1.2 : 
                   density === InformationDensity.DENSE ? 0.9 : 1.0;
  
  const lineHeight = complexity === UIComplexity.MINIMAL ? 1.8 :
                     complexity === UIComplexity.COMPREHENSIVE ? 1.4 : 1.6;
  
  return {
    fontSize: 14 * baseSize,
    h1: {
      fontSize: `${2.5 * baseSize}rem`,
      lineHeight: lineHeight,
      fontWeight: complexity === UIComplexity.MINIMAL ? 400 : 500
    },
    h2: {
      fontSize: `${2 * baseSize}rem`,
      lineHeight: lineHeight,
      fontWeight: complexity === UIComplexity.MINIMAL ? 400 : 500
    },
    h3: {
      fontSize: `${1.75 * baseSize}rem`,
      lineHeight: lineHeight
    },
    body1: {
      fontSize: `${1 * baseSize}rem`,
      lineHeight: lineHeight
    },
    body2: {
      fontSize: `${0.875 * baseSize}rem`,
      lineHeight: lineHeight
    },
    button: {
      fontSize: `${0.875 * baseSize}rem`,
      fontWeight: complexity === UIComplexity.MINIMAL ? 400 : 500,
      textTransform: (complexity === UIComplexity.MINIMAL ? 'none' : 'uppercase') as 'none' | 'uppercase'
    }
  };
}

function createAdaptiveComponents(config: AdaptiveThemeConfig, psychState: any) {
  const isStressed = psychState.stressLevel > 0.6;
  const isHighCognitiveLoad = psychState.cognitiveLoad > 0.7;
  
  return {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: config.complexity === UIComplexity.MINIMAL ? 8 : 4,
          padding: isHighCognitiveLoad ? '12px 24px' : '8px 16px',
          fontSize: isHighCognitiveLoad ? '1.1rem' : '0.875rem',
          transition: config.animationLevel === AnimationLevel.NONE ? 'none' : 
                     config.animationLevel === AnimationLevel.SUBTLE ? 'all 0.2s ease' :
                     'all 0.3s ease-in-out',
          '&:hover': {
            transform: config.animationLevel === AnimationLevel.DYNAMIC && !isStressed ? 
                      'translateY(-2px)' : 'none',
            boxShadow: config.animationLevel === AnimationLevel.DYNAMIC && !isStressed ?
                      '0 4px 8px rgba(0,0,0,0.2)' : 'none'
          }
        }
      }
    },
    
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: config.complexity === UIComplexity.MINIMAL ? 12 : 8,
          boxShadow: isStressed ? '0 2px 4px rgba(0,0,0,0.1)' : '0 2px 8px rgba(0,0,0,0.15)',
          transition: config.animationLevel === AnimationLevel.NONE ? 'none' : 'all 0.3s ease',
          '&:hover': {
            boxShadow: config.animationLevel === AnimationLevel.DYNAMIC && !isStressed ?
                      '0 8px 16px rgba(0,0,0,0.2)' : undefined
          }
        }
      }
    },
    
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: config.complexity === UIComplexity.MINIMAL ? 8 : 4,
            fontSize: isHighCognitiveLoad ? '1.1rem' : '1rem',
            '& fieldset': {
              borderWidth: isStressed ? '1px' : '2px'
            }
          }
        }
      }
    },
    
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: config.complexity === UIComplexity.MINIMAL ? 16 : 8,
          fontSize: config.informationDensity === InformationDensity.SPARSE ? '0.9rem' : '0.8rem'
        }
      }
    },
    
    MuiAlert: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          fontSize: isHighCognitiveLoad ? '1rem' : '0.875rem',
          '& .MuiAlert-icon': {
            fontSize: isHighCognitiveLoad ? '1.5rem' : '1.25rem'
          }
        }
      }
    }
  };
}

function getDefaultAnimationConfig(): AnimationConfig {
  return {
    transitionDuration: 300,
    easing: 'ease-in-out',
    enableHover: true,
    enableFocus: true,
    enablePageTransitions: true
  };
}

function createAnimationConfig(animationLevel: AnimationLevel): AnimationConfig {
  switch (animationLevel) {
    case AnimationLevel.NONE:
      return {
        transitionDuration: 0,
        easing: 'linear',
        enableHover: false,
        enableFocus: false,
        enablePageTransitions: false
      };
      
    case AnimationLevel.SUBTLE:
      return {
        transitionDuration: 200,
        easing: 'ease',
        enableHover: true,
        enableFocus: true,
        enablePageTransitions: false
      };
      
    case AnimationLevel.DYNAMIC:
      return {
        transitionDuration: 400,
        easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
        enableHover: true,
        enableFocus: true,
        enablePageTransitions: true
      };
      
    default: // MODERATE
      return getDefaultAnimationConfig();
  }
}

export default AdaptiveInterface;
