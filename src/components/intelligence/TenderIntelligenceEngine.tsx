import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Ty<PERSON><PERSON>,
  Grid,
  Chip,
  Stack,
  LinearProgress,
  Alert,
  Button,
  IconButton,
  Tooltip,
  Avatar,
  Divider
} from '@mui/material';
import {
  Description,
  Psychology,
  Build,
  School,
  People,
  LocationOn,
  TrendingUp,
  Warning,
  CheckCircle,
  FlashOn,
  Refresh
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useNeuroMarketing } from '../../hooks/useNeuroMarketing';

interface TenderDocument {
  id: string;
  title: string;
  description: string;
  location: {
    province: string;
    city: string;
    coordinates: { lat: number; lng: number };
    radius: number;
  };
  requirements: {
    skills: TenderSkillRequirement[];
    tools: TenderToolRequirement[];
    certifications: string[];
    experience: string[];
    bbbeeLevel: number;
    turnoverRequirement: number;
  };
  value: number;
  deadline: string;
  category: string;
  complexity: 'low' | 'medium' | 'high' | 'expert';
}

interface TenderSkillRequirement {
  skillName: string;
  level: 'basic' | 'intermediate' | 'advanced' | 'expert';
  mandatory: boolean;
  weight: number;
  alternatives: string[];
}

interface TenderToolRequirement {
  toolName: string;
  version?: string;
  mandatory: boolean;
  weight: number;
  alternatives: string[];
}

interface UserProfile {
  id: string;
  type: 'bidder' | 'skill_provider' | 'tool_provider' | 'contractor';
  preferences: {
    categories: string[];
    locations: string[];
    valueRange: { min: number; max: number };
    complexity: string[];
  };
  capabilities: {
    skills: UserSkill[];
    tools: UserTool[];
    certifications: string[];
    experience: string[];
    bbbeeLevel: number;
    turnover: number;
  };
  location: {
    province: string;
    city: string;
    coordinates: { lat: number; lng: number };
    serviceRadius: number;
  };
}

interface UserSkill {
  name: string;
  level: 'basic' | 'intermediate' | 'advanced' | 'expert';
  certified: boolean;
  yearsExperience: number;
}

interface UserTool {
  name: string;
  version?: string;
  licensed: boolean;
  proficiency: 'basic' | 'intermediate' | 'advanced' | 'expert';
}

interface IntelligenceMatch {
  type: 'skill_gap' | 'tool_gap' | 'opportunity' | 'contractor_match' | 'supplier_match';
  urgency: 'critical' | 'high' | 'medium' | 'low';
  title: string;
  description: string;
  action: string;
  value: number;
  psychTrigger: string;
  matchScore: number;
  location?: { distance: number; inRange: boolean };
}

const TenderIntelligenceEngine: React.FC = () => {
  const { user } = useAuth();
  const { psychologicalState, isStressed } = useNeuroMarketing();
  
  const [currentTender, setCurrentTender] = useState<TenderDocument>({
    id: 'tender-001',
    title: 'Municipal Infrastructure Development - R15.6M',
    description: 'Construction of water treatment facility with advanced filtration systems',
    location: {
      province: 'Gauteng',
      city: 'Johannesburg',
      coordinates: { lat: -26.2041, lng: 28.0473 },
      radius: 50
    },
    requirements: {
      skills: [
        {
          skillName: 'Project Management Professional (PMP)',
          level: 'advanced',
          mandatory: true,
          weight: 0.3,
          alternatives: ['PRINCE2', 'CAPM']
        },
        {
          skillName: 'Water Treatment Engineering',
          level: 'expert',
          mandatory: true,
          weight: 0.4,
          alternatives: ['Environmental Engineering', 'Chemical Engineering']
        },
        {
          skillName: 'Construction Management',
          level: 'advanced',
          mandatory: false,
          weight: 0.2,
          alternatives: ['Civil Engineering']
        }
      ],
      tools: [
        {
          toolName: 'AutoCAD',
          version: '2024',
          mandatory: true,
          weight: 0.3,
          alternatives: ['MicroStation', 'SolidWorks']
        },
        {
          toolName: 'Microsoft Project',
          mandatory: true,
          weight: 0.2,
          alternatives: ['Primavera P6']
        }
      ],
      certifications: ['ISO 9001:2015', 'OHSAS 18001'],
      experience: ['Municipal Projects', 'Water Infrastructure'],
      bbbeeLevel: 4,
      turnoverRequirement: 10000000
    },
    value: 15600000,
    deadline: '2024-02-15T17:00:00',
    category: 'Infrastructure',
    complexity: 'high'
  });

  const [userProfile, setUserProfile] = useState<UserProfile>({
    id: user?.id || 'user-001',
    type: 'bidder',
    preferences: {
      categories: ['Infrastructure', 'Construction'],
      locations: ['Gauteng', 'Western Cape'],
      valueRange: { min: 1000000, max: 50000000 },
      complexity: ['medium', 'high']
    },
    capabilities: {
      skills: [
        { name: 'Construction Management', level: 'advanced', certified: true, yearsExperience: 8 },
        { name: 'Civil Engineering', level: 'expert', certified: true, yearsExperience: 12 }
      ],
      tools: [
        { name: 'AutoCAD', version: '2023', licensed: true, proficiency: 'advanced' },
        { name: 'Microsoft Office', licensed: true, proficiency: 'expert' }
      ],
      certifications: ['ISO 9001:2015'],
      experience: ['Construction Projects', 'Civil Engineering'],
      bbbeeLevel: 3,
      turnover: 25000000
    },
    location: {
      province: 'Gauteng',
      city: 'Pretoria',
      coordinates: { lat: -25.7479, lng: 28.2293 },
      serviceRadius: 100
    }
  });

  const [intelligenceMatches, setIntelligenceMatches] = useState<IntelligenceMatch[]>([]);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    analyzeIntelligence();
  }, [currentTender, userProfile]);

  const analyzeIntelligence = () => {
    const matches: IntelligenceMatch[] = [];
    
    // Analyze skill gaps based on tender requirements
    currentTender.requirements.skills.forEach(reqSkill => {
      const userSkill = userProfile.capabilities.skills.find(s => 
        s.name === reqSkill.skillName || reqSkill.alternatives.includes(s.name)
      );
      
      if (!userSkill && reqSkill.mandatory) {
        matches.push({
          type: 'skill_gap',
          urgency: 'critical',
          title: `CRITICAL: Missing ${reqSkill.skillName}`,
          description: `This tender requires ${reqSkill.skillName} (${reqSkill.level} level) - you'll be disqualified without it!`,
          action: 'Get SkillSync Certification',
          value: currentTender.value * reqSkill.weight,
          psychTrigger: `DISQUALIFICATION RISK: R${(currentTender.value / 1000000).toFixed(1)}M tender at stake!`,
          matchScore: 0,
          location: calculateDistance(userProfile.location, currentTender.location)
        });
      } else if (userSkill && getSkillLevelScore(userSkill.level) < getSkillLevelScore(reqSkill.level)) {
        matches.push({
          type: 'skill_gap',
          urgency: 'high',
          title: `Upgrade ${reqSkill.skillName}`,
          description: `Your ${userSkill.level} level needs upgrading to ${reqSkill.level} for competitive advantage`,
          action: 'Upgrade via SkillSync',
          value: currentTender.value * reqSkill.weight * 0.3,
          psychTrigger: `COMPETITIVE DISADVANTAGE: Others have higher skill levels!`,
          matchScore: getSkillLevelScore(userSkill.level) / getSkillLevelScore(reqSkill.level) * 100,
          location: calculateDistance(userProfile.location, currentTender.location)
        });
      }
    });

    // Analyze tool gaps
    currentTender.requirements.tools.forEach(reqTool => {
      const userTool = userProfile.capabilities.tools.find(t => 
        t.name === reqTool.toolName || reqTool.alternatives.includes(t.name)
      );
      
      if (!userTool && reqTool.mandatory) {
        matches.push({
          type: 'tool_gap',
          urgency: 'critical',
          title: `CRITICAL: Missing ${reqTool.toolName}`,
          description: `This tender requires ${reqTool.toolName} - get ToolSync license immediately!`,
          action: 'Get ToolSync License',
          value: currentTender.value * reqTool.weight,
          psychTrigger: `TOOL SHORTAGE: Can't submit without ${reqTool.toolName}!`,
          matchScore: 0,
          location: calculateDistance(userProfile.location, currentTender.location)
        });
      }
    });

    // Analyze opportunities for other user types
    if (userProfile.type === 'skill_provider') {
      currentTender.requirements.skills.forEach(reqSkill => {
        const canProvide = userProfile.capabilities.skills.find(s => s.name === reqSkill.skillName);
        if (canProvide) {
          matches.push({
            type: 'opportunity',
            urgency: 'high',
            title: `Training Opportunity: ${reqSkill.skillName}`,
            description: `Bidders need ${reqSkill.skillName} training for this R${(currentTender.value/1000000).toFixed(1)}M tender`,
            action: 'Offer SkillSync Course',
            value: 50000, // Estimated training revenue
            psychTrigger: `REVENUE OPPORTUNITY: High demand for your expertise!`,
            matchScore: 95,
            location: calculateDistance(userProfile.location, currentTender.location)
          });
        }
      });
    }

    // Analyze contractor sync opportunities
    if (userProfile.type === 'contractor') {
      const locationMatch = calculateDistance(userProfile.location, currentTender.location);
      if (locationMatch.inRange) {
        matches.push({
          type: 'contractor_match',
          urgency: 'medium',
          title: `ContractorSync Match: ${currentTender.title}`,
          description: `Perfect match for your capabilities in ${currentTender.category}`,
          action: 'Join as Subcontractor',
          value: currentTender.value * 0.15, // Estimated subcontractor share
          psychTrigger: `PERFECT MATCH: In your service area and expertise!`,
          matchScore: calculateMatchScore(userProfile, currentTender),
          location: locationMatch
        });
      }
    }

    setIntelligenceMatches(matches.sort((a, b) => {
      const urgencyOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      return urgencyOrder[b.urgency] - urgencyOrder[a.urgency];
    }));
  };

  const calculateDistance = (userLoc: any, tenderLoc: any) => {
    // Simplified distance calculation
    const distance = Math.sqrt(
      Math.pow(userLoc.coordinates.lat - tenderLoc.coordinates.lat, 2) +
      Math.pow(userLoc.coordinates.lng - tenderLoc.coordinates.lng, 2)
    ) * 111; // Rough km conversion
    
    return {
      distance: Math.round(distance),
      inRange: distance <= userLoc.serviceRadius
    };
  };

  const getSkillLevelScore = (level: string) => {
    const scores = { basic: 1, intermediate: 2, advanced: 3, expert: 4 };
    return scores[level as keyof typeof scores] || 0;
  };

  const calculateMatchScore = (profile: UserProfile, tender: TenderDocument) => {
    let score = 0;
    let totalWeight = 0;

    // Category match
    if (profile.preferences.categories.includes(tender.category)) score += 30;
    
    // Location match
    const locationMatch = calculateDistance(profile.location, tender.location);
    if (locationMatch.inRange) score += 25;
    
    // Value range match
    if (tender.value >= profile.preferences.valueRange.min && 
        tender.value <= profile.preferences.valueRange.max) score += 20;
    
    // Skill matches
    tender.requirements.skills.forEach(reqSkill => {
      const userSkill = profile.capabilities.skills.find(s => s.name === reqSkill.skillName);
      if (userSkill) {
        score += (getSkillLevelScore(userSkill.level) / getSkillLevelScore(reqSkill.level)) * 25;
      }
    });

    return Math.min(score, 100);
  };

  const handleRefresh = () => {
    setRefreshing(true);
    analyzeIntelligence();
    setTimeout(() => setRefreshing(false), 1000);
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'critical': return '#f44336';
      case 'high': return '#ff9800';
      case 'medium': return '#2196f3';
      case 'low': return '#4caf50';
      default: return '#757575';
    }
  };

  const formatCurrency = (value: number) => {
    if (value >= 1000000) return `R${(value / 1000000).toFixed(1)}M`;
    if (value >= 1000) return `R${(value / 1000).toFixed(0)}k`;
    return `R${value}`;
  };

  return (
    <Card>
      <CardContent>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" fontWeight="bold" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Description color="primary" />
            Tender Intelligence Engine
          </Typography>
          <IconButton onClick={handleRefresh} disabled={refreshing}>
            <Refresh sx={{ animation: refreshing ? 'spin 1s linear infinite' : 'none' }} />
          </IconButton>
        </Box>

        {/* Current Tender Analysis */}
        <Card variant="outlined" sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
              📋 Analyzing: {currentTender.title}
            </Typography>
            
            <Grid container spacing={2} sx={{ mb: 2 }}>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">Value</Typography>
                <Typography variant="h6" fontWeight="bold" color="success.main">
                  {formatCurrency(currentTender.value)}
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">Location</Typography>
                <Typography variant="body2" fontWeight="bold">
                  {currentTender.location.city}, {currentTender.location.province}
                </Typography>
              </Grid>
            </Grid>

            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              Requirements Analysis:
            </Typography>
            <Stack direction="row" spacing={1} flexWrap="wrap" gap={1}>
              {currentTender.requirements.skills.map((skill, index) => (
                <Chip 
                  key={index}
                  label={`${skill.skillName} (${skill.level})`}
                  size="small"
                  color={skill.mandatory ? 'error' : 'default'}
                />
              ))}
              {currentTender.requirements.tools.map((tool, index) => (
                <Chip 
                  key={index}
                  label={tool.toolName}
                  size="small"
                  color={tool.mandatory ? 'warning' : 'default'}
                />
              ))}
            </Stack>
          </CardContent>
        </Card>

        {/* Intelligence Matches */}
        <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
          🧠 Intelligence Matches for {userProfile.type.replace('_', ' ').toUpperCase()}
        </Typography>

        <Stack spacing={2}>
          {intelligenceMatches.map((match, index) => (
            <Card 
              key={index}
              variant="outlined"
              sx={{ 
                border: `2px solid ${getUrgencyColor(match.urgency)}40`,
                '&:hover': {
                  transform: 'translateY(-1px)',
                  boxShadow: 2
                }
              }}
            >
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body1" fontWeight="bold">
                      {match.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      {match.description}
                    </Typography>
                    <Typography variant="caption" color="error.main" fontWeight="bold">
                      {match.psychTrigger}
                    </Typography>
                  </Box>
                  <Stack spacing={1} alignItems="flex-end">
                    <Chip 
                      label={match.urgency.toUpperCase()}
                      size="small"
                      sx={{ 
                        backgroundColor: getUrgencyColor(match.urgency),
                        color: 'white',
                        fontWeight: 'bold'
                      }}
                    />
                    {match.matchScore > 0 && (
                      <Typography variant="caption" color="success.main">
                        {match.matchScore}% match
                      </Typography>
                    )}
                  </Stack>
                </Box>

                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2 }}>
                  <Box>
                    <Typography variant="body2" fontWeight="bold" color="success.main">
                      Value: {formatCurrency(match.value)}
                    </Typography>
                    {match.location && (
                      <Typography variant="caption" color="text.secondary">
                        📍 {match.location.distance}km away
                        {match.location.inRange && ' ✅ In range'}
                      </Typography>
                    )}
                  </Box>
                  <Button
                    variant="contained"
                    size="small"
                    startIcon={<FlashOn />}
                    sx={{
                      backgroundColor: getUrgencyColor(match.urgency),
                      '&:hover': {
                        backgroundColor: getUrgencyColor(match.urgency),
                        opacity: 0.8
                      }
                    }}
                  >
                    {match.action}
                  </Button>
                </Box>
              </CardContent>
            </Card>
          ))}
        </Stack>

        {intelligenceMatches.length === 0 && (
          <Alert severity="success">
            <Typography variant="body2" fontWeight="bold">
              🎯 PERFECT MATCH: You meet all requirements for this tender!
            </Typography>
            <Typography variant="caption">
              Your profile is fully aligned with tender requirements
            </Typography>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};

export default TenderIntelligenceEngine;
