import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  <PERSON>,
  Alert,
  LinearProgress,
  Stack,
  Button,
  IconButton,
  Tooltip,
  Avatar,
  Grid,
  Divider,
  Badge
} from '@mui/material';
import {
  Warning,
  FlashOn,
  Build,
  School,
  Security,
  CheckCircle,
  Cancel,
  Timer,
  TrendingUp,
  Psychology,
  Refresh
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';

interface ComplianceGap {
  id: string;
  type: 'skill' | 'tool' | 'certification';
  name: string;
  urgency: 'critical' | 'high' | 'medium';
  tendersBlocked: number;
  potentialValue: number;
  availableProviders: number;
  timeToComplete: string;
  costRange: string;
  psychTrigger: string;
  successRate: number;
}

interface ComplianceProvider {
  id: string;
  name: string;
  type: 'skillsync' | 'toolsync';
  service: string;
  availability: 'available' | 'limited' | 'waitlist' | 'unavailable';
  price: number;
  surgeMultiplier: number;
  slotsLeft: number;
  totalSlots: number;
  rating: number;
  completionTime: string;
  guaranteedCompliance: boolean;
}

const ComplianceScarcitySystem: React.FC = () => {
  const { user } = useAuth();
  
  const [complianceGaps, setComplianceGaps] = useState<ComplianceGap[]>([
    {
      id: 'gap-1',
      type: 'skill',
      name: 'Certified Project Manager (PMP)',
      urgency: 'critical',
      tendersBlocked: 12,
      potentialValue: 45000000,
      availableProviders: 3,
      timeToComplete: '6 weeks',
      costRange: 'R15k - R25k',
      psychTrigger: 'BLOCKING R45M in tenders!',
      successRate: 89
    },
    {
      id: 'gap-2',
      type: 'tool',
      name: 'AutoCAD Professional License',
      urgency: 'high',
      tendersBlocked: 8,
      potentialValue: 28000000,
      availableProviders: 2,
      timeToComplete: '2 days',
      costRange: 'R8k - R12k',
      psychTrigger: 'Missing from 8 engineering tenders!',
      successRate: 95
    },
    {
      id: 'gap-3',
      type: 'certification',
      name: 'ISO 9001:2015 Quality Management',
      urgency: 'critical',
      tendersBlocked: 15,
      potentialValue: 67000000,
      availableProviders: 1,
      timeToComplete: '4 weeks',
      costRange: 'R35k - R50k',
      psychTrigger: 'LAST PROVIDER available this quarter!',
      successRate: 92
    }
  ]);

  const [providers, setProviders] = useState<ComplianceProvider[]>([
    {
      id: 'skill-1',
      name: 'SkillSync Elite Training',
      type: 'skillsync',
      service: 'PMP Certification Fast-Track',
      availability: 'limited',
      price: 18000,
      surgeMultiplier: 1.3,
      slotsLeft: 2,
      totalSlots: 12,
      rating: 4.9,
      completionTime: '6 weeks',
      guaranteedCompliance: true
    },
    {
      id: 'tool-1',
      name: 'ToolSync Pro Access',
      type: 'toolsync',
      service: 'AutoCAD + Training Package',
      availability: 'available',
      price: 9500,
      surgeMultiplier: 1.1,
      slotsLeft: 8,
      totalSlots: 15,
      rating: 4.8,
      completionTime: '2 days',
      guaranteedCompliance: true
    },
    {
      id: 'cert-1',
      name: 'SkillSync Compliance Hub',
      type: 'skillsync',
      service: 'ISO 9001 Certification',
      availability: 'waitlist',
      price: 42000,
      surgeMultiplier: 1.6,
      slotsLeft: 0,
      totalSlots: 8,
      rating: 4.9,
      completionTime: '4 weeks',
      guaranteedCompliance: true
    }
  ]);

  const [showCriticalAlert, setShowCriticalAlert] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    // Simulate real-time compliance updates
    const interval = setInterval(() => {
      updateComplianceScarcity();
    }, 25000); // Update every 25 seconds

    return () => clearInterval(interval);
  }, []);

  const updateComplianceScarcity = () => {
    // Update provider availability
    setProviders(prev => prev.map(provider => {
      const newSlots = Math.max(0, provider.slotsLeft + Math.floor(Math.random() * 3) - 1);
      return {
        ...provider,
        slotsLeft: newSlots,
        availability: newSlots === 0 ? 'waitlist' :
                     newSlots <= 2 ? 'limited' :
                     newSlots <= 5 ? 'available' : 'available',
        surgeMultiplier: newSlots <= 2 ? 1.6 : newSlots <= 5 ? 1.3 : 1.1
      };
    }));

    // Update compliance gaps
    setComplianceGaps(prev => prev.map(gap => ({
      ...gap,
      tendersBlocked: Math.max(5, gap.tendersBlocked + Math.floor(Math.random() * 4) - 2),
      availableProviders: Math.max(1, gap.availableProviders + Math.floor(Math.random() * 2) - 1)
    })));
  };

  const handleRefresh = () => {
    setRefreshing(true);
    updateComplianceScarcity();
    setTimeout(() => setRefreshing(false), 1000);
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'critical': return '#f44336';
      case 'high': return '#ff9800';
      case 'medium': return '#2196f3';
      default: return '#757575';
    }
  };

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case 'unavailable': return '#f44336';
      case 'waitlist': return '#ff5722';
      case 'limited': return '#ff9800';
      case 'available': return '#4caf50';
      default: return '#757575';
    }
  };

  const formatCurrency = (value: number) => {
    if (value >= 1000000) return `R${(value / 1000000).toFixed(1)}M`;
    if (value >= 1000) return `R${(value / 1000).toFixed(0)}k`;
    return `R${value}`;
  };

  const handleBookCompliance = (providerId: string) => {
    // Navigate to compliance booking with urgency flags
    window.location.href = `/compliance/book/${providerId}?urgent=true&scarcity=true`;
  };

  const totalBlockedValue = complianceGaps.reduce((sum, gap) => sum + gap.potentialValue, 0);
  const criticalGaps = complianceGaps.filter(gap => gap.urgency === 'critical').length;

  return (
    <Card sx={{ position: 'relative' }}>
      <CardContent>
        {/* Header with Refresh */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" fontWeight="bold" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            🚨 Compliance Crisis Alert
          </Typography>
          <IconButton onClick={handleRefresh} disabled={refreshing}>
            <Refresh sx={{ animation: refreshing ? 'spin 1s linear infinite' : 'none' }} />
          </IconButton>
        </Box>

        {/* Critical Alert */}
        {criticalGaps > 0 && showCriticalAlert && (
          <Alert 
            severity="error" 
            sx={{ mb: 2 }}
            onClose={() => setShowCriticalAlert(false)}
          >
            <Typography variant="body2" fontWeight="bold">
              🚨 COMPLIANCE EMERGENCY: {criticalGaps} critical gaps blocking {formatCurrency(totalBlockedValue)} in tenders!
            </Typography>
            <Typography variant="caption">
              You're missing out on life-changing opportunities due to compliance gaps
            </Typography>
          </Alert>
        )}

        {/* Compliance Overview */}
        <Box sx={{ mb: 3 }}>
          <Grid container spacing={2}>
            <Grid item xs={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h3" fontWeight="bold" color="error.main">
                  {complianceGaps.length}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Compliance Gaps
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h3" fontWeight="bold" color="warning.main">
                  {complianceGaps.reduce((sum, gap) => sum + gap.tendersBlocked, 0)}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Tenders Blocked
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h3" fontWeight="bold" color="error.main">
                  {formatCurrency(totalBlockedValue)}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Value at Risk
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Box>

        <Divider sx={{ my: 2 }} />

        {/* Critical Compliance Gaps */}
        <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
          🚨 Critical Compliance Gaps
        </Typography>
        
        <Stack spacing={2} sx={{ mb: 3 }}>
          {complianceGaps.map((gap) => (
            <Card 
              key={gap.id}
              variant="outlined"
              sx={{ 
                border: `2px solid ${getUrgencyColor(gap.urgency)}40`,
                '&:hover': {
                  transform: 'translateY(-1px)',
                  boxShadow: 2
                }
              }}
            >
              <CardContent sx={{ p: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body1" fontWeight="bold" sx={{ mb: 0.5 }}>
                      {gap.name}
                    </Typography>
                    <Typography variant="caption" color="error.main" fontWeight="bold">
                      {gap.psychTrigger}
                    </Typography>
                  </Box>
                  <Stack direction="row" spacing={1}>
                    <Chip 
                      label={gap.type.toUpperCase()}
                      size="small"
                      color={gap.type === 'skill' ? 'primary' : gap.type === 'tool' ? 'secondary' : 'warning'}
                    />
                    <Chip 
                      label={gap.urgency.toUpperCase()}
                      size="small"
                      sx={{ 
                        backgroundColor: getUrgencyColor(gap.urgency),
                        color: 'white',
                        fontWeight: 'bold'
                      }}
                    />
                  </Stack>
                </Box>

                <Grid container spacing={2} sx={{ mb: 2 }}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">Tenders Blocked</Typography>
                    <Typography variant="h6" fontWeight="bold" color="warning.main">
                      {gap.tendersBlocked}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">Value at Risk</Typography>
                    <Typography variant="h6" fontWeight="bold" color="error.main">
                      {formatCurrency(gap.potentialValue)}
                    </Typography>
                  </Grid>
                </Grid>

                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                  <Typography variant="body2">
                    <strong>{gap.availableProviders}</strong> providers available • <strong>{gap.timeToComplete}</strong> completion
                  </Typography>
                  <Typography variant="body2" color="success.main" fontWeight="bold">
                    {gap.successRate}% success rate
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          ))}
        </Stack>

        <Divider sx={{ my: 2 }} />

        {/* SkillSync & ToolSync Providers */}
        <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
          🛠️ SkillSync & ToolSync Solutions
        </Typography>
        
        <Stack spacing={2}>
          {providers.map((provider) => (
            <Card 
              key={provider.id}
              variant="outlined"
              sx={{ 
                border: `2px solid ${getAvailabilityColor(provider.availability)}40`,
                '&:hover': {
                  transform: 'translateY(-1px)',
                  boxShadow: 2
                }
              }}
            >
              <CardContent sx={{ p: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body1" fontWeight="bold" sx={{ mb: 0.5 }}>
                      {provider.service}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      by {provider.name} • ⭐ {provider.rating}/5
                    </Typography>
                  </Box>
                  <Chip 
                    label={provider.availability.toUpperCase()}
                    size="small"
                    sx={{ 
                      backgroundColor: getAvailabilityColor(provider.availability),
                      color: 'white',
                      fontWeight: 'bold'
                    }}
                  />
                </Box>

                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Box>
                    <Typography variant="h6" fontWeight="bold" color="primary.main">
                      R{provider.price.toLocaleString()}
                    </Typography>
                    {provider.surgeMultiplier > 1.2 && (
                      <Typography variant="caption" color="error.main">
                        +{Math.round((provider.surgeMultiplier - 1) * 100)}% surge pricing
                      </Typography>
                    )}
                  </Box>
                  <Box sx={{ textAlign: 'right' }}>
                    <Typography variant="body2" fontWeight="bold">
                      {provider.slotsLeft}/{provider.totalSlots} slots
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {provider.completionTime}
                    </Typography>
                  </Box>
                </Box>

                <LinearProgress 
                  variant="determinate" 
                  value={(provider.slotsLeft / provider.totalSlots) * 100}
                  sx={{ 
                    height: 6, 
                    borderRadius: 3,
                    mb: 2,
                    backgroundColor: '#e0e0e0',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: getAvailabilityColor(provider.availability),
                      borderRadius: 3
                    }
                  }} 
                />

                <Button
                  variant="contained"
                  fullWidth
                  startIcon={<FlashOn />}
                  onClick={() => handleBookCompliance(provider.id)}
                  disabled={provider.availability === 'unavailable'}
                  sx={{
                    backgroundColor: getAvailabilityColor(provider.availability),
                    fontWeight: 'bold',
                    '&:hover': {
                      backgroundColor: getAvailabilityColor(provider.availability),
                      opacity: 0.8
                    }
                  }}
                >
                  {provider.availability === 'unavailable' ? 'FULLY BOOKED' : 
                   provider.availability === 'waitlist' ? '📝 JOIN WAITLIST' :
                   provider.availability === 'limited' ? '🔥 BOOK LAST SLOT!' : 'BOOK NOW'}
                </Button>
              </CardContent>
            </Card>
          ))}
        </Stack>

        {/* Success Alert */}
        <Alert severity="success" sx={{ mt: 2 }}>
          <Typography variant="body2" fontWeight="bold">
            🎯 COMPLIANCE BOOST: Bidders with complete compliance win 67% more tenders!
          </Typography>
          <Typography variant="caption">
            SkillSync & ToolSync users report 3.2X higher success rates
          </Typography>
        </Alert>
      </CardContent>
    </Card>
  );
};

export default ComplianceScarcitySystem;
