import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  IconButton,
  Chip,
  Stack,
  Avatar,
  LinearProgress,
  Tooltip,
  Collapse,
  Alert
} from '@mui/material';
import {
  Psychology,
  VolumeUp,
  VolumeOff,
  TrendingUp,
  EmojiEvents,
  Speed,
  GpsFixed as Target,
  FlashOn,
  Refresh,
  ExpandMore,
  ExpandLess,
  Lightbulb
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useNeuroMarketing } from '../../hooks/useNeuroMarketing';
import usePsychologicalSystems from '../../hooks/usePsychologicalSystems';

interface ConfidenceMetrics {
  winStreakAnalysis: {
    current: number;
    trend: 'up' | 'down' | 'flat';
    confidence: number;
    prediction: string;
  };
  psychologicalState: {
    motivation: number;
    stress: number;
    confidence: number;
    momentum: number;
  };
  recommendations: Array<{
    id: string;
    type: 'rfq' | 'tender' | 'strategy' | 'timing';
    message: string;
    urgency: 'low' | 'medium' | 'high';
    action?: string;
  }>;
  encouragement: string;
  nextGoal: string;
}

const ConfidenceCoach: React.FC = () => {
  const { user } = useAuth();
  const { psychologicalState, isStressed, needsSimplification } = useNeuroMarketing();
  const { profile: psychProfile } = usePsychologicalSystems();
  
  const [audioEnabled, setAudioEnabled] = useState(false);
  const [expanded, setExpanded] = useState(false);
  const [metrics, setMetrics] = useState<ConfidenceMetrics>({
    winStreakAnalysis: {
      current: 2,
      trend: 'up',
      confidence: 89,
      prediction: 'Your 200% win streak suggests boldness!'
    },
    psychologicalState: {
      motivation: 85,
      stress: 25,
      confidence: 89,
      momentum: 92
    },
    recommendations: [
      {
        id: '1',
        type: 'rfq',
        message: 'Your RFQ success rate is 89% - create 3 more this week to hit your target mix!',
        urgency: 'high',
        action: 'Create RFQ'
      },
      {
        id: '2',
        type: 'strategy',
        message: 'Your confidence is peaking - perfect time for a challenging tender',
        urgency: 'medium',
        action: 'Browse Tenders'
      },
      {
        id: '3',
        type: 'timing',
        message: 'Friday afternoon submissions have 23% higher success rate for you',
        urgency: 'low'
      }
    ],
    encouragement: 'Your 200% win streak suggests boldness!',
    nextGoal: 'Reach 5-win streak for Elite status'
  });

  const [currentMessage, setCurrentMessage] = useState(0);

  useEffect(() => {
    // Rotate through different encouragement messages
    const interval = setInterval(() => {
      setCurrentMessage(prev => (prev + 1) % encouragementMessages.length);
    }, 8000);

    return () => clearInterval(interval);
  }, []);

  const encouragementMessages = [
    'Your 200% win streak suggests boldness!',
    'Peak confidence detected - time to strike!',
    'Your momentum is building - ride the wave!',
    'Success pattern recognized - keep pushing!',
    'Elite performance mode activated!'
  ];

  const getConfidenceColor = (value: number) => {
    if (value >= 80) return '#4caf50';
    if (value >= 60) return '#2196f3';
    if (value >= 40) return '#ff9800';
    return '#f44336';
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'high': return '#f44336';
      case 'medium': return '#ff9800';
      case 'low': return '#4caf50';
      default: return '#2196f3';
    }
  };

  const handleAudioToggle = () => {
    setAudioEnabled(!audioEnabled);
    if (!audioEnabled) {
      // In real implementation, would trigger text-to-speech
      console.log('Audio coaching enabled');
    }
  };

  const handleRefreshAnalysis = () => {
    // Simulate AI analysis refresh
    setMetrics(prev => ({
      ...prev,
      psychologicalState: {
        ...prev.psychologicalState,
        confidence: Math.min(prev.psychologicalState.confidence + Math.random() * 5, 100),
        momentum: Math.min(prev.psychologicalState.momentum + Math.random() * 3, 100)
      }
    }));
  };

  const handleRecommendationAction = (recommendation: any) => {
    switch (recommendation.action) {
      case 'Create RFQ':
        window.location.href = '/rfq/create';
        break;
      case 'Browse Tenders':
        window.location.href = '/tenders';
        break;
      default:
        console.log('Action:', recommendation.action);
    }
  };

  return (
    <Card 
      sx={{ 
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', 
        color: 'white',
        position: 'relative',
        overflow: 'visible'
      }}
    >
      <CardContent sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar sx={{ backgroundColor: 'rgba(255,255,255,0.2)' }}>
              <Psychology sx={{ color: 'white' }} />
            </Avatar>
            <Typography variant="h6" fontWeight="bold">
              Confidence Coach
            </Typography>
          </Box>
          
          <Stack direction="row" spacing={1}>
            <Tooltip title={audioEnabled ? 'Disable Audio' : 'Enable Audio Coaching'}>
              <IconButton onClick={handleAudioToggle} sx={{ color: 'white' }}>
                {audioEnabled ? <VolumeUp /> : <VolumeOff />}
              </IconButton>
            </Tooltip>
            <Tooltip title="Refresh Analysis">
              <IconButton onClick={handleRefreshAnalysis} sx={{ color: 'white' }}>
                <Refresh />
              </IconButton>
            </Tooltip>
          </Stack>
        </Box>

        {/* Main Encouragement */}
        <Box sx={{ mb: 3, textAlign: 'center' }}>
          <Typography 
            variant="h6" 
            fontWeight="bold" 
            sx={{ 
              mb: 2,
              minHeight: '3rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            {encouragementMessages[currentMessage]}
          </Typography>
          
          {/* Confidence Metrics */}
          <Stack direction="row" spacing={2} justifyContent="center">
            <Chip 
              label={`${metrics.psychologicalState.confidence}% Confident`}
              sx={{ 
                backgroundColor: 'rgba(255,255,255,0.2)', 
                color: 'white',
                fontWeight: 'bold'
              }}
            />
            <Chip 
              label={`${metrics.psychologicalState.momentum}% Momentum`}
              sx={{ 
                backgroundColor: 'rgba(255,255,255,0.2)', 
                color: 'white',
                fontWeight: 'bold'
              }}
            />
          </Stack>
        </Box>

        {/* Primary Recommendation */}
        <Alert 
          severity="info" 
          sx={{ 
            mb: 2,
            backgroundColor: 'rgba(255,255,255,0.15)',
            color: 'white',
            '& .MuiAlert-icon': { color: 'white' }
          }}
        >
          <Typography variant="body2" fontWeight="bold">
            🎯 {metrics.recommendations[0]?.message}
          </Typography>
        </Alert>

        {/* Action Button */}
        {metrics.recommendations[0]?.action && (
          <Button
            variant="contained"
            fullWidth
            startIcon={<FlashOn />}
            onClick={() => handleRecommendationAction(metrics.recommendations[0])}
            sx={{
              mb: 2,
              backgroundColor: 'rgba(255,255,255,0.2)',
              color: 'white',
              fontWeight: 'bold',
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,0.3)',
                transform: 'translateY(-1px)'
              }
            }}
          >
            {metrics.recommendations[0].action}
          </Button>
        )}

        {/* Expandable Details */}
        <Box>
          <Button
            fullWidth
            onClick={() => setExpanded(!expanded)}
            endIcon={expanded ? <ExpandLess /> : <ExpandMore />}
            sx={{ 
              color: 'white', 
              textTransform: 'none',
              justifyContent: 'space-between'
            }}
          >
            <Typography variant="body2">
              {expanded ? 'Hide Details' : 'Show Detailed Analysis'}
            </Typography>
          </Button>
          
          <Collapse in={expanded}>
            <Box sx={{ mt: 2 }}>
              {/* Psychological State Bars */}
              <Typography variant="body2" sx={{ mb: 2, opacity: 0.9 }}>
                Psychological Analysis:
              </Typography>
              
              <Stack spacing={1.5}>
                {Object.entries(metrics.psychologicalState).map(([key, value]) => (
                  <Box key={key}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                      <Typography variant="caption" sx={{ textTransform: 'capitalize' }}>
                        {key.replace(/([A-Z])/g, ' $1')}
                      </Typography>
                      <Typography variant="caption" fontWeight="bold">
                        {value}%
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={value}
                      sx={{
                        height: 6,
                        borderRadius: 3,
                        backgroundColor: 'rgba(255,255,255,0.2)',
                        '& .MuiLinearProgress-bar': {
                          backgroundColor: getConfidenceColor(value),
                          borderRadius: 3
                        }
                      }}
                    />
                  </Box>
                ))}
              </Stack>

              {/* Additional Recommendations */}
              <Typography variant="body2" sx={{ mt: 3, mb: 1, opacity: 0.9 }}>
                Additional Insights:
              </Typography>
              
              <Stack spacing={1}>
                {metrics.recommendations.slice(1).map((rec) => (
                  <Box 
                    key={rec.id}
                    sx={{ 
                      p: 1.5, 
                      borderRadius: 1, 
                      backgroundColor: 'rgba(255,255,255,0.1)',
                      border: `1px solid ${getUrgencyColor(rec.urgency)}40`
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                      <Lightbulb sx={{ fontSize: 16 }} />
                      <Chip 
                        label={rec.urgency}
                        size="small"
                        sx={{ 
                          backgroundColor: getUrgencyColor(rec.urgency),
                          color: 'white',
                          fontSize: '0.7rem',
                          height: 20
                        }}
                      />
                    </Box>
                    <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                      {rec.message}
                    </Typography>
                  </Box>
                ))}
              </Stack>

              {/* Next Goal */}
              <Box sx={{ 
                mt: 3, 
                p: 2, 
                borderRadius: 2, 
                backgroundColor: 'rgba(255,255,255,0.1)',
                textAlign: 'center'
              }}>
                <Typography variant="body2" fontWeight="bold" sx={{ mb: 1 }}>
                  🎯 Next Goal
                </Typography>
                <Typography variant="body2">
                  {metrics.nextGoal}
                </Typography>
              </Box>
            </Box>
          </Collapse>
        </Box>
      </CardContent>
    </Card>
  );
};

export default ConfidenceCoach;
