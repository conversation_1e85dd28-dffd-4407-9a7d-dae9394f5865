import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typo<PERSON>,
  Chip,
  Alert,
  LinearProgress,
  Stack,
  Button,
  IconButton,
  Tooltip,
  Avatar,
  Grid,
  Divider,
  Badge
} from '@mui/material';
import {
  Warning,
  FlashOn,
  Timer,
  TrendingUp,
  MonetizationOn,
  Psychology,
  Refresh,
  Visibility,
  People,
  Speed
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';

interface TenderScarcity {
  category: string;
  totalTenders: number;
  availableTenders: number;
  closingSoon: number;
  competitorCount: number;
  avgBidders: number;
  scarcityLevel: 'low' | 'medium' | 'high' | 'critical';
  priceRange: string;
  successRate: number;
  urgencyMessage: string;
}

interface ScarceTender {
  id: string;
  title: string;
  value: number;
  deadline: string;
  hoursLeft: number;
  currentBidders: number;
  maxBidders: number;
  scarcityType: 'deadline' | 'competition' | 'qualification' | 'exclusive';
  urgencyLevel: 'critical' | 'high' | 'medium';
  psychTrigger: string;
  successProbability: number;
  premiumAccess: boolean;
}

const TenderScarcitySystem: React.FC = () => {
  const { user } = useAuth();
  
  const [tenderScarcity, setTenderScarcity] = useState<TenderScarcity>({
    category: 'Construction',
    totalTenders: 156,
    availableTenders: 23,
    closingSoon: 8,
    competitorCount: 847,
    avgBidders: 12,
    scarcityLevel: 'high',
    priceRange: 'R2.5M - R45M',
    successRate: 73,
    urgencyMessage: '⚠️ HIGH COMPETITION: 847 bidders active in construction!'
  });

  const [scarceTenders, setScarceTenders] = useState<ScarceTender[]>([
    {
      id: 'tender-001',
      title: 'Municipal Infrastructure Development - R15.6M',
      value: 15600000,
      deadline: '2024-02-15T17:00:00',
      hoursLeft: 4,
      currentBidders: 8,
      maxBidders: 10,
      scarcityType: 'deadline',
      urgencyLevel: 'critical',
      psychTrigger: 'ONLY 4 HOURS LEFT!',
      successProbability: 89,
      premiumAccess: false
    },
    {
      id: 'tender-002',
      title: 'Grade 7+ ONLY: Highway Construction - R22.4M',
      value: 22400000,
      deadline: '2024-02-20T12:00:00',
      hoursLeft: 72,
      currentBidders: 3,
      maxBidders: 5,
      scarcityType: 'qualification',
      urgencyLevel: 'high',
      psychTrigger: 'EXCLUSIVE: Only Grade 7+ qualify!',
      successProbability: 94,
      premiumAccess: true
    },
    {
      id: 'tender-003',
      title: 'IT Infrastructure Upgrade - R8.5M',
      value: 8500000,
      deadline: '2024-02-18T16:00:00',
      hoursLeft: 48,
      currentBidders: 14,
      maxBidders: 15,
      scarcityType: 'competition',
      urgencyLevel: 'critical',
      psychTrigger: 'LAST SLOT: 14/15 bidders registered!',
      successProbability: 67,
      premiumAccess: false
    }
  ]);

  const [showUrgentAlert, setShowUrgentAlert] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    // Simulate real-time tender scarcity updates
    const interval = setInterval(() => {
      updateTenderScarcity();
    }, 20000); // Update every 20 seconds

    return () => clearInterval(interval);
  }, []);

  const updateTenderScarcity = () => {
    setTenderScarcity(prev => {
      const newAvailable = Math.max(15, prev.availableTenders + Math.floor(Math.random() * 8) - 4);
      const newClosingSoon = Math.max(3, prev.closingSoon + Math.floor(Math.random() * 4) - 2);
      const newCompetitors = prev.competitorCount + Math.floor(Math.random() * 20) - 10;
      
      const scarcityLevel = newAvailable <= 20 ? 'critical' : 
                           newAvailable <= 35 ? 'high' : 
                           newAvailable <= 60 ? 'medium' : 'low';
      
      return {
        ...prev,
        availableTenders: newAvailable,
        closingSoon: newClosingSoon,
        competitorCount: newCompetitors,
        scarcityLevel,
        urgencyMessage: newAvailable <= 20 ? '🚨 CRITICAL: Only 20 tenders available!' :
                       newCompetitors > 800 ? '⚠️ HIGH COMPETITION: 800+ active bidders!' :
                       '✅ Good tender availability'
      };
    });

    // Update individual tender scarcity
    setScarceTenders(prev => prev.map(tender => {
      const newBidders = Math.min(tender.maxBidders, tender.currentBidders + Math.floor(Math.random() * 3));
      const newHours = Math.max(1, tender.hoursLeft - 0.5);
      
      return {
        ...tender,
        currentBidders: newBidders,
        hoursLeft: newHours,
        urgencyLevel: newHours <= 6 ? 'critical' : newHours <= 24 ? 'high' : 'medium',
        psychTrigger: newHours <= 6 ? `URGENT: ${Math.floor(newHours)} hours left!` :
                     newBidders >= tender.maxBidders - 1 ? `LAST SLOT: ${newBidders}/${tender.maxBidders} bidders!` :
                     tender.psychTrigger
      };
    }));
  };

  const handleRefresh = () => {
    setRefreshing(true);
    updateTenderScarcity();
    setTimeout(() => setRefreshing(false), 1000);
  };

  const getScarcityColor = (level: string) => {
    switch (level) {
      case 'critical': return '#f44336';
      case 'high': return '#ff5722';
      case 'medium': return '#ff9800';
      case 'low': return '#4caf50';
      default: return '#757575';
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'critical': return '#f44336';
      case 'high': return '#ff9800';
      case 'medium': return '#2196f3';
      default: return '#757575';
    }
  };

  const formatCurrency = (value: number) => {
    if (value >= 1000000) return `R${(value / 1000000).toFixed(1)}M`;
    if (value >= 1000) return `R${(value / 1000).toFixed(0)}k`;
    return `R${value}`;
  };

  const handleBidNow = (tenderId: string) => {
    // Navigate to bid creation with urgency flags
    window.location.href = `/bids/create?tender=${tenderId}&urgent=true&scarcity=true`;
  };

  const handleViewTender = (tenderId: string) => {
    window.location.href = `/tenders/${tenderId}`;
  };

  return (
    <Card sx={{ position: 'relative' }}>
      <CardContent>
        {/* Header with Refresh */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" fontWeight="bold" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            🎯 Tender Scarcity Alert
          </Typography>
          <IconButton onClick={handleRefresh} disabled={refreshing}>
            <Refresh sx={{ animation: refreshing ? 'spin 1s linear infinite' : 'none' }} />
          </IconButton>
        </Box>

        {/* Critical Scarcity Alert */}
        {tenderScarcity.scarcityLevel === 'critical' && showUrgentAlert && (
          <Alert 
            severity="error" 
            sx={{ mb: 2 }}
            onClose={() => setShowUrgentAlert(false)}
          >
            <Typography variant="body2" fontWeight="bold">
              🚨 TENDER SHORTAGE! Only {tenderScarcity.availableTenders} tenders available in {tenderScarcity.category}!
            </Typography>
            <Typography variant="caption">
              {tenderScarcity.competitorCount}+ bidders competing for limited opportunities
            </Typography>
          </Alert>
        )}

        {/* Market Overview */}
        <Box sx={{ mb: 3 }}>
          <Grid container spacing={2}>
            <Grid item xs={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h3" fontWeight="bold" color={getScarcityColor(tenderScarcity.scarcityLevel)}>
                  {tenderScarcity.availableTenders}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Available Tenders
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h3" fontWeight="bold" color="error.main">
                  {tenderScarcity.closingSoon}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Closing Soon
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h3" fontWeight="bold" color="warning.main">
                  {tenderScarcity.competitorCount}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Active Bidders
                </Typography>
              </Box>
            </Grid>
          </Grid>
          
          <Box sx={{ mt: 2 }}>
            <LinearProgress 
              variant="determinate" 
              value={(tenderScarcity.competitorCount / 1000) * 100}
              sx={{ 
                height: 8, 
                borderRadius: 4,
                backgroundColor: '#e0e0e0',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: getScarcityColor(tenderScarcity.scarcityLevel),
                  borderRadius: 4
                }
              }} 
            />
            <Typography variant="body2" sx={{ mt: 1, textAlign: 'center' }}>
              {tenderScarcity.urgencyMessage}
            </Typography>
          </Box>
        </Box>

        <Divider sx={{ my: 2 }} />

        {/* Scarce Tenders */}
        <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
          🚨 Critical Tender Opportunities
        </Typography>
        
        <Stack spacing={2}>
          {scarceTenders.map((tender) => (
            <Card 
              key={tender.id}
              variant="outlined"
              sx={{ 
                border: `2px solid ${getUrgencyColor(tender.urgencyLevel)}40`,
                '&:hover': {
                  transform: 'translateY(-1px)',
                  boxShadow: 3
                }
              }}
            >
              <CardContent sx={{ p: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body1" fontWeight="bold" sx={{ mb: 0.5 }}>
                      {tender.title}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {tender.psychTrigger}
                    </Typography>
                  </Box>
                  <Stack direction="row" spacing={1}>
                    {tender.premiumAccess && (
                      <Chip 
                        label="EXCLUSIVE"
                        size="small"
                        sx={{ 
                          backgroundColor: '#9c27b0',
                          color: 'white',
                          fontWeight: 'bold'
                        }}
                      />
                    )}
                    <Chip 
                      label={tender.urgencyLevel.toUpperCase()}
                      size="small"
                      sx={{ 
                        backgroundColor: getUrgencyColor(tender.urgencyLevel),
                        color: 'white',
                        fontWeight: 'bold'
                      }}
                    />
                  </Stack>
                </Box>

                <Grid container spacing={2} sx={{ mb: 2 }}>
                  <Grid item xs={4}>
                    <Typography variant="body2" color="text.secondary">Value</Typography>
                    <Typography variant="h6" fontWeight="bold" color="success.main">
                      {formatCurrency(tender.value)}
                    </Typography>
                  </Grid>
                  <Grid item xs={4}>
                    <Typography variant="body2" color="text.secondary">Time Left</Typography>
                    <Typography variant="h6" fontWeight="bold" color={getUrgencyColor(tender.urgencyLevel)}>
                      {Math.floor(tender.hoursLeft)}h
                    </Typography>
                  </Grid>
                  <Grid item xs={4}>
                    <Typography variant="body2" color="text.secondary">Bidders</Typography>
                    <Typography variant="h6" fontWeight="bold" color="warning.main">
                      {tender.currentBidders}/{tender.maxBidders}
                    </Typography>
                  </Grid>
                </Grid>

                <LinearProgress 
                  variant="determinate" 
                  value={(tender.currentBidders / tender.maxBidders) * 100}
                  sx={{ 
                    height: 6, 
                    borderRadius: 3,
                    mb: 2,
                    backgroundColor: '#e0e0e0',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: getUrgencyColor(tender.urgencyLevel),
                      borderRadius: 3
                    }
                  }} 
                />

                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<Visibility />}
                    onClick={() => handleViewTender(tender.id)}
                    sx={{ flex: 1 }}
                  >
                    View Details
                  </Button>
                  <Button
                    variant="contained"
                    size="small"
                    startIcon={<FlashOn />}
                    onClick={() => handleBidNow(tender.id)}
                    sx={{
                      flex: 2,
                      backgroundColor: getUrgencyColor(tender.urgencyLevel),
                      fontWeight: 'bold',
                      '&:hover': {
                        backgroundColor: getUrgencyColor(tender.urgencyLevel),
                        opacity: 0.8
                      }
                    }}
                  >
                    {tender.urgencyLevel === 'critical' ? '🚨 BID NOW!' : 
                     tender.currentBidders >= tender.maxBidders - 1 ? '⚡ LAST CHANCE!' : 'BID NOW'}
                  </Button>
                </Box>
              </CardContent>
            </Card>
          ))}
        </Stack>

        {/* Success Rate Alert */}
        <Alert severity="success" sx={{ mt: 2 }}>
          <Typography variant="body2" fontWeight="bold">
            🎯 SUCCESS BOOST: Bidders who act on scarcity alerts win {tenderScarcity.successRate}% more tenders!
          </Typography>
          <Typography variant="caption">
            Limited opportunities = less competition = higher success rates
          </Typography>
        </Alert>
      </CardContent>
    </Card>
  );
};

export default TenderScarcitySystem;
