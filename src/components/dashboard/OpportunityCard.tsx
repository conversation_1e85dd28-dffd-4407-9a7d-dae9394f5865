import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardContent,
  Box,
  Typo<PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  LinearProgress,
  IconButton,
  Tooltip,
  Avatar,
  Divider,
  useTheme,
  alpha,
  Collapse
} from '@mui/material';
import {
  FlashOn as FlashOnIcon,
  AttachMoney as MoneyIcon,
  Timer as TimerIcon,
  TrendingUp as TrendingUpIcon,
  Star as StarIcon,
  Psychology as BrainIcon,
  Visibility as EyeIcon,
  Speed as SpeedIcon,
  GpsFixed as TargetIcon,
  EmojiEvents as TrophyIcon,
  Warning as WarningIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Launch as LaunchIcon,
  Bookmark as BookmarkIcon,
  Share as ShareIcon
} from '@mui/icons-material';
import { useNeuroMarketing } from '../../hooks/useNeuroMarketing';
import usePsychologicalSystems from '../../hooks/usePsychologicalSystems';

interface OpportunityData {
  id: string;
  title: string;
  client: string;
  location: string;
  value: number;
  timeLeft: string;
  timeLeftHours: number;
  successRate: number;
  competitorCount: number;
  urgency: 'low' | 'medium' | 'high' | 'critical';
  category: string;
  requirements: string[];
  psychologicalMatch: number;
  profitPotential: 'low' | 'medium' | 'high' | 'excellent';
  difficultyLevel: 'easy' | 'moderate' | 'challenging' | 'expert';
  exclusiveAccess?: boolean;
  trending?: boolean;
  hotDeal?: boolean;
  achievementUnlock?: string;
  rankingImpact?: number;
}

interface OpportunityCardProps {
  opportunity: OpportunityData;
  onBidNow?: (opportunityId: string) => void;
  onSave?: (opportunityId: string) => void;
  onAnalyze?: (opportunityId: string) => void;
  compact?: boolean;
}

const OpportunityCard: React.FC<OpportunityCardProps> = ({
  opportunity,
  onBidNow,
  onSave,
  onAnalyze,
  compact = false
}) => {
  const theme = useTheme();
  const { psychologicalState, isStressed, needsSimplification } = useNeuroMarketing();
  const { profile: psychProfile } = usePsychologicalSystems();
  
  const [expanded, setExpanded] = useState(false);
  const [saved, setSaved] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState(opportunity.timeLeft);

  const userArchetype = psychProfile?.archetype || 'achiever';

  // Update countdown timer
  useEffect(() => {
    const interval = setInterval(() => {
      // Mock countdown update - in real app, calculate from actual deadline
      const hours = opportunity.timeLeftHours;
      if (hours > 0) {
        const newHours = Math.max(0, hours - 0.1); // Simulate time passing
        const days = Math.floor(newHours / 24);
        const remainingHours = Math.floor(newHours % 24);
        const minutes = Math.floor((newHours % 1) * 60);
        
        if (days > 0) {
          setTimeRemaining(`${days}d ${remainingHours}h`);
        } else if (remainingHours > 0) {
          setTimeRemaining(`${remainingHours}h ${minutes}m`);
        } else {
          setTimeRemaining(`${minutes}m`);
        }
      }
    }, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [opportunity.timeLeftHours]);

  // Get urgency color
  const getUrgencyColor = () => {
    switch (opportunity.urgency) {
      case 'critical': return theme.palette.error.main;
      case 'high': return theme.palette.warning.main;
      case 'medium': return theme.palette.info.main;
      case 'low': return theme.palette.success.main;
      default: return theme.palette.grey[500];
    }
  };

  // Get profit potential color
  const getProfitColor = () => {
    switch (opportunity.profitPotential) {
      case 'excellent': return theme.palette.success.main;
      case 'high': return theme.palette.info.main;
      case 'medium': return theme.palette.warning.main;
      case 'low': return theme.palette.grey[500];
      default: return theme.palette.grey[500];
    }
  };

  // Get archetype-specific messaging
  const getArchetypeMessage = () => {
    switch (userArchetype) {
      case 'achiever':
        return {
          icon: '👑',
          message: opportunity.exclusiveAccess 
            ? 'EXCLUSIVE: Perfect for your reputation goals'
            : `${opportunity.successRate}% success rate - dominate this!`,
          color: theme.palette.primary.main
        };
      case 'hunter':
        return {
          icon: '🎯',
          message: opportunity.competitorCount < 5
            ? 'WEAK COMPETITION: Strike fast!'
            : 'HUNT MODE: Beat the competition',
          color: theme.palette.error.main
        };
      case 'analyst':
        return {
          icon: '📊',
          message: `Data shows ${opportunity.successRate}% win probability`,
          color: theme.palette.info.main
        };
      case 'relationship_builder':
        return {
          icon: '❤️',
          message: 'Great client relationship opportunity',
          color: theme.palette.secondary.main
        };
      default:
        return {
          icon: '⭐',
          message: `${opportunity.successRate}% match for your profile`,
          color: theme.palette.primary.main
        };
    }
  };

  const archetypeConfig = getArchetypeMessage();
  const urgencyColor = getUrgencyColor();
  const profitColor = getProfitColor();

  // Format currency
  const formatCurrency = (amount: number) => {
    if (amount >= 1000000) {
      return `R${(amount / 1000000).toFixed(1)}M`;
    }
    return `R${(amount / 1000).toFixed(0)}K`;
  };

  return (
    <Card
      sx={{
        position: 'relative',
        overflow: 'visible',
        transition: 'all 0.3s ease-in-out',
        border: `2px solid ${alpha(urgencyColor, 0.3)}`,
        background: `linear-gradient(135deg, ${alpha(urgencyColor, 0.05)} 0%, transparent 100%)`,
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: `0 8px 32px ${alpha(urgencyColor, 0.3)}`,
          border: `2px solid ${alpha(urgencyColor, 0.6)}`
        }
      }}
    >
      {/* Urgency Indicator */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: 4,
          backgroundColor: urgencyColor,
          animation: opportunity.urgency === 'critical' ? 'pulse 1s infinite' : 'none',
          '@keyframes pulse': {
            '0%': { opacity: 1 },
            '50%': { opacity: 0.5 },
            '100%': { opacity: 1 }
          }
        }}
      />

      {/* Special Badges */}
      {(opportunity.exclusiveAccess || opportunity.trending || opportunity.hotDeal) && (
        <Box sx={{ position: 'absolute', top: 12, right: 12, display: 'flex', gap: 1 }}>
          {opportunity.exclusiveAccess && (
            <Chip
              label="💎 EXCLUSIVE"
              size="small"
              sx={{
                backgroundColor: theme.palette.secondary.main,
                color: 'white',
                fontWeight: 'bold',
                fontSize: '0.7rem'
              }}
            />
          )}
          {opportunity.trending && (
            <Chip
              label="🔥 TRENDING"
              size="small"
              sx={{
                backgroundColor: theme.palette.warning.main,
                color: 'white',
                fontWeight: 'bold',
                fontSize: '0.7rem'
              }}
            />
          )}
          {opportunity.hotDeal && (
            <Chip
              label="⚡ HOT"
              size="small"
              sx={{
                backgroundColor: theme.palette.error.main,
                color: 'white',
                fontWeight: 'bold',
                fontSize: '0.7rem'
              }}
            />
          )}
        </Box>
      )}

      <CardContent sx={{ p: 3, pb: 2 }}>
        {/* Header */}
        <Box sx={{ mb: 2 }}>
          <Typography
            variant="h6"
            sx={{
              fontWeight: 700,
              color: 'text.primary',
              mb: 0.5,
              pr: 8 // Space for badges
            }}
          >
            {opportunity.title}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {opportunity.client} • {opportunity.location}
          </Typography>
        </Box>

        {/* Key Metrics Row */}
        <Box sx={{ display: 'flex', gap: 2, mb: 2, flexWrap: 'wrap' }}>
          {/* Value */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <MoneyIcon sx={{ color: profitColor, fontSize: 20 }} />
            <Typography variant="h6" fontWeight="bold" color={profitColor}>
              {formatCurrency(opportunity.value)}
            </Typography>
          </Box>

          {/* Success Rate */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <TargetIcon sx={{ color: theme.palette.success.main, fontSize: 20 }} />
            <Typography variant="body1" fontWeight="bold" color="success.main">
              {opportunity.successRate}%
            </Typography>
            <Typography variant="caption" color="text.secondary">
              match
            </Typography>
          </Box>

          {/* Time Left */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <TimerIcon sx={{ color: urgencyColor, fontSize: 20 }} />
            <Typography variant="body1" fontWeight="bold" color={urgencyColor}>
              {timeRemaining}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              left
            </Typography>
          </Box>

          {/* Competitors */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <EyeIcon sx={{ color: 'text.secondary', fontSize: 20 }} />
            <Typography variant="body1" fontWeight="bold">
              {opportunity.competitorCount}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              bidders
            </Typography>
          </Box>
        </Box>

        {/* Archetype-Specific Insight */}
        <Box
          sx={{
            p: 1.5,
            borderRadius: 1,
            backgroundColor: alpha(archetypeConfig.color, 0.1),
            border: `1px solid ${alpha(archetypeConfig.color, 0.3)}`,
            mb: 2
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography sx={{ fontSize: '1.2rem' }}>
              {archetypeConfig.icon}
            </Typography>
            <Typography
              variant="body2"
              fontWeight="bold"
              sx={{ color: archetypeConfig.color }}
            >
              {archetypeConfig.message}
            </Typography>
          </Box>
        </Box>

        {/* Achievement Unlock */}
        {opportunity.achievementUnlock && (
          <Box
            sx={{
              p: 1,
              borderRadius: 1,
              backgroundColor: alpha(theme.palette.warning.main, 0.1),
              border: `1px solid ${alpha(theme.palette.warning.main, 0.3)}`,
              mb: 2
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <TrophyIcon sx={{ color: theme.palette.warning.main, fontSize: 20 }} />
              <Typography variant="body2" fontWeight="bold">
                Win this to unlock "{opportunity.achievementUnlock}" badge!
              </Typography>
            </Box>
          </Box>
        )}

        {/* Action Buttons */}
        <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
          <Button
            variant="contained"
            startIcon={<FlashOnIcon />}
            onClick={() => onBidNow?.(opportunity.id)}
            sx={{
              flex: 1,
              backgroundColor: urgencyColor,
              '&:hover': {
                backgroundColor: alpha(urgencyColor, 0.8)
              },
              fontWeight: 'bold',
              textTransform: userArchetype === 'hunter' ? 'uppercase' : 'none'
            }}
          >
            {userArchetype === 'hunter' ? 'STRIKE NOW' : 
             userArchetype === 'achiever' ? 'DOMINATE' : 'BID NOW'}
          </Button>

          <IconButton
            onClick={() => {
              setSaved(!saved);
              onSave?.(opportunity.id);
            }}
            sx={{
              color: saved ? theme.palette.warning.main : 'text.secondary',
              '&:hover': {
                backgroundColor: alpha(theme.palette.warning.main, 0.1)
              }
            }}
          >
            <BookmarkIcon />
          </IconButton>

          <IconButton
            onClick={() => onAnalyze?.(opportunity.id)}
            sx={{
              color: 'text.secondary',
              '&:hover': {
                backgroundColor: alpha(theme.palette.info.main, 0.1)
              }
            }}
          >
            <BrainIcon />
          </IconButton>

          <IconButton
            onClick={() => setExpanded(!expanded)}
            sx={{
              color: 'text.secondary',
              '&:hover': {
                backgroundColor: alpha(theme.palette.primary.main, 0.1)
              }
            }}
          >
            {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        </Box>

        {/* Expandable Details */}
        <Collapse in={expanded}>
          <Divider sx={{ my: 2 }} />
          <Box>
            <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
              Requirements:
            </Typography>
            <Box sx={{ mb: 2 }}>
              {opportunity.requirements.map((req, index) => (
                <Chip
                  key={index}
                  label={req}
                  size="small"
                  variant="outlined"
                  sx={{ mr: 1, mb: 1 }}
                />
              ))}
            </Box>

            <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
              <Box>
                <Typography variant="caption" color="text.secondary">
                  Difficulty
                </Typography>
                <Typography variant="body2" fontWeight="bold">
                  {opportunity.difficultyLevel.charAt(0).toUpperCase() + opportunity.difficultyLevel.slice(1)}
                </Typography>
              </Box>
              <Box>
                <Typography variant="caption" color="text.secondary">
                  Profit Potential
                </Typography>
                <Typography variant="body2" fontWeight="bold" color={profitColor}>
                  {opportunity.profitPotential.charAt(0).toUpperCase() + opportunity.profitPotential.slice(1)}
                </Typography>
              </Box>
              {opportunity.rankingImpact && (
                <Box>
                  <Typography variant="caption" color="text.secondary">
                    Ranking Impact
                  </Typography>
                  <Typography variant="body2" fontWeight="bold" color="success.main">
                    +{opportunity.rankingImpact} spots
                  </Typography>
                </Box>
              )}
            </Box>

            <Button
              variant="outlined"
              startIcon={<LaunchIcon />}
              fullWidth
              onClick={() => onAnalyze?.(opportunity.id)}
            >
              View Full Details
            </Button>
          </Box>
        </Collapse>
      </CardContent>
    </Card>
  );
};

export default OpportunityCard;
