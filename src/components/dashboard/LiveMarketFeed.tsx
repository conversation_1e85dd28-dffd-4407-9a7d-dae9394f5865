import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON>,
  Typography,
  Chip,
  IconButton,
  Tooltip,
  useTheme,
  alpha
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  FlashOn as FlashOnIcon,
  EmojiEvents as TrophyIcon,
  Warning as WarningIcon,
  Star as StarIcon,
  Timer as TimerIcon,
  AttachMoney as MoneyIcon,
  Psychology as BrainIcon,
  Visibility as EyeIcon,
  PlayArrow as PlayIcon,
  Pause as PauseIcon
} from '@mui/icons-material';
import { useNeuroMarketing } from '../../hooks/useNeuroMarketing';
import usePsychologicalSystems from '../../hooks/usePsychologicalSystems';

interface MarketFeedItem {
  id: string;
  type: 'win' | 'new_tender' | 'deadline' | 'competitor' | 'achievement' | 'market_move' | 'insight' | 'rfq_trigger' | 'mix_alert';
  title: string;
  subtitle?: string;
  value?: string;
  urgency: 'low' | 'medium' | 'high' | 'critical';
  timestamp: Date;
  icon: string;
  color: string;
  psychologicalTrigger: 'greed' | 'fomo' | 'competition' | 'achievement' | 'social_proof' | 'scarcity' | 'rfq_addiction' | 'mix_optimization';
  archetypeRelevance?: {
    achiever?: number;
    hunter?: number;
    analyst?: number;
    relationship_builder?: number;
  };
  actionable?: boolean;
  tenderId?: string;
  rfqCategory?: string;
  mixDeviation?: number;
}

interface LiveMarketFeedProps {
  height?: number;
  speed?: 'slow' | 'medium' | 'fast';
  enablePsychologicalFiltering?: boolean;
  maxItems?: number;
}

const LiveMarketFeed: React.FC<LiveMarketFeedProps> = ({
  height = 60,
  speed = 'medium',
  enablePsychologicalFiltering = true,
  maxItems = 50
}) => {
  const theme = useTheme();
  const { psychologicalState, psychologicalProfile, isStressed, needsSimplification } = useNeuroMarketing();
  const { profile: psychProfile } = usePsychologicalSystems();
  
  const [feedItems, setFeedItems] = useState<MarketFeedItem[]>([]);
  const [isPlaying, setIsPlaying] = useState(true);
  const [currentIndex, setCurrentIndex] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Speed configuration
  const speedConfig = {
    slow: 8000,
    medium: 5000,
    fast: 3000
  };

  // Get user's archetype for personalization
  const userArchetype = psychProfile?.archetype || 'achiever';

  useEffect(() => {
    generateMockFeedItems();
    startFeed();
    
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (isPlaying) {
      startFeed();
    } else {
      stopFeed();
    }
  }, [isPlaying, speed]);

  const generateMockFeedItems = () => {
    const mockItems: MarketFeedItem[] = [
      // RFQ Force-Feeding Triggers
      {
        id: 'rfq-1',
        type: 'rfq_trigger',
        title: '🔥 URGENT: 5 suppliers bidding on similar RFQ - CREATE YOURS NOW!',
        subtitle: 'Beat them to the market',
        urgency: 'critical',
        timestamp: new Date(),
        icon: '🚀',
        color: '#4CAF50',
        psychologicalTrigger: 'rfq_addiction',
        archetypeRelevance: { achiever: 0.95, hunter: 0.9, analyst: 0.7, relationship_builder: 0.6 },
        actionable: true,
        rfqCategory: 'Office Supplies'
      },
      {
        id: 'rfq-2',
        type: 'rfq_trigger',
        title: '💰 PROFIT ALERT: RFQs averaging 23% savings this week',
        subtitle: 'Your success rate: 89%',
        urgency: 'high',
        timestamp: new Date(),
        icon: '💰',
        color: '#2196F3',
        psychologicalTrigger: 'greed',
        archetypeRelevance: { achiever: 0.9, hunter: 0.8, analyst: 0.85, relationship_builder: 0.5 },
        actionable: true
      },
      {
        id: 'mix-1',
        type: 'mix_alert',
        title: '⚠️ MIX ALERT: You\'re at 45% RFQ - need 5 more to hit 60% target!',
        subtitle: 'Get back on track',
        urgency: 'medium',
        timestamp: new Date(),
        icon: '⚖️',
        color: '#FF9800',
        psychologicalTrigger: 'mix_optimization',
        archetypeRelevance: { achiever: 0.9, hunter: 0.7, analyst: 0.8, relationship_builder: 0.6 },
        actionable: true,
        mixDeviation: 15
      },
      // Wins and Social Proof
      {
        id: '1',
        type: 'win',
        title: 'MegaCorp just SNATCHED R12.8M infrastructure deal',
        subtitle: 'Could have been yours!',
        value: 'R12.8M',
        urgency: 'high',
        timestamp: new Date(),
        icon: '🏆',
        color: '#f44336',
        psychologicalTrigger: 'fomo',
        archetypeRelevance: { achiever: 0.9, hunter: 0.8, analyst: 0.6, relationship_builder: 0.5 }
      },
      {
        id: '2',
        type: 'new_tender',
        title: 'R8.4M road tender LIVE NOW',
        subtitle: '73% success rate for your profile',
        value: 'R8.4M',
        urgency: 'critical',
        timestamp: new Date(),
        icon: '🔥',
        color: '#ff9800',
        psychologicalTrigger: 'greed',
        archetypeRelevance: { achiever: 0.85, hunter: 0.95, analyst: 0.7, relationship_builder: 0.6 },
        actionable: true,
        tenderId: 'tender_001'
      },
      {
        id: '3',
        type: 'deadline',
        title: 'URGENT: Only 4 hours left on R5.2M security contract',
        urgency: 'critical',
        timestamp: new Date(),
        icon: '⏰',
        color: '#f44336',
        psychologicalTrigger: 'fomo',
        archetypeRelevance: { hunter: 0.9, achiever: 0.8, analyst: 0.5, relationship_builder: 0.4 }
      },
      {
        id: '4',
        type: 'competitor',
        title: 'TOP BIDDER ALERT: You\'re #1 trending in construction',
        urgency: 'medium',
        timestamp: new Date(),
        icon: '👑',
        color: '#9c27b0',
        psychologicalTrigger: 'achievement',
        archetypeRelevance: { achiever: 1.0, hunter: 0.7, analyst: 0.6, relationship_builder: 0.8 }
      },
      {
        id: '5',
        type: 'new_tender',
        title: 'EXCLUSIVE: Grade 7+ only tender worth R15.6M',
        subtitle: 'You qualify!',
        value: 'R15.6M',
        urgency: 'high',
        timestamp: new Date(),
        icon: '💎',
        color: '#673ab7',
        psychologicalTrigger: 'scarcity',
        archetypeRelevance: { achiever: 0.9, analyst: 0.8, hunter: 0.7, relationship_builder: 0.6 }
      },
      {
        id: '6',
        type: 'market_move',
        title: 'MARKET SURGE: Infrastructure tenders up 34% today',
        urgency: 'medium',
        timestamp: new Date(),
        icon: '📈',
        color: '#4caf50',
        psychologicalTrigger: 'greed',
        archetypeRelevance: { analyst: 0.9, achiever: 0.7, hunter: 0.6, relationship_builder: 0.5 }
      },
      {
        id: '7',
        type: 'competitor',
        title: 'BuildCorp just passed you on leaderboard',
        subtitle: 'Fight back!',
        urgency: 'high',
        timestamp: new Date(),
        icon: '⚔️',
        color: '#f44336',
        psychologicalTrigger: 'competition',
        archetypeRelevance: { achiever: 0.95, hunter: 0.9, analyst: 0.4, relationship_builder: 0.3 }
      },
      {
        id: '8',
        type: 'insight',
        title: 'AI INSIGHT: Your bid timing is 34% better on Tuesday mornings',
        urgency: 'low',
        timestamp: new Date(),
        icon: '🧠',
        color: '#2196f3',
        psychologicalTrigger: 'social_proof',
        archetypeRelevance: { analyst: 0.9, achiever: 0.7, hunter: 0.5, relationship_builder: 0.6 }
      },
      {
        id: '9',
        type: 'achievement',
        title: 'Sarah M. just earned "Speed Demon" badge',
        subtitle: 'Can you beat her time?',
        urgency: 'low',
        timestamp: new Date(),
        icon: '🎉',
        color: '#ff9800',
        psychologicalTrigger: 'competition',
        archetypeRelevance: { achiever: 0.8, hunter: 0.7, analyst: 0.4, relationship_builder: 0.9 }
      },
      // BEE SCARCITY FOMO TRIGGERS
      {
        id: 'bee-1',
        type: 'rfq_trigger',
        title: '🚨 BEE CRISIS: Only 3 bees left in Gauteng - BOOK NOW!',
        subtitle: 'Prices up 60% due to shortage',
        urgency: 'critical',
        timestamp: new Date(),
        icon: '🐝',
        color: '#f44336',
        psychologicalTrigger: 'scarcity',
        archetypeRelevance: { hunter: 0.95, achiever: 0.9, analyst: 0.7, relationship_builder: 0.6 },
        actionable: true
      },
      {
        id: 'bee-2',
        type: 'rfq_trigger',
        title: '💰 BEE SURGE PRICING: +40% but 34% higher RFQ success!',
        subtitle: 'Fast delivery = more wins',
        urgency: 'high',
        timestamp: new Date(),
        icon: '📈',
        color: '#ff9800',
        psychologicalTrigger: 'greed',
        archetypeRelevance: { achiever: 0.9, analyst: 0.8, hunter: 0.7, relationship_builder: 0.5 },
        actionable: true
      },
      {
        id: 'bee-3',
        type: 'rfq_trigger',
        title: '⏰ LAST BEE SLOT: 2-hour delivery available until 16:00',
        subtitle: 'Next slot tomorrow morning',
        urgency: 'critical',
        timestamp: new Date(),
        icon: '⏰',
        color: '#f44336',
        psychologicalTrigger: 'fomo',
        archetypeRelevance: { hunter: 0.95, achiever: 0.85, analyst: 0.6, relationship_builder: 0.5 },
        actionable: true
      },
      // Government RFQ Force-Feeding Triggers
      {
        id: 'gov-rfq-1',
        type: 'rfq_trigger',
        title: '🏛️ GOVERNMENT RFQ: 88% success rate - Municipal Office Supplies',
        subtitle: 'Higher success than tenders',
        urgency: 'high',
        timestamp: new Date(),
        icon: '🏛️',
        color: '#4CAF50',
        psychologicalTrigger: 'government_rfq_opportunity',
        archetypeRelevance: { achiever: 0.9, analyst: 0.85, hunter: 0.8, relationship_builder: 0.7 },
        actionable: true,
        rfqCategory: 'Office Supplies'
      },
      {
        id: 'gov-rfq-2',
        type: 'rfq_trigger',
        title: '🎯 PORTFOLIO FIX: Bid on Government RFQ to reach 60% RFQ ratio',
        subtitle: 'Balance optimization opportunity',
        urgency: 'medium',
        timestamp: new Date(),
        icon: '🎯',
        color: '#FF9800',
        psychologicalTrigger: 'balance_optimization',
        archetypeRelevance: { analyst: 0.95, achiever: 0.8, hunter: 0.7, relationship_builder: 0.6 },
        actionable: true
      },
      // More RFQ Force-Feeding Triggers
      {
        id: 'rfq-3',
        type: 'rfq_trigger',
        title: '⚡ QUICK WIN: Create RFQ in 90 seconds, get quotes in 2 hours',
        subtitle: 'Instant gratification mode',
        urgency: 'medium',
        timestamp: new Date(),
        icon: '⚡',
        color: '#4CAF50',
        psychologicalTrigger: 'rfq_addiction',
        archetypeRelevance: { hunter: 0.9, achiever: 0.8, analyst: 0.6, relationship_builder: 0.5 },
        actionable: true
      },
      {
        id: 'rfq-4',
        type: 'rfq_trigger',
        title: '🎯 MARKET OPPORTUNITY: Steel prices dropping - RFQ NOW!',
        subtitle: 'Perfect timing for bulk orders',
        urgency: 'high',
        timestamp: new Date(),
        icon: '🎯',
        color: '#2196F3',
        psychologicalTrigger: 'greed',
        archetypeRelevance: { analyst: 0.9, achiever: 0.8, hunter: 0.7, relationship_builder: 0.5 },
        actionable: true,
        rfqCategory: 'Construction Materials'
      },
      {
        id: 'rfq-5',
        type: 'rfq_trigger',
        title: '👑 POWER MOVE: 12 suppliers waiting for your next RFQ',
        subtitle: 'They\'re ready to compete',
        urgency: 'medium',
        timestamp: new Date(),
        icon: '👑',
        color: '#9C27B0',
        psychologicalTrigger: 'social_proof',
        archetypeRelevance: { achiever: 0.9, hunter: 0.8, analyst: 0.6, relationship_builder: 0.7 },
        actionable: true
      },
      // TENDER SCARCITY FOMO TRIGGERS
      {
        id: 'tender-scarcity-1',
        type: 'new_tender',
        title: '🚨 TENDER CRISIS: Only 23 tenders left in construction!',
        subtitle: '847 bidders competing for limited spots',
        urgency: 'critical',
        timestamp: new Date(),
        icon: '🎯',
        color: '#f44336',
        psychologicalTrigger: 'scarcity',
        archetypeRelevance: { hunter: 0.95, achiever: 0.9, analyst: 0.8, relationship_builder: 0.6 },
        actionable: true,
        tenderId: 'tender_scarcity_001'
      },
      {
        id: 'tender-scarcity-2',
        type: 'deadline',
        title: '⏰ LAST 4 HOURS: R15.6M infrastructure tender closing!',
        subtitle: 'Only 2 bidder slots remaining',
        urgency: 'critical',
        timestamp: new Date(),
        icon: '⏰',
        color: '#f44336',
        psychologicalTrigger: 'fomo',
        archetypeRelevance: { hunter: 0.95, achiever: 0.9, analyst: 0.7, relationship_builder: 0.5 },
        actionable: true,
        tenderId: 'tender_urgent_001'
      },
      {
        id: 'tender-scarcity-3',
        type: 'new_tender',
        title: '💎 EXCLUSIVE: Grade 7+ ONLY tender - R22.4M highway!',
        subtitle: 'You qualify! Only 3/5 bidders registered',
        urgency: 'high',
        timestamp: new Date(),
        icon: '💎',
        color: '#9c27b0',
        psychologicalTrigger: 'scarcity',
        archetypeRelevance: { achiever: 0.95, analyst: 0.9, hunter: 0.8, relationship_builder: 0.7 },
        actionable: true,
        tenderId: 'tender_exclusive_001'
      },
      {
        id: 'tender-scarcity-4',
        type: 'competitor',
        title: '⚔️ BIDDER SURGE: 14/15 slots taken on R8.5M IT tender!',
        subtitle: 'LAST CHANCE to register',
        urgency: 'critical',
        timestamp: new Date(),
        icon: '⚔️',
        color: '#ff5722',
        psychologicalTrigger: 'competition',
        archetypeRelevance: { hunter: 0.95, achiever: 0.9, analyst: 0.6, relationship_builder: 0.4 },
        actionable: true,
        tenderId: 'tender_competition_001'
      },
      // COMPLIANCE SCARCITY TERROR TRIGGERS
      {
        id: 'compliance-1',
        type: 'rfq_trigger',
        title: '🚨 COMPLIANCE CRISIS: Missing PMP blocking R45M in tenders!',
        subtitle: 'Only 2 SkillSync slots left this quarter',
        urgency: 'critical',
        timestamp: new Date(),
        icon: '🚨',
        color: '#f44336',
        psychologicalTrigger: 'scarcity',
        archetypeRelevance: { achiever: 0.95, analyst: 0.9, hunter: 0.8, relationship_builder: 0.7 },
        actionable: true
      },
      {
        id: 'compliance-2',
        type: 'rfq_trigger',
        title: '⚠️ TOOL SHORTAGE: AutoCAD licenses running out - 8 tenders at risk!',
        subtitle: 'ToolSync has 3 licenses left',
        urgency: 'high',
        timestamp: new Date(),
        icon: '🛠️',
        color: '#ff9800',
        psychologicalTrigger: 'fomo',
        archetypeRelevance: { analyst: 0.9, achiever: 0.8, hunter: 0.7, relationship_builder: 0.6 },
        actionable: true
      },
      {
        id: 'compliance-3',
        type: 'rfq_trigger',
        title: '💀 DISQUALIFICATION ALERT: ISO 9001 required for 15 tenders!',
        subtitle: 'Last SkillSync certification slot available',
        urgency: 'critical',
        timestamp: new Date(),
        icon: '💀',
        color: '#f44336',
        psychologicalTrigger: 'scarcity',
        archetypeRelevance: { achiever: 0.95, analyst: 0.9, hunter: 0.8, relationship_builder: 0.7 },
        actionable: true
      },
      {
        id: 'compliance-4',
        type: 'rfq_trigger',
        title: '🎯 COMPLIANCE BOOST: Complete bidders win 67% more tenders!',
        subtitle: 'SkillSync + ToolSync = 3.2X success rate',
        urgency: 'medium',
        timestamp: new Date(),
        icon: '🎯',
        color: '#4CAF50',
        psychologicalTrigger: 'greed',
        archetypeRelevance: { achiever: 0.9, analyst: 0.85, hunter: 0.7, relationship_builder: 0.6 },
        actionable: true
      },
      // BEE MONETIZATION TRIGGERS
      {
        id: 'bee-4',
        type: 'rfq_trigger',
        title: '🐝 BEE PREMIUM: Skip the queue with Bee Air - 2 hour delivery!',
        subtitle: 'Only R525 (was R350)',
        urgency: 'high',
        timestamp: new Date(),
        icon: '✈️',
        color: '#2196F3',
        psychologicalTrigger: 'scarcity',
        archetypeRelevance: { achiever: 0.9, hunter: 0.85, analyst: 0.7, relationship_builder: 0.6 },
        actionable: true
      },
      // More BEE MONETIZATION TRIGGERS
      {
        id: 'bee-4',
        type: 'rfq_trigger',
        title: '🐝 BEE PREMIUM: Skip the queue with Bee Air - 2 hour delivery!',
        subtitle: 'Only R525 (was R350)',
        urgency: 'high',
        timestamp: new Date(),
        icon: '✈️',
        color: '#2196F3',
        psychologicalTrigger: 'scarcity',
        archetypeRelevance: { achiever: 0.9, hunter: 0.85, analyst: 0.7, relationship_builder: 0.6 },
        actionable: true
      },
      {
        id: 'bee-5',
        type: 'rfq_trigger',
        title: '📦 BEE ADVANTAGE: Fast delivery = 34% more RFQ wins!',
        subtitle: 'Suppliers prefer quick bidders',
        urgency: 'medium',
        timestamp: new Date(),
        icon: '📦',
        color: '#4CAF50',
        psychologicalTrigger: 'greed',
        archetypeRelevance: { analyst: 0.9, achiever: 0.8, hunter: 0.7, relationship_builder: 0.6 },
        actionable: true
      },
      {
        id: 'bee-6',
        type: 'rfq_trigger',
        title: '🚨 BEE ALERT: High demand in your area - book before prices rise!',
        subtitle: 'Current surge: +25%',
        urgency: 'high',
        timestamp: new Date(),
        icon: '🚨',
        color: '#ff5722',
        psychologicalTrigger: 'fomo',
        archetypeRelevance: { hunter: 0.9, achiever: 0.8, analyst: 0.6, relationship_builder: 0.5 },
        actionable: true
      },
      {
        id: '10',
        type: 'new_tender',
        title: 'BREAKING: R22.4M government infrastructure just posted',
        subtitle: 'Perfect for your expertise',
        value: 'R22.4M',
        urgency: 'critical',
        timestamp: new Date(),
        icon: '🚨',
        color: '#f44336',
        psychologicalTrigger: 'greed',
        archetypeRelevance: { achiever: 0.9, hunter: 0.85, analyst: 0.8, relationship_builder: 0.7 },
        actionable: true,
        tenderId: 'tender_002'
      }
    ];

    // Filter items based on psychological profile if enabled
    let filteredItems = mockItems;
    if (enablePsychologicalFiltering && userArchetype) {
      filteredItems = mockItems
        .map(item => ({
          ...item,
          relevanceScore: item.archetypeRelevance?.[userArchetype as keyof typeof item.archetypeRelevance] || 0.5
        }))
        .sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0))
        .slice(0, maxItems);
    }

    // Adapt for stress levels
    if (isStressed && needsSimplification) {
      filteredItems = filteredItems.filter(item => 
        item.psychologicalTrigger !== 'competition' && 
        item.urgency !== 'critical'
      );
    }

    setFeedItems(filteredItems);
  };

  const startFeed = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    
    intervalRef.current = setInterval(() => {
      setCurrentIndex(prev => (prev + 1) % feedItems.length);
    }, speedConfig[speed]);
  };

  const stopFeed = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'critical': return '#f44336';
      case 'high': return '#ff9800';
      case 'medium': return '#2196f3';
      case 'low': return '#4caf50';
      default: return '#757575';
    }
  };

  const getPsychologicalTriggerIcon = (trigger: string) => {
    switch (trigger) {
      case 'greed': return <MoneyIcon />;
      case 'fomo': return <TimerIcon />;
      case 'competition': return <TrophyIcon />;
      case 'achievement': return <StarIcon />;
      case 'social_proof': return <EyeIcon />;
      case 'scarcity': return <WarningIcon />;
      case 'rfq_addiction': return <FlashOnIcon />;
      case 'mix_optimization': return <TrendingUpIcon />;
      default: return <BrainIcon />;
    }
  };

  const handleItemClick = () => {
    if (!currentItem.actionable) return;

    switch (currentItem.type) {
      case 'rfq_trigger':
        // Navigate to unified opportunities or RFQ creation
        if (currentItem.rfqCategory) {
          window.location.href = `/opportunities?category=${encodeURIComponent(currentItem.rfqCategory)}&type=government_rfq`;
        } else {
          window.location.href = '/opportunities?filter=rfq_opportunities';
        }
        break;
      case 'mix_alert':
        // Navigate to RFQ creation to fix mix
        window.location.href = '/rfq/create?source=mix_alert';
        break;
      case 'new_tender':
        if (currentItem.tenderId) {
          console.log('Navigate to tender:', currentItem.tenderId);
          // window.location.href = `/tenders/${currentItem.tenderId}`;
        }
        break;
      default:
        console.log('Item clicked:', currentItem.type);
    }
  };

  const currentItem = feedItems[currentIndex];

  if (!currentItem) {
    return null;
  }

  return (
    <Box
      ref={containerRef}
      sx={{
        height: height,
        backgroundColor: alpha(theme.palette.background.paper, 0.95),
        border: `1px solid ${alpha(currentItem.color, 0.3)}`,
        borderRadius: 1,
        overflow: 'hidden',
        position: 'relative',
        display: 'flex',
        alignItems: 'center',
        px: 2,
        background: `linear-gradient(90deg, ${alpha(currentItem.color, 0.1)} 0%, transparent 100%)`,
        transition: 'all 0.5s ease-in-out',
        cursor: currentItem.actionable ? 'pointer' : 'default',
        '&:hover': currentItem.actionable ? {
          backgroundColor: alpha(currentItem.color, 0.05),
          transform: 'scale(1.01)',
          boxShadow: `0 4px 20px ${alpha(currentItem.color, 0.3)}`
        } : {}
      }}
      onClick={handleItemClick}
    >
      {/* Urgency Indicator */}
      <Box
        sx={{
          position: 'absolute',
          left: 0,
          top: 0,
          bottom: 0,
          width: 4,
          backgroundColor: getUrgencyColor(currentItem.urgency),
          animation: currentItem.urgency === 'critical' ? 'pulse 1s infinite' : 'none',
          '@keyframes pulse': {
            '0%': { opacity: 1 },
            '50%': { opacity: 0.5 },
            '100%': { opacity: 1 }
          }
        }}
      />

      {/* Icon */}
      <Box sx={{ mr: 2, fontSize: '1.5rem', display: 'flex', alignItems: 'center' }}>
        {currentItem.icon}
      </Box>

      {/* Content */}
      <Box sx={{ flex: 1, minWidth: 0 }}>
        <Typography
          variant="body1"
          sx={{
            fontWeight: currentItem.urgency === 'critical' ? 700 : 600,
            color: currentItem.urgency === 'critical' ? currentItem.color : 'text.primary',
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            fontSize: needsSimplification ? '0.9rem' : '1rem'
          }}
        >
          {currentItem.title}
        </Typography>
        {currentItem.subtitle && (
          <Typography
            variant="caption"
            sx={{
              color: 'text.secondary',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis'
            }}
          >
            {currentItem.subtitle}
          </Typography>
        )}
      </Box>

      {/* Value */}
      {currentItem.value && (
        <Chip
          label={currentItem.value}
          size="small"
          sx={{
            backgroundColor: alpha(currentItem.color, 0.1),
            color: currentItem.color,
            fontWeight: 'bold',
            mr: 1
          }}
        />
      )}

      {/* Psychological Trigger Indicator */}
      <Tooltip title={`Psychological trigger: ${currentItem.psychologicalTrigger}`}>
        <IconButton size="small" sx={{ color: alpha(currentItem.color, 0.7) }}>
          {getPsychologicalTriggerIcon(currentItem.psychologicalTrigger)}
        </IconButton>
      </Tooltip>

      {/* Play/Pause Control */}
      <Tooltip title={isPlaying ? 'Pause feed' : 'Resume feed'}>
        <IconButton
          size="small"
          onClick={(e) => {
            e.stopPropagation();
            setIsPlaying(!isPlaying);
          }}
          sx={{ ml: 1 }}
        >
          {isPlaying ? <PauseIcon /> : <PlayIcon />}
        </IconButton>
      </Tooltip>

      {/* Progress Indicator */}
      <Box
        sx={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          height: 2,
          backgroundColor: alpha(currentItem.color, 0.3),
          width: `${((currentIndex + 1) / feedItems.length) * 100}%`,
          transition: 'width 0.3s ease'
        }}
      />
    </Box>
  );
};

export default LiveMarketFeed;
