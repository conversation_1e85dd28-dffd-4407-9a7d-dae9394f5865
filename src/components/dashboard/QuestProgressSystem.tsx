import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  CardHeader,
  <PERSON><PERSON><PERSON>,
  LinearProgress,
  Chip,
  Stack,
  Avatar,
  IconButton,
  Tooltip,
  Badge,
  Divider
} from '@mui/material';
import {
  TrendingUp,
  EmojiEvents,
  Star,
  LocalFireDepartment,
  Speed,
  Psychology,
  Refresh,
  Notifications
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, ResponsiveContainer, Tooltip as RechartsTooltip, Area, AreaChart } from 'recharts';
import { useAuth } from '../../contexts/AuthContext';
import { useNeuroMarketing } from '../../hooks/useNeuroMarketing';

interface QuestData {
  current: number;
  target: number;
  percentage: number;
  level: number;
  nextMilestone: number;
  streakBonus: number;
}

interface WinStreakData {
  current: number;
  trend: 'up' | 'down' | 'flat';
  history: Array<{
    week: string;
    wins: number;
    value: number;
  }>;
  encouragement: string;
  nextTarget: number;
}

interface VictoryReelItem {
  id: string;
  type: 'user_win' | 'social_proof' | 'milestone';
  message: string;
  value?: number;
  timestamp: Date;
  isHighlight: boolean;
}

const QuestProgressSystem: React.FC = () => {
  const { user } = useAuth();
  const { psychologicalState, isStressed } = useNeuroMarketing();
  
  const [questData, setQuestData] = useState<QuestData>({
    current: 173,
    target: 8650, // 173 is 2% of this target
    percentage: 2,
    level: 3,
    nextMilestone: 500,
    streakBonus: 1.2
  });

  const [winStreakData, setWinStreakData] = useState<WinStreakData>({
    current: 2,
    trend: 'up',
    history: [
      { week: 'W1', wins: 1, value: 1.0 },
      { week: 'W2', wins: 1, value: 1.5 },
      { week: 'W3', wins: 2, value: 2.2 },
      { week: 'W4', wins: 3, value: 2.8 },
      { week: 'W5', wins: 2, value: 3.1 }
    ],
    encouragement: 'Your 200% win streak suggests boldness!',
    nextTarget: 5
  });

  const [victoryReel, setVictoryReel] = useState<VictoryReelItem[]>([
    {
      id: '1',
      type: 'user_win',
      message: 'Your Wins: 2 in a row!',
      value: 2,
      timestamp: new Date(),
      isHighlight: true
    },
    {
      id: '2',
      type: 'social_proof',
      message: 'John from Gauteng won R500k!',
      value: 500000,
      timestamp: new Date(Date.now() - 3600000),
      isHighlight: false
    },
    {
      id: '3',
      type: 'milestone',
      message: 'Level 3 Achiever unlocked!',
      timestamp: new Date(Date.now() - 7200000),
      isHighlight: false
    }
  ]);

  const [showNotification, setShowNotification] = useState(false);

  useEffect(() => {
    // Simulate real-time updates
    const interval = setInterval(() => {
      updateQuestProgress();
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const updateQuestProgress = () => {
    // Simulate progress updates
    setQuestData(prev => ({
      ...prev,
      current: prev.current + Math.floor(Math.random() * 5),
      percentage: Math.min(((prev.current + Math.floor(Math.random() * 5)) / prev.target) * 100, 100)
    }));
    
    setShowNotification(true);
    setTimeout(() => setShowNotification(false), 3000);
  };

  const getProgressColor = () => {
    if (questData.percentage < 25) return '#f44336'; // Red
    if (questData.percentage < 50) return '#ff9800'; // Orange
    if (questData.percentage < 75) return '#2196f3'; // Blue
    return '#4caf50'; // Green
  };

  const getStreakIcon = () => {
    if (winStreakData.current >= 5) return <LocalFireDepartment sx={{ color: '#ff5722' }} />;
    if (winStreakData.current >= 3) return <Star sx={{ color: '#ffc107' }} />;
    return <TrendingUp sx={{ color: '#4caf50' }} />;
  };

  const formatCurrency = (value: number) => {
    if (value >= 1000000) return `R${(value / 1000000).toFixed(1)}M`;
    if (value >= 1000) return `R${(value / 1000).toFixed(0)}k`;
    return `R${value}`;
  };

  return (
    <Card sx={{ position: 'relative' }}>
      {/* Notification Badge */}
      {showNotification && (
        <Badge
          badgeContent="New!"
          color="error"
          sx={{
            position: 'absolute',
            top: 16,
            right: 16,
            zIndex: 1
          }}
        >
          <Notifications color="primary" />
        </Badge>
      )}

      <CardHeader 
        title="Quest Progress"
        action={
          <Tooltip title="Refresh Progress">
            <IconButton onClick={updateQuestProgress}>
              <Refresh />
            </IconButton>
          </Tooltip>
        }
        sx={{ pb: 1 }}
      />
      
      <CardContent>
        {/* Main Progress Bar */}
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Typography variant="body2" color="text.secondary">
              Level {questData.level} Progress
            </Typography>
            <Chip 
              label={`${questData.percentage}%`}
              size="small"
              color={questData.percentage > 50 ? 'success' : 'warning'}
            />
          </Box>
          
          <LinearProgress 
            variant="determinate" 
            value={questData.percentage} 
            sx={{ 
              height: 12, 
              borderRadius: 6,
              backgroundColor: '#e0e0e0',
              '& .MuiLinearProgress-bar': {
                backgroundColor: getProgressColor(),
                borderRadius: 6
              }
            }} 
          />
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
            <Typography variant="h4" fontWeight="bold" color={getProgressColor()}>
              {questData.current.toLocaleString()}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Target: {questData.target.toLocaleString()}
            </Typography>
          </Box>
          
          {/* Next Milestone */}
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            🎯 Next milestone: {questData.nextMilestone} points ({questData.nextMilestone - questData.current} to go)
          </Typography>
        </Box>

        <Divider sx={{ my: 2 }} />

        {/* Win Streak Trend */}
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
            {getStreakIcon()}
            <Typography variant="h6" fontWeight="bold">
              Win Streak Trend
            </Typography>
            <Chip 
              label={`${winStreakData.current} wins`}
              color="success"
              size="small"
            />
          </Box>
          
          <Box sx={{ height: 120, mb: 2 }}>
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={winStreakData.history}>
                <defs>
                  <linearGradient id="colorWins" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#2196F3" stopOpacity={0.8}/>
                    <stop offset="95%" stopColor="#2196F3" stopOpacity={0.1}/>
                  </linearGradient>
                </defs>
                <XAxis dataKey="week" axisLine={false} tickLine={false} />
                <YAxis hide />
                <RechartsTooltip 
                  formatter={(value, name) => [`${value} wins`, 'Streak']}
                  labelFormatter={(label) => `Week ${label}`}
                />
                <Area 
                  type="monotone" 
                  dataKey="value" 
                  stroke="#2196F3" 
                  strokeWidth={3}
                  fill="url(#colorWins)"
                />
              </AreaChart>
            </ResponsiveContainer>
          </Box>
          
          <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center' }}>
            🔥 Next target: {winStreakData.nextTarget} consecutive wins
          </Typography>
        </Box>

        <Divider sx={{ my: 2 }} />

        {/* Victory Reel */}
        <Box>
          <Typography variant="h6" fontWeight="bold" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
            <EmojiEvents color="warning" />
            Victory Reel
          </Typography>
          
          <Stack spacing={2}>
            {victoryReel.map((item) => (
              <Box 
                key={item.id}
                sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  gap: 2,
                  p: 1.5,
                  borderRadius: 2,
                  backgroundColor: item.isHighlight ? 'rgba(76, 175, 80, 0.1)' : 'rgba(0, 0, 0, 0.02)',
                  border: item.isHighlight ? '1px solid rgba(76, 175, 80, 0.3)' : '1px solid transparent'
                }}
              >
                <Avatar 
                  sx={{ 
                    width: 32, 
                    height: 32,
                    backgroundColor: item.type === 'user_win' ? '#4caf50' : 
                                   item.type === 'social_proof' ? '#2196f3' : '#ff9800'
                  }}
                >
                  {item.type === 'user_win' ? '🏆' : 
                   item.type === 'social_proof' ? '👤' : '🎖️'}
                </Avatar>
                
                <Box sx={{ flex: 1 }}>
                  <Typography 
                    variant="body2" 
                    fontWeight={item.isHighlight ? 'bold' : 'normal'}
                    color={item.isHighlight ? 'success.main' : 'text.primary'}
                  >
                    {item.message}
                  </Typography>
                  {item.value && (
                    <Typography variant="caption" color="text.secondary">
                      {item.type === 'user_win' ? `Streak: ${item.value}` : formatCurrency(item.value)}
                    </Typography>
                  )}
                </Box>
                
                <Typography variant="caption" color="text.secondary">
                  {item.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </Typography>
              </Box>
            ))}
          </Stack>
          
          {/* Streak Bonus Indicator */}
          {winStreakData.current >= 2 && (
            <Box sx={{ 
              mt: 2, 
              p: 2, 
              borderRadius: 2, 
              background: 'linear-gradient(45deg, #ff9800, #ffc107)',
              color: 'white',
              textAlign: 'center'
            }}>
              <Typography variant="body2" fontWeight="bold">
                🔥 STREAK BONUS ACTIVE! 
              </Typography>
              <Typography variant="caption">
                {questData.streakBonus}x multiplier on next win
              </Typography>
            </Box>
          )}
        </Box>
      </CardContent>
    </Card>
  );
};

export default QuestProgressSystem;
