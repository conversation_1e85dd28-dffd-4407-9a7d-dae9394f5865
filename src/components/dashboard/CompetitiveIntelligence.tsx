import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardContent,
  CardHeader,
  Box,
  Typography,
  Avatar,
  Chip,
  LinearProgress,
  IconButton,
  Tooltip,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Divider,
  Button,
  useTheme,
  alpha
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon,
  EmojiEvents as TrophyIcon,
  Visibility as EyeIcon,
  Speed as SpeedIcon,
  Psychology as BrainIcon,
  Warning as WarningIcon,
  Star as StarIcon,
  FlashOn as FlashOnIcon,
  GpsFixed as TargetIcon,
  AttachMoney as MoneyIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { useNeuroMarketing } from '../../hooks/useNeuroMarketing';
import usePsychologicalSystems from '../../hooks/usePsychologicalSystems';

interface CompetitorData {
  id: string;
  name: string;
  avatar?: string;
  currentRanking: number;
  previousRanking: number;
  totalEarnings: number;
  successRate: number;
  activeBids: number;
  recentActivity: string;
  threatLevel: 'low' | 'medium' | 'high' | 'critical';
  specialization: string;
  lastSeen: string;
  isRival?: boolean;
  isTarget?: boolean;
}

interface UserRankingData {
  currentPosition: number;
  previousPosition: number;
  totalCompetitors: number;
  pointsToNext: number;
  pointsFromPrevious: number;
  category: string;
  percentile: number;
}

interface CompetitiveIntelligenceProps {
  userRanking: UserRankingData;
  competitors: CompetitorData[];
  onViewCompetitor?: (competitorId: string) => void;
  onRefresh?: () => void;
  compact?: boolean;
}

const CompetitiveIntelligence: React.FC<CompetitiveIntelligenceProps> = ({
  userRanking,
  competitors,
  onViewCompetitor,
  onRefresh,
  compact = false
}) => {
  const theme = useTheme();
  const { psychologicalState, isStressed, needsSimplification } = useNeuroMarketing();
  const { profile: psychProfile } = usePsychologicalSystems();
  
  const [refreshing, setRefreshing] = useState(false);
  const [highlightedCompetitor, setHighlightedCompetitor] = useState<string | null>(null);

  const userArchetype = psychProfile?.archetype || 'achiever';

  // Mock real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      // Simulate competitor activity
      const randomCompetitor = competitors[Math.floor(Math.random() * competitors.length)];
      if (randomCompetitor) {
        setHighlightedCompetitor(randomCompetitor.id);
        setTimeout(() => setHighlightedCompetitor(null), 3000);
      }
    }, 15000); // Every 15 seconds

    return () => clearInterval(interval);
  }, [competitors]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
    onRefresh?.();
    setRefreshing(false);
  };

  const getRankingChange = (current: number, previous: number) => {
    const change = previous - current; // Lower number = better ranking
    if (change > 0) return { direction: 'up', value: change, color: theme.palette.success.main };
    if (change < 0) return { direction: 'down', value: Math.abs(change), color: theme.palette.error.main };
    return { direction: 'same', value: 0, color: theme.palette.grey[500] };
  };

  const getThreatColor = (threatLevel: string) => {
    switch (threatLevel) {
      case 'critical': return theme.palette.error.main;
      case 'high': return theme.palette.warning.main;
      case 'medium': return theme.palette.info.main;
      case 'low': return theme.palette.success.main;
      default: return theme.palette.grey[500];
    }
  };

  const getArchetypeMessage = () => {
    const userChange = getRankingChange(userRanking.currentPosition, userRanking.previousPosition);
    
    switch (userArchetype) {
      case 'achiever':
        if (userChange.direction === 'up') {
          return {
            icon: '👑',
            message: `Excellent! You climbed ${userChange.value} spots. Keep dominating!`,
            color: theme.palette.success.main
          };
        } else if (userChange.direction === 'down') {
          return {
            icon: '⚔️',
            message: `Fight back! You dropped ${userChange.value} spots. Time to reclaim your throne!`,
            color: theme.palette.error.main
          };
        }
        return {
          icon: '👑',
          message: 'Maintain your position. Look for opportunities to climb higher!',
          color: theme.palette.primary.main
        };
      
      case 'hunter':
        const threats = competitors.filter(c => c.threatLevel === 'high' || c.threatLevel === 'critical');
        return {
          icon: '🎯',
          message: threats.length > 0 
            ? `${threats.length} high-threat targets detected. Time to hunt!`
            : 'No immediate threats. Perfect time to strike!',
          color: theme.palette.error.main
        };
      
      case 'analyst':
        return {
          icon: '📊',
          message: `You're in the ${userRanking.percentile}th percentile. Data suggests focusing on high-value bids.`,
          color: theme.palette.info.main
        };
      
      default:
        return {
          icon: '⭐',
          message: `Ranking #${userRanking.currentPosition} of ${userRanking.totalCompetitors}`,
          color: theme.palette.primary.main
        };
    }
  };

  const archetypeConfig = getArchetypeMessage();
  const userChange = getRankingChange(userRanking.currentPosition, userRanking.previousPosition);

  // Sort competitors by threat level and ranking
  const sortedCompetitors = [...competitors].sort((a, b) => {
    const threatOrder = { critical: 4, high: 3, medium: 2, low: 1 };
    const aThreat = threatOrder[a.threatLevel];
    const bThreat = threatOrder[b.threatLevel];
    
    if (aThreat !== bThreat) return bThreat - aThreat;
    return a.currentRanking - b.currentRanking;
  });

  return (
    <Card sx={{ height: '100%' }}>
      <CardHeader
        title={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <TrophyIcon />
            <Typography variant="h6">Live Leaderboard</Typography>
          </Box>
        }
        action={
          <IconButton onClick={handleRefresh} disabled={refreshing}>
            <RefreshIcon sx={{ 
              animation: refreshing ? 'spin 1s linear infinite' : 'none',
              '@keyframes spin': {
                '0%': { transform: 'rotate(0deg)' },
                '100%': { transform: 'rotate(360deg)' }
              }
            }} />
          </IconButton>
        }
      />
      
      <CardContent sx={{ pt: 0 }}>
        {/* User Position Card */}
        <Box
          sx={{
            p: 2,
            borderRadius: 2,
            background: `linear-gradient(135deg, ${alpha(archetypeConfig.color, 0.1)} 0%, ${alpha(archetypeConfig.color, 0.05)} 100%)`,
            border: `2px solid ${alpha(archetypeConfig.color, 0.3)}`,
            mb: 3
          }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="h4" fontWeight="bold" color={archetypeConfig.color}>
                #{userRanking.currentPosition}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                {userChange.direction === 'up' && <TrendingUpIcon sx={{ color: userChange.color }} />}
                {userChange.direction === 'down' && <TrendingDownIcon sx={{ color: userChange.color }} />}
                {userChange.direction === 'same' && <TrendingFlatIcon sx={{ color: userChange.color }} />}
                {userChange.value > 0 && (
                  <Typography variant="body2" fontWeight="bold" color={userChange.color}>
                    {userChange.direction === 'up' ? '+' : '-'}{userChange.value}
                  </Typography>
                )}
              </Box>
            </Box>
            
            <Typography variant="caption" color="text.secondary">
              of {userRanking.totalCompetitors.toLocaleString()} bidders
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
            <Typography sx={{ fontSize: '1.2rem' }}>
              {archetypeConfig.icon}
            </Typography>
            <Typography variant="body2" fontWeight="bold" color={archetypeConfig.color}>
              {archetypeConfig.message}
            </Typography>
          </Box>
          
          {/* Progress to next rank */}
          <Box sx={{ mt: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="caption" color="text.secondary">
                Progress to #{userRanking.currentPosition - 1}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {userRanking.pointsToNext} points needed
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={75} // Mock progress
              sx={{
                height: 6,
                borderRadius: 3,
                backgroundColor: alpha(archetypeConfig.color, 0.2),
                '& .MuiLinearProgress-bar': {
                  backgroundColor: archetypeConfig.color
                }
              }}
            />
          </Box>
        </Box>

        {/* Competitor List */}
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          🎯 Competitive Intelligence
        </Typography>
        
        <List sx={{ p: 0 }}>
          {sortedCompetitors.slice(0, compact ? 3 : 5).map((competitor, index) => {
            const change = getRankingChange(competitor.currentRanking, competitor.previousRanking);
            const isHighlighted = highlightedCompetitor === competitor.id;
            const threatColor = getThreatColor(competitor.threatLevel);
            
            return (
              <React.Fragment key={competitor.id}>
                <ListItem
                  sx={{
                    px: 0,
                    py: 1,
                    backgroundColor: isHighlighted ? alpha(threatColor, 0.1) : 'transparent',
                    borderRadius: 1,
                    transition: 'all 0.3s ease',
                    cursor: 'pointer',
                    '&:hover': {
                      backgroundColor: alpha(threatColor, 0.05)
                    }
                  }}
                  onClick={() => onViewCompetitor?.(competitor.id)}
                >
                  <ListItemAvatar>
                    <Avatar
                      src={competitor.avatar}
                      sx={{
                        bgcolor: threatColor,
                        border: competitor.isRival ? `2px solid ${theme.palette.error.main}` : 'none'
                      }}
                    >
                      {competitor.name.charAt(0)}
                    </Avatar>
                  </ListItemAvatar>
                  
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body2" fontWeight="bold">
                          #{competitor.currentRanking} {competitor.name}
                        </Typography>
                        {competitor.isRival && (
                          <Chip
                            label="RIVAL"
                            size="small"
                            sx={{
                              backgroundColor: theme.palette.error.main,
                              color: 'white',
                              fontSize: '0.6rem',
                              height: 20
                            }}
                          />
                        )}
                        {competitor.isTarget && (
                          <Chip
                            label="TARGET"
                            size="small"
                            sx={{
                              backgroundColor: theme.palette.warning.main,
                              color: 'white',
                              fontSize: '0.6rem',
                              height: 20
                            }}
                          />
                        )}
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="caption" color="text.secondary">
                          {competitor.specialization} • {competitor.successRate}% success rate
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                          <Typography variant="caption" color="text.secondary">
                            {competitor.recentActivity}
                          </Typography>
                          {isHighlighted && (
                            <Chip
                              label="ACTIVE NOW"
                              size="small"
                              sx={{
                                backgroundColor: theme.palette.error.main,
                                color: 'white',
                                fontSize: '0.6rem',
                                height: 16,
                                animation: 'pulse 1s infinite'
                              }}
                            />
                          )}
                        </Box>
                      </Box>
                    }
                  />
                  
                  <Box sx={{ textAlign: 'right' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, justifyContent: 'flex-end' }}>
                      {change.direction === 'up' && <TrendingUpIcon sx={{ color: change.color, fontSize: 16 }} />}
                      {change.direction === 'down' && <TrendingDownIcon sx={{ color: change.color, fontSize: 16 }} />}
                      {change.direction === 'same' && <TrendingFlatIcon sx={{ color: change.color, fontSize: 16 }} />}
                      {change.value > 0 && (
                        <Typography variant="caption" fontWeight="bold" color={change.color}>
                          {change.direction === 'up' ? '+' : '-'}{change.value}
                        </Typography>
                      )}
                    </Box>
                    
                    <Chip
                      label={competitor.threatLevel.toUpperCase()}
                      size="small"
                      sx={{
                        backgroundColor: alpha(threatColor, 0.2),
                        color: threatColor,
                        fontSize: '0.6rem',
                        fontWeight: 'bold',
                        mt: 0.5
                      }}
                    />
                  </Box>
                </ListItem>
                
                {index < sortedCompetitors.slice(0, compact ? 3 : 5).length - 1 && (
                  <Divider variant="inset" component="li" />
                )}
              </React.Fragment>
            );
          })}
        </List>

        {/* Action Buttons */}
        <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<EyeIcon />}
            size="small"
            fullWidth
            onClick={() => {/* Navigate to full leaderboard */}}
          >
            View Full Leaderboard
          </Button>
          
          {userArchetype === 'hunter' && (
            <Button
              variant="contained"
              startIcon={<TargetIcon />}
              size="small"
              color="error"
              onClick={() => {/* Navigate to competitor analysis */}}
            >
              Hunt Mode
            </Button>
          )}
        </Box>
      </CardContent>
    </Card>
  );
};

export default CompetitiveIntelligence;
