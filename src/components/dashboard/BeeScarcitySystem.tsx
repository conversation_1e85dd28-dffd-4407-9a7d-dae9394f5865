import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Chip,
  Alert,
  LinearProgress,
  Stack,
  Button,
  IconButton,
  Tooltip,
  Avatar,
  Grid,
  Divider
} from '@mui/material';
import {
  Warning,
  FlashOn,
  LocalShipping,
  Speed,
  Timer,
  TrendingUp,
  MonetizationOn,
  Psychology,
  Refresh
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';

interface BeeAvailability {
  region: string;
  totalBees: number;
  availableBees: number;
  busyBees: number;
  demandLevel: 'low' | 'medium' | 'high' | 'critical';
  priceMultiplier: number;
  estimatedWaitTime: string;
  urgencyMessage: string;
}

interface BeeService {
  id: string;
  name: string;
  basePrice: number;
  currentPrice: number;
  availability: 'available' | 'limited' | 'scarce' | 'unavailable';
  beesRequired: number;
  availableSlots: number;
  totalSlots: number;
  nextAvailable: string;
  demandSurge: number;
}

const BeeScarcitySystem: React.FC = () => {
  const { user } = useAuth();
  
  const [beeAvailability, setBeeAvailability] = useState<BeeAvailability>({
    region: 'Gauteng',
    totalBees: 45,
    availableBees: 8,
    busyBees: 37,
    demandLevel: 'high',
    priceMultiplier: 1.4,
    estimatedWaitTime: '2-3 hours',
    urgencyMessage: '⚠️ HIGH DEMAND: Only 8 bees available in your area!'
  });

  const [beeServices, setBeeServices] = useState<BeeService[]>([
    {
      id: 'bee_direct',
      name: 'Bee Direct (Same Day)',
      basePrice: 150,
      currentPrice: 210,
      availability: 'limited',
      beesRequired: 1,
      availableSlots: 3,
      totalSlots: 15,
      nextAvailable: '14:30',
      demandSurge: 40
    },
    {
      id: 'bee_air',
      name: 'Bee Air (2 Hour Rush)',
      basePrice: 350,
      currentPrice: 525,
      availability: 'scarce',
      beesRequired: 2,
      availableSlots: 1,
      totalSlots: 8,
      nextAvailable: '16:00',
      demandSurge: 50
    },
    {
      id: 'courier_plus_bee',
      name: 'Courier + Bee Hybrid',
      basePrice: 95,
      currentPrice: 119,
      availability: 'available',
      beesRequired: 1,
      availableSlots: 12,
      totalSlots: 20,
      nextAvailable: 'Now',
      demandSurge: 25
    }
  ]);

  const [showUrgentAlert, setShowUrgentAlert] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    // Simulate real-time bee availability updates
    const interval = setInterval(() => {
      updateBeeAvailability();
    }, 15000); // Update every 15 seconds

    return () => clearInterval(interval);
  }, []);

  const updateBeeAvailability = () => {
    setBeeAvailability(prev => {
      const newAvailable = Math.max(1, prev.availableBees + Math.floor(Math.random() * 6) - 3);
      const newBusy = prev.totalBees - newAvailable;
      const demandLevel = newAvailable <= 5 ? 'critical' : 
                         newAvailable <= 10 ? 'high' : 
                         newAvailable <= 20 ? 'medium' : 'low';
      
      return {
        ...prev,
        availableBees: newAvailable,
        busyBees: newBusy,
        demandLevel,
        priceMultiplier: newAvailable <= 5 ? 1.8 : newAvailable <= 10 ? 1.4 : 1.2,
        estimatedWaitTime: newAvailable <= 3 ? '4-6 hours' : 
                          newAvailable <= 8 ? '2-3 hours' : '30-60 mins',
        urgencyMessage: newAvailable <= 3 ? '🚨 CRITICAL: Only 3 bees left!' :
                       newAvailable <= 8 ? '⚠️ HIGH DEMAND: Limited bees available!' :
                       '✅ Good availability in your area'
      };
    });

    // Update service availability
    setBeeServices(prev => prev.map(service => {
      const newSlots = Math.max(0, service.availableSlots + Math.floor(Math.random() * 4) - 2);
      return {
        ...service,
        availableSlots: newSlots,
        availability: newSlots === 0 ? 'unavailable' :
                     newSlots <= 2 ? 'scarce' :
                     newSlots <= 5 ? 'limited' : 'available',
        currentPrice: Math.round(service.basePrice * (newSlots <= 2 ? 1.6 : newSlots <= 5 ? 1.3 : 1.1)),
        demandSurge: newSlots <= 2 ? 60 : newSlots <= 5 ? 30 : 10
      };
    }));
  };

  const handleRefresh = () => {
    setRefreshing(true);
    updateBeeAvailability();
    setTimeout(() => setRefreshing(false), 1000);
  };

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case 'unavailable': return '#f44336';
      case 'scarce': return '#ff5722';
      case 'limited': return '#ff9800';
      case 'available': return '#4caf50';
      default: return '#757575';
    }
  };

  const getDemandColor = (level: string) => {
    switch (level) {
      case 'critical': return '#f44336';
      case 'high': return '#ff9800';
      case 'medium': return '#2196f3';
      case 'low': return '#4caf50';
      default: return '#757575';
    }
  };

  const handleBookBee = (serviceId: string) => {
    // Trigger RFQ creation with bee service pre-selected
    window.location.href = `/rfq/create?bee_service=${serviceId}&urgent=true`;
  };

  return (
    <Card sx={{ position: 'relative' }}>
      <CardContent>
        {/* Header with Refresh */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" fontWeight="bold" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            🐝 Bee Availability
          </Typography>
          <IconButton onClick={handleRefresh} disabled={refreshing}>
            <Refresh sx={{ animation: refreshing ? 'spin 1s linear infinite' : 'none' }} />
          </IconButton>
        </Box>

        {/* Urgent Scarcity Alert */}
        {beeAvailability.demandLevel === 'critical' && showUrgentAlert && (
          <Alert 
            severity="error" 
            sx={{ mb: 2 }}
            onClose={() => setShowUrgentAlert(false)}
          >
            <Typography variant="body2" fontWeight="bold">
              🚨 CRITICAL BEE SHORTAGE! Only {beeAvailability.availableBees} bees available in {beeAvailability.region}!
            </Typography>
            <Typography variant="caption">
              Prices increased by {Math.round((beeAvailability.priceMultiplier - 1) * 100)}% due to high demand
            </Typography>
          </Alert>
        )}

        {/* Regional Bee Status */}
        <Box sx={{ mb: 3 }}>
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h3" fontWeight="bold" color={getDemandColor(beeAvailability.demandLevel)}>
                  {beeAvailability.availableBees}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Available Bees
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h3" fontWeight="bold" color="warning.main">
                  {beeAvailability.busyBees}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Busy Bees
                </Typography>
              </Box>
            </Grid>
          </Grid>
          
          <Box sx={{ mt: 2 }}>
            <LinearProgress 
              variant="determinate" 
              value={(beeAvailability.busyBees / beeAvailability.totalBees) * 100}
              sx={{ 
                height: 8, 
                borderRadius: 4,
                backgroundColor: '#e0e0e0',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: getDemandColor(beeAvailability.demandLevel),
                  borderRadius: 4
                }
              }} 
            />
            <Typography variant="body2" sx={{ mt: 1, textAlign: 'center' }}>
              {beeAvailability.urgencyMessage}
            </Typography>
          </Box>
        </Box>

        <Divider sx={{ my: 2 }} />

        {/* Bee Services with Scarcity Pricing */}
        <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
          🚀 Book Your Bee Now
        </Typography>
        
        <Stack spacing={2}>
          {beeServices.map((service) => (
            <Card 
              key={service.id}
              variant="outlined"
              sx={{ 
                border: `2px solid ${getAvailabilityColor(service.availability)}40`,
                '&:hover': {
                  transform: 'translateY(-1px)',
                  boxShadow: 2
                }
              }}
            >
              <CardContent sx={{ p: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                  <Box>
                    <Typography variant="body1" fontWeight="bold">
                      {service.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Next available: {service.nextAvailable}
                    </Typography>
                  </Box>
                  <Chip 
                    label={service.availability.toUpperCase()}
                    size="small"
                    sx={{ 
                      backgroundColor: getAvailabilityColor(service.availability),
                      color: 'white',
                      fontWeight: 'bold'
                    }}
                  />
                </Box>

                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Box>
                    <Typography variant="h6" fontWeight="bold" color="primary.main">
                      R{service.currentPrice}
                    </Typography>
                    {service.currentPrice > service.basePrice && (
                      <Typography variant="caption" sx={{ textDecoration: 'line-through' }}>
                        R{service.basePrice}
                      </Typography>
                    )}
                  </Box>
                  <Box sx={{ textAlign: 'right' }}>
                    <Typography variant="body2" fontWeight="bold">
                      {service.availableSlots}/{service.totalSlots} slots
                    </Typography>
                    {service.demandSurge > 0 && (
                      <Typography variant="caption" color="error.main">
                        +{service.demandSurge}% surge
                      </Typography>
                    )}
                  </Box>
                </Box>

                <LinearProgress 
                  variant="determinate" 
                  value={(service.availableSlots / service.totalSlots) * 100}
                  sx={{ 
                    height: 6, 
                    borderRadius: 3,
                    mb: 2,
                    backgroundColor: '#e0e0e0',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: getAvailabilityColor(service.availability),
                      borderRadius: 3
                    }
                  }} 
                />

                <Button
                  variant="contained"
                  fullWidth
                  startIcon={<FlashOn />}
                  onClick={() => handleBookBee(service.id)}
                  disabled={service.availability === 'unavailable'}
                  sx={{
                    backgroundColor: getAvailabilityColor(service.availability),
                    fontWeight: 'bold',
                    '&:hover': {
                      backgroundColor: getAvailabilityColor(service.availability),
                      opacity: 0.8
                    }
                  }}
                >
                  {service.availability === 'unavailable' ? 'FULLY BOOKED' : 
                   service.availability === 'scarce' ? '🔥 BOOK LAST SLOT!' :
                   service.availability === 'limited' ? '⚡ BOOK NOW!' : 'BOOK BEE'}
                </Button>
              </CardContent>
            </Card>
          ))}
        </Stack>

        {/* FOMO Message */}
        <Alert severity="warning" sx={{ mt: 2 }}>
          <Typography variant="body2" fontWeight="bold">
            💰 PROFIT ALERT: Bidders using Bee services win 34% more RFQs!
          </Typography>
          <Typography variant="caption">
            Fast delivery = faster quotes = higher success rates
          </Typography>
        </Alert>
      </CardContent>
    </Card>
  );
};

export default BeeScarcitySystem;
