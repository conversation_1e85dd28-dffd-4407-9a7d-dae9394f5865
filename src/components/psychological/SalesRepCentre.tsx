import React, { useState, useEffect } from 'react';
import { 
  Target, 
  TrendingUp, 
  Award, 
  Users, 
  DollarSign,
  Calendar,
  Trophy,
  Zap,
  Brain,
  Heart,
  Star,
  Flame,
  Crown,
  ChevronUp,
  ChevronDown,
  Settings,
  Bell,
  BarChart3,
  Pie<PERSON><PERSON>,
  Activity
} from 'lucide-react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from '@mui/material';
import { <PERSON><PERSON>, Badge, LinearProgress, Tabs, Tab, Avatar, Alert } from '@mui/material';
import { Box, Typography, Grid, Chip } from '@mui/material';

interface SalesTarget {
  target_id: string;
  target_type: string;
  period: string;
  target_value: number;
  current_value: number;
  unit: string;
  start_date: string;
  end_date: string;
  completion_percentage: number;
  days_remaining: number;
  psychological_calibration: any;
}

interface PsychologicalState {
  stress_level: number;
  motivation_level: number;
  confidence_level: number;
  urgency_response: number;
  competitive_spirit: number;
  financial_motivation: number;
  cognitive_load: number;
  engagement_level: number;
}

interface SalesRepProfile {
  rep_id: string;
  archetype: string;
  motivation_factors: string[];
  psychological_state: PsychologicalState;
  target_preferences: any;
  performance_history: any;
}

interface BehavioralNudge {
  nudge_id: string;
  trigger_type: string;
  message: string;
  intensity: string;
  psychological_principle: string;
  expires_at?: string;
}

interface Achievement {
  achievement_id: string;
  name: string;
  description: string;
  icon: string;
  tier: string;
  category: string;
  xp_reward: number;
  psychological_benefit: string;
  unlocked_at: string;
}

const SalesRepCentre: React.FC = () => {
  const [profile, setProfile] = useState<SalesRepProfile | null>(null);
  const [targets, setTargets] = useState<SalesTarget[]>([]);
  const [nudges, setNudges] = useState<BehavioralNudge[]>([]);
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState('monthly');
  const [tabValue, setTabValue] = useState(0);

  useEffect(() => {
    loadRepData();
    
    // Auto-refresh every 30 seconds for real-time psychological engagement
    const interval = setInterval(loadRepData, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadRepData = async () => {
    try {
      const repId = 'current-rep-id'; // Replace with actual auth
      
      // Load profile
      const profileResponse = await fetch(`/api/sales-rep/${repId}/profile`);
      if (profileResponse.ok) {
        const profileData = await profileResponse.json();
        setProfile(profileData);
      }

      // Load targets
      const targetsResponse = await fetch(`/api/sales-rep/${repId}/targets?period=${selectedPeriod}`);
      if (targetsResponse.ok) {
        const targetsData = await targetsResponse.json();
        setTargets(targetsData.targets || []);
      }

      // Generate behavioral nudges
      const context = {
        target_completion: targets[0]?.completion_percentage || 0,
        days_left: targets[0]?.days_remaining || 30,
        rank_dropped: false,
        commission_opportunity: 15000,
        potential_commission: 15000
      };

      const nudgesResponse = await fetch(`/api/sales-rep/${repId}/nudges`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(context)
      });

      if (nudgesResponse.ok) {
        const nudgesData = await nudgesResponse.json();
        setNudges(nudgesData.nudges || []);
      }

      // Load achievements
      const achievementsResponse = await fetch(`/api/sales-rep/${repId}/achievements`);
      if (achievementsResponse.ok) {
        const achievementsData = await achievementsResponse.json();
        setAchievements(achievementsData.achievements || []);
      }

    } catch (err) {
      console.error('Failed to load rep data:', err);
    }
  };

  const getArchetypeColor = (archetype: string) => {
    const colors = {
      'achiever': '#9c27b0',
      'hunter': '#f44336',
      'relationship_builder': '#2196f3',
      'analyst': '#4caf50'
    };
    return colors[archetype as keyof typeof colors] || '#757575';
  };

  const getArchetypeIcon = (archetype: string) => {
    const icons = {
      'achiever': <Crown />,
      'hunter': <Target />,
      'relationship_builder': <Heart />,
      'analyst': <Brain />
    };
    return icons[archetype as keyof typeof icons] || <Star />;
  };

  const getPsychologicalStateColor = (value: number) => {
    if (value >= 0.8) return '#4caf50';
    if (value >= 0.6) return '#ff9800';
    if (value >= 0.4) return '#ff5722';
    return '#f44336';
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const currentTarget = targets.find(t => t.period === selectedPeriod);

  return (
    <Box sx={{ maxWidth: '1200px', mx: 'auto', p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h3" component="h1" gutterBottom>
            Sales Rep Centre
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Your personalized sales intelligence dashboard
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button variant="outlined" startIcon={<Activity />} onClick={loadRepData}>
            Refresh
          </Button>
          <Button variant="outlined" startIcon={<Settings />}>
            Settings
          </Button>
        </Box>
      </Box>

      {/* Psychological Profile Card */}
      {profile && (
        <Card sx={{ mb: 3, background: 'linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%)', border: '2px solid #e1bee7' }}>
          <CardHeader
            title={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Brain style={{ color: '#9c27b0' }} />
                Your Sales Psychology Profile
              </Box>
            }
          />
          <CardContent>
            <Grid container spacing={3}>
              <Grid item xs={12} md={3} sx={{ textAlign: 'center' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 1 }}>
                  <Chip 
                    icon={getArchetypeIcon(profile.archetype)}
                    label={profile.archetype.replace('_', ' ').toUpperCase()}
                    sx={{ 
                      backgroundColor: getArchetypeColor(profile.archetype),
                      color: 'white',
                      fontWeight: 'bold'
                    }}
                  />
                </Box>
                <Typography variant="body2" color="text.secondary">Sales Archetype</Typography>
              </Grid>

              <Grid item xs={12} md={3} sx={{ textAlign: 'center' }}>
                <Typography 
                  variant="h4" 
                  sx={{ 
                    color: getPsychologicalStateColor(profile.psychological_state.motivation_level),
                    fontWeight: 'bold'
                  }}
                >
                  {(profile.psychological_state.motivation_level * 100).toFixed(0)}%
                </Typography>
                <Typography variant="body2" color="text.secondary">Motivation Level</Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={profile.psychological_state.motivation_level * 100} 
                  sx={{ mt: 1, height: 8, borderRadius: 4 }}
                />
              </Grid>

              <Grid item xs={12} md={3} sx={{ textAlign: 'center' }}>
                <Typography 
                  variant="h4" 
                  sx={{ 
                    color: getPsychologicalStateColor(profile.psychological_state.confidence_level),
                    fontWeight: 'bold'
                  }}
                >
                  {(profile.psychological_state.confidence_level * 100).toFixed(0)}%
                </Typography>
                <Typography variant="body2" color="text.secondary">Confidence Level</Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={profile.psychological_state.confidence_level * 100} 
                  sx={{ mt: 1, height: 8, borderRadius: 4 }}
                />
              </Grid>

              <Grid item xs={12} md={3} sx={{ textAlign: 'center' }}>
                <Typography 
                  variant="h4" 
                  sx={{ 
                    color: getPsychologicalStateColor(1 - profile.psychological_state.stress_level),
                    fontWeight: 'bold'
                  }}
                >
                  {((1 - profile.psychological_state.stress_level) * 100).toFixed(0)}%
                </Typography>
                <Typography variant="body2" color="text.secondary">Wellness Score</Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={(1 - profile.psychological_state.stress_level) * 100} 
                  sx={{ mt: 1, height: 8, borderRadius: 4 }}
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Behavioral Nudges */}
      {nudges.length > 0 && (
        <Box sx={{ mb: 3 }}>
            <Typography variant="h5" sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
            <Zap style={{ color: '#ff9800' }} />
            Smart Insights & Nudges
          </Typography>
          {nudges.map((nudge) => (
            <Alert 
              key={nudge.nudge_id} 
              severity={nudge.intensity === 'high' ? 'error' : nudge.intensity === 'medium' ? 'warning' : 'info'}
              sx={{ mb: 1 }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start' }}>
                <Box>
                  <Typography variant="body1" fontWeight="medium">{nudge.message}</Typography>
                  <Typography variant="caption" color="text.secondary">
                    Psychology: {nudge.psychological_principle}
                  </Typography>
                </Box>
                <Chip label={nudge.intensity} size="small" variant="outlined" />
              </Box>
            </Alert>
          ))}
        </Box>
      )}

      <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)} sx={{ mb: 3 }}>
        <Tab label="Monthly Targets" />
        <Tab label="Quarterly Targets" />
        <Tab label="Annual Targets" />
      </Tabs>

      {/* Target Progress */}
      {currentTarget && (
        <Card sx={{ mb: 3 }}>
          <CardHeader
            title={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Target />
                {selectedPeriod.charAt(0).toUpperCase() + selectedPeriod.slice(1)} Target Progress
              </Box>
            }
          />
          <CardContent>
            <Grid container spacing={3}>
              <Grid item xs={12} md={4} sx={{ textAlign: 'center' }}>
                <Box sx={{ position: 'relative', display: 'inline-flex', mb: 2 }}>
                  <Box
                    sx={{
                      width: 120,
                      height: 120,
                      borderRadius: '50%',
                      background: `conic-gradient(${currentTarget.completion_percentage >= 100 ? '#4caf50' : 
                                 currentTarget.completion_percentage >= 80 ? '#ff9800' : '#f44336'} ${currentTarget.completion_percentage * 3.6}deg, #e0e0e0 0deg)`,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" fontWeight="bold">
                        {currentTarget.completion_percentage.toFixed(0)}%
                      </Typography>
                      <Typography variant="caption">Complete</Typography>
                    </Box>
                  </Box>
                </Box>
                <Typography variant="body2" color="text.secondary">Target Achievement</Typography>
              </Grid>

              <Grid item xs={12} md={4}>
                <Box sx={{ mb: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">Current Progress</Typography>
                    <Typography variant="body2" fontWeight="medium">
                      {currentTarget.unit === 'ZAR' 
                        ? formatCurrency(currentTarget.current_value)
                        : currentTarget.current_value.toLocaleString()
                      }
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">Target</Typography>
                    <Typography variant="body2" fontWeight="medium">
                      {currentTarget.unit === 'ZAR' 
                        ? formatCurrency(currentTarget.target_value)
                        : currentTarget.target_value.toLocaleString()
                      }
                    </Typography>
                  </Box>
                  <LinearProgress 
                    variant="determinate" 
                    value={currentTarget.completion_percentage} 
                    sx={{ height: 12, borderRadius: 6 }}
                  />
                </Box>

                <Grid container spacing={2} sx={{ textAlign: 'center' }}>
                  <Grid item xs={6}>
                    <Typography variant="h6" color="primary" fontWeight="bold">
                      {currentTarget.days_remaining}
                    </Typography>
                    <Typography variant="caption">Days Left</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="h6" color="success.main" fontWeight="bold">
                      {currentTarget.unit === 'ZAR' 
                        ? formatCurrency(currentTarget.target_value - currentTarget.current_value)
                        : (currentTarget.target_value - currentTarget.current_value).toLocaleString()
                      }
                    </Typography>
                    <Typography variant="caption">To Go</Typography>
                  </Grid>
                </Grid>
              </Grid>

              <Grid item xs={12} md={4}>
                <Typography variant="h6" gutterBottom>Psychological Insights</Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2">Stress Level:</Typography>
                    <Typography 
                      variant="body2" 
                      sx={{ color: getPsychologicalStateColor(1 - (profile?.psychological_state.stress_level || 0)) }}
                    >
                      {profile ? ((1 - profile.psychological_state.stress_level) * 100).toFixed(0) : 0}%
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2">Motivation:</Typography>
                    <Typography 
                      variant="body2" 
                      sx={{ color: getPsychologicalStateColor(profile?.psychological_state.motivation_level || 0) }}
                    >
                      {profile ? (profile.psychological_state.motivation_level * 100).toFixed(0) : 0}%
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2">Confidence:</Typography>
                    <Typography 
                      variant="body2" 
                      sx={{ color: getPsychologicalStateColor(profile?.psychological_state.confidence_level || 0) }}
                    >
                      {profile ? (profile.psychological_state.confidence_level * 100).toFixed(0) : 0}%
                    </Typography>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Achievements */}
      <Card>
        <CardHeader
          title={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Trophy />
              Recent Achievements
            </Box>
          }
        />
        <CardContent>
          {achievements.length === 0 ? (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Award style={{ fontSize: 48, color: '#bdbdbd', marginBottom: 16 }} />
              <Typography variant="h6" color="text.primary" gutterBottom>No Achievements Yet</Typography>
              <Typography variant="body2" color="text.secondary">Start hitting your targets to unlock achievements!</Typography>
            </Box>
          ) : (
            <Grid container spacing={2}>
              {achievements.slice(0, 6).map((achievement) => (
                <Grid item xs={12} md={6} lg={4} key={achievement.achievement_id}>
                  <Card variant="outlined" sx={{ p: 2, '&:hover': { boxShadow: 2 } }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                      <Typography variant="h4">{achievement.icon}</Typography>
                      <Box>
                        <Typography variant="subtitle1" fontWeight="medium">{achievement.name}</Typography>
                        <Chip label={achievement.tier} size="small" />
                      </Box>
                    </Box>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      {achievement.description}
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="caption" color="primary">+{achievement.xp_reward} XP</Typography>
                      <Typography variant="caption" color="text.secondary">
                        {new Date(achievement.unlocked_at).toLocaleDateString()}
                      </Typography>
                    </Box>
                  </Card>
                </Grid>
              ))}
            </Grid>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default SalesRepCentre;
