import React, { useState } from 'react';
import { 
  Brain, 
  Target, 
  Trophy, 
  Users, 
  ArrowRight, 
  CheckCircle, 
  Star,
  Zap,
  Heart,
  TrendingUp,
  Award,
  Crown
} from 'lucide-react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON>nt, 
  <PERSON><PERSON><PERSON>er, 
  <PERSON>ton,
  TextField,
  RadioGroup,
  FormControlLabel,
  Radio,
  Stepper,
  Step,
  StepLabel,
  Box,
  Typography,
  Grid,
  Chip,
  Alert,
  LinearProgress,
  Avatar
} from '@mui/material';

interface OnboardingQuizResponse {
  question_id: string;
  response: string;
  weight: number;
}

interface PsychologicalProfile {
  archetype: string;
  motivation_factors: string[];
  confidence_scores: Record<string, number>;
  psychological_state: {
    stress_level: number;
    motivation_level: number;
    confidence_level: number;
    urgency_response: number;
    competitive_spirit: number;
    financial_motivation: number;
  };
}

const SalesRepSelfOnboarding: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [quizResponses, setQuizResponses] = useState<OnboardingQuizResponse[]>([]);
  const [profile, setProfile] = useState<PsychologicalProfile | null>(null);
  const [personalInfo, setPersonalInfo] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: ''
  });

  const steps = ['Welcome', 'Personal Info', 'Psychology Quiz', 'Profile Results', 'Dashboard Access'];

  const psychologyQuestions = [
    {
      id: 'motivation_driver',
      question: 'What motivates you most in sales?',
      options: [
        { value: 'recognition', label: 'Recognition and status', archetype: 'achiever', weight: 3 },
        { value: 'money', label: 'Financial rewards', archetype: 'hunter', weight: 3 },
        { value: 'relationships', label: 'Building client relationships', archetype: 'relationship_builder', weight: 3 },
        { value: 'data', label: 'Analyzing performance data', archetype: 'analyst', weight: 3 }
      ]
    },
    {
      id: 'pressure_response',
      question: 'How do you perform under pressure?',
      options: [
        { value: 'thrive', label: 'I thrive and get energized', archetype: 'hunter', weight: 2 },
        { value: 'focused', label: 'I become more focused', archetype: 'achiever', weight: 2 },
        { value: 'methodical', label: 'I stick to my process', archetype: 'analyst', weight: 2 },
        { value: 'collaborative', label: 'I seek team support', archetype: 'relationship_builder', weight: 2 }
      ]
    },
    {
      id: 'goal_setting',
      question: 'How do you prefer to set goals?',
      options: [
        { value: 'aggressive', label: 'Aggressive, stretch targets', archetype: 'hunter', weight: 2 },
        { value: 'public', label: 'Public commitments for accountability', archetype: 'achiever', weight: 2 },
        { value: 'data_driven', label: 'Based on historical data', archetype: 'analyst', weight: 2 },
        { value: 'collaborative', label: 'With team input and support', archetype: 'relationship_builder', weight: 2 }
      ]
    },
    {
      id: 'success_celebration',
      question: 'How do you like to celebrate wins?',
      options: [
        { value: 'public_recognition', label: 'Public recognition and awards', archetype: 'achiever', weight: 2 },
        { value: 'financial_bonus', label: 'Financial bonuses', archetype: 'hunter', weight: 2 },
        { value: 'team_celebration', label: 'Team celebrations', archetype: 'relationship_builder', weight: 2 },
        { value: 'personal_reflection', label: 'Personal reflection on improvement', archetype: 'analyst', weight: 2 }
      ]
    },
    {
      id: 'learning_style',
      question: 'How do you prefer to learn new sales techniques?',
      options: [
        { value: 'competition', label: 'Through competition and challenges', archetype: 'achiever', weight: 1 },
        { value: 'real_world', label: 'Real-world application immediately', archetype: 'hunter', weight: 1 },
        { value: 'mentorship', label: 'Mentorship and coaching', archetype: 'relationship_builder', weight: 1 },
        { value: 'research', label: 'Research and case studies', archetype: 'analyst', weight: 1 }
      ]
    }
  ];

  const handleQuizResponse = (questionId: string, response: string, archetype: string, weight: number) => {
    const newResponse: OnboardingQuizResponse = {
      question_id: questionId,
      response,
      weight
    };

    setQuizResponses(prev => {
      const filtered = prev.filter(r => r.question_id !== questionId);
      return [...filtered, newResponse];
    });
  };

  const analyzeProfile = async () => {
    setLoading(true);
    
    try {
      // Calculate archetype scores
      const archetypeScores = {
        achiever: 0,
        hunter: 0,
        relationship_builder: 0,
        analyst: 0
      };

      psychologyQuestions.forEach(question => {
        const response = quizResponses.find(r => r.question_id === question.id);
        if (response) {
          const option = question.options.find(opt => opt.value === response.response);
          if (option) {
            archetypeScores[option.archetype as keyof typeof archetypeScores] += option.weight;
          }
        }
      });

      // Determine primary archetype
      const primaryArchetype = Object.entries(archetypeScores)
        .sort(([,a], [,b]) => b - a)[0][0];

      // Generate psychological profile
      const newProfile: PsychologicalProfile = {
        archetype: primaryArchetype,
        motivation_factors: getMotivationFactors(primaryArchetype),
        confidence_scores: {
          sales_ability: 0.75,
          product_knowledge: 0.65,
          negotiation: 0.70,
          relationship_building: 0.80
        },
        psychological_state: {
          stress_level: 0.3,
          motivation_level: 0.85,
          confidence_level: 0.75,
          urgency_response: primaryArchetype === 'hunter' ? 0.9 : 0.6,
          competitive_spirit: primaryArchetype === 'achiever' ? 0.9 : 0.5,
          financial_motivation: primaryArchetype === 'hunter' ? 0.95 : 0.6
        }
      };

      setProfile(newProfile);
      setCurrentStep(3);

      // Submit to backend
      const onboardingData = {
        ...personalInfo,
        quiz_responses: quizResponses,
        psychological_profile: newProfile
      };

      const response = await fetch('/api/sales-rep/instant-onboard', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(onboardingData)
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Onboarding successful:', result);
      }

    } catch (error) {
      console.error('Profile analysis failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const getMotivationFactors = (archetype: string): string[] => {
    const factors = {
      achiever: ['recognition', 'status', 'competition', 'public_acknowledgment', 'leadership_opportunities'],
      hunter: ['financial_rewards', 'commission', 'bonuses', 'immediate_results', 'aggressive_targets'],
      relationship_builder: ['client_satisfaction', 'long_term_relationships', 'team_collaboration', 'trust_building', 'referrals'],
      analyst: ['data_insights', 'process_optimization', 'performance_metrics', 'continuous_improvement', 'strategic_planning']
    };
    return factors[archetype as keyof typeof factors] || [];
  };

  const getArchetypeDescription = (archetype: string) => {
    const descriptions = {
      achiever: {
        title: 'The Achiever',
        description: 'You thrive on recognition, competition, and status. You\'re motivated by public acknowledgment and leadership opportunities.',
        icon: <Crown />,
        color: '#9c27b0',
        traits: ['Competitive', 'Status-driven', 'Recognition-seeking', 'Leadership-oriented']
      },
      hunter: {
        title: 'The Hunter',
        description: 'You\'re driven by financial rewards and immediate results. You excel under pressure and love aggressive targets.',
        icon: <Target />,
        color: '#f44336',
        traits: ['Results-driven', 'Money-motivated', 'Pressure-performer', 'Goal-crusher']
      },
      relationship_builder: {
        title: 'The Relationship Builder',
        description: 'You focus on long-term client relationships and team collaboration. Trust and satisfaction drive your success.',
        icon: <Heart />,
        color: '#2196f3',
        traits: ['Relationship-focused', 'Trust-builder', 'Collaborative', 'Client-centric']
      },
      analyst: {
        title: 'The Analyst',
        description: 'You leverage data insights and process optimization. Performance metrics and continuous improvement guide your approach.',
        icon: <Brain />,
        color: '#4caf50',
        traits: ['Data-driven', 'Process-oriented', 'Analytical', 'Improvement-focused']
      }
    };
    return descriptions[archetype as keyof typeof descriptions];
  };

  const renderWelcomeStep = () => (
    <Card sx={{ maxWidth: 600, mx: 'auto', textAlign: 'center' }}>
      <CardContent sx={{ p: 4 }}>
        <Brain size={64} color="#9c27b0" style={{ marginBottom: 16 }} />
        <Typography variant="h4" gutterBottom>
          🎯 Discover Your Sales Superpower
        </Typography>
        <Typography variant="h6" color="text.secondary" gutterBottom>
          2-minute psychological quiz to unlock your personalized sales intelligence
        </Typography>
        
        <Box sx={{ my: 3, p: 2, bgcolor: 'primary.50', borderRadius: 2 }}>
          <Typography variant="body1" sx={{ mb: 2 }}>
            <strong>Join 2,847 sales reps already crushing their targets!</strong>
          </Typography>
          <Grid container spacing={2} sx={{ textAlign: 'center' }}>
            <Grid item xs={4}>
              <Trophy color="#ffc107" />
              <Typography variant="caption" display="block">Instant Profile</Typography>
            </Grid>
            <Grid item xs={4}>
              <Target color="#ff5722" />
              <Typography variant="caption" display="block">Smart Targets</Typography>
            </Grid>
            <Grid item xs={4}>
              <Zap color="#ff9800" />
              <Typography variant="caption" display="block">Live Dashboard</Typography>
            </Grid>
          </Grid>
        </Box>

        <Button 
          variant="contained" 
          size="large" 
          onClick={() => setCurrentStep(1)}
          endIcon={<ArrowRight />}
          sx={{ mt: 2, py: 1.5, px: 4 }}
        >
          Start Your Journey
        </Button>
        
        <Typography variant="caption" display="block" sx={{ mt: 2, color: 'text.secondary' }}>
          ✨ Instant access • No credit card required • Scientific assessment
        </Typography>
      </CardContent>
    </Card>
  );

  const renderPersonalInfoStep = () => (
    <Card sx={{ maxWidth: 600, mx: 'auto' }}>
      <CardHeader title="Tell Us About Yourself" />
      <CardContent>
        <Grid container spacing={2}>
          <Grid item xs={6}>
            <TextField
              fullWidth
              label="First Name"
              value={personalInfo.firstName}
              onChange={(e) => setPersonalInfo(prev => ({ ...prev, firstName: e.target.value }))}
              required
            />
          </Grid>
          <Grid item xs={6}>
            <TextField
              fullWidth
              label="Last Name"
              value={personalInfo.lastName}
              onChange={(e) => setPersonalInfo(prev => ({ ...prev, lastName: e.target.value }))}
              required
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Email"
              type="email"
              value={personalInfo.email}
              onChange={(e) => setPersonalInfo(prev => ({ ...prev, email: e.target.value }))}
              required
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Phone (Optional)"
              value={personalInfo.phone}
              onChange={(e) => setPersonalInfo(prev => ({ ...prev, phone: e.target.value }))}
            />
          </Grid>
        </Grid>
        
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
          <Button onClick={() => setCurrentStep(0)}>Back</Button>
          <Button 
            variant="contained" 
            onClick={() => setCurrentStep(2)}
            disabled={!personalInfo.firstName || !personalInfo.lastName || !personalInfo.email}
          >
            Continue to Quiz
          </Button>
        </Box>
      </CardContent>
    </Card>
  );

  const renderQuizStep = () => (
    <Card sx={{ maxWidth: 800, mx: 'auto' }}>
      <CardHeader 
        title={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Brain />
            Sales Psychology Assessment
          </Box>
        }
        subheader={
          <LinearProgress 
            variant="determinate" 
            value={(quizResponses.length / psychologyQuestions.length) * 100} 
            sx={{ mt: 1 }}
          />
        }
      />
      <CardContent>
        {psychologyQuestions.map((question, index) => (
          <Box key={question.id} sx={{ mb: 4 }}>
            <Typography variant="h6" gutterBottom>
              {index + 1}. {question.question}
            </Typography>
            <RadioGroup
              value={quizResponses.find(r => r.question_id === question.id)?.response || ''}
              onChange={(e) => {
                const option = question.options.find(opt => opt.value === e.target.value);
                if (option) {
                  handleQuizResponse(question.id, e.target.value, option.archetype, option.weight);
                }
              }}
            >
              {question.options.map((option) => (
                <FormControlLabel
                  key={option.value}
                  value={option.value}
                  control={<Radio />}
                  label={option.label}
                />
              ))}
            </RadioGroup>
          </Box>
        ))}
        
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
          <Button onClick={() => setCurrentStep(1)}>Back</Button>
          <Button 
            variant="contained" 
            onClick={analyzeProfile}
            disabled={quizResponses.length !== psychologyQuestions.length || loading}
          >
            {loading ? 'Analyzing...' : 'Analyze My Profile'}
          </Button>
        </Box>
      </CardContent>
    </Card>
  );

  const renderProfileResults = () => {
    if (!profile) return null;
    
    const archetypeInfo = getArchetypeDescription(profile.archetype);
    
    return (
      <Card sx={{ maxWidth: 800, mx: 'auto' }}>
        <CardContent sx={{ textAlign: 'center', p: 4 }}>
          <Box sx={{ mb: 3 }}>
            <Avatar 
              sx={{ 
                width: 80, 
                height: 80, 
                bgcolor: archetypeInfo.color, 
                mx: 'auto', 
                mb: 2,
                fontSize: '2rem'
              }}
            >
              {archetypeInfo.icon}
            </Avatar>
            <Typography variant="h4" gutterBottom>
              You are {archetypeInfo.title}!
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
              {archetypeInfo.description}
            </Typography>
          </Box>

          <Grid container spacing={2} sx={{ mb: 3 }}>
            {archetypeInfo.traits.map((trait) => (
              <Grid item xs={6} md={3} key={trait}>
                <Chip label={trait} variant="outlined" />
              </Grid>
            ))}
          </Grid>

          <Alert severity="success" sx={{ mb: 3 }}>
            <Typography variant="body1">
              🎉 <strong>Congratulations!</strong> You've unlocked your personalized sales dashboard with:
            </Typography>
            <Box component="ul" sx={{ mt: 1, pl: 2 }}>
              <li>Psychological target calibration</li>
              <li>Personalized achievement system</li>
              <li>Smart behavioral nudges</li>
              <li>Real-time motivation tracking</li>
            </Box>
          </Alert>

          <Button 
            variant="contained" 
            size="large" 
            onClick={() => setCurrentStep(4)}
            endIcon={<ArrowRight />}
            sx={{ py: 1.5, px: 4 }}
          >
            Access Your Dashboard
          </Button>
        </CardContent>
      </Card>
    );
  };

  const renderDashboardAccess = () => (
    <Card sx={{ maxWidth: 600, mx: 'auto', textAlign: 'center' }}>
      <CardContent sx={{ p: 4 }}>
        <CheckCircle size={64} color="#4caf50" style={{ marginBottom: 16 }} />
        <Typography variant="h4" gutterBottom>
          Welcome to Your Sales Command Center!
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          Your personalized dashboard is ready. Start setting targets and tracking your psychological performance.
        </Typography>
        
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={4}>
            <Box sx={{ p: 2, border: '1px solid', borderColor: 'divider', borderRadius: 2 }}>
              <Target color="#ff5722" />
              <Typography variant="caption" display="block">5 Quotes/Month</Typography>
            </Box>
          </Grid>
          <Grid item xs={4}>
            <Box sx={{ p: 2, border: '1px solid', borderColor: 'divider', borderRadius: 2 }}>
              <Trophy color="#ffc107" />
              <Typography variant="caption" display="block">Achievement System</Typography>
            </Box>
          </Grid>
          <Grid item xs={4}>
            <Box sx={{ p: 2, border: '1px solid', borderColor: 'divider', borderRadius: 2 }}>
              <TrendingUp color="#4caf50" />
              <Typography variant="caption" display="block">Performance Analytics</Typography>
            </Box>
          </Grid>
        </Grid>

        <Button 
          variant="contained" 
          size="large" 
          href="/sales-rep-centre"
          sx={{ py: 1.5, px: 4 }}
        >
          Enter Dashboard
        </Button>
      </CardContent>
    </Card>
  );

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'grey.50', py: 4 }}>
      <Box sx={{ maxWidth: 1200, mx: 'auto', px: 2 }}>
        <Stepper activeStep={currentStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {currentStep === 0 && renderWelcomeStep()}
        {currentStep === 1 && renderPersonalInfoStep()}
        {currentStep === 2 && renderQuizStep()}
        {currentStep === 3 && renderProfileResults()}
        {currentStep === 4 && renderDashboardAccess()}
      </Box>
    </Box>
  );
};

export default SalesRepSelfOnboarding;
