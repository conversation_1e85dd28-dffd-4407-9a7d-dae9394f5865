import React, { useState, useEffect } from 'react';
import { 
  Search, 
  Users, 
  MapPin, 
  Star, 
  Filter,
  Send,
  Clock,
  CheckCircle,
  AlertCircle,
  Building,
  Award,
  TrendingUp
} from 'lucide-react';
import { 
  <PERSON>, 
  CardContent, 
  CardHeader, 
  Button,
  Badge,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Alert,
  Tabs,
  Tab,
  Box,
  Typography,
  Grid,
  Avatar,
  LinearProgress
} from '@mui/material';

interface Supplier {
  supplier_id: string;
  company_name: string;
  specializations: string[];
  location: {
    city: string;
    province: string;
  };
  rating: number;
  bbbee_level: number;
  certifications: string[];
  capacity: {
    monthly_volume: number;
    project_size: string;
  };
  match_score: number;
  distance_km: number;
  response_time_avg: string;
  quote_acceptance_rate: number;
}

interface QuoteRequest {
  supplier_id: string;
  category: string;
  specifications: string;
  quantity: number;
  delivery_location: string;
  delivery_deadline: string;
  budget_range: {
    min_budget: number;
    max_budget: number;
  };
  special_requirements: string[];
}

const ContractorSupplierAccess: React.FC = () => {
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [searchCriteria, setSearchCriteria] = useState({
    category: '',
    location: '',
    max_distance: 50,
    min_bbbee_level: 1,
    min_rating: 0
  });
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null);
  const [quoteRequest, setQuoteRequest] = useState<Partial<QuoteRequest>>({});
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const contractorId = 'current-contractor-id'; // Would get from auth context

  const handleSearch = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/contractor/${contractorId}/find-suppliers`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(searchCriteria)
      });

      if (response.ok) {
        const data = await response.json();
        setSuppliers(data.suppliers || []);
        setActiveTab(1);
      } else {
        setError('Failed to search suppliers');
      }
    } catch (err) {
      setError('Search failed');
      console.error('Search failed:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleRequestQuote = async () => {
    if (!selectedSupplier) return;

    try {
      setLoading(true);
      const request = {
        ...quoteRequest,
        supplier_id: selectedSupplier.supplier_id
      };
      
      const response = await fetch(`/api/contractor/${contractorId}/request-quote`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request)
      });

      if (response.ok) {
        setActiveTab(3);
        setSelectedSupplier(null);
        setQuoteRequest({});
      } else {
        setError('Failed to request quote');
      }
    } catch (err) {
      setError('Quote request failed');
      console.error('Quote request failed:', err);
    } finally {
      setLoading(false);
    }
  };

  const getBBBEEColor = (level: number) => {
    if (level <= 2) return { color: '#4caf50', bg: '#e8f5e8' };
    if (level <= 4) return { color: '#2196f3', bg: '#e3f2fd' };
    if (level <= 6) return { color: '#ff9800', bg: '#fff3e0' };
    return { color: '#757575', bg: '#f5f5f5' };
  };

  const getMatchScoreColor = (score: number) => {
    if (score >= 80) return '#4caf50';
    if (score >= 60) return '#ff9800';
    return '#f44336';
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  return (
    <Box sx={{ maxWidth: 1400, mx: 'auto', p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h3" component="h1" gutterBottom>
            Supplier Network Access
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Find and connect with verified suppliers for your projects
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Chip 
            icon={<Users />} 
            label="Premium Access" 
            variant="outlined" 
            sx={{ bgcolor: 'primary.50' }}
          />
          <Chip 
            icon={<CheckCircle />} 
            label="25 Quotes/Month" 
            variant="outlined" 
            sx={{ bgcolor: 'success.50' }}
          />
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)} sx={{ mb: 3 }}>
        <Tab label="Search Suppliers" />
        <Tab label={`Results (${suppliers.length})`} />
        <Tab label="Request Quote" />
        <Tab label="My Requests" />
      </Tabs>

      {/* Search Tab */}
      {activeTab === 0 && (
        <Card>
          <CardHeader
            title={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Search />
                Find Suppliers
              </Box>
            }
          />
          <CardContent>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6} lg={4}>
                <FormControl fullWidth>
                  <InputLabel>Category</InputLabel>
                  <Select
                    value={searchCriteria.category}
                    onChange={(e) => setSearchCriteria(prev => ({ ...prev, category: e.target.value }))}
                  >
                    <MenuItem value="construction">Construction Materials</MenuItem>
                    <MenuItem value="electrical">Electrical Supplies</MenuItem>
                    <MenuItem value="plumbing">Plumbing Supplies</MenuItem>
                    <MenuItem value="equipment">Equipment Rental</MenuItem>
                    <MenuItem value="services">Professional Services</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6} lg={4}>
                <TextField
                  fullWidth
                  label="Location"
                  placeholder="City or Province"
                  value={searchCriteria.location}
                  onChange={(e) => setSearchCriteria(prev => ({ ...prev, location: e.target.value }))}
                />
              </Grid>

              <Grid item xs={12} md={6} lg={4}>
                <FormControl fullWidth>
                  <InputLabel>Max Distance</InputLabel>
                  <Select
                    value={searchCriteria.max_distance}
                    onChange={(e) => setSearchCriteria(prev => ({ ...prev, max_distance: Number(e.target.value) }))}
                  >
                    <MenuItem value={25}>25 km</MenuItem>
                    <MenuItem value={50}>50 km</MenuItem>
                    <MenuItem value={100}>100 km</MenuItem>
                    <MenuItem value={250}>250 km</MenuItem>
                    <MenuItem value={500}>500 km</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6} lg={4}>
                <FormControl fullWidth>
                  <InputLabel>Min B-BBEE Level</InputLabel>
                  <Select
                    value={searchCriteria.min_bbbee_level}
                    onChange={(e) => setSearchCriteria(prev => ({ ...prev, min_bbbee_level: Number(e.target.value) }))}
                  >
                    <MenuItem value={1}>Level 1</MenuItem>
                    <MenuItem value={2}>Level 2</MenuItem>
                    <MenuItem value={3}>Level 3</MenuItem>
                    <MenuItem value={4}>Level 4</MenuItem>
                    <MenuItem value={8}>Any Level</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6} lg={4}>
                <FormControl fullWidth>
                  <InputLabel>Min Rating</InputLabel>
                  <Select
                    value={searchCriteria.min_rating}
                    onChange={(e) => setSearchCriteria(prev => ({ ...prev, min_rating: Number(e.target.value) }))}
                  >
                    <MenuItem value={0}>Any Rating</MenuItem>
                    <MenuItem value={3}>3+ Stars</MenuItem>
                    <MenuItem value={4}>4+ Stars</MenuItem>
                    <MenuItem value={4.5}>4.5+ Stars</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>

            <Button 
              variant="contained" 
              onClick={handleSearch} 
              disabled={loading || !searchCriteria.category} 
              sx={{ mt: 3, py: 1.5, px: 4 }}
              startIcon={<Search />}
            >
              {loading ? 'Searching...' : 'Find Suppliers'}
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Results Tab */}
      {activeTab === 1 && (
        <Box>
          {suppliers.length === 0 ? (
            <Card>
              <CardContent sx={{ textAlign: 'center', py: 8 }}>
                <Search size={48} color="#bdbdbd" style={{ marginBottom: 16 }} />
                <Typography variant="h6" color="text.primary" gutterBottom>
                  No suppliers found
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Try adjusting your search criteria
                </Typography>
              </CardContent>
            </Card>
          ) : (
            <Grid container spacing={3}>
              {suppliers.map((supplier) => (
                <Grid item xs={12} lg={6} key={supplier.supplier_id}>
                  <Card sx={{ height: '100%', '&:hover': { boxShadow: 4 } }}>
                    <CardContent sx={{ p: 3 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start', mb: 2 }}>
                        <Box>
                          <Typography variant="h6" gutterBottom>
                            {supplier.company_name}
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                            <MapPin size={16} color="#757575" />
                            <Typography variant="body2" color="text.secondary">
                              {supplier.location.city}, {supplier.location.province} ({supplier.distance_km}km)
                            </Typography>
                          </Box>
                        </Box>
                        <Box sx={{ textAlign: 'right' }}>
                          <Typography 
                            variant="h6" 
                            sx={{ color: getMatchScoreColor(supplier.match_score), fontWeight: 'bold' }}
                          >
                            {supplier.match_score}% Match
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            <Star size={16} color="#ffc107" fill="#ffc107" />
                            <Typography variant="body2" fontWeight="medium">
                              {supplier.rating}
                            </Typography>
                          </Box>
                        </Box>
                      </Box>

                      <Box sx={{ mb: 2 }}>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 2 }}>
                          {supplier.specializations.slice(0, 3).map((spec) => (
                            <Chip key={spec} label={spec} size="small" variant="outlined" />
                          ))}
                        </Box>
                      </Box>

                      <Grid container spacing={2} sx={{ mb: 2 }}>
                        <Grid item xs={6}>
                          <Typography variant="caption" color="text.secondary">B-BBEE Level:</Typography>
                          <Chip 
                            label={`Level ${supplier.bbbee_level}`}
                            size="small"
                            sx={{ 
                              ml: 1,
                              ...getBBBEEColor(supplier.bbbee_level)
                            }}
                          />
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="caption" color="text.secondary">Response Time:</Typography>
                          <Typography variant="body2" fontWeight="medium" component="span" sx={{ ml: 1 }}>
                            {supplier.response_time_avg}
                          </Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="caption" color="text.secondary">Acceptance Rate:</Typography>
                          <Typography variant="body2" fontWeight="medium" component="span" sx={{ ml: 1 }}>
                            {supplier.quote_acceptance_rate}%
                          </Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="caption" color="text.secondary">Capacity:</Typography>
                          <Typography variant="body2" fontWeight="medium" component="span" sx={{ ml: 1 }}>
                            {supplier.capacity.project_size}
                          </Typography>
                        </Grid>
                      </Grid>

                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Button 
                          variant="contained" 
                          size="small" 
                          sx={{ flex: 1 }}
                          startIcon={<Send />}
                          onClick={() => {
                            setSelectedSupplier(supplier);
                            setActiveTab(2);
                          }}
                        >
                          Request Quote
                        </Button>
                        <Button variant="outlined" size="small">
                          View Profile
                        </Button>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          )}
        </Box>
      )}

      {/* Quote Request Tab */}
      {activeTab === 2 && (
        <Box>
          {selectedSupplier ? (
            <Card>
              <CardHeader
                title={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Send />
                    Request Quote from {selectedSupplier.company_name}
                  </Box>
                }
              />
              <CardContent>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Category"
                      value={quoteRequest.category || ''}
                      onChange={(e) => setQuoteRequest(prev => ({ ...prev, category: e.target.value }))}
                      placeholder="e.g., Construction Materials"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Quantity"
                      type="number"
                      value={quoteRequest.quantity || ''}
                      onChange={(e) => setQuoteRequest(prev => ({ ...prev, quantity: parseInt(e.target.value) }))}
                      placeholder="e.g., 100"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      multiline
                      rows={4}
                      label="Specifications"
                      value={quoteRequest.specifications || ''}
                      onChange={(e) => setQuoteRequest(prev => ({ ...prev, specifications: e.target.value }))}
                      placeholder="Detailed specifications and requirements..."
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Delivery Location"
                      value={quoteRequest.delivery_location || ''}
                      onChange={(e) => setQuoteRequest(prev => ({ ...prev, delivery_location: e.target.value }))}
                      placeholder="Delivery address"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Delivery Deadline"
                      type="date"
                      value={quoteRequest.delivery_deadline || ''}
                      onChange={(e) => setQuoteRequest(prev => ({ ...prev, delivery_deadline: e.target.value }))}
                      InputLabelProps={{ shrink: true }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Min Budget (ZAR)"
                      type="number"
                      value={quoteRequest.budget_range?.min_budget || ''}
                      onChange={(e) => setQuoteRequest(prev => ({ 
                        ...prev, 
                        budget_range: { 
                          min_budget: parseInt(e.target.value),
                          max_budget: prev.budget_range?.max_budget || 0
                        } 
                      }))}
                      placeholder="Minimum budget"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Max Budget (ZAR)"
                      type="number"
                      value={quoteRequest.budget_range?.max_budget || ''}
                      onChange={(e) => setQuoteRequest(prev => ({ 
                        ...prev, 
                        budget_range: { 
                          min_budget: prev.budget_range?.min_budget || 0,
                          max_budget: parseInt(e.target.value)
                        } 
                      }))}
                      placeholder="Maximum budget"
                    />
                  </Grid>
                </Grid>

                <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
                  <Button 
                    variant="contained" 
                    onClick={handleRequestQuote} 
                    disabled={loading}
                    sx={{ flex: 1 }}
                    startIcon={<Send />}
                  >
                    {loading ? 'Sending...' : 'Send Quote Request'}
                  </Button>
                  <Button variant="outlined" onClick={() => setSelectedSupplier(null)}>
                    Cancel
                  </Button>
                </Box>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent sx={{ textAlign: 'center', py: 8 }}>
                <Send size={48} color="#bdbdbd" style={{ marginBottom: 16 }} />
                <Typography variant="h6" color="text.primary" gutterBottom>
                  No supplier selected
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Select a supplier from the results to request a quote
                </Typography>
              </CardContent>
            </Card>
          )}
        </Box>
      )}

      {/* My Requests Tab */}
      {activeTab === 3 && (
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 8 }}>
            <Clock size={48} color="#bdbdbd" style={{ marginBottom: 16 }} />
            <Typography variant="h6" color="text.primary" gutterBottom>
              Quote Requests
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Your quote requests will appear here
            </Typography>
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default ContractorSupplierAccess;
