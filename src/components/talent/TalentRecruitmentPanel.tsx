'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Grid,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Rating,
  LinearProgress,
  Tabs,
  Tab,
  Badge,
  IconButton,
  Tooltip,
  Divider
} from '@mui/material';
import {
  PersonSearch as TalentIcon,
  Add as AddIcon,
  Star as StarIcon,
  Schedule as ScheduleIcon,
  AttachMoney as MoneyIcon,
  Work as WorkIcon,
  Send as SendIcon,
  Visibility as ViewIcon,
  Message as MessageIcon,
  CheckCircle as SelectIcon,
  TrendingUp as TrendingUpIcon
} from '@mui/icons-material';

import { TenderTalentRequest, TalentApplication } from '../../types/skillsyncIntegration';
import { TeamActiveWorkspace } from '../../types/teamCollaboration';
import CrossPlatformIntegrationService from '../../services/CrossPlatformIntegrationService';

interface TalentRecruitmentPanelProps {
  teamWorkspace: TeamActiveWorkspace;
  onTalentHired?: (application: TalentApplication) => void;
}

export default function TalentRecruitmentPanel({ 
  teamWorkspace, 
  onTalentHired 
}: TalentRecruitmentPanelProps) {
  const [activeTab, setActiveTab] = useState(0);
  const [showPostDialog, setShowPostDialog] = useState(false);
  const [talentRequests, setTalentRequests] = useState<TenderTalentRequest[]>([]);
  const [selectedRequest, setSelectedRequest] = useState<TenderTalentRequest | null>(null);
  const [applications, setApplications] = useState<TalentApplication[]>([]);
  const [loading, setLoading] = useState(false);

  const integrationService = CrossPlatformIntegrationService.getInstance();

  useEffect(() => {
    loadTalentRequests();
  }, [teamWorkspace]);

  const loadTalentRequests = async () => {
    try {
      setLoading(true);
      // In real implementation, would fetch from API
      // For now, showing mock data
      setTalentRequests([]);
    } catch (error) {
      console.error('Error loading talent requests:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePostTalentRequest = async (requestData: any) => {
    try {
      setLoading(true);
      
      const talentRequest = await integrationService.postTalentRequest(
        teamWorkspace.teamBidInterest.organizationId,
        teamWorkspace.teamBidInterest.tenderId,
        {
          tenderTitle: teamWorkspace.tender.title || 'Municipal Infrastructure Project',
          tenderReference: teamWorkspace.tender.reference || 'TND-2024-001',
          requiredSkills: requestData.skills,
          projectDescription: requestData.description,
          responsibilities: requestData.responsibilities,
          budgetRange: requestData.budget,
          timeline: requestData.timeline,
          urgency: requestData.urgency
        }
      );

      setTalentRequests(prev => [...prev, talentRequest]);
      setShowPostDialog(false);
      
    } catch (error) {
      console.error('Error posting talent request:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleViewApplications = async (talentRequest: TenderTalentRequest) => {
    try {
      setLoading(true);
      setSelectedRequest(talentRequest);
      
      const apps = await integrationService.getTalentApplications(talentRequest.id);
      setApplications(apps);
      setActiveTab(1);
      
    } catch (error) {
      console.error('Error loading applications:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectCandidate = async (application: TalentApplication) => {
    try {
      setLoading(true);
      
      const result = await integrationService.selectCandidate(
        application.talentRequestId,
        application.id,
        {
          startDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          finalRate: application.proposedRate,
          projectTerms: 'Standard project terms and conditions'
        }
      );

      onTalentHired?.(result.application);
      
      // Refresh applications
      if (selectedRequest) {
        await handleViewApplications(selectedRequest);
      }
      
    } catch (error) {
      console.error('Error selecting candidate:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'success';
      case 'receiving_applications': return 'info';
      case 'reviewing': return 'warning';
      case 'candidate_selected': return 'primary';
      default: return 'default';
    }
  };

  const getApplicationStatusColor = (status: string) => {
    switch (status) {
      case 'submitted': return 'info';
      case 'shortlisted': return 'warning';
      case 'selected': return 'success';
      case 'rejected': return 'error';
      default: return 'default';
    }
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 3, borderBottom: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <TalentIcon sx={{ fontSize: 32, color: 'primary.main' }} />
            <Box>
              <Typography variant="h5" sx={{ fontWeight: 600 }}>
                🎯 SkillSync Talent Marketplace
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Recruit specialized talent for {teamWorkspace.tender.title}
              </Typography>
            </Box>
          </Box>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setShowPostDialog(true)}
            disabled={loading}
          >
            Post Talent Request
          </Button>
        </Box>

        {/* Tabs */}
        <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
          <Tab 
            label={
              <Badge badgeContent={talentRequests.length} color="primary">
                My Talent Requests
              </Badge>
            } 
          />
          <Tab 
            label={
              <Badge badgeContent={applications.length} color="secondary">
                Applications
              </Badge>
            } 
            disabled={!selectedRequest}
          />
          <Tab label="Hired Talent" />
          <Tab label="Analytics" />
        </Tabs>
      </Box>

      {/* Content */}
      <Box sx={{ flex: 1, overflow: 'auto', p: 3 }}>
        {loading && <LinearProgress sx={{ mb: 2 }} />}

        {/* Tab 0: Talent Requests */}
        {activeTab === 0 && (
          <Box>
            {talentRequests.length === 0 ? (
              <Card elevation={1}>
                <CardContent sx={{ textAlign: 'center', py: 6 }}>
                  <TalentIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    No Talent Requests Yet
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    Post your first talent request to find specialized experts from SkillSync
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => setShowPostDialog(true)}
                  >
                    Post Your First Request
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <Grid container spacing={3}>
                {talentRequests.map((request) => (
                  <Grid item xs={12} md={6} key={request.id}>
                    <Card elevation={2}>
                      <CardContent>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                          <Box>
                            <Typography variant="h6" sx={{ fontWeight: 600 }}>
                              {request.tenderTitle}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {request.tenderReference}
                            </Typography>
                          </Box>
                          <Chip 
                            label={request.status.replace('_', ' ').toUpperCase()}
                            color={getStatusColor(request.status) as any}
                            size="small"
                          />
                        </Box>

                        <Typography variant="body2" sx={{ mb: 2 }}>
                          {request.projectDescription}
                        </Typography>

                        <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
                          {request.requiredSkills.slice(0, 3).map((skill, index) => (
                            <Chip key={index} label={skill.skill} size="small" variant="outlined" />
                          ))}
                          {request.requiredSkills.length > 3 && (
                            <Chip label={`+${request.requiredSkills.length - 3} more`} size="small" />
                          )}
                        </Box>

                        <Grid container spacing={2} sx={{ mb: 2 }}>
                          <Grid item xs={6}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <MoneyIcon fontSize="small" color="primary" />
                              <Typography variant="body2">
                                {formatCurrency(request.budgetRange.min)} - {formatCurrency(request.budgetRange.max)}
                              </Typography>
                            </Box>
                          </Grid>
                          <Grid item xs={6}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <ScheduleIcon fontSize="small" color="primary" />
                              <Typography variant="body2">
                                {request.projectDuration} days
                              </Typography>
                            </Box>
                          </Grid>
                        </Grid>

                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Typography variant="body2" color="text.secondary">
                            {request.applications.length} applications
                          </Typography>
                          <Button
                            variant="outlined"
                            size="small"
                            startIcon={<ViewIcon />}
                            onClick={() => handleViewApplications(request)}
                          >
                            View Applications
                          </Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}
          </Box>
        )}

        {/* Tab 1: Applications */}
        {activeTab === 1 && selectedRequest && (
          <Box>
            <Alert severity="info" sx={{ mb: 3 }}>
              <Typography variant="body2">
                Viewing applications for: <strong>{selectedRequest.tenderTitle}</strong>
              </Typography>
            </Alert>

            {applications.length === 0 ? (
              <Card elevation={1}>
                <CardContent sx={{ textAlign: 'center', py: 6 }}>
                  <WorkIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    No Applications Yet
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Your talent request is live on SkillSync. Applications will appear here.
                  </Typography>
                </CardContent>
              </Card>
            ) : (
              <Grid container spacing={3}>
                {applications.map((application) => (
                  <Grid item xs={12} key={application.id}>
                    <Card elevation={2}>
                      <CardContent>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            <Avatar sx={{ width: 56, height: 56 }}>
                              {application.applicantId.charAt(0).toUpperCase()}
                            </Avatar>
                            <Box>
                              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                                SkillSync Professional
                              </Typography>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Rating value={4.8} precision={0.1} size="small" readOnly />
                                <Typography variant="body2" color="text.secondary">
                                  4.8 (127 reviews)
                                </Typography>
                              </Box>
                            </Box>
                          </Box>
                          <Box sx={{ textAlign: 'right' }}>
                            <Chip 
                              label={application.status.replace('_', ' ').toUpperCase()}
                              color={getApplicationStatusColor(application.status) as any}
                              size="small"
                            />
                            <Typography variant="h6" sx={{ fontWeight: 600, mt: 1 }}>
                              {formatCurrency(application.proposedRate)}/hour
                            </Typography>
                          </Box>
                        </Box>

                        <Typography variant="body2" sx={{ mb: 2 }}>
                          {application.coverLetter}
                        </Typography>

                        <Grid container spacing={2} sx={{ mb: 2 }}>
                          <Grid item xs={12} sm={3}>
                            <Typography variant="caption" color="text.secondary">
                              Skills Match
                            </Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <LinearProgress 
                                variant="determinate" 
                                value={application.skillsMatch} 
                                sx={{ flex: 1, height: 8, borderRadius: 4 }}
                                color="success"
                              />
                              <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                {application.skillsMatch}%
                              </Typography>
                            </Box>
                          </Grid>
                          <Grid item xs={12} sm={3}>
                            <Typography variant="caption" color="text.secondary">
                              Experience
                            </Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <LinearProgress 
                                variant="determinate" 
                                value={application.experienceMatch} 
                                sx={{ flex: 1, height: 8, borderRadius: 4 }}
                                color="info"
                              />
                              <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                {application.experienceMatch}%
                              </Typography>
                            </Box>
                          </Grid>
                          <Grid item xs={12} sm={3}>
                            <Typography variant="caption" color="text.secondary">
                              Budget Fit
                            </Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <LinearProgress 
                                variant="determinate" 
                                value={application.budgetFit} 
                                sx={{ flex: 1, height: 8, borderRadius: 4 }}
                                color="warning"
                              />
                              <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                {application.budgetFit}%
                              </Typography>
                            </Box>
                          </Grid>
                          <Grid item xs={12} sm={3}>
                            <Typography variant="caption" color="text.secondary">
                              Overall Score
                            </Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <LinearProgress 
                                variant="determinate" 
                                value={application.overallScore} 
                                sx={{ flex: 1, height: 8, borderRadius: 4 }}
                                color="primary"
                              />
                              <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                {application.overallScore}%
                              </Typography>
                            </Box>
                          </Grid>
                        </Grid>

                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Typography variant="body2" color="text.secondary">
                            Applied {new Date(application.submittedAt).toLocaleDateString()}
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <Tooltip title="Send Message">
                              <IconButton size="small">
                                <MessageIcon />
                              </IconButton>
                            </Tooltip>
                            <Button
                              variant="contained"
                              size="small"
                              startIcon={<SelectIcon />}
                              onClick={() => handleSelectCandidate(application)}
                              disabled={application.status !== 'submitted' || loading}
                            >
                              Select Candidate
                            </Button>
                          </Box>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}
          </Box>
        )}

        {/* Tab 2: Hired Talent */}
        {activeTab === 2 && (
          <Card elevation={1}>
            <CardContent sx={{ textAlign: 'center', py: 6 }}>
              <StarIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                Hired Talent
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Your hired SkillSync professionals will appear here
              </Typography>
            </CardContent>
          </Card>
        )}

        {/* Tab 3: Analytics */}
        {activeTab === 3 && (
          <Card elevation={1}>
            <CardContent sx={{ textAlign: 'center', py: 6 }}>
              <TrendingUpIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                Talent Analytics
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Analytics and insights about your talent recruitment will appear here
              </Typography>
            </CardContent>
          </Card>
        )}
      </Box>

      {/* Post Talent Request Dialog */}
      <TalentRequestDialog
        open={showPostDialog}
        onClose={() => setShowPostDialog(false)}
        onSubmit={handlePostTalentRequest}
        tenderInfo={{
          title: teamWorkspace.tender.title || 'Municipal Infrastructure Project',
          reference: teamWorkspace.tender.reference || 'TND-2024-001'
        }}
      />
    </Box>
  );
}

// Talent Request Dialog Component
interface TalentRequestDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
  tenderInfo: { title: string; reference: string };
}

function TalentRequestDialog({ open, onClose, onSubmit, tenderInfo }: TalentRequestDialogProps) {
  const [formData, setFormData] = useState({
    description: '',
    skills: [],
    responsibilities: [],
    budget: { min: 10000, max: 50000 },
    urgency: 'medium' as 'low' | 'medium' | 'high' | 'urgent'
  });

  const handleSubmit = () => {
    onSubmit(formData);
    setFormData({
      description: '',
      skills: [],
      responsibilities: [],
      budget: { min: 10000, max: 50000 },
      urgency: 'medium'
    });
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Typography variant="h6">Post Talent Request</Typography>
        <Typography variant="body2" color="text.secondary">
          {tenderInfo.title} ({tenderInfo.reference})
        </Typography>
      </DialogTitle>
      <DialogContent>
        <Grid container spacing={3} sx={{ mt: 1 }}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              multiline
              rows={4}
              label="Project Description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Describe what you need help with for this tender..."
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              type="number"
              label="Minimum Budget (ZAR)"
              value={formData.budget.min}
              onChange={(e) => setFormData(prev => ({ 
                ...prev, 
                budget: { ...prev.budget, min: parseInt(e.target.value) }
              }))}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              type="number"
              label="Maximum Budget (ZAR)"
              value={formData.budget.max}
              onChange={(e) => setFormData(prev => ({ 
                ...prev, 
                budget: { ...prev.budget, max: parseInt(e.target.value) }
              }))}
            />
          </Grid>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel>Urgency</InputLabel>
              <Select
                value={formData.urgency}
                onChange={(e) => setFormData(prev => ({ ...prev, urgency: e.target.value as any }))}
                label="Urgency"
              >
                <MenuItem value="low">Low - Standard timeline</MenuItem>
                <MenuItem value="medium">Medium - Moderate urgency</MenuItem>
                <MenuItem value="high">High - Urgent need</MenuItem>
                <MenuItem value="urgent">Urgent - Critical timeline</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button 
          variant="contained" 
          onClick={handleSubmit}
          startIcon={<SendIcon />}
          disabled={!formData.description.trim()}
        >
          Post to SkillSync
        </Button>
      </DialogActions>
    </Dialog>
  );
}
