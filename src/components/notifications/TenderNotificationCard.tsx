'use client';

import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  Chip,
  Box,
  Grid,
  IconButton,
  Collapse,
  Alert,
  LinearProgress,
  Stack,
  Divider
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  LocationOn as LocationIcon,
  Schedule as ScheduleIcon,
  AttachMoney as MoneyIcon,
  Business as BusinessIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  Star as StarIcon,
  Close as CloseIcon,
  AccessTime as TimeIcon,
  People as PeopleIcon
} from '@mui/icons-material';

import { EnhancedNotification } from '../../types/bidWorkflow';
import BidWorkflowService from '../../services/BidWorkflowService';

interface TenderNotificationCardProps {
  notification: EnhancedNotification;
  onInterestExpressed?: (tenderId: string) => void;
  onDismissed?: (tenderId: string) => void;
}

export default function TenderNotificationCard({
  notification,
  onInterestExpressed,
  onDismissed
}: TenderNotificationCardProps) {
  const [expanded, setExpanded] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [dismissed, setDismissed] = useState(false);

  const workflowService = BidWorkflowService.getInstance();

  const handleInterestedInBid = async () => {
    try {
      setProcessing(true);
      
      // Express interest through workflow service
      await workflowService.expressInterest(notification.tenderId, 'user-123'); // Mock user ID
      
      // Notify parent component
      if (onInterestExpressed) {
        onInterestExpressed(notification.tenderId);
      }
      
      console.log(`🎯 Expressed interest in tender: ${notification.title}`);
    } catch (error) {
      console.error('Error expressing interest:', error);
    } finally {
      setProcessing(false);
    }
  };

  const handleDismiss = async () => {
    try {
      await workflowService.dismissNotification(notification.tenderId);
      setDismissed(true);
      
      if (onDismissed) {
        onDismissed(notification.tenderId);
      }
    } catch (error) {
      console.error('Error dismissing notification:', error);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'critical': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  const getDaysRemaining = () => {
    const closing = new Date(notification.closingDate);
    const now = new Date();
    const diffTime = closing.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  if (dismissed) {
    return null;
  }

  const daysRemaining = getDaysRemaining();

  return (
    <Card 
      elevation={2} 
      sx={{ 
        mb: 2, 
        border: notification.urgencyLevel === 'critical' ? 2 : 1,
        borderColor: notification.urgencyLevel === 'critical' ? 'error.main' : 'divider',
        position: 'relative',
        overflow: 'visible'
      }}
    >
      {/* Urgency Badge */}
      {notification.urgencyLevel === 'critical' && (
        <Chip
          label="URGENT"
          color="error"
          size="small"
          sx={{
            position: 'absolute',
            top: -8,
            right: 16,
            fontWeight: 600,
            zIndex: 1
          }}
        />
      )}

      <CardContent sx={{ pb: 1 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Box sx={{ flex: 1 }}>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
              {notification.title}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {notification.organization}
            </Typography>
          </Box>
          <IconButton size="small" onClick={handleDismiss}>
            <CloseIcon />
          </IconButton>
        </Box>

        {/* Key Metrics */}
        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={6} sm={3}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <MoneyIcon fontSize="small" color="success" />
              <Typography variant="body2" sx={{ fontWeight: 600 }}>
                {formatCurrency(notification.estimatedValue)}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <LocationIcon fontSize="small" color="info" />
              <Typography variant="body2">
                {notification.location}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <ScheduleIcon fontSize="small" color={daysRemaining <= 3 ? 'error' : 'warning'} />
              <Typography 
                variant="body2" 
                color={daysRemaining <= 3 ? 'error.main' : 'text.primary'}
                sx={{ fontWeight: daysRemaining <= 3 ? 600 : 400 }}
              >
                {daysRemaining} days left
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <TrendingUpIcon fontSize="small" color="primary" />
              <Typography variant="body2" color="primary.main" sx={{ fontWeight: 600 }}>
                {notification.matchScore}% match
              </Typography>
            </Box>
          </Grid>
        </Grid>

        {/* Psychological Triggers */}
        <Stack direction="row" spacing={1} sx={{ mb: 2, flexWrap: 'wrap', gap: 1 }}>
          <Chip
            icon={<WarningIcon />}
            label={notification.psychologicalTriggers.scarcity}
            size="small"
            color="warning"
            variant="outlined"
          />
          <Chip
            icon={<PeopleIcon />}
            label={notification.psychologicalTriggers.urgency}
            size="small"
            color="info"
            variant="outlined"
          />
          <Chip
            icon={<StarIcon />}
            label={notification.psychologicalTriggers.social_proof}
            size="small"
            color="success"
            variant="outlined"
          />
        </Stack>

        {/* Expandable Details */}
        <Collapse in={expanded} timeout="auto" unmountOnExit>
          <Divider sx={{ my: 2 }} />
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            {notification.description}
          </Typography>
          
          <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
            Key Requirements:
          </Typography>
          <Box sx={{ mb: 2 }}>
            {notification.requirements.slice(0, 3).map((req, index) => (
              <Typography key={index} variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                • {req}
              </Typography>
            ))}
            {notification.requirements.length > 3 && (
              <Typography variant="body2" color="text.secondary">
                ... and {notification.requirements.length - 3} more requirements
              </Typography>
            )}
          </Box>

          <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
            Documents Available: {notification.documents.length}
          </Typography>
        </Collapse>
      </CardContent>

      <CardActions sx={{ px: 2, pb: 2, pt: 0 }}>
        <Grid container spacing={1}>
          <Grid item xs={12} sm={6}>
            <Button
              variant="contained"
              fullWidth
              startIcon={<StarIcon />}
              onClick={handleInterestedInBid}
              disabled={processing}
              sx={{
                bgcolor: 'primary.main',
                '&:hover': { bgcolor: 'primary.dark' },
                fontWeight: 600,
                py: 1
              }}
            >
              {processing ? (
                <>
                  <LinearProgress size={20} sx={{ mr: 1 }} />
                  Processing...
                </>
              ) : (
                '🎯 INTERESTED IN BID'
              )}
            </Button>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Button
              variant="outlined"
              fullWidth
              size="small"
              onClick={() => setExpanded(!expanded)}
              endIcon={
                <ExpandMoreIcon 
                  sx={{ 
                    transform: expanded ? 'rotate(180deg)' : 'rotate(0deg)',
                    transition: 'transform 0.3s'
                  }} 
                />
              }
            >
              {expanded ? 'Less' : 'Details'}
            </Button>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Button
              variant="text"
              fullWidth
              size="small"
              onClick={() => alert('Remind me later')}
            >
              Later
            </Button>
          </Grid>
        </Grid>
      </CardActions>

      {/* Processing Overlay */}
      {processing && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            bgcolor: 'rgba(255, 255, 255, 0.8)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 2
          }}
        >
          <Alert severity="info" sx={{ minWidth: 300 }}>
            <Typography variant="body2">
              🎯 Moving to your active bids workspace...
            </Typography>
          </Alert>
        </Box>
      )}
    </Card>
  );
}
