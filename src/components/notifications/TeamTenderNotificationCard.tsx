'use client';

import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  Chip,
  Box,
  Grid,
  IconButton,
  Collapse,
  Alert,
  LinearProgress,
  Stack,
  Divider,
  Avatar,
  AvatarGroup,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  FormControlLabel,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  LocationOn as LocationIcon,
  Schedule as ScheduleIcon,
  AttachMoney as MoneyIcon,
  Business as BusinessIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  Star as StarIcon,
  Close as CloseIcon,
  AccessTime as TimeIcon,
  People as PeopleIcon,
  Group as GroupIcon,
  Approval as ApprovalIcon,
  Assignment as AssignmentIcon,
  CheckCircle as CheckIcon
} from '@mui/icons-material';

import { EnhancedNotification } from '../../types/bidWorkflow';
import { TeamMember, UserRole } from '../../types/teamCollaboration';
import TeamCollaborationService from '../../services/TeamCollaborationService';

interface TeamTenderNotificationCardProps {
  notification: EnhancedNotification;
  teamMembers: TeamMember[];
  organizationId: string;
  currentUserId: string;
  currentUserRole: UserRole;
  onTeamInterestExpressed?: (tenderId: string) => void;
  onDismissed?: (tenderId: string) => void;
}

export default function TeamTenderNotificationCard({
  notification,
  teamMembers,
  organizationId,
  currentUserId,
  currentUserRole,
  onTeamInterestExpressed,
  onDismissed
}: TeamTenderNotificationCardProps) {
  const [expanded, setExpanded] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [dismissed, setDismissed] = useState(false);
  const [showTeamAssignmentDialog, setShowTeamAssignmentDialog] = useState(false);
  const [selectedTeamMembers, setSelectedTeamMembers] = useState<string[]>([]);
  const [requiresApproval, setRequiresApproval] = useState(true);

  const teamService = TeamCollaborationService.getInstance();

  const handleTeamInterestedInBid = async () => {
    try {
      setProcessing(true);
      
      // Express team interest through collaboration service
      await teamService.expressTeamInterest(
        notification.tenderId, 
        organizationId, 
        currentUserId
      );
      
      // Notify parent component
      if (onTeamInterestExpressed) {
        onTeamInterestExpressed(notification.tenderId);
      }
      
      console.log(`🎯 Team expressed interest in tender: ${notification.title}`);
    } catch (error) {
      console.error('Error expressing team interest:', error);
    } finally {
      setProcessing(false);
    }
  };

  const handleShowTeamAssignment = () => {
    setShowTeamAssignmentDialog(true);
  };

  const handleTeamAssignmentSubmit = async () => {
    try {
      setProcessing(true);
      
      // First express interest
      const workspace = await teamService.expressTeamInterest(
        notification.tenderId, 
        organizationId, 
        currentUserId
      );
      
      // Then assign selected team members
      const memberAssignments = selectedTeamMembers.map(memberId => ({
        memberId,
        responsibilities: ['Initial tender review', 'Collaborative analysis']
      }));
      
      await teamService.assignTeamMembers(workspace.teamBidInterest.id, memberAssignments);
      
      setShowTeamAssignmentDialog(false);
      
      if (onTeamInterestExpressed) {
        onTeamInterestExpressed(notification.tenderId);
      }
      
      console.log(`👥 Team interest expressed with ${selectedTeamMembers.length} members assigned`);
    } catch (error) {
      console.error('Error with team assignment:', error);
    } finally {
      setProcessing(false);
    }
  };

  const handleDismiss = async () => {
    try {
      setDismissed(true);
      
      if (onDismissed) {
        onDismissed(notification.tenderId);
      }
    } catch (error) {
      console.error('Error dismissing notification:', error);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const getDaysRemaining = () => {
    const closing = new Date(notification.closingDate);
    const now = new Date();
    const diffTime = closing.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const canExpressInterest = () => {
    // Check if user has permission to express interest for the team
    return ['owner', 'admin', 'project_manager', 'business_dev'].includes(currentUserRole);
  };

  const getRelevantTeamMembers = () => {
    // Filter team members based on tender category and requirements
    return teamMembers.filter(member => 
      member.status === 'active' && 
      (member.specializations.some(spec => 
        notification.category.toLowerCase().includes(spec.toLowerCase())
      ) || ['project_manager', 'estimator', 'technical_lead'].includes(member.role))
    );
  };

  if (dismissed) {
    return null;
  }

  const daysRemaining = getDaysRemaining();
  const relevantMembers = getRelevantTeamMembers();

  return (
    <>
      <Card 
        elevation={2} 
        sx={{ 
          mb: 2, 
          border: notification.urgencyLevel === 'critical' ? 2 : 1,
          borderColor: notification.urgencyLevel === 'critical' ? 'error.main' : 'divider',
          position: 'relative',
          overflow: 'visible'
        }}
      >
        {/* Team Badge */}
        <Chip
          label="TEAM OPPORTUNITY"
          color="primary"
          size="small"
          sx={{
            position: 'absolute',
            top: -8,
            left: 16,
            fontWeight: 600,
            zIndex: 1
          }}
        />

        {/* Urgency Badge */}
        {notification.urgencyLevel === 'critical' && (
          <Chip
            label="URGENT"
            color="error"
            size="small"
            sx={{
              position: 'absolute',
              top: -8,
              right: 16,
              fontWeight: 600,
              zIndex: 1
            }}
          />
        )}

        <CardContent sx={{ pb: 1, pt: 3 }}>
          {/* Header */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
            <Box sx={{ flex: 1 }}>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
                {notification.title}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {notification.organization}
              </Typography>
            </Box>
            <IconButton size="small" onClick={handleDismiss}>
              <CloseIcon />
            </IconButton>
          </Box>

          {/* Key Metrics */}
          <Grid container spacing={2} sx={{ mb: 2 }}>
            <Grid item xs={6} sm={3}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <MoneyIcon fontSize="small" color="success" />
                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                  {formatCurrency(notification.estimatedValue)}
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <LocationIcon fontSize="small" color="info" />
                <Typography variant="body2">
                  {notification.location}
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <ScheduleIcon fontSize="small" color={daysRemaining <= 3 ? 'error' : 'warning'} />
                <Typography 
                  variant="body2" 
                  color={daysRemaining <= 3 ? 'error.main' : 'text.primary'}
                  sx={{ fontWeight: daysRemaining <= 3 ? 600 : 400 }}
                >
                  {daysRemaining} days left
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <TrendingUpIcon fontSize="small" color="primary" />
                <Typography variant="body2" color="primary.main" sx={{ fontWeight: 600 }}>
                  {notification.matchScore}% match
                </Typography>
              </Box>
            </Grid>
          </Grid>

          {/* Team Relevance */}
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              Relevant Team Members ({relevantMembers.length}):
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <AvatarGroup max={4} sx={{ '& .MuiAvatar-root': { width: 28, height: 28, fontSize: '0.875rem' } }}>
                {relevantMembers.slice(0, 4).map((member) => (
                  <Avatar key={member.id} title={member.title}>
                    {member.title.charAt(0)}
                  </Avatar>
                ))}
              </AvatarGroup>
              {relevantMembers.length > 4 && (
                <Typography variant="caption" color="text.secondary">
                  +{relevantMembers.length - 4} more
                </Typography>
              )}
            </Box>
          </Box>

          {/* Psychological Triggers */}
          <Stack direction="row" spacing={1} sx={{ mb: 2, flexWrap: 'wrap', gap: 1 }}>
            <Chip
              icon={<WarningIcon />}
              label={notification.psychologicalTriggers.scarcity}
              size="small"
              color="warning"
              variant="outlined"
            />
            <Chip
              icon={<PeopleIcon />}
              label={notification.psychologicalTriggers.urgency}
              size="small"
              color="info"
              variant="outlined"
            />
            <Chip
              icon={<GroupIcon />}
              label={`${teamMembers.length} team members available`}
              size="small"
              color="primary"
              variant="outlined"
            />
          </Stack>

          {/* Expandable Details */}
          <Collapse in={expanded} timeout="auto" unmountOnExit>
            <Divider sx={{ my: 2 }} />
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              {notification.description}
            </Typography>
            
            <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
              Key Requirements:
            </Typography>
            <Box sx={{ mb: 2 }}>
              {notification.requirements.slice(0, 3).map((req, index) => (
                <Typography key={index} variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                  • {req}
                </Typography>
              ))}
              {notification.requirements.length > 3 && (
                <Typography variant="body2" color="text.secondary">
                  ... and {notification.requirements.length - 3} more requirements
                </Typography>
              )}
            </Box>

            <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
              Team Collaboration Features:
            </Typography>
            <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap', mb: 2 }}>
              <Chip label="Multi-user Analysis" size="small" variant="outlined" />
              <Chip label="Role-based Tasks" size="small" variant="outlined" />
              <Chip label="Approval Workflow" size="small" variant="outlined" />
              <Chip label="Real-time Chat" size="small" variant="outlined" />
            </Box>
          </Collapse>
        </CardContent>

        <CardActions sx={{ px: 2, pb: 2, pt: 0 }}>
          <Grid container spacing={1}>
            <Grid item xs={12} sm={canExpressInterest() ? 6 : 8}>
              <Button
                variant="contained"
                fullWidth
                startIcon={<GroupIcon />}
                onClick={canExpressInterest() ? handleTeamInterestedInBid : undefined}
                disabled={processing || !canExpressInterest()}
                sx={{
                  bgcolor: 'primary.main',
                  '&:hover': { bgcolor: 'primary.dark' },
                  fontWeight: 600,
                  py: 1
                }}
              >
                {processing ? (
                  <>
                    <LinearProgress size={20} sx={{ mr: 1 }} />
                    Processing...
                  </>
                ) : canExpressInterest() ? (
                  '👥 TEAM INTERESTED IN BID'
                ) : (
                  '🔒 REQUIRES MANAGER APPROVAL'
                )}
              </Button>
            </Grid>
            {canExpressInterest() && (
              <Grid item xs={12} sm={3}>
                <Button
                  variant="outlined"
                  fullWidth
                  size="small"
                  startIcon={<AssignmentIcon />}
                  onClick={handleShowTeamAssignment}
                  disabled={processing}
                >
                  Assign Team
                </Button>
              </Grid>
            )}
            <Grid item xs={6} sm={canExpressInterest() ? 2 : 2}>
              <Button
                variant="outlined"
                fullWidth
                size="small"
                onClick={() => setExpanded(!expanded)}
                endIcon={
                  <ExpandMoreIcon 
                    sx={{ 
                      transform: expanded ? 'rotate(180deg)' : 'rotate(0deg)',
                      transition: 'transform 0.3s'
                    }} 
                  />
                }
              >
                {expanded ? 'Less' : 'Details'}
              </Button>
            </Grid>
            <Grid item xs={6} sm={2}>
              <Button
                variant="text"
                fullWidth
                size="small"
                onClick={() => alert('Remind team later')}
              >
                Later
              </Button>
            </Grid>
          </Grid>
        </CardActions>

        {/* Processing Overlay */}
        {processing && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              bgcolor: 'rgba(255, 255, 255, 0.8)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 2
            }}
          >
            <Alert severity="info" sx={{ minWidth: 300 }}>
              <Typography variant="body2">
                👥 Creating team workspace...
              </Typography>
            </Alert>
          </Box>
        )}
      </Card>

      {/* Team Assignment Dialog */}
      <Dialog 
        open={showTeamAssignmentDialog} 
        onClose={() => setShowTeamAssignmentDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <GroupIcon color="primary" />
            <Typography variant="h6">Assign Team Members</Typography>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Select team members to assign to this tender opportunity. They will be notified and can collaborate on the bid preparation.
          </Typography>
          
          <FormControlLabel
            control={
              <Checkbox
                checked={requiresApproval}
                onChange={(e) => setRequiresApproval(e.target.checked)}
              />
            }
            label="Require approval workflow before proceeding"
            sx={{ mb: 2 }}
          />

          <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
            Recommended Team Members:
          </Typography>
          <List>
            {relevantMembers.map((member) => (
              <ListItem key={member.id} dense>
                <ListItemIcon>
                  <Checkbox
                    checked={selectedTeamMembers.includes(member.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedTeamMembers([...selectedTeamMembers, member.id]);
                      } else {
                        setSelectedTeamMembers(selectedTeamMembers.filter(id => id !== member.id));
                      }
                    }}
                  />
                </ListItemIcon>
                <ListItemText
                  primary={member.title}
                  secondary={
                    <Box>
                      <Chip 
                        label={member.role.replace('_', ' ').toUpperCase()} 
                        size="small" 
                        variant="outlined" 
                        sx={{ mr: 1 }}
                      />
                      {member.specializations.slice(0, 2).map(spec => (
                        <Chip key={spec} label={spec} size="small" variant="outlined" sx={{ mr: 0.5 }} />
                      ))}
                    </Box>
                  }
                />
              </ListItem>
            ))}
          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowTeamAssignmentDialog(false)}>
            Cancel
          </Button>
          <Button 
            variant="contained" 
            onClick={handleTeamAssignmentSubmit}
            disabled={selectedTeamMembers.length === 0}
            startIcon={<CheckIcon />}
          >
            Express Interest & Assign Team ({selectedTeamMembers.length})
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
