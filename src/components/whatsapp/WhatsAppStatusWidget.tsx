import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>Content,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Button,
  <PERSON>,
  Alert,
  <PERSON><PERSON>,
  IconButton,
  Tooltip,
  LinearProgress,
  Grid,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  WhatsApp,
  Settings,
  CheckCircle,
  Warning,
  Error,
  Refresh,
  AutoAwesome,
  Notifications,
  Speed
} from '@mui/icons-material';
import {
  useGetWhatsAppStatusQuery,
  useToggleAutoBidMutation
} from '../../services/api/whatsapp.api';

interface WhatsAppStatusWidgetProps {
  userId: string;
  onConfigureSettings?: () => void;
}

interface WhatsAppStatus {
  is_connected: boolean;
  is_verified: boolean;
  auto_bid_enabled: boolean;
  messages_processed: number;
  auto_bids_triggered: number;
  last_activity: string;
  connection_quality: 'excellent' | 'good' | 'poor' | 'disconnected';
  pending_messages: number;
  settings: {
    auto_bid_preference: 'always' | 'ask_first' | 'never' | 'conditions_based';
    max_bid_value: number;
    working_hours_only: boolean;
  };
}

const WhatsAppStatusWidget: React.FC<WhatsAppStatusWidgetProps> = ({
  userId,
  onConfigureSettings
}) => {
  // Use RTK Query hooks for real API data
  const {
    data: status,
    isLoading: loading,
    error,
    refetch
  } = useGetWhatsAppStatusQuery({ userId });

  const [toggleAutoBid] = useToggleAutoBidMutation();

  const loadWhatsAppStatus = () => {
    refetch();
  };

  const getConnectionIcon = () => {
    if (!status) return <Warning color="warning" />;
    
    if (!status.is_connected) return <Error color="error" />;
    if (status.connection_quality === 'excellent') return <CheckCircle color="success" />;
    if (status.connection_quality === 'good') return <CheckCircle color="info" />;
    return <Warning color="warning" />;
  };

  const getConnectionColor = () => {
    if (!status?.is_connected) return 'error';
    if (status.connection_quality === 'excellent') return 'success';
    if (status.connection_quality === 'good') return 'info';
    return 'warning';
  };

  const getAutoBidStatusText = () => {
    if (!status?.auto_bid_enabled) return 'Disabled';
    
    switch (status.settings.auto_bid_preference) {
      case 'always': return 'Auto-bid Always';
      case 'ask_first': return 'Ask First';
      case 'never': return 'Notifications Only';
      case 'conditions_based': return 'Conditions Based';
      default: return 'Unknown';
    }
  };

  const handleToggleAutoBid = async () => {
    if (!status) return;

    try {
      await toggleAutoBid({
        userId,
        enabled: !status.auto_bid_enabled
      }).unwrap();
      // The query will automatically refetch and update the UI
    } catch (error) {
      console.error('Failed to toggle auto-bid:', error);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent>
          <LinearProgress />
          <Typography variant="body2" sx={{ mt: 1, textAlign: 'center' }}>
            Loading WhatsApp status...
          </Typography>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert severity="error">
        Failed to load WhatsApp status. Please try again.
      </Alert>
    );
  }

  if (!status) {
    return (
      <Alert severity="info">
        WhatsApp integration not configured. Set up WhatsApp auto-bidding to get started.
      </Alert>
    );
  }

  return (
    <Card>
      <CardContent>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <WhatsApp color="success" />
            <Typography variant="h6" fontWeight="bold">
              WhatsApp Auto-Bidding
            </Typography>
          </Box>
          <Stack direction="row" spacing={1}>
            <IconButton size="small" onClick={loadWhatsAppStatus}>
              <Refresh />
            </IconButton>
            {onConfigureSettings && (
              <IconButton size="small" onClick={onConfigureSettings}>
                <Settings />
              </IconButton>
            )}
          </Stack>
        </Box>

        {/* Connection Status */}
        <Box sx={{ mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
            {getConnectionIcon()}
            <Typography variant="body2" fontWeight="bold">
              Connection Status
            </Typography>
            <Chip 
              label={status.is_connected ? 'Connected' : 'Disconnected'}
              color={getConnectionColor() as any}
              size="small"
            />
          </Box>
          
          {status.is_connected && (
            <Typography variant="caption" color="text.secondary">
              Quality: {status.connection_quality} • Last activity: {status.last_activity}
            </Typography>
          )}
        </Box>

        {/* Auto-Bid Settings */}
        <Box sx={{ mb: 2 }}>
          <FormControlLabel
            control={
            <Switch
              checked={status.auto_bid_enabled}
              onChange={handleToggleAutoBid}
              color="primary"
            />
            }
            label={
              <Box>
                <Typography variant="body2" fontWeight="bold">
                  Auto-Bidding
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {getAutoBidStatusText()}
                </Typography>
              </Box>
            }
          />
        </Box>

        {/* Statistics */}
        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={6}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" fontWeight="bold" color="primary">
                {status.messages_processed}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Messages Processed
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" fontWeight="bold" color="success.main">
                {status.auto_bids_triggered}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Auto-Bids Triggered
              </Typography>
            </Box>
          </Grid>
        </Grid>

        {/* Pending Messages Alert */}
        {status.pending_messages > 0 && (
          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="body2">
              {status.pending_messages} pending message{status.pending_messages > 1 ? 's' : ''} waiting for processing
            </Typography>
          </Alert>
        )}

        {/* Quick Settings */}
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" fontWeight="bold" sx={{ mb: 1 }}>
            Quick Settings
          </Typography>
          <Stack spacing={1}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="body2">Max Bid Value:</Typography>
              <Chip 
                label={`R${((status.settings?.max_bid_value || 0) / 1000000).toFixed(1)}M`}
                size="small"
                variant="outlined"
              />
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="body2">Working Hours Only:</Typography>
              <Chip 
                label={status.settings.working_hours_only ? 'Yes' : 'No'}
                size="small"
                color={status.settings.working_hours_only ? 'success' : 'default'}
                variant="outlined"
              />
            </Box>
          </Stack>
        </Box>

        {/* Action Buttons */}
        <Stack direction="row" spacing={2}>
          {onConfigureSettings && (
            <Button
              variant="outlined"
              startIcon={<Settings />}
              onClick={onConfigureSettings}
              size="small"
              sx={{ flex: 1 }}
            >
              Configure
            </Button>
          )}
          <Button
            variant="contained"
            startIcon={<AutoAwesome />}
            size="small"
            sx={{ flex: 1 }}
            disabled={!status.is_connected}
          >
            Test Auto-Bid
          </Button>
        </Stack>

        {/* Status Indicators */}
        <Box sx={{ mt: 2, pt: 2, borderTop: '1px solid #e0e0e0' }}>
          <Stack direction="row" spacing={2} justifyContent="center">
            <Tooltip title="Connection Quality">
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <Speed fontSize="small" color={getConnectionColor() as any} />
                <Typography variant="caption">
                  {status.connection_quality}
                </Typography>
              </Box>
            </Tooltip>
            
            <Tooltip title="Auto-Bid Status">
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <AutoAwesome fontSize="small" color={status.auto_bid_enabled ? 'primary' : 'disabled'} />
                <Typography variant="caption">
                  {status.auto_bid_enabled ? 'Active' : 'Inactive'}
                </Typography>
              </Box>
            </Tooltip>
            
            <Tooltip title="Notifications">
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <Notifications fontSize="small" color="info" />
                <Typography variant="caption">
                  {status.pending_messages}
                </Typography>
              </Box>
            </Tooltip>
          </Stack>
        </Box>
      </CardContent>
    </Card>
  );
};

export default WhatsAppStatusWidget;
