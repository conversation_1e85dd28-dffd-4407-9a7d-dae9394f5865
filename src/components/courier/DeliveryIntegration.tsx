/**
 * Delivery Integration Component
 * Seamlessly integrates courier dispatch into the bid workflow
 */

import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Alert,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Divider
} from '@mui/material';
import {
  LocalShipping as CourierIcon,
  Flight as AirIcon,
  DirectionsCar as BeeIcon,
  Schedule as TimeIcon,
  AttachMoney as CostIcon,
  CheckCircle as CheckIcon,
  LocationOn as LocationIcon
} from '@mui/icons-material';
import CourierDispatchEngine, {
  DeliveryMode,
  DeliveryPriority,
  DeliveryAddress,
  DeliveryEstimate
} from '../../services/CourierDispatchEngine';
import { Tender } from '../../types/tender.types';

interface DeliveryIntegrationProps {
  tender: Tender;
  onDeliveryScheduled?: (deliveryId: string) => void;
  autoShow?: boolean;
}

const DeliveryIntegration: React.FC<DeliveryIntegrationProps> = ({
  tender,
  onDeliveryScheduled,
  autoShow = false
}) => {
  const [showDialog, setShowDialog] = useState(autoShow);
  const [activeStep, setActiveStep] = useState(0);
  const [deliveryEstimates, setDeliveryEstimates] = useState<DeliveryEstimate[]>([]);
  const [selectedEstimate, setSelectedEstimate] = useState<DeliveryEstimate | null>(null);
  const [deliveryDetails, setDeliveryDetails] = useState({
    pickupAddress: '',
    pickupContact: '',
    pickupPhone: '',
    deliveryAddress: '',
    deliveryContact: '',
    deliveryPhone: '',
    deadline: '',
    documentType: 'Tender Documents',
    specialInstructions: ''
  });

  const courierEngine = CourierDispatchEngine.getInstance();

  const handleCalculateEstimates = async () => {
    try {
      // Mock delivery addresses for demo
      const pickupAddress: DeliveryAddress = {
        street: deliveryDetails.pickupAddress,
        city: 'Cape Town',
        province: 'Western Cape',
        postalCode: '8001',
        country: 'South Africa',
        coordinates: { latitude: -33.9249, longitude: 18.4241 },
        contactPerson: deliveryDetails.pickupContact,
        contactPhone: deliveryDetails.pickupPhone
      };

      const deliveryAddress: DeliveryAddress = {
        street: deliveryDetails.deliveryAddress,
        city: tender.location || 'Johannesburg',
        province: 'Gauteng',
        postalCode: '2000',
        country: 'South Africa',
        coordinates: { latitude: -26.2041, longitude: 28.0473 },
        contactPerson: deliveryDetails.deliveryContact,
        contactPhone: deliveryDetails.deliveryPhone
      };

      // Calculate estimates (mock implementation)
      const estimates: DeliveryEstimate[] = [
        {
          mode: DeliveryMode.BEE_DIRECT,
          estimatedTime: 4,
          estimatedCost: 350,
          reliability: 85,
          reason: 'Direct bee delivery - fastest option',
          requirements: ['Available bee in pickup area'],
          limitations: ['Weather dependent']
        },
        {
          mode: DeliveryMode.COURIER,
          estimatedTime: 24,
          estimatedCost: 180,
          reliability: 95,
          reason: 'Standard courier - most reliable',
          requirements: ['Courier service coverage'],
          limitations: ['Overnight delivery only']
        },
        {
          mode: DeliveryMode.BEE_AIR_BEE,
          estimatedTime: 6,
          estimatedCost: 450,
          reliability: 90,
          reason: 'Air delivery - fast for long distances',
          requirements: ['Airport access', 'Available bees'],
          limitations: ['Weather dependent', 'Higher cost']
        }
      ];

      setDeliveryEstimates(estimates);
      setActiveStep(1);
    } catch (error) {
      console.error('Failed to calculate delivery estimates:', error);
    }
  };

  const handleScheduleDelivery = async () => {
    if (!selectedEstimate) return;

    try {
      const pickupAddress: DeliveryAddress = {
        street: deliveryDetails.pickupAddress,
        city: 'Cape Town',
        province: 'Western Cape',
        postalCode: '8001',
        country: 'South Africa',
        coordinates: { latitude: -33.9249, longitude: 18.4241 },
        contactPerson: deliveryDetails.pickupContact,
        contactPhone: deliveryDetails.pickupPhone
      };

      const deliveryAddress: DeliveryAddress = {
        street: deliveryDetails.deliveryAddress,
        city: tender.location || 'Johannesburg',
        province: 'Gauteng',
        postalCode: '2000',
        country: 'South Africa',
        coordinates: { latitude: -26.2041, longitude: 28.0473 },
        contactPerson: deliveryDetails.deliveryContact,
        contactPhone: deliveryDetails.deliveryPhone
      };

      const courierRequest = await courierEngine.createDeliveryRequest(
        tender.id,
        'user-001', // Mock user ID
        pickupAddress,
        deliveryAddress,
        deliveryDetails.documentType,
        deliveryDetails.deadline,
        DeliveryPriority.SPEED,
        {
          specialInstructions: deliveryDetails.specialInstructions,
          requiresSignature: true,
          confidential: true
        }
      );

      setActiveStep(2);
      onDeliveryScheduled?.(courierRequest.id);
    } catch (error) {
      console.error('Failed to schedule delivery:', error);
    }
  };

  const getDeliveryModeIcon = (mode: DeliveryMode) => {
    switch (mode) {
      case DeliveryMode.BEE_DIRECT: return <BeeIcon color="warning" />;
      case DeliveryMode.COURIER: return <CourierIcon color="primary" />;
      case DeliveryMode.BEE_AIR_BEE: return <AirIcon color="info" />;
      default: return <CourierIcon />;
    }
  };

  const getDeliveryModeDescription = (mode: DeliveryMode) => {
    switch (mode) {
      case DeliveryMode.BEE_DIRECT: return 'Direct bee delivery';
      case DeliveryMode.COURIER: return 'Standard courier service';
      case DeliveryMode.BEE_AIR_BEE: return 'Air delivery with bee pickup/dropoff';
      default: return 'Unknown delivery mode';
    }
  };

  const formatTimeEstimate = (hours: number) => {
    if (hours < 1) return `${Math.round(hours * 60)} minutes`;
    if (hours < 24) return `${hours.toFixed(1)} hours`;
    return `${Math.round(hours / 24)} days`;
  };

  return (
    <>
      {/* Trigger Button */}
      <Button
        variant="outlined"
        startIcon={<CourierIcon />}
        onClick={() => setShowDialog(true)}
        sx={{ mb: 2 }}
      >
        Schedule Document Delivery
      </Button>

      {/* Delivery Scheduling Dialog */}
      <Dialog open={showDialog} onClose={() => setShowDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          📦 Schedule Document Delivery for {tender.title}
        </DialogTitle>
        
        <DialogContent>
          <Stepper activeStep={activeStep} orientation="vertical">
            {/* Step 1: Delivery Details */}
            <Step>
              <StepLabel>Delivery Details</StepLabel>
              <StepContent>
                <Grid container spacing={3} sx={{ mt: 1 }}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="h6" sx={{ mb: 2 }}>Pickup Information</Typography>
                    
                    <TextField
                      fullWidth
                      label="Pickup Address"
                      value={deliveryDetails.pickupAddress}
                      onChange={(e) => setDeliveryDetails(prev => ({ ...prev, pickupAddress: e.target.value }))}
                      sx={{ mb: 2 }}
                    />
                    
                    <TextField
                      fullWidth
                      label="Contact Person"
                      value={deliveryDetails.pickupContact}
                      onChange={(e) => setDeliveryDetails(prev => ({ ...prev, pickupContact: e.target.value }))}
                      sx={{ mb: 2 }}
                    />
                    
                    <TextField
                      fullWidth
                      label="Contact Phone"
                      value={deliveryDetails.pickupPhone}
                      onChange={(e) => setDeliveryDetails(prev => ({ ...prev, pickupPhone: e.target.value }))}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Typography variant="h6" sx={{ mb: 2 }}>Delivery Information</Typography>
                    
                    <TextField
                      fullWidth
                      label="Delivery Address"
                      value={deliveryDetails.deliveryAddress}
                      onChange={(e) => setDeliveryDetails(prev => ({ ...prev, deliveryAddress: e.target.value }))}
                      sx={{ mb: 2 }}
                    />
                    
                    <TextField
                      fullWidth
                      label="Contact Person"
                      value={deliveryDetails.deliveryContact}
                      onChange={(e) => setDeliveryDetails(prev => ({ ...prev, deliveryContact: e.target.value }))}
                      sx={{ mb: 2 }}
                    />
                    
                    <TextField
                      fullWidth
                      label="Contact Phone"
                      value={deliveryDetails.deliveryPhone}
                      onChange={(e) => setDeliveryDetails(prev => ({ ...prev, deliveryPhone: e.target.value }))}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Divider sx={{ my: 2 }} />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      type="datetime-local"
                      label="Delivery Deadline"
                      value={deliveryDetails.deadline}
                      onChange={(e) => setDeliveryDetails(prev => ({ ...prev, deadline: e.target.value }))}
                      InputLabelProps={{ shrink: true }}
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      label="Document Type"
                      value={deliveryDetails.documentType}
                      onChange={(e) => setDeliveryDetails(prev => ({ ...prev, documentType: e.target.value }))}
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      label="Special Instructions"
                      value={deliveryDetails.specialInstructions}
                      onChange={(e) => setDeliveryDetails(prev => ({ ...prev, specialInstructions: e.target.value }))}
                      placeholder="e.g., Fragile, Urgent, etc."
                    />
                  </Grid>
                </Grid>

                <Box sx={{ mt: 3 }}>
                  <Button
                    variant="contained"
                    onClick={handleCalculateEstimates}
                    disabled={!deliveryDetails.pickupAddress || !deliveryDetails.deliveryAddress || !deliveryDetails.deadline}
                  >
                    Calculate Delivery Options
                  </Button>
                </Box>
              </StepContent>
            </Step>

            {/* Step 2: Select Delivery Mode */}
            <Step>
              <StepLabel>Select Delivery Mode</StepLabel>
              <StepContent>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  Choose the best delivery option based on your requirements:
                </Typography>

                <List>
                  {deliveryEstimates.map((estimate) => (
                    <ListItem
                      key={estimate.mode}
                      button
                      selected={selectedEstimate?.mode === estimate.mode}
                      onClick={() => setSelectedEstimate(estimate)}
                      sx={{ 
                        border: '1px solid',
                        borderColor: selectedEstimate?.mode === estimate.mode ? 'primary.main' : 'divider',
                        borderRadius: 2,
                        mb: 1
                      }}
                    >
                      <ListItemIcon>
                        {getDeliveryModeIcon(estimate.mode)}
                      </ListItemIcon>
                      
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="subtitle1">
                              {getDeliveryModeDescription(estimate.mode)}
                            </Typography>
                            <Chip label={`${estimate.reliability}% reliable`} size="small" />
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                <TimeIcon fontSize="small" />
                                <Typography variant="body2">
                                  {formatTimeEstimate(estimate.estimatedTime)}
                                </Typography>
                              </Box>
                              
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                <CostIcon fontSize="small" />
                                <Typography variant="body2">
                                  R{estimate.estimatedCost.toFixed(2)}
                                </Typography>
                              </Box>
                            </Box>
                            
                            <Typography variant="caption" color="text.secondary">
                              {estimate.reason}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                </List>

                <Box sx={{ mt: 3 }}>
                  <Button
                    variant="contained"
                    onClick={handleScheduleDelivery}
                    disabled={!selectedEstimate}
                  >
                    Schedule Delivery
                  </Button>
                </Box>
              </StepContent>
            </Step>

            {/* Step 3: Confirmation */}
            <Step>
              <StepLabel>Delivery Scheduled</StepLabel>
              <StepContent>
                <Alert severity="success" sx={{ mb: 2 }}>
                  <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                    ✅ Delivery Successfully Scheduled!
                  </Typography>
                  <Typography variant="body2">
                    Your documents will be delivered using {selectedEstimate && getDeliveryModeDescription(selectedEstimate.mode)}.
                  </Typography>
                </Alert>

                {selectedEstimate && (
                  <Card sx={{ mt: 2 }}>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2 }}>Delivery Summary</Typography>
                      
                      <Grid container spacing={2}>
                        <Grid item xs={6}>
                          <Typography variant="body2" color="text.secondary">Estimated Time:</Typography>
                          <Typography variant="body1">{formatTimeEstimate(selectedEstimate.estimatedTime)}</Typography>
                        </Grid>
                        
                        <Grid item xs={6}>
                          <Typography variant="body2" color="text.secondary">Estimated Cost:</Typography>
                          <Typography variant="body1">R{selectedEstimate.estimatedCost.toFixed(2)}</Typography>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                )}
              </StepContent>
            </Step>
          </Stepper>
        </DialogContent>
        
        <DialogActions>
          <Button onClick={() => setShowDialog(false)}>
            {activeStep === 2 ? 'Close' : 'Cancel'}
          </Button>
          {activeStep === 2 && (
            <Button variant="contained" onClick={() => window.open('/courier/tracking')}>
              Track Delivery
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </>
  );
};

export default DeliveryIntegration;
