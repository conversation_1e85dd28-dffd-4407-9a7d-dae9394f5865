'use client';

import React from 'react';
import { Provider } from 'react-redux';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import { store } from '../../store/store';
import { AuthProvider } from '../../contexts/AuthContext';
import SimpleAdaptiveInterface from '../adaptive/SimpleAdaptiveInterface';

// Import ProductionInitializer
// Create sophisticated theme with dark mode support
const theme = createTheme({
  palette: {
    mode: 'dark', // Enable dark mode by default
    primary: {
      main: '#00bcd4', // Bright cyan for better visibility
      light: '#4dd0e1',
      dark: '#0097a7',
      contrastText: '#ffffff',
    },
    secondary: {
      main: '#ff4081', // Bright pink for accent
      light: '#ff79b0',
      dark: '#c60055',
      contrastText: '#ffffff',
    },
    background: {
      default: '#121212', // Dark background
      paper: '#1e1e1e', // Slightly lighter for cards
    },
    text: {
      primary: '#ffffff', // White text for maximum contrast
      secondary: '#b3b3b3', // Light gray for secondary text
    },
    success: {
      main: '#4caf50',
      contrastText: '#ffffff',
    },
    warning: {
      main: '#ff9800',
      contrastText: '#000000',
    },
    error: {
      main: '#f44336',
      contrastText: '#ffffff',
    },
    info: {
      main: '#2196f3',
      contrastText: '#ffffff',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h1: { color: '#ffffff', fontWeight: 600 },
    h2: { color: '#ffffff', fontWeight: 600 },
    h3: { color: '#ffffff', fontWeight: 600 },
    h4: { color: '#ffffff', fontWeight: 600 },
    h5: { color: '#ffffff', fontWeight: 600 },
    h6: { color: '#ffffff', fontWeight: 600 },
    body1: { color: '#ffffff' },
    body2: { color: '#b3b3b3' },
    caption: { color: '#b3b3b3' },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 8,
          fontWeight: 600,
          '&.MuiButton-contained': {
            color: '#ffffff',
            boxShadow: '0 4px 12px rgba(0,188,212,0.3)',
          },
          '&.MuiButton-outlined': {
            borderColor: '#00bcd4',
            color: '#00bcd4',
            '&:hover': {
              backgroundColor: 'rgba(0,188,212,0.1)',
            },
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0 4px 20px rgba(0,0,0,0.3)',
          backgroundColor: '#1e1e1e',
          border: '1px solid #333333',
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          color: '#ffffff',
          fontWeight: 500,
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          color: '#ffffff',
          borderBottom: '1px solid #333333',
        },
        head: {
          color: '#00bcd4',
          fontWeight: 600,
        },
      },
    },
    MuiAlert: {
      styleOverrides: {
        root: {
          color: '#ffffff',
          '& .MuiAlert-icon': {
            color: 'inherit',
          },
        },
      },
    },
    MuiAccordion: {
      styleOverrides: {
        root: {
          backgroundColor: '#1e1e1e',
          color: '#ffffff',
          '&:before': {
            display: 'none',
          },
        },
      },
    },
    MuiAccordionSummary: {
      styleOverrides: {
        root: {
          backgroundColor: '#2a2a2a',
          color: '#ffffff',
          '& .MuiAccordionSummary-content': {
            color: '#ffffff',
          },
          '& .MuiSvgIcon-root': {
            color: '#ffffff',
          },
        },
      },
    },
    MuiAccordionDetails: {
      styleOverrides: {
        root: {
          backgroundColor: '#1e1e1e',
          color: '#ffffff',
        },
      },
    },
    MuiListItemText: {
      styleOverrides: {
        primary: {
          color: '#ffffff',
          fontWeight: 500,
        },
        secondary: {
          color: '#b3b3b3',
        },
      },
    },
    MuiTypography: {
      styleOverrides: {
        root: {
          color: '#ffffff',
        },
        h1: { color: '#ffffff', fontWeight: 600 },
        h2: { color: '#ffffff', fontWeight: 600 },
        h3: { color: '#ffffff', fontWeight: 600 },
        h4: { color: '#ffffff', fontWeight: 600 },
        h5: { color: '#ffffff', fontWeight: 600 },
        h6: { color: '#ffffff', fontWeight: 600 },
        body1: { color: '#ffffff' },
        body2: { color: '#b3b3b3' },
        caption: { color: '#b3b3b3' },
        subtitle1: { color: '#ffffff' },
        subtitle2: { color: '#ffffff' },
      },
    },
  },
});

interface AppProvidersProps {
  children: React.ReactNode;
}

const AppProviders: React.FC<AppProvidersProps> = ({ children }) => {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      {children}
    </ThemeProvider>
  );
};

export default AppProviders;
