'use client';

import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alog<PERSON>ctions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Box,
  Typography,
  Alert,
  Stepper,
  Step,
  StepLabel,
  Grid,
  Autocomplete,
  FormHelperText,
  LinearProgress
} from '@mui/material';
import {
  PersonAdd as PersonAddIcon,
  Email as EmailIcon,
  Work as WorkIcon,
  Assignment as AssignmentIcon,
  Send as SendIcon
} from '@mui/icons-material';

import { UserRole } from '../../types/teamCollaboration';
import { TeamMemberInvitation } from '../../types/teamOnboarding';
import TeamMemberOnboardingService from '../../services/TeamMemberOnboardingService';

interface TeamMemberInvitationDialogProps {
  open: boolean;
  onClose: () => void;
  organizationId: string;
  currentUserRole: UserRole;
  onInvitationSent?: (invitation: TeamMemberInvitation) => void;
}

const steps = ['Basic Info', 'Role & Responsibilities', 'Personal Message', 'Review & Send'];

const roleDescriptions = {
  owner: 'Full system access and organization management',
  admin: 'Team management and system administration',
  project_manager: 'Bid coordination and team leadership',
  estimator: 'Cost analysis and pricing expertise',
  technical_lead: 'Technical specifications and engineering',
  legal_counsel: 'Compliance and legal requirements',
  business_dev: 'Opportunity identification and client relations',
  finance: 'Financial analysis and budget management',
  viewer: 'Read-only access to projects and documents',
  guest: 'Limited temporary access'
};

const commonResponsibilities = {
  project_manager: [
    'Coordinate bid preparation activities',
    'Manage project timelines and milestones',
    'Lead team meetings and communication',
    'Ensure quality and compliance standards'
  ],
  estimator: [
    'Analyze tender requirements and specifications',
    'Prepare detailed cost estimates and pricing',
    'Review subcontractor and supplier quotes',
    'Validate technical and commercial proposals'
  ],
  technical_lead: [
    'Review technical specifications and drawings',
    'Provide technical expertise and guidance',
    'Coordinate with engineering consultants',
    'Ensure technical compliance and feasibility'
  ],
  legal_counsel: [
    'Review contract terms and conditions',
    'Ensure regulatory and legal compliance',
    'Advise on risk management and liability',
    'Handle legal documentation and submissions'
  ]
};

export default function TeamMemberInvitationDialog({
  open,
  onClose,
  organizationId,
  currentUserRole,
  onInvitationSent
}: TeamMemberInvitationDialogProps) {
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form data
  const [formData, setFormData] = useState({
    email: '',
    role: 'viewer' as UserRole,
    suggestedTitle: '',
    department: '',
    responsibilities: [] as string[],
    personalMessage: ''
  });

  const onboardingService = TeamMemberOnboardingService.getInstance();

  const handleNext = () => {
    if (validateCurrentStep()) {
      setActiveStep((prevStep) => prevStep + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const handleReset = () => {
    setActiveStep(0);
    setFormData({
      email: '',
      role: 'viewer',
      suggestedTitle: '',
      department: '',
      responsibilities: [],
      personalMessage: ''
    });
    setError(null);
  };

  const validateCurrentStep = (): boolean => {
    setError(null);
    
    switch (activeStep) {
      case 0: // Basic Info
        if (!formData.email || !formData.email.includes('@')) {
          setError('Please enter a valid email address');
          return false;
        }
        if (!formData.suggestedTitle.trim()) {
          setError('Please enter a suggested job title');
          return false;
        }
        break;
      case 1: // Role & Responsibilities
        if (formData.responsibilities.length === 0) {
          setError('Please add at least one responsibility');
          return false;
        }
        break;
    }
    
    return true;
  };

  const handleSendInvitation = async () => {
    if (!validateCurrentStep()) return;

    try {
      setLoading(true);
      setError(null);

      const invitation = await onboardingService.inviteTeamMember(
        organizationId,
        'current-user-id', // Would get from auth context
        {
          email: formData.email,
          role: formData.role,
          personalMessage: formData.personalMessage || undefined,
          suggestedTitle: formData.suggestedTitle,
          department: formData.department || undefined,
          responsibilities: formData.responsibilities
        }
      );

      onInvitationSent?.(invitation);
      handleReset();
      onClose();

    } catch (error) {
      console.error('Error sending invitation:', error);
      setError(error instanceof Error ? error.message : 'Failed to send invitation');
    } finally {
      setLoading(false);
    }
  };

  const addResponsibility = (responsibility: string) => {
    if (responsibility.trim() && !formData.responsibilities.includes(responsibility.trim())) {
      setFormData(prev => ({
        ...prev,
        responsibilities: [...prev.responsibilities, responsibility.trim()]
      }));
    }
  };

  const removeResponsibility = (index: number) => {
    setFormData(prev => ({
      ...prev,
      responsibilities: prev.responsibilities.filter((_, i) => i !== index)
    }));
  };

  const getAvailableRoles = (): UserRole[] => {
    // Filter roles based on current user's authority
    const allRoles: UserRole[] = ['viewer', 'guest', 'estimator', 'technical_lead', 'legal_counsel', 'business_dev', 'finance', 'project_manager'];
    
    if (currentUserRole === 'owner') {
      return [...allRoles, 'admin']; // Owner can invite admins
    }
    if (currentUserRole === 'admin') {
      return allRoles; // Admin can invite all except owner
    }
    if (currentUserRole === 'project_manager') {
      return ['viewer', 'guest', 'estimator', 'technical_lead']; // PM can invite core team roles
    }
    if (currentUserRole === 'business_dev') {
      return ['viewer', 'guest']; // Business dev can invite limited roles
    }
    
    return ['viewer']; // Default minimal access
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0: // Basic Info
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Email Address"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                placeholder="<EMAIL>"
                InputProps={{
                  startAdornment: <EmailIcon sx={{ mr: 1, color: 'text.secondary' }} />
                }}
                helperText="The person will receive an invitation email at this address"
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Suggested Job Title"
                value={formData.suggestedTitle}
                onChange={(e) => setFormData(prev => ({ ...prev, suggestedTitle: e.target.value }))}
                placeholder="Senior Project Manager"
                InputProps={{
                  startAdornment: <WorkIcon sx={{ mr: 1, color: 'text.secondary' }} />
                }}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Department (Optional)"
                value={formData.department}
                onChange={(e) => setFormData(prev => ({ ...prev, department: e.target.value }))}
                placeholder="Engineering"
              />
            </Grid>
          </Grid>
        );

      case 1: // Role & Responsibilities
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <FormControl fullWidth required>
                <InputLabel>Role</InputLabel>
                <Select
                  value={formData.role}
                  onChange={(e) => setFormData(prev => ({ ...prev, role: e.target.value as UserRole }))}
                  label="Role"
                >
                  {getAvailableRoles().map((role) => (
                    <MenuItem key={role} value={role}>
                      <Box>
                        <Typography variant="body1" sx={{ textTransform: 'capitalize' }}>
                          {role.replace('_', ' ')}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {roleDescriptions[role]}
                        </Typography>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
                <FormHelperText>
                  Select the role that best matches their responsibilities
                </FormHelperText>
              </FormControl>
            </Grid>
            
            <Grid item xs={12}>
              <Typography variant="subtitle2" sx={{ mb: 2 }}>
                Responsibilities
              </Typography>
              
              {/* Common responsibilities for selected role */}
              {commonResponsibilities[formData.role as keyof typeof commonResponsibilities] && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    Suggested responsibilities for {formData.role.replace('_', ' ')}:
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {commonResponsibilities[formData.role as keyof typeof commonResponsibilities].map((resp, index) => (
                      <Chip
                        key={index}
                        label={resp}
                        variant="outlined"
                        size="small"
                        onClick={() => addResponsibility(resp)}
                        sx={{ cursor: 'pointer' }}
                      />
                    ))}
                  </Box>
                </Box>
              )}

              {/* Custom responsibility input */}
              <Autocomplete
                freeSolo
                options={[]}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Add Responsibility"
                    placeholder="Type a responsibility and press Enter"
                    InputProps={{
                      ...params.InputProps,
                      startAdornment: <AssignmentIcon sx={{ mr: 1, color: 'text.secondary' }} />
                    }}
                  />
                )}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    const target = e.target as HTMLInputElement;
                    addResponsibility(target.value);
                    target.value = '';
                  }
                }}
              />

              {/* Selected responsibilities */}
              {formData.responsibilities.length > 0 && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    Selected responsibilities:
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {formData.responsibilities.map((resp, index) => (
                      <Chip
                        key={index}
                        label={resp}
                        onDelete={() => removeResponsibility(index)}
                        color="primary"
                        size="small"
                      />
                    ))}
                  </Box>
                </Box>
              )}
            </Grid>
          </Grid>
        );

      case 2: // Personal Message
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={6}
                label="Personal Message (Optional)"
                value={formData.personalMessage}
                onChange={(e) => setFormData(prev => ({ ...prev, personalMessage: e.target.value }))}
                placeholder="Hi [Name], we'd love to have you join our team at [Organization]. Your expertise in [area] would be a great addition to our tender preparation efforts..."
                helperText="Add a personal touch to make the invitation more welcoming"
              />
            </Grid>
          </Grid>
        );

      case 3: // Review & Send
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Alert severity="info" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  Review the invitation details below. Once sent, the recipient will receive an email with a link to accept the invitation and begin onboarding.
                </Typography>
              </Alert>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">Email</Typography>
              <Typography variant="body1">{formData.email}</Typography>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">Role</Typography>
              <Typography variant="body1" sx={{ textTransform: 'capitalize' }}>
                {formData.role.replace('_', ' ')}
              </Typography>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="text.secondary">Suggested Title</Typography>
              <Typography variant="body1">{formData.suggestedTitle}</Typography>
            </Grid>
            
            {formData.department && (
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">Department</Typography>
                <Typography variant="body1">{formData.department}</Typography>
              </Grid>
            )}
            
            <Grid item xs={12}>
              <Typography variant="subtitle2" color="text.secondary">Responsibilities</Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                {formData.responsibilities.map((resp, index) => (
                  <Chip key={index} label={resp} size="small" />
                ))}
              </Box>
            </Grid>
            
            {formData.personalMessage && (
              <Grid item xs={12}>
                <Typography variant="subtitle2" color="text.secondary">Personal Message</Typography>
                <Typography variant="body2" sx={{ mt: 1, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                  {formData.personalMessage}
                </Typography>
              </Grid>
            )}
          </Grid>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <PersonAddIcon />
          <Typography variant="h6">Invite Team Member</Typography>
        </Box>
      </DialogTitle>

      <DialogContent>
        {loading && <LinearProgress sx={{ mb: 2 }} />}
        
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {renderStepContent(activeStep)}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          Cancel
        </Button>
        
        {activeStep > 0 && (
          <Button onClick={handleBack} disabled={loading}>
            Back
          </Button>
        )}
        
        {activeStep < steps.length - 1 ? (
          <Button variant="contained" onClick={handleNext} disabled={loading}>
            Next
          </Button>
        ) : (
          <Button
            variant="contained"
            onClick={handleSendInvitation}
            disabled={loading}
            startIcon={<SendIcon />}
          >
            Send Invitation
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
}
