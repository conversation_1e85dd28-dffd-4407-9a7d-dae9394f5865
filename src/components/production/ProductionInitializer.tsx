'use client';

import React, { useEffect, useState } from 'react';
import { useFeatureFlags } from '../../hooks/useFeatureFlags';
import { PRODUCTION_FEATURE_CONFIG } from '../../../deployment/production-feature-config';

interface ProductionInitializerProps {
  children: React.ReactNode;
}

interface InitializationStatus {
  isInitialized: boolean;
  enabledFeatures: number;
  totalFeatures: number;
  error?: string;
}

const ProductionInitializer: React.FC<ProductionInitializerProps> = ({ children }) => {
  const [status, setStatus] = useState<InitializationStatus>({
    isInitialized: false,
    enabledFeatures: 0,
    totalFeatures: 0
  });

  const featureFlags = useFeatureFlags();

  const initializeProductionFeatures = React.useCallback(async () => {
    // Only initialize in production environment
    if (process.env.NODE_ENV !== 'production' && !process.env.NEXT_PUBLIC_PRODUCTION_MODE) {
      setStatus({
        isInitialized: true,
        enabledFeatures: 0,
        totalFeatures: 0
      });
      return;
    }

    try {
      console.log('🚀 Initializing BidBeez Production Features...');
      
      let enabledCount = 0;
      const totalFeatures = Object.keys(PRODUCTION_FEATURE_CONFIG).length;

      // Apply each feature configuration
      for (const [flagId, config] of Object.entries(PRODUCTION_FEATURE_CONFIG)) {
        try {
          if (featureFlags.updateFlag) {
            featureFlags.updateFlag(flagId, config);
            enabledCount++;
            console.log(`✅ Enabled: ${flagId} for ${config.userSegments?.join(', ')}`);
          }
        } catch (error) {
          console.error(`❌ Failed to enable ${flagId}:`, error);
        }
      }

      console.log(`🎉 Production Features Initialized: ${enabledCount}/${totalFeatures}`);
      
      setStatus({
        isInitialized: true,
        enabledFeatures: enabledCount,
        totalFeatures
      });

      // Log feature summary
      console.log('\n🎯 BidBeez Production Feature Summary:');
      console.log('=====================================');
      console.log('✅ Core Features: Tender Search, Bid Submission, Dashboard');
      console.log('💼 Professional: NeuroMarketing, WhatsApp Auto-Bid, Analytics');
      console.log('🛡️  Compliance: SA Compliance Tools, B-BBEE Integration');
      console.log('🏢 Enterprise: Team Features, API Access, White-label');
      console.log('🚀 BidBeez is ready to dominate the market!');

    } catch (error) {
      console.error('❌ Production initialization failed:', error);
      setStatus({
        isInitialized: true,
        enabledFeatures: 0,
        totalFeatures: Object.keys(PRODUCTION_FEATURE_CONFIG).length,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }, [featureFlags]);

  useEffect(() => {
    initializeProductionFeatures();
  }, [initializeProductionFeatures]);

  // Show loading state during initialization
  if (!status.isInitialized) {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        backgroundColor: '#f5f5f5',
        fontFamily: 'Arial, sans-serif'
      }}>
        <div style={{
          padding: '2rem',
          backgroundColor: 'white',
          borderRadius: '8px',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
          textAlign: 'center',
          maxWidth: '400px'
        }}>
          <div style={{
            width: '40px',
            height: '40px',
            border: '4px solid #f3f3f3',
            borderTop: '4px solid #007bff',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 1rem'
          }} />
          <h2 style={{ color: '#333', marginBottom: '0.5rem' }}>
            🚀 Initializing BidBeez
          </h2>
          <p style={{ color: '#666', margin: 0 }}>
            Loading premium features...
          </p>
        </div>
        <style jsx>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  // Show error state if initialization failed
  if (status.error) {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        backgroundColor: '#f5f5f5',
        fontFamily: 'Arial, sans-serif'
      }}>
        <div style={{
          padding: '2rem',
          backgroundColor: 'white',
          borderRadius: '8px',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
          textAlign: 'center',
          maxWidth: '400px',
          border: '2px solid #dc3545'
        }}>
          <h2 style={{ color: '#dc3545', marginBottom: '1rem' }}>
            ⚠️ Initialization Error
          </h2>
          <p style={{ color: '#666', marginBottom: '1rem' }}>
            {status.error}
          </p>
          <button
            onClick={() => window.location.reload()}
            style={{
              padding: '0.5rem 1rem',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Render children once initialized
  return <>{children}</>;
};

export default ProductionInitializer;
