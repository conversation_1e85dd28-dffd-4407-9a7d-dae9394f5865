'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Grid,
  Chip,
  LinearProgress,
  <PERSON><PERSON>,
  <PERSON>ert,
  Di<PERSON>r,
  <PERSON>ton,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>questQuote,
  LocationOn,
  AttachMoney,
  People,
  Timer,
  Warning,
  FlashOn,
  Psychology,
  Refresh,
  Visibility
} from '@mui/icons-material';
import { PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, ResponsiveContainer, LineChart, Line } from 'recharts';

interface MarketStatistics {
  // Total Market Numbers
  totalTenders: number;
  totalGovernmentRFQs: number;
  totalBidderRFQs: number;
  totalOpportunities: number;
  
  // Value Statistics
  totalMarketValue: number;
  averageTenderValue: number;
  averageRFQValue: number;
  
  // Activity Statistics
  activeBidders: number;
  dailyNewOpportunities: number;
  closingToday: number;
  closingThisWeek: number;
  
  // Success Rates
  tenderSuccessRate: number;
  governmentRFQSuccessRate: number;
  bidderRFQSuccessRate: number;
  
  // Geographic Distribution
  provinceDistribution: Array<{
    province: string;
    tenders: number;
    rfqs: number;
    totalValue: number;
  }>;
  
  // Category Distribution
  categoryDistribution: Array<{
    category: string;
    count: number;
    value: number;
    successRate: number;
  }>;
  
  // Time-based Trends
  weeklyTrends: Array<{
    week: string;
    tenders: number;
    rfqs: number;
    totalValue: number;
  }>;
}

interface UserEngagementStats {
  userBids: number;
  userRFQs: number;
  userSuccessRate: number;
  marketPenetration: number; // percentage of total market user is engaging with
  missedOpportunities: number;
  potentialEarnings: number;
}

const MarketStatsDashboard: React.FC = () => {
  const [marketStats, setMarketStats] = useState<MarketStatistics | null>(null);
  const [userStats, setUserStats] = useState<UserEngagementStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Mock data - in production this would come from real APIs
  useEffect(() => {
    loadMarketStatistics();
    loadUserEngagementStats();
  }, []);

  const loadMarketStatistics = async () => {
    setLoading(true);
    
    // Simulate API call with realistic South African market data
    setTimeout(() => {
      setMarketStats({
        totalTenders: 15247,
        totalGovernmentRFQs: 8934,
        totalBidderRFQs: 12456,
        totalOpportunities: 36637,
        
        totalMarketValue: 89500000000, // R89.5 billion
        averageTenderValue: 4200000,   // R4.2M
        averageRFQValue: 850000,       // R850k
        
        activeBidders: 23456,
        dailyNewOpportunities: 127,
        closingToday: 43,
        closingThisWeek: 312,
        
        tenderSuccessRate: 75,
        governmentRFQSuccessRate: 88,
        bidderRFQSuccessRate: 92,
        
        provinceDistribution: [
          { province: 'Gauteng', tenders: 4567, rfqs: 3234, totalValue: 28500000000 },
          { province: 'Western Cape', tenders: 3234, rfqs: 2456, totalValue: 19200000000 },
          { province: 'KwaZulu-Natal', tenders: 2456, rfqs: 1789, totalValue: 15600000000 },
          { province: 'Eastern Cape', tenders: 1789, rfqs: 1234, totalValue: 8900000000 },
          { province: 'Free State', tenders: 1234, rfqs: 890, totalValue: 6700000000 },
          { province: 'Limpopo', tenders: 890, rfqs: 678, totalValue: 4800000000 },
          { province: 'Mpumalanga', tenders: 678, rfqs: 567, totalValue: 3200000000 },
          { province: 'North West', tenders: 567, rfqs: 456, totalValue: 2100000000 },
          { province: 'Northern Cape', tenders: 456, rfqs: 345, totalValue: 1500000000 }
        ],
        
        categoryDistribution: [
          { category: 'Construction', count: 8934, value: 35600000000, successRate: 72 },
          { category: 'IT Services', count: 5678, value: 18900000000, successRate: 85 },
          { category: 'Professional Services', count: 4567, value: 12300000000, successRate: 78 },
          { category: 'Security Services', count: 3456, value: 8900000000, successRate: 82 },
          { category: 'Office Supplies', count: 2345, value: 4500000000, successRate: 89 },
          { category: 'Cleaning Services', count: 1890, value: 3200000000, successRate: 86 }
        ],
        
        weeklyTrends: [
          { week: 'Week 1', tenders: 1234, rfqs: 890, totalValue: 5600000000 },
          { week: 'Week 2', tenders: 1456, rfqs: 1023, totalValue: 6200000000 },
          { week: 'Week 3', tenders: 1345, rfqs: 967, totalValue: 5900000000 },
          { week: 'Week 4', tenders: 1567, rfqs: 1134, totalValue: 6800000000 }
        ]
      });
      setLoading(false);
    }, 1000);
  };

  const loadUserEngagementStats = async () => {
    // Simulate user engagement data
    setTimeout(() => {
      setUserStats({
        userBids: 23,
        userRFQs: 12,
        userSuccessRate: 78,
        marketPenetration: 0.095, // 0.095% of total market
        missedOpportunities: 1247,
        potentialEarnings: 15600000 // R15.6M
      });
    }, 1200);
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadMarketStatistics();
    await loadUserEngagementStats();
    setRefreshing(false);
  };

  const formatCurrency = (value: number) => {
    if (value >= 1000000000) return `R${(value / 1000000000).toFixed(1)}B`;
    if (value >= 1000000) return `R${(value / 1000000).toFixed(1)}M`;
    if (value >= 1000) return `R${(value / 1000).toFixed(0)}k`;
    return `R${value}`;
  };

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('en-ZA').format(value);
  };

  if (loading || !marketStats || !userStats) {
    return (
      <Box sx={{ p: 3 }}>
        <LinearProgress />
        <Typography variant="body2" sx={{ mt: 2, textAlign: 'center' }}>
          Loading South African market statistics...
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box>
          <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold' }}>
            📊 South African Tender & RFQ Market
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Real-time market intelligence and opportunity statistics
          </Typography>
        </Box>
        
        <Tooltip title="Refresh Statistics">
          <IconButton 
            onClick={handleRefresh} 
            disabled={refreshing}
            sx={{ 
              animation: refreshing ? 'spin 1s linear infinite' : 'none',
              '@keyframes spin': {
                '0%': { transform: 'rotate(0deg)' },
                '100%': { transform: 'rotate(360deg)' }
              }
            }}
          >
            <Refresh />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Market Penetration Alert - PSYCHOLOGICAL PRESSURE */}
      <Alert 
        severity="warning" 
        sx={{ mb: 3 }}
        action={
          <Button color="inherit" size="small" href="/opportunities">
            EXPLORE NOW
          </Button>
        }
      >
        <Typography variant="body2">
          <strong>⚠️ MARKET PENETRATION ALERT:</strong> You're engaging with only {(userStats.marketPenetration * 100).toFixed(3)}% 
          of the total market! You've missed {formatNumber(userStats.missedOpportunities)} opportunities worth {formatCurrency(userStats.potentialEarnings)}.
        </Typography>
      </Alert>

      {/* Total Market Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={3}>
          <Card sx={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h3" sx={{ fontWeight: 'bold' }}>
                    {formatNumber(marketStats.totalOpportunities)}
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    Total Active Opportunities
                  </Typography>
                </Box>
                <TrendingUp sx={{ fontSize: 48, opacity: 0.7 }} />
              </Box>
              <Typography variant="caption" sx={{ mt: 1, display: 'block' }}>
                Worth {formatCurrency(marketStats.totalMarketValue)} total value
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#2196F3' }}>
                    {formatNumber(marketStats.totalTenders)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Government Tenders
                  </Typography>
                </Box>
                <Gavel sx={{ fontSize: 40, color: '#2196F3' }} />
              </Box>
              <Chip 
                label={`${marketStats.tenderSuccessRate}% Success Rate`} 
                size="small" 
                color="primary" 
                sx={{ mt: 1 }}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#4CAF50' }}>
                    {formatNumber(marketStats.totalGovernmentRFQs)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Government RFQs
                  </Typography>
                </Box>
                <RequestQuote sx={{ fontSize: 40, color: '#4CAF50' }} />
              </Box>
              <Chip 
                label={`${marketStats.governmentRFQSuccessRate}% Success Rate`} 
                size="small" 
                color="success" 
                sx={{ mt: 1 }}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#FF9800' }}>
                    {formatNumber(marketStats.totalBidderRFQs)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Bidder RFQs
                  </Typography>
                </Box>
                <FlashOn sx={{ fontSize: 40, color: '#FF9800' }} />
              </Box>
              <Chip 
                label={`${marketStats.bidderRFQSuccessRate}% Success Rate`} 
                size="small" 
                sx={{ mt: 1, backgroundColor: '#FF9800', color: 'white' }}
              />
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* User vs Market Comparison - PSYCHOLOGICAL PRESSURE */}
      <Card sx={{ mb: 4, border: '2px solid #f44336' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Psychology color="error" />
            Your Market Engagement vs Total Market
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Your Active Bids: {userStats.userBids} of {formatNumber(marketStats.totalTenders)} tenders
                </Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={(userStats.userBids / marketStats.totalTenders) * 100} 
                  sx={{ mt: 1, height: 8 }}
                  color="error"
                />
                <Typography variant="caption" color="error">
                  {((userStats.userBids / marketStats.totalTenders) * 100).toFixed(3)}% market penetration
                </Typography>
              </Box>
              
              <Box>
                <Typography variant="body2" color="text.secondary">
                  Your RFQ Activities: {userStats.userRFQs} of {formatNumber(marketStats.totalGovernmentRFQs + marketStats.totalBidderRFQs)} RFQs
                </Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={(userStats.userRFQs / (marketStats.totalGovernmentRFQs + marketStats.totalBidderRFQs)) * 100} 
                  sx={{ mt: 1, height: 8 }}
                  color="warning"
                />
                <Typography variant="caption" color="warning.main">
                  {((userStats.userRFQs / (marketStats.totalGovernmentRFQs + marketStats.totalBidderRFQs)) * 100).toFixed(3)}% RFQ penetration
                </Typography>
              </Box>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Alert severity="error">
                <Typography variant="body2">
                  <strong>🚨 MASSIVE OPPORTUNITY GAP!</strong><br />
                  • You're missing {formatNumber(marketStats.totalOpportunities - userStats.userBids - userStats.userRFQs)} active opportunities<br />
                  • Potential missed earnings: {formatCurrency(userStats.potentialEarnings)}<br />
                  • {formatNumber(marketStats.activeBidders - 1)} other bidders are competing for these opportunities
                </Typography>
              </Alert>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Daily Activity Pressure */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <Card sx={{ border: '1px solid #ff9800' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Timer color="warning" />
                Today's Activity
              </Typography>
              
              <Stack spacing={2}>
                <Box>
                  <Typography variant="h4" color="warning.main" sx={{ fontWeight: 'bold' }}>
                    {marketStats.dailyNewOpportunities}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    New opportunities published today
                  </Typography>
                </Box>
                
                <Box>
                  <Typography variant="h4" color="error.main" sx={{ fontWeight: 'bold' }}>
                    {marketStats.closingToday}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Opportunities closing TODAY
                  </Typography>
                </Box>
                
                <Alert severity="warning" sx={{ mt: 2 }}>
                  <Typography variant="caption">
                    ⏰ <strong>TIME PRESSURE:</strong> {marketStats.closingThisWeek} opportunities close this week!
                  </Typography>
                </Alert>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                📈 Weekly Market Trends
              </Typography>
              
              <ResponsiveContainer width="100%" height={200}>
                <LineChart data={marketStats.weeklyTrends}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="week" />
                  <YAxis />
                  <Line type="monotone" dataKey="tenders" stroke="#2196F3" strokeWidth={2} />
                  <Line type="monotone" dataKey="rfqs" stroke="#4CAF50" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
              
              <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                Blue: Tenders | Green: RFQs | Market activity is increasing!
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Call to Action */}
      <Card sx={{ background: 'linear-gradient(45deg, #FF6B6B, #4ECDC4)', color: 'white' }}>
        <CardContent sx={{ textAlign: 'center' }}>
          <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold' }}>
            🚀 Don't Miss Out on {formatNumber(marketStats.totalOpportunities)} Active Opportunities!
          </Typography>
          <Typography variant="body1" sx={{ mb: 3, opacity: 0.9 }}>
            Join {formatNumber(marketStats.activeBidders)} active bidders competing for {formatCurrency(marketStats.totalMarketValue)} in opportunities.
            Your current market penetration is only {(userStats.marketPenetration * 100).toFixed(3)}% - there's massive room for growth!
          </Typography>
          
          <Stack direction="row" spacing={2} justifyContent="center">
            <Button 
              variant="contained" 
              size="large" 
              href="/opportunities"
              sx={{ 
                backgroundColor: 'rgba(255,255,255,0.2)', 
                color: 'white',
                fontWeight: 'bold',
                '&:hover': { backgroundColor: 'rgba(255,255,255,0.3)' }
              }}
            >
              🎯 EXPLORE ALL OPPORTUNITIES
            </Button>
            
            <Button 
              variant="outlined" 
              size="large" 
              href="/rfq/create"
              sx={{ 
                borderColor: 'rgba(255,255,255,0.5)', 
                color: 'white',
                '&:hover': { borderColor: 'white', backgroundColor: 'rgba(255,255,255,0.1)' }
              }}
            >
              ⚡ CREATE RFQ NOW
            </Button>
          </Stack>
        </CardContent>
      </Card>
    </Box>
  );
};

export default MarketStatsDashboard;
