/**
 * 🎨 Adaptive Interface Demo Component
 * Extracted from MVP for client demonstrations and psychological profiling showcases
 * Adapts UI based on user's psychological state for optimal engagement
 */

'use client';

import React from 'react';
import { Box, BoxProps } from '@mui/material';

// Psychological state interface for demo
export interface PsychologicalState {
  archetype: 'Achiever' | 'Hunter' | 'Analyst' | 'Relationship Builder';
  stressLevel: number; // 0-1
  confidenceLevel: number; // 0-1
  engagementLevel: number; // 0-1
  cognitiveLoad: number; // 0-1
  motivationFactors: string[];
  decisionMakingStyle: 'Analytical' | 'Intuitive' | 'Collaborative' | 'Decisive';
  riskTolerance: 'Low' | 'Moderate' | 'High';
}

export interface AdaptiveInterfaceDemoProps extends Omit<BoxProps, 'children'> {
  children: React.ReactNode;
  psychologicalState: PsychologicalState;
  demoMode?: boolean;
  showAdaptations?: boolean;
}

/**
 * AdaptiveInterfaceDemo Component
 * 
 * Dynamically adapts the interface based on user's psychological profile:
 * - Stress Level: Affects background color and spacing
 * - Archetype: Influences shadow depth and border radius
 * - Cognitive Load: Controls complexity of visual elements
 * - Engagement Level: Adjusts animation and interaction feedback
 */
const AdaptiveInterfaceDemo: React.FC<AdaptiveInterfaceDemoProps> = ({ 
  children, 
  psychologicalState,
  demoMode = false,
  showAdaptations = false,
  sx,
  ...boxProps
}) => {
  const getAdaptiveStyles = () => {
    const { 
      stressLevel, 
      archetype, 
      cognitiveLoad, 
      engagementLevel,
      confidenceLevel 
    } = psychologicalState;
    
    // Base styles
    let styles: any = {
      borderRadius: '8px',
      transition: 'all 0.3s ease',
      position: 'relative'
    };

    // Stress Level Adaptations
    if (stressLevel > 0.7) {
      // High stress: Calming colors, more spacing
      styles.backgroundColor = '#f8f9fa';
      styles.padding = '24px';
      styles.border = '1px solid #e3f2fd';
    } else if (stressLevel > 0.4) {
      // Medium stress: Neutral colors
      styles.backgroundColor = '#fafafa';
      styles.padding = '20px';
    } else {
      // Low stress: Clean white background
      styles.backgroundColor = '#ffffff';
      styles.padding = '16px';
    }

    // Archetype-based Shadow and Visual Depth
    switch (archetype) {
      case 'Achiever':
        // Bold, confident shadows for achievers
        styles.boxShadow = confidenceLevel > 0.7 
          ? '0 8px 24px rgba(0,0,0,0.12)' 
          : '0 4px 12px rgba(0,0,0,0.08)';
        styles.borderRadius = '12px';
        break;
      
      case 'Hunter':
        // Sharp, focused design for hunters
        styles.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
        styles.borderRadius = '6px';
        styles.border = '1px solid #e0e0e0';
        break;
      
      case 'Analyst':
        // Clean, structured design for analysts
        styles.boxShadow = '0 1px 4px rgba(0,0,0,0.05)';
        styles.borderRadius = '4px';
        styles.border = '1px solid #f0f0f0';
        break;
      
      case 'Relationship Builder':
        // Soft, welcoming design for relationship builders
        styles.boxShadow = '0 6px 16px rgba(0,0,0,0.06)';
        styles.borderRadius = '16px';
        styles.backgroundColor = stressLevel > 0.5 ? '#fef7f0' : styles.backgroundColor;
        break;
    }

    // Cognitive Load Adaptations
    if (cognitiveLoad > 0.7) {
      // High cognitive load: Simplify visual complexity
      styles.boxShadow = '0 2px 4px rgba(0,0,0,0.05)';
      styles.border = '1px solid #e8e8e8';
    }

    // Engagement Level Adaptations
    if (engagementLevel > 0.8) {
      // High engagement: Add subtle animations
      styles.transform = 'translateY(0px)';
      styles['&:hover'] = {
        transform: 'translateY(-2px)',
        boxShadow: styles.boxShadow.replace('rgba(0,0,0,0.', 'rgba(0,0,0,0.1')
      };
    }

    // Demo mode enhancements
    if (demoMode) {
      styles.position = 'relative';
      styles.overflow = 'visible';
    }

    return styles;
  };

  const adaptiveStyles = getAdaptiveStyles();

  return (
    <Box 
      sx={{
        ...adaptiveStyles,
        ...sx
      }}
      {...boxProps}
    >
      {/* Demo Mode: Show adaptation indicators */}
      {demoMode && showAdaptations && (
        <Box
          sx={{
            position: 'absolute',
            top: -8,
            right: -8,
            backgroundColor: 'primary.main',
            color: 'white',
            borderRadius: '12px',
            padding: '4px 8px',
            fontSize: '10px',
            fontWeight: 'bold',
            zIndex: 1000,
            boxShadow: '0 2px 8px rgba(0,0,0,0.2)'
          }}
        >
          🧠 {psychologicalState.archetype}
        </Box>
      )}
      
      {children}
    </Box>
  );
};

export default AdaptiveInterfaceDemo;

// Export helper function for psychological state generation
export const generateDemoPsychologicalState = (
  archetype: PsychologicalState['archetype'] = 'Achiever'
): PsychologicalState => {
  const baseStates = {
    Achiever: {
      stressLevel: 0.3,
      confidenceLevel: 0.8,
      engagementLevel: 0.9,
      cognitiveLoad: 0.4,
      motivationFactors: ['Financial Growth', 'Recognition', 'Achievement'],
      decisionMakingStyle: 'Decisive' as const,
      riskTolerance: 'Moderate' as const
    },
    Hunter: {
      stressLevel: 0.2,
      confidenceLevel: 0.9,
      engagementLevel: 0.8,
      cognitiveLoad: 0.3,
      motivationFactors: ['Competition', 'Quick Wins', 'Opportunity'],
      decisionMakingStyle: 'Intuitive' as const,
      riskTolerance: 'High' as const
    },
    Analyst: {
      stressLevel: 0.4,
      confidenceLevel: 0.7,
      engagementLevel: 0.7,
      cognitiveLoad: 0.6,
      motivationFactors: ['Data-driven Decisions', 'Accuracy', 'Understanding'],
      decisionMakingStyle: 'Analytical' as const,
      riskTolerance: 'Low' as const
    },
    'Relationship Builder': {
      stressLevel: 0.3,
      confidenceLevel: 0.6,
      engagementLevel: 0.8,
      cognitiveLoad: 0.4,
      motivationFactors: ['Collaboration', 'Trust Building', 'Long-term Success'],
      decisionMakingStyle: 'Collaborative' as const,
      riskTolerance: 'Moderate' as const
    }
  };

  return {
    archetype,
    ...baseStates[archetype]
  };
};
