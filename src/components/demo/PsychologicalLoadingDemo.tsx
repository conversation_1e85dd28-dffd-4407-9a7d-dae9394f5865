/**
 * ⏳ Psychological Loading Demo Component
 * Extracted from MVP for client demonstrations
 * Shows sophisticated loading states for psychological analysis
 */

'use client';

import React from 'react';
import {
  Box,
  Typography,
  CircularProgress,
  LinearProgress,
  Card,
  CardContent,
  Fade,
  Chip,
  Stack
} from '@mui/material';
import {
  Psychology as BrainIcon,
  AutoAwesome as AIIcon,
  TrendingUp as AnalyticsIcon,
  Visibility as ScanIcon
} from '@mui/icons-material';

export interface PsychologicalLoadingDemoProps {
  isLoading: boolean;
  progress: number;
  stage?: 'scanning' | 'analyzing' | 'profiling' | 'optimizing' | 'complete';
  showStages?: boolean;
  title?: string;
  subtitle?: string;
}

const PsychologicalLoadingDemo: React.FC<PsychologicalLoadingDemoProps> = ({
  isLoading,
  progress,
  stage = 'analyzing',
  showStages = true,
  title = '🧠 Analyzing Psychological Profile...',
  subtitle = 'Our AI is processing your behavioral patterns and psychological archetype.'
}) => {
  const stages = [
    {
      id: 'scanning',
      label: 'Behavioral Scanning',
      icon: <ScanIcon />,
      description: 'Analyzing interaction patterns',
      color: 'info'
    },
    {
      id: 'analyzing',
      label: 'Psychological Analysis',
      icon: <BrainIcon />,
      description: 'Processing cognitive patterns',
      color: 'primary'
    },
    {
      id: 'profiling',
      label: 'Archetype Detection',
      icon: <AIIcon />,
      description: 'Identifying personality archetype',
      color: 'secondary'
    },
    {
      id: 'optimizing',
      label: 'Interface Optimization',
      icon: <AnalyticsIcon />,
      description: 'Customizing user experience',
      color: 'success'
    }
  ];

  const currentStageIndex = stages.findIndex(s => s.id === stage);
  const completedStages = stages.slice(0, currentStageIndex + 1);

  if (!isLoading) {
    return null;
  }

  return (
    <Fade in={isLoading}>
      <Box
        display="flex"
        flexDirection="column"
        justifyContent="center"
        alignItems="center"
        minHeight="400px"
        sx={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          borderRadius: '16px',
          color: 'white',
          padding: 4,
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        {/* Animated Background Elements */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: `
              radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
              radial-gradient(circle at 40% 40%, rgba(255,255,255,0.05) 0%, transparent 50%)
            `,
            animation: 'pulse 3s ease-in-out infinite'
          }}
        />

        {/* Main Loading Content */}
        <Box sx={{ position: 'relative', zIndex: 1, textAlign: 'center', width: '100%', maxWidth: 500 }}>
          {/* Main Loading Spinner */}
          <Box sx={{ position: 'relative', display: 'inline-flex', mb: 3 }}>
            <CircularProgress
              size={80}
              thickness={4}
              sx={{
                color: 'rgba(255,255,255,0.8)',
                animationDuration: '2s'
              }}
            />
            <Box
              sx={{
                top: 0,
                left: 0,
                bottom: 0,
                right: 0,
                position: 'absolute',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <BrainIcon sx={{ fontSize: 32, color: 'white' }} />
            </Box>
          </Box>

          {/* Title and Subtitle */}
          <Typography variant="h5" gutterBottom sx={{ fontWeight: 600 }}>
            {title}
          </Typography>
          <Typography variant="body1" sx={{ mb: 3, opacity: 0.9 }}>
            {subtitle}
          </Typography>

          {/* Progress Bar */}
          <Box sx={{ width: '100%', mb: 4 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2" sx={{ opacity: 0.8 }}>
                Progress
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.8 }}>
                {Math.round(progress)}%
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={progress}
              sx={{
                height: 8,
                borderRadius: 4,
                backgroundColor: 'rgba(255,255,255,0.2)',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: 'white',
                  borderRadius: 4
                }
              }}
            />
          </Box>

          {/* Stage Indicators */}
          {showStages && (
            <Card sx={{ backgroundColor: 'rgba(255,255,255,0.1)', backdropFilter: 'blur(10px)' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ color: 'white', mb: 2 }}>
                  Analysis Stages
                </Typography>
                <Stack spacing={2}>
                  {stages.map((stageItem, index) => {
                    const isCompleted = index < currentStageIndex;
                    const isCurrent = index === currentStageIndex;
                    const isUpcoming = index > currentStageIndex;

                    return (
                      <Box
                        key={stageItem.id}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 2,
                          opacity: isUpcoming ? 0.5 : 1,
                          transition: 'all 0.3s ease'
                        }}
                      >
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            width: 40,
                            height: 40,
                            borderRadius: '50%',
                            backgroundColor: isCompleted 
                              ? 'rgba(76, 175, 80, 0.8)' 
                              : isCurrent 
                                ? 'rgba(255, 255, 255, 0.2)' 
                                : 'rgba(255, 255, 255, 0.1)',
                            color: 'white',
                            position: 'relative'
                          }}
                        >
                          {isCurrent && (
                            <CircularProgress
                              size={40}
                              thickness={2}
                              sx={{
                                position: 'absolute',
                                color: 'white',
                                animationDuration: '1.5s'
                              }}
                            />
                          )}
                          {React.cloneElement(stageItem.icon, { fontSize: 'small' })}
                        </Box>
                        
                        <Box sx={{ flex: 1, textAlign: 'left' }}>
                          <Typography variant="body1" sx={{ color: 'white', fontWeight: 500 }}>
                            {stageItem.label}
                          </Typography>
                          <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                            {stageItem.description}
                          </Typography>
                        </Box>

                        {isCompleted && (
                          <Chip
                            label="✓ Complete"
                            size="small"
                            sx={{
                              backgroundColor: 'rgba(76, 175, 80, 0.8)',
                              color: 'white',
                              fontWeight: 500
                            }}
                          />
                        )}
                        
                        {isCurrent && (
                          <Chip
                            label="Processing..."
                            size="small"
                            sx={{
                              backgroundColor: 'rgba(255, 255, 255, 0.2)',
                              color: 'white',
                              fontWeight: 500
                            }}
                          />
                        )}
                      </Box>
                    );
                  })}
                </Stack>
              </CardContent>
            </Card>
          )}

          {/* Fun Facts During Loading */}
          <Box sx={{ mt: 3, opacity: 0.8 }}>
            <Typography variant="body2" sx={{ fontStyle: 'italic' }}>
              💡 Did you know? Our AI analyzes over 50 behavioral patterns to create your unique psychological profile.
            </Typography>
          </Box>
        </Box>

        {/* CSS Animation for Background */}
        <style jsx>{`
          @keyframes pulse {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 0.8; }
          }
        `}</style>
      </Box>
    </Fade>
  );
};

export default PsychologicalLoadingDemo;
