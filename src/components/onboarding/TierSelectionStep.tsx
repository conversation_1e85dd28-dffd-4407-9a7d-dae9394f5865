'use client';

import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Chip,
  Stack,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  Alert,
  LinearProgress,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  Person as PersonIcon,
  Group as GroupIcon,
  CheckCircle as CheckIcon,
  Star as StarIcon,
  TrendingUp as TrendingUpIcon,
  Psychology as AIIcon,
  Assignment as TaskIcon,
  Chat as ChatIcon,
  Approval as ApprovalIcon,
  Analytics as AnalyticsIcon,
  Security as SecurityIcon
} from '@mui/icons-material';

interface TierSelectionStepProps {
  onNext: (data: { selectedTier: 'individual' | 'team'; tierData: any }) => void;
  onBack: () => void;
  organizationType?: string;
  teamSize?: number;
}

export default function TierSelectionStep({ 
  onNext, 
  onBack, 
  organizationType, 
  teamSize 
}: TierSelectionStepProps) {
  const [selectedTier, setSelectedTier] = useState<'individual' | 'team'>('individual');
  const [loading, setLoading] = useState(false);

  const handleNext = async () => {
    setLoading(true);
    
    const tierData = {
      selectedTier,
      organizationType,
      teamSize,
      timestamp: new Date().toISOString()
    };

    // Simulate processing
    setTimeout(() => {
      onNext({ selectedTier, tierData });
      setLoading(false);
    }, 1000);
  };

  const getRecommendedTier = () => {
    if (teamSize && teamSize > 1) return 'team';
    if (organizationType && ['medium_enterprise', 'large_corporation', 'engineering_firm'].includes(organizationType)) return 'team';
    return 'individual';
  };

  const isRecommended = (tier: 'individual' | 'team') => {
    return getRecommendedTier() === tier;
  };

  return (
    <Box sx={{ maxWidth: 1000, mx: 'auto', p: 3 }}>
      {/* Header */}
      <Box sx={{ textAlign: 'center', mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 600, mb: 2, color: 'primary.main' }}>
          Choose Your BidBeez Experience
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          Select the tier that best matches how you work with tenders
        </Typography>
        <LinearProgress variant="determinate" value={50} sx={{ height: 8, borderRadius: 4 }} />
        <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
          Step 3 of 6
        </Typography>
      </Box>

      {/* Tier Selection */}
      <FormControl component="fieldset" sx={{ width: '100%' }}>
        <RadioGroup
          value={selectedTier}
          onChange={(e) => setSelectedTier(e.target.value as 'individual' | 'team')}
        >
          <Grid container spacing={3}>
            {/* Individual Tier */}
            <Grid item xs={12} md={6}>
              <Card 
                elevation={selectedTier === 'individual' ? 8 : 2}
                sx={{ 
                  height: '100%',
                  border: selectedTier === 'individual' ? 2 : 1,
                  borderColor: selectedTier === 'individual' ? 'primary.main' : 'divider',
                  position: 'relative',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  '&:hover': { elevation: 4 }
                }}
                onClick={() => setSelectedTier('individual')}
              >
                {isRecommended('individual') && (
                  <Chip
                    label="RECOMMENDED FOR YOU"
                    color="success"
                    size="small"
                    sx={{
                      position: 'absolute',
                      top: -8,
                      left: 16,
                      fontWeight: 600,
                      zIndex: 1
                    }}
                  />
                )}
                
                <CardContent sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <FormControlLabel
                      value="individual"
                      control={<Radio />}
                      label=""
                      sx={{ mr: 1 }}
                    />
                    <PersonIcon sx={{ fontSize: 40, color: 'primary.main', mr: 2 }} />
                    <Box>
                      <Typography variant="h5" sx={{ fontWeight: 600 }}>
                        Individual Tier
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Perfect for solo contractors
                      </Typography>
                    </Box>
                  </Box>

                  <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                    Streamlined workflow designed for individual contractors and small businesses who manage tenders independently.
                  </Typography>

                  <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 2 }}>
                    ✨ Key Features:
                  </Typography>
                  <List dense sx={{ mb: 3 }}>
                    <ListItem sx={{ py: 0.5 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <TrendingUpIcon fontSize="small" color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="INTERESTED IN BID Workflow"
                        secondary="Psychological commitment funnel"
                      />
                    </ListItem>
                    <ListItem sx={{ py: 0.5 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <AIIcon fontSize="small" color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="AI-Powered Analysis"
                        secondary="Smart tender analysis and insights"
                      />
                    </ListItem>
                    <ListItem sx={{ py: 0.5 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <TaskIcon fontSize="small" color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Bee Worker Tasks"
                        secondary="Assign tasks to specialized workers"
                      />
                    </ListItem>
                    <ListItem sx={{ py: 0.5 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <SecurityIcon fontSize="small" color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Personal Workspace"
                        secondary="Individual bid management"
                      />
                    </ListItem>
                  </List>

                  <Box sx={{ mt: 'auto' }}>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'success.main' }}>
                      Best for:
                    </Typography>
                    <Stack direction="row" spacing={1} sx={{ flexWrap: 'wrap', gap: 1, mt: 1 }}>
                      <Chip label="Solo Contractors" size="small" variant="outlined" />
                      <Chip label="Small Businesses" size="small" variant="outlined" />
                      <Chip label="Independent Bidders" size="small" variant="outlined" />
                    </Stack>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Team Tier */}
            <Grid item xs={12} md={6}>
              <Card 
                elevation={selectedTier === 'team' ? 8 : 2}
                sx={{ 
                  height: '100%',
                  border: selectedTier === 'team' ? 2 : 1,
                  borderColor: selectedTier === 'team' ? 'primary.main' : 'divider',
                  position: 'relative',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  '&:hover': { elevation: 4 }
                }}
                onClick={() => setSelectedTier('team')}
              >
                {isRecommended('team') && (
                  <Chip
                    label="RECOMMENDED FOR YOU"
                    color="success"
                    size="small"
                    sx={{
                      position: 'absolute',
                      top: -8,
                      left: 16,
                      fontWeight: 600,
                      zIndex: 1
                    }}
                  />
                )}
                
                <CardContent sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <FormControlLabel
                      value="team"
                      control={<Radio />}
                      label=""
                      sx={{ mr: 1 }}
                    />
                    <GroupIcon sx={{ fontSize: 40, color: 'primary.main', mr: 2 }} />
                    <Box>
                      <Typography variant="h5" sx={{ fontWeight: 600 }}>
                        Team Tier
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Advanced collaboration for teams
                      </Typography>
                    </Box>
                  </Box>

                  <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                    Comprehensive collaboration platform for teams and organizations with role-based workflows and advanced coordination.
                  </Typography>

                  <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 2 }}>
                    🚀 Advanced Features:
                  </Typography>
                  <List dense sx={{ mb: 3 }}>
                    <ListItem sx={{ py: 0.5 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <GroupIcon fontSize="small" color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Team Collaboration"
                        secondary="Multi-user workspace and coordination"
                      />
                    </ListItem>
                    <ListItem sx={{ py: 0.5 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <ApprovalIcon fontSize="small" color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Approval Workflows"
                        secondary="Role-based approval processes"
                      />
                    </ListItem>
                    <ListItem sx={{ py: 0.5 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <ChatIcon fontSize="small" color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Real-Time Discussion"
                        secondary="Team chat and communication"
                      />
                    </ListItem>
                    <ListItem sx={{ py: 0.5 }}>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <AnalyticsIcon fontSize="small" color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Team Analytics"
                        secondary="Performance and collaboration metrics"
                      />
                    </ListItem>
                  </List>

                  <Box sx={{ mt: 'auto' }}>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'success.main' }}>
                      Best for:
                    </Typography>
                    <Stack direction="row" spacing={1} sx={{ flexWrap: 'wrap', gap: 1, mt: 1 }}>
                      <Chip label="Construction Companies" size="small" variant="outlined" />
                      <Chip label="Engineering Firms" size="small" variant="outlined" />
                      <Chip label="Large Organizations" size="small" variant="outlined" />
                    </Stack>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </RadioGroup>
      </FormControl>

      {/* Comparison Alert */}
      <Alert severity="info" sx={{ mt: 3 }}>
        <Typography variant="body2">
          <strong>Don't worry!</strong> You can always switch between Individual and Team modes later in your profile settings. 
          Your subscription plan will determine which features are available.
        </Typography>
      </Alert>

      {/* Navigation */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
        <Button
          variant="outlined"
          onClick={onBack}
          disabled={loading}
        >
          Back
        </Button>
        <Button
          variant="contained"
          onClick={handleNext}
          disabled={loading}
          startIcon={loading ? <LinearProgress size={20} /> : <CheckIcon />}
          sx={{ minWidth: 200 }}
        >
          {loading ? 'Processing...' : `Continue with ${selectedTier === 'individual' ? 'Individual' : 'Team'} Tier`}
        </Button>
      </Box>

      {/* Help Text */}
      <Box sx={{ textAlign: 'center', mt: 3 }}>
        <Typography variant="caption" color="text.secondary">
          Need help choosing? Our recommendation is based on your organization type and team size.
        </Typography>
      </Box>
    </Box>
  );
}
