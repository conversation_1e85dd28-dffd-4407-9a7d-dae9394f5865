'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Chip,
  Stack,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  Alert,
  LinearProgress,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Switch,
  Badge
} from '@mui/material';
import {
  CheckCircle as CheckIcon,
  Star as StarIcon,
  TrendingUp as TrendingUpIcon,
  Group as GroupIcon,
  Business as BusinessIcon,
  Diamond as DiamondIcon,
  Free as FreeIcon,
  Person as PersonIcon
} from '@mui/icons-material';

import { SubscriptionPlan, SubscriptionTier } from '../../types/subscription';
import SubscriptionService from '../../services/SubscriptionService';

interface PlanSelectionStepProps {
  onNext: (data: { selectedPlan: string; billingCycle: 'monthly' | 'annually' }) => void;
  onBack: () => void;
  selectedTier: 'individual' | 'team';
  organizationType?: string;
  teamSize?: number;
}

export default function PlanSelectionStep({ 
  onNext, 
  onBack, 
  selectedTier,
  organizationType,
  teamSize 
}: PlanSelectionStepProps) {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [selectedPlan, setSelectedPlan] = useState<string>('');
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'annually'>('monthly');
  const [loading, setLoading] = useState(false);
  const [plansLoading, setPlansLoading] = useState(true);

  const subscriptionService = SubscriptionService.getInstance();

  useEffect(() => {
    loadPlans();
  }, []);

  const loadPlans = async () => {
    try {
      setPlansLoading(true);
      const allPlans = await subscriptionService.getSubscriptionPlans();
      
      // Filter plans based on selected tier
      let filteredPlans = allPlans;
      if (selectedTier === 'individual') {
        filteredPlans = allPlans.filter(plan => 
          plan.tier === SubscriptionTier.FREE || 
          plan.tier === SubscriptionTier.BASIC
        );
      } else {
        filteredPlans = allPlans.filter(plan => 
          plan.tier === SubscriptionTier.PROFESSIONAL || 
          plan.tier === SubscriptionTier.ENTERPRISE
        );
      }
      
      setPlans(filteredPlans);
      
      // Auto-select recommended plan
      const recommendedPlan = getRecommendedPlan(filteredPlans);
      if (recommendedPlan) {
        setSelectedPlan(recommendedPlan.id);
      }
    } catch (error) {
      console.error('Error loading plans:', error);
    } finally {
      setPlansLoading(false);
    }
  };

  const getRecommendedPlan = (availablePlans: SubscriptionPlan[]) => {
    if (selectedTier === 'individual') {
      return availablePlans.find(p => p.tier === SubscriptionTier.BASIC);
    } else {
      if (teamSize && teamSize > 20) {
        return availablePlans.find(p => p.tier === SubscriptionTier.ENTERPRISE);
      }
      return availablePlans.find(p => p.tier === SubscriptionTier.PROFESSIONAL);
    }
  };

  const handleNext = async () => {
    if (!selectedPlan) return;
    
    setLoading(true);
    
    // Simulate processing
    setTimeout(() => {
      onNext({ selectedPlan, billingCycle });
      setLoading(false);
    }, 1000);
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  const getAnnualSavings = (plan: SubscriptionPlan) => {
    const monthlyCost = plan.price.monthly * 12;
    const annualCost = plan.price.yearly;
    return monthlyCost - annualCost;
  };

  const getPlanIcon = (tier: SubscriptionTier) => {
    switch (tier) {
      case SubscriptionTier.FREE: return <FreeIcon sx={{ fontSize: 32 }} />;
      case SubscriptionTier.BASIC: return <PersonIcon sx={{ fontSize: 32 }} />;
      case SubscriptionTier.PROFESSIONAL: return <GroupIcon sx={{ fontSize: 32 }} />;
      case SubscriptionTier.ENTERPRISE: return <BusinessIcon sx={{ fontSize: 32 }} />;
      default: return <StarIcon sx={{ fontSize: 32 }} />;
    }
  };

  const isRecommended = (plan: SubscriptionPlan) => {
    const recommended = getRecommendedPlan(plans);
    return recommended?.id === plan.id;
  };

  if (plansLoading) {
    return (
      <Box sx={{ textAlign: 'center', py: 8 }}>
        <LinearProgress sx={{ mb: 2 }} />
        <Typography>Loading subscription plans...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      {/* Header */}
      <Box sx={{ textAlign: 'center', mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 600, mb: 2, color: 'primary.main' }}>
          Choose Your Plan
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          Select the plan that fits your needs and budget for {selectedTier} tier
        </Typography>
        <LinearProgress variant="determinate" value={66} sx={{ height: 8, borderRadius: 4 }} />
        <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
          Step 4 of 6
        </Typography>
      </Box>

      {/* Billing Cycle Toggle */}
      <Box sx={{ display: 'flex', justifyContent: 'center', mb: 4 }}>
        <Card elevation={1} sx={{ p: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="body2">Monthly</Typography>
            <Switch
              checked={billingCycle === 'annually'}
              onChange={(e) => setBillingCycle(e.target.checked ? 'annually' : 'monthly')}
              color="primary"
            />
            <Typography variant="body2">
              Annually 
              <Chip 
                label="Save up to 17%" 
                size="small" 
                color="success" 
                sx={{ ml: 1 }}
              />
            </Typography>
          </Box>
        </Card>
      </Box>

      {/* Plan Selection */}
      <FormControl component="fieldset" sx={{ width: '100%' }}>
        <RadioGroup
          value={selectedPlan}
          onChange={(e) => setSelectedPlan(e.target.value)}
        >
          <Grid container spacing={3}>
            {plans.map((plan) => (
              <Grid item xs={12} md={plans.length <= 2 ? 6 : 4} key={plan.id}>
                <Card 
                  elevation={selectedPlan === plan.id ? 8 : 2}
                  sx={{ 
                    height: '100%',
                    border: selectedPlan === plan.id ? 2 : 1,
                    borderColor: selectedPlan === plan.id ? 'primary.main' : 'divider',
                    position: 'relative',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    '&:hover': { elevation: 4 }
                  }}
                  onClick={() => setSelectedPlan(plan.id)}
                >
                  {isRecommended(plan) && (
                    <Chip
                      label="RECOMMENDED"
                      color="success"
                      size="small"
                      sx={{
                        position: 'absolute',
                        top: -8,
                        left: 16,
                        fontWeight: 600,
                        zIndex: 1
                      }}
                    />
                  )}

                  {plan.popular && (
                    <Chip
                      label="MOST POPULAR"
                      color="primary"
                      size="small"
                      sx={{
                        position: 'absolute',
                        top: -8,
                        right: 16,
                        fontWeight: 600,
                        zIndex: 1
                      }}
                    />
                  )}
                  
                  <CardContent sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <FormControlLabel
                        value={plan.id}
                        control={<Radio />}
                        label=""
                        sx={{ mr: 1 }}
                      />
                      <Box sx={{ color: 'primary.main', mr: 2 }}>
                        {getPlanIcon(plan.tier)}
                      </Box>
                      <Box>
                        <Typography variant="h6" sx={{ fontWeight: 600 }}>
                          {plan.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {plan.description}
                        </Typography>
                      </Box>
                    </Box>

                    {/* Pricing */}
                    <Box sx={{ textAlign: 'center', mb: 3 }}>
                      {plan.tier === SubscriptionTier.FREE ? (
                        <Typography variant="h3" sx={{ fontWeight: 600, color: 'success.main' }}>
                          FREE
                        </Typography>
                      ) : (
                        <>
                          <Typography variant="h3" sx={{ fontWeight: 600, color: 'primary.main' }}>
                            {formatPrice(billingCycle === 'monthly' ? plan.price.monthly : plan.price.yearly / 12)}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            per month{billingCycle === 'annually' && ', billed annually'}
                          </Typography>
                          {billingCycle === 'annually' && getAnnualSavings(plan) > 0 && (
                            <Typography variant="caption" color="success.main" sx={{ display: 'block', mt: 1 }}>
                              Save {formatPrice(getAnnualSavings(plan))} per year
                            </Typography>
                          )}
                        </>
                      )}
                    </Box>

                    {/* Features */}
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 2 }}>
                      What's included:
                    </Typography>
                    <List dense sx={{ mb: 3, flexGrow: 1 }}>
                      {plan.features.slice(0, 5).map((feature, index) => (
                        <ListItem key={index} sx={{ py: 0.5, px: 0 }}>
                          <ListItemIcon sx={{ minWidth: 24 }}>
                            <CheckIcon fontSize="small" color="success" />
                          </ListItemIcon>
                          <ListItemText 
                            primary={feature.name}
                            secondary={feature.limit ? `Up to ${feature.limit}` : undefined}
                            primaryTypographyProps={{ variant: 'body2' }}
                            secondaryTypographyProps={{ variant: 'caption' }}
                          />
                        </ListItem>
                      ))}
                      {plan.features.length > 5 && (
                        <ListItem sx={{ py: 0.5, px: 0 }}>
                          <ListItemText 
                            primary={`+ ${plan.features.length - 5} more features`}
                            primaryTypographyProps={{ variant: 'body2', color: 'primary.main' }}
                          />
                        </ListItem>
                      )}
                    </List>

                    {/* Limits */}
                    <Box sx={{ mt: 'auto' }}>
                      <Divider sx={{ mb: 2 }} />
                      <Grid container spacing={1}>
                        <Grid item xs={6}>
                          <Typography variant="caption" color="text.secondary">
                            Monthly Bids
                          </Typography>
                          <Typography variant="body2" sx={{ fontWeight: 600 }}>
                            {plan.limits.monthlyBids === 999999 ? 'Unlimited' : plan.limits.monthlyBids}
                          </Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="caption" color="text.secondary">
                            Team Members
                          </Typography>
                          <Typography variant="body2" sx={{ fontWeight: 600 }}>
                            {plan.limits.teamMembers === 999999 ? 'Unlimited' : plan.limits.teamMembers}
                          </Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="caption" color="text.secondary">
                            Storage
                          </Typography>
                          <Typography variant="body2" sx={{ fontWeight: 600 }}>
                            {plan.limits.storageGB === 999999 ? 'Unlimited' : `${plan.limits.storageGB}GB`}
                          </Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="caption" color="text.secondary">
                            Support
                          </Typography>
                          <Typography variant="body2" sx={{ fontWeight: 600 }}>
                            {plan.limits.supportLevel}
                          </Typography>
                        </Grid>
                      </Grid>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </RadioGroup>
      </FormControl>

      {/* Plan Comparison Alert */}
      <Alert severity="info" sx={{ mt: 3 }}>
        <Typography variant="body2">
          <strong>Need more details?</strong> You can compare all features and upgrade or downgrade your plan anytime from your account settings.
        </Typography>
      </Alert>

      {/* Navigation */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
        <Button
          variant="outlined"
          onClick={onBack}
          disabled={loading}
        >
          Back
        </Button>
        <Button
          variant="contained"
          onClick={handleNext}
          disabled={loading || !selectedPlan}
          startIcon={loading ? <LinearProgress size={20} /> : <CheckIcon />}
          sx={{ minWidth: 200 }}
        >
          {loading ? 'Processing...' : 'Continue to Payment'}
        </Button>
      </Box>

      {/* Help Text */}
      <Box sx={{ textAlign: 'center', mt: 3 }}>
        <Typography variant="caption" color="text.secondary">
          All plans include a 14-day free trial. Cancel anytime during the trial period.
        </Typography>
      </Box>
    </Box>
  );
}
