'use client';

import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Button,
  Box,
  FormControlLabel,
  Switch,
  FormControl,
  Select,
  MenuItem,
  InputLabel,
  Alert,
  Stepper,
  Step,
  StepLabel,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider
} from '@mui/material';
import {
  LocationOn,
  Security,
  Visibility,
  Schedule,
  CheckCircle,
  Warning,
  Info,
  Shield
} from '@mui/icons-material';

interface LocationPrivacySettings {
  trackingEnabled: boolean;
  shareWithClients: boolean;
  shareWithQueenBee: boolean;
  trackingMode: 'tasks_only' | 'working_hours' | 'always' | 'emergency_only';
  accuracyLevel: 'exact' | 'approximate' | 'city_only';
  shareLocationHistory: boolean;
  dataRetentionDays: number;
}

interface Props {
  open: boolean;
  onClose: () => void;
  onConsent: (settings: LocationPrivacySettings) => void;
  onDecline: () => void;
}

export default function LocationConsentDialog({ open, onClose, onConsent, onDecline }: Props) {
  const [currentStep, setCurrentStep] = useState(0);
  const [settings, setSettings] = useState<LocationPrivacySettings>({
    trackingEnabled: false,
    shareWithClients: false,
    shareWithQueenBee: true,
    trackingMode: 'tasks_only',
    accuracyLevel: 'approximate',
    shareLocationHistory: false,
    dataRetentionDays: 30
  });

  const steps = ['Privacy Notice', 'Location Settings', 'Confirmation'];

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onConsent(settings);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleDecline = () => {
    setSettings(prev => ({ ...prev, trackingEnabled: false }));
    onDecline();
  };

  const renderPrivacyNotice = () => (
    <Box>
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          📍 Location Services & Privacy
        </Typography>
        <Typography variant="body2">
          BidBeez uses location data to provide better service. Your privacy is important to us.
        </Typography>
      </Alert>

      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Info color="primary" /> How We Use Location Data
          </Typography>
          
          <List>
            <ListItem>
              <ListItemIcon><CheckCircle color="success" /></ListItemIcon>
              <ListItemText 
                primary="Task Assignment"
                secondary="Match you with nearby tasks to reduce travel time"
              />
            </ListItem>
            <ListItem>
              <ListItemIcon><CheckCircle color="success" /></ListItemIcon>
              <ListItemText 
                primary="Navigation Assistance"
                secondary="Provide GPS directions to task locations"
              />
            </ListItem>
            <ListItem>
              <ListItemIcon><CheckCircle color="success" /></ListItemIcon>
              <ListItemText 
                primary="Client Transparency"
                secondary="Show clients your progress during assigned tasks"
              />
            </ListItem>
            <ListItem>
              <ListItemIcon><CheckCircle color="success" /></ListItemIcon>
              <ListItemText 
                primary="Safety & Security"
                secondary="Emergency location sharing for worker safety"
              />
            </ListItem>
          </List>
        </CardContent>
      </Card>

      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Shield color="primary" /> Your Privacy Rights
          </Typography>
          
          <List>
            <ListItem>
              <ListItemIcon><Security color="primary" /></ListItemIcon>
              <ListItemText 
                primary="Full Control"
                secondary="You can enable/disable location tracking anytime"
              />
            </ListItem>
            <ListItem>
              <ListItemIcon><Visibility color="primary" /></ListItemIcon>
              <ListItemText 
                primary="Granular Sharing"
                secondary="Choose who can see your location and when"
              />
            </ListItem>
            <ListItem>
              <ListItemIcon><Schedule color="primary" /></ListItemIcon>
              <ListItemText 
                primary="Data Retention"
                secondary="Set how long your location data is stored"
              />
            </ListItem>
          </List>
        </CardContent>
      </Card>
    </Box>
  );

  const renderLocationSettings = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Configure Your Location Preferences
      </Typography>
      
      <Alert severity="warning" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>Note:</strong> Disabling location tracking may limit available task assignments in your area.
        </Typography>
      </Alert>

      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
        <FormControlLabel
          control={
            <Switch
              checked={settings.trackingEnabled}
              onChange={(e) => setSettings(prev => ({ ...prev, trackingEnabled: e.target.checked }))}
            />
          }
          label={
            <Box>
              <Typography variant="body1">Enable Location Tracking</Typography>
              <Typography variant="body2" color="text.secondary">
                Allow BidBeez to access your location for task assignment and navigation
              </Typography>
            </Box>
          }
        />

        {settings.trackingEnabled && (
          <>
            <FormControl fullWidth>
              <InputLabel>When to Track Location</InputLabel>
              <Select
                value={settings.trackingMode}
                onChange={(e) => setSettings(prev => ({ ...prev, trackingMode: e.target.value as any }))}
                label="When to Track Location"
              >
                <MenuItem value="tasks_only">During Tasks Only (Recommended)</MenuItem>
                <MenuItem value="working_hours">Working Hours Only</MenuItem>
                <MenuItem value="always">Always (24/7)</MenuItem>
                <MenuItem value="emergency_only">Emergency Only</MenuItem>
              </Select>
            </FormControl>

            <FormControl fullWidth>
              <InputLabel>Location Accuracy</InputLabel>
              <Select
                value={settings.accuracyLevel}
                onChange={(e) => setSettings(prev => ({ ...prev, accuracyLevel: e.target.value as any }))}
                label="Location Accuracy"
              >
                <MenuItem value="exact">Exact Location (GPS)</MenuItem>
                <MenuItem value="approximate">Approximate Area ±1km (Recommended)</MenuItem>
                <MenuItem value="city_only">City Level Only</MenuItem>
              </Select>
            </FormControl>

            <Divider />

            <Typography variant="subtitle1">Sharing Preferences</Typography>

            <FormControlLabel
              control={
                <Switch
                  checked={settings.shareWithClients}
                  onChange={(e) => setSettings(prev => ({ ...prev, shareWithClients: e.target.checked }))}
                />
              }
              label={
                <Box>
                  <Typography variant="body2">Share with Clients</Typography>
                  <Typography variant="caption" color="text.secondary">
                    Clients can see your location during assigned tasks for transparency
                  </Typography>
                </Box>
              }
            />

            <FormControlLabel
              control={
                <Switch
                  checked={settings.shareWithQueenBee}
                  onChange={(e) => setSettings(prev => ({ ...prev, shareWithQueenBee: e.target.checked }))}
                />
              }
              label={
                <Box>
                  <Typography variant="body2">Share with Queen Bee</Typography>
                  <Typography variant="caption" color="text.secondary">
                    Your Queen Bee can see your location for task coordination
                  </Typography>
                </Box>
              }
            />

            <FormControlLabel
              control={
                <Switch
                  checked={settings.shareLocationHistory}
                  onChange={(e) => setSettings(prev => ({ ...prev, shareLocationHistory: e.target.checked }))}
                />
              }
              label={
                <Box>
                  <Typography variant="body2">Share Location History</Typography>
                  <Typography variant="caption" color="text.secondary">
                    Allow access to past location data for performance analytics
                  </Typography>
                </Box>
              }
            />
          </>
        )}
      </Box>
    </Box>
  );

  const renderConfirmation = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Confirm Your Location Privacy Settings
      </Typography>
      
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="subtitle1" gutterBottom>Your Choices:</Typography>
          
          <List dense>
            <ListItem>
              <ListItemText 
                primary="Location Tracking"
                secondary={settings.trackingEnabled ? 'Enabled' : 'Disabled'}
              />
              <Box sx={{ color: settings.trackingEnabled ? 'success.main' : 'error.main' }}>
                {settings.trackingEnabled ? <CheckCircle /> : <Warning />}
              </Box>
            </ListItem>
            
            {settings.trackingEnabled && (
              <>
                <ListItem>
                  <ListItemText 
                    primary="Tracking Mode"
                    secondary={settings.trackingMode.replace('_', ' ').toUpperCase()}
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Accuracy Level"
                    secondary={settings.accuracyLevel.replace('_', ' ').toUpperCase()}
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Share with Clients"
                    secondary={settings.shareWithClients ? 'Yes' : 'No'}
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Share with Queen Bee"
                    secondary={settings.shareWithQueenBee ? 'Yes' : 'No'}
                  />
                </ListItem>
              </>
            )}
          </List>
        </CardContent>
      </Card>

      <Alert severity="info">
        <Typography variant="body2">
          You can change these settings anytime in your profile. Your privacy is protected by encryption and 
          strict access controls.
        </Typography>
      </Alert>
    </Box>
  );

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return renderPrivacyNotice();
      case 1:
        return renderLocationSettings();
      case 2:
        return renderConfirmation();
      default:
        return null;
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="md" 
      fullWidth
      PaperProps={{
        sx: { minHeight: '70vh' }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <LocationOn color="primary" />
          <Typography variant="h6">Location Privacy & Consent</Typography>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Stepper activeStep={currentStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {renderStepContent()}
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Button onClick={handleDecline} color="error">
          Decline Location Services
        </Button>
        
        <Box sx={{ flex: 1 }} />
        
        {currentStep > 0 && (
          <Button onClick={handleBack}>
            Back
          </Button>
        )}
        
        <Button 
          variant="contained" 
          onClick={handleNext}
          disabled={currentStep === 1 && !settings.trackingEnabled}
        >
          {currentStep === steps.length - 1 ? 'Confirm & Continue' : 'Next'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
