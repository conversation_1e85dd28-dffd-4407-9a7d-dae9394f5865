import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Tabs,
  Tab,
  Button,
  Chip,
  LinearProgress,
  Alert,
  Stack,
  IconButton,
  Tooltip,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Paper,
  Divider
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  Assessment,
  Timeline,
  PieChart,
  BarChart,
  Download,
  Refresh,
  FilterList,
  Psychology,
  EmojiEvents,
  MonetizationOn,
  Speed,
  GpsFixed as Target,
  CompareArrows,
  Insights
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart as RechartsBarChart,
  Bar,
  PieChart as RechartsPieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';

interface BidAnalyticsProps {
  userId: string;
  timeRange: 'week' | 'month' | 'quarter' | 'year';
  onTimeRangeChange: (range: 'week' | 'month' | 'quarter' | 'year') => void;
}

interface BidPerformanceMetrics {
  // Core Performance
  totalBids: number;
  successfulBids: number;
  successRate: number;
  totalValue: number;
  wonValue: number;
  averageBidValue: number;
  
  // Financial Analytics
  revenue: number;
  profit: number;
  profitMargin: number;
  roi: number;
  costPerBid: number;
  revenuePerBid: number;
  
  // Competitive Analytics
  marketPosition: number;
  competitiveWinRate: number;
  averageCompetitors: number;
  winRateVsCompetitors: number;
  
  // Efficiency Metrics
  averageResponseTime: number;
  averagePreparationTime: number;
  documentCompletionRate: number;
  complianceScore: number;
  
  // Trend Data
  monthlyTrends: MonthlyTrend[];
  categoryPerformance: CategoryPerformance[];
  competitorAnalysis: CompetitorAnalysis[];
  
  // Psychological Insights
  stressImpactOnSuccess: number;
  optimalBiddingTimes: string[];
  behavioralPatterns: BehavioralPattern[];
  
  // Economic Impact
  jobsCreated: number;
  economicValue: number;
  communityImpact: string;
}

interface MonthlyTrend {
  month: string;
  bids: number;
  wins: number;
  value: number;
  successRate: number;
  avgBidValue: number;
}

interface CategoryPerformance {
  category: string;
  bids: number;
  wins: number;
  successRate: number;
  totalValue: number;
  avgValue: number;
  profitability: number;
}

interface CompetitorAnalysis {
  competitorName: string;
  headsUpWins: number;
  headsUpLosses: number;
  winRate: number;
  avgBidDifference: number;
  lastEncounter: string;
}

interface BehavioralPattern {
  pattern: string;
  impact: 'positive' | 'negative' | 'neutral';
  frequency: number;
  recommendation: string;
}

const BidAnalyticsDashboard: React.FC<BidAnalyticsProps> = ({
  userId,
  timeRange,
  onTimeRangeChange
}) => {
  const [activeTab, setActiveTab] = useState(0);
  const [metrics, setMetrics] = useState<BidPerformanceMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [showPsychologicalInsights, setShowPsychologicalInsights] = useState(false);

  useEffect(() => {
    loadBidAnalytics();
  }, [userId, timeRange]);

  const loadBidAnalytics = async () => {
    setLoading(true);
    try {
      // Simulate API call - replace with actual API
      const mockMetrics: BidPerformanceMetrics = {
        // Core Performance
        totalBids: 47,
        successfulBids: 32,
        successRate: 68.1,
        totalValue: 156750000,
        wonValue: 89250000,
        averageBidValue: 3335106,
        
        // Financial Analytics
        revenue: 89250000,
        profit: 12495000,
        profitMargin: 14.0,
        roi: 156.7,
        costPerBid: 45000,
        revenuePerBid: 1898936,
        
        // Competitive Analytics
        marketPosition: 7,
        competitiveWinRate: 72.3,
        averageCompetitors: 8.4,
        winRateVsCompetitors: 15.8,
        
        // Efficiency Metrics
        averageResponseTime: 2.3,
        averagePreparationTime: 18.5,
        documentCompletionRate: 94.7,
        complianceScore: 96.2,
        
        // Trend Data
        monthlyTrends: [
          { month: 'Jan', bids: 8, wins: 5, value: 15200000, successRate: 62.5, avgBidValue: 1900000 },
          { month: 'Feb', bids: 6, wins: 4, value: 12800000, successRate: 66.7, avgBidValue: 2133333 },
          { month: 'Mar', bids: 9, wins: 7, value: 18900000, successRate: 77.8, avgBidValue: 2100000 },
          { month: 'Apr', bids: 7, wins: 5, value: 16750000, successRate: 71.4, avgBidValue: 2392857 },
          { month: 'May', bids: 8, wins: 6, value: 14200000, successRate: 75.0, avgBidValue: 1775000 },
          { month: 'Jun', bids: 9, wins: 5, value: 11400000, successRate: 55.6, avgBidValue: 1266667 }
        ],
        
        categoryPerformance: [
          { category: 'Construction', bids: 18, wins: 13, successRate: 72.2, totalValue: 45600000, avgValue: 2533333, profitability: 16.2 },
          { category: 'IT Services', bids: 12, wins: 9, successRate: 75.0, totalValue: 28900000, avgValue: 2408333, profitability: 22.1 },
          { category: 'Professional Services', bids: 8, wins: 5, successRate: 62.5, totalValue: 8750000, avgValue: 1093750, profitability: 18.7 },
          { category: 'Maintenance', bids: 9, wins: 5, successRate: 55.6, totalValue: 6000000, avgValue: 666667, profitability: 12.3 }
        ],
        
        competitorAnalysis: [
          { competitorName: 'ABC Construction', headsUpWins: 5, headsUpLosses: 3, winRate: 62.5, avgBidDifference: -125000, lastEncounter: '2024-05-15' },
          { competitorName: 'TechSolutions Pro', headsUpWins: 3, headsUpLosses: 2, winRate: 60.0, avgBidDifference: 89000, lastEncounter: '2024-05-22' },
          { competitorName: 'BuildRight Ltd', headsUpWins: 4, headsUpLosses: 4, winRate: 50.0, avgBidDifference: -45000, lastEncounter: '2024-06-01' }
        ],
        
        // Psychological Insights
        stressImpactOnSuccess: -12.5,
        optimalBiddingTimes: ['09:00-11:00', '14:00-16:00'],
        behavioralPatterns: [
          { pattern: 'Higher success rate on Tuesdays', impact: 'positive', frequency: 85, recommendation: 'Schedule important bids on Tuesdays' },
          { pattern: 'Lower performance under stress', impact: 'negative', frequency: 67, recommendation: 'Use stress management techniques before bidding' },
          { pattern: 'Better results with longer preparation time', impact: 'positive', frequency: 78, recommendation: 'Start bid preparation earlier' }
        ],
        
        // Economic Impact
        jobsCreated: 247,
        economicValue: 11137500,
        communityImpact: 'Major community impact - Supporting 247 jobs across multiple sectors!'
      };
      
      setMetrics(mockMetrics);
    } catch (error) {
      console.error('Failed to load bid analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const exportAnalytics = () => {
    // Export functionality
    console.log('Exporting analytics...');
  };

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <LinearProgress />
        <Typography variant="h6" sx={{ mt: 2, textAlign: 'center' }}>
          🧠 Analyzing your bidding performance...
        </Typography>
      </Box>
    );
  }

  if (!metrics) {
    return (
      <Alert severity="error">
        Failed to load bid analytics. Please try again.
      </Alert>
    );
  }

  const renderOverviewTab = () => (
    <Grid container spacing={3}>
      {/* Key Performance Indicators */}
      <Grid item xs={12}>
        <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
          📊 Key Performance Indicators
        </Typography>
      </Grid>
      
      <Grid item xs={12} md={3}>
        <Card sx={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box>
                <Typography variant="h4" fontWeight="bold">
                  {metrics.successRate.toFixed(1)}%
                </Typography>
                <Typography variant="body2">
                  Success Rate
                </Typography>
              </Box>
              <EmojiEvents sx={{ fontSize: 40, opacity: 0.8 }} />
            </Box>
            <Typography variant="caption">
              {metrics.successfulBids} wins out of {metrics.totalBids} bids
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={3}>
        <Card sx={{ background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', color: 'white' }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box>
                <Typography variant="h4" fontWeight="bold">
                  R{(metrics.wonValue / 1000000).toFixed(1)}M
                </Typography>
                <Typography variant="body2">
                  Total Won Value
                </Typography>
              </Box>
              <MonetizationOn sx={{ fontSize: 40, opacity: 0.8 }} />
            </Box>
            <Typography variant="caption">
              R{(metrics.averageBidValue / 1000000).toFixed(1)}M average per bid
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={3}>
        <Card sx={{ background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', color: 'white' }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box>
                <Typography variant="h4" fontWeight="bold">
                  {metrics.roi.toFixed(1)}%
                </Typography>
                <Typography variant="body2">
                  Return on Investment
                </Typography>
              </Box>
              <TrendingUp sx={{ fontSize: 40, opacity: 0.8 }} />
            </Box>
            <Typography variant="caption">
              {metrics.profitMargin.toFixed(1)}% profit margin
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={3}>
        <Card sx={{ background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', color: 'white' }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box>
                <Typography variant="h4" fontWeight="bold">
                  #{metrics.marketPosition}
                </Typography>
                <Typography variant="body2">
                  Market Position
                </Typography>
              </Box>
              <Target sx={{ fontSize: 40, opacity: 0.8 }} />
            </Box>
            <Typography variant="caption">
              Top 10 in your region
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      {/* Economic Impact */}
      <Grid item xs={12}>
        <Card sx={{ 
          background: 'linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%)',
          border: '2px solid #4caf50'
        }}>
          <CardContent>
            <Typography variant="h6" fontWeight="bold" sx={{ color: '#2e7d32', mb: 2 }}>
              🌍 Your Economic Impact
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={4}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h3" sx={{ color: '#2e7d32', fontWeight: 'bold' }}>
                    {metrics.jobsCreated}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    💼 Jobs Created
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md={4}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h3" sx={{ color: '#2e7d32', fontWeight: 'bold' }}>
                    R{(metrics.economicValue / 1000000).toFixed(1)}M
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    💰 Economic Value
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md={4}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h3" sx={{ color: '#2e7d32', fontWeight: 'bold' }}>
                    {Math.floor(metrics.jobsCreated * 3.2)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    👨‍👩‍👧‍👦 Families Impacted
                  </Typography>
                </Box>
              </Grid>
            </Grid>
            <Alert severity="success" sx={{ mt: 2, backgroundColor: '#e8f5e8' }}>
              <Typography variant="body2" fontWeight="bold">
                {metrics.communityImpact}
              </Typography>
            </Alert>
          </CardContent>
        </Card>
      </Grid>

      {/* Performance Trends */}
      <Grid item xs={12} md={8}>
        <Card>
          <CardContent>
            <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
              📈 Performance Trends
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={metrics.monthlyTrends}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis yAxisId="left" />
                <YAxis yAxisId="right" orientation="right" />
                <RechartsTooltip />
                <Legend />
                <Line 
                  yAxisId="left" 
                  type="monotone" 
                  dataKey="successRate" 
                  stroke="#8884d8" 
                  strokeWidth={3}
                  name="Success Rate (%)"
                />
                <Line 
                  yAxisId="right" 
                  type="monotone" 
                  dataKey="bids" 
                  stroke="#82ca9d" 
                  strokeWidth={2}
                  name="Bids Submitted"
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </Grid>

      {/* Category Performance */}
      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
              🎯 Category Performance
            </Typography>
            <Stack spacing={2}>
              {metrics.categoryPerformance.map((category, index) => (
                <Box key={index}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2" fontWeight="bold">
                      {category.category}
                    </Typography>
                    <Typography variant="body2" color="primary">
                      {category.successRate.toFixed(1)}%
                    </Typography>
                  </Box>
                  <LinearProgress 
                    variant="determinate" 
                    value={category.successRate} 
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                  <Typography variant="caption" color="text.secondary">
                    {category.wins}/{category.bids} wins • R{(category.totalValue / 1000000).toFixed(1)}M value
                  </Typography>
                </Box>
              ))}
            </Stack>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const renderCompetitiveTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
          🥊 Competitive Analysis
        </Typography>
      </Grid>
      
      {/* Competitor Head-to-Head */}
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
              👥 Head-to-Head Performance
            </Typography>
            <Grid container spacing={2}>
              {metrics.competitorAnalysis.map((competitor, index) => (
                <Grid item xs={12} md={4} key={index}>
                  <Paper sx={{ p: 2, border: '1px solid #e0e0e0' }}>
                    <Typography variant="subtitle1" fontWeight="bold">
                      {competitor.competitorName}
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                      <Typography variant="body2">Win Rate:</Typography>
                      <Chip 
                        label={`${competitor.winRate.toFixed(1)}%`}
                        color={competitor.winRate > 50 ? 'success' : 'error'}
                        size="small"
                      />
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                      <Typography variant="body2">Record:</Typography>
                      <Typography variant="body2">
                        {competitor.headsUpWins}W - {competitor.headsUpLosses}L
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                      <Typography variant="body2">Avg Difference:</Typography>
                      <Typography 
                        variant="body2" 
                        color={competitor.avgBidDifference > 0 ? 'error' : 'success'}
                      >
                        R{Math.abs(competitor.avgBidDifference).toLocaleString()}
                        {competitor.avgBidDifference > 0 ? ' higher' : ' lower'}
                      </Typography>
                    </Box>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const renderPsychologicalTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
          🧠 Psychological Insights
        </Typography>
      </Grid>
      
      {/* Behavioral Patterns */}
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
              🎭 Behavioral Patterns
            </Typography>
            <Stack spacing={2}>
              {metrics.behavioralPatterns.map((pattern, index) => (
                <Alert 
                  key={index}
                  severity={pattern.impact === 'positive' ? 'success' : pattern.impact === 'negative' ? 'warning' : 'info'}
                >
                  <Typography variant="body1" fontWeight="bold">
                    {pattern.pattern}
                  </Typography>
                  <Typography variant="body2">
                    {pattern.recommendation}
                  </Typography>
                  <Typography variant="caption">
                    Frequency: {pattern.frequency}% of observations
                  </Typography>
                </Alert>
              ))}
            </Stack>
          </CardContent>
        </Card>
      </Grid>

      {/* Optimal Performance Times */}
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
              ⏰ Optimal Bidding Times
            </Typography>
            <Stack spacing={1}>
              {metrics.optimalBiddingTimes.map((time, index) => (
                <Chip 
                  key={index}
                  label={time}
                  color="primary"
                  variant="outlined"
                />
              ))}
            </Stack>
            <Typography variant="body2" sx={{ mt: 2 }} color="text.secondary">
              Your success rate is {Math.abs(metrics.stressImpactOnSuccess)}% higher during these times.
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      {/* Stress Impact */}
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
              😰 Stress Impact Analysis
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Typography variant="h4" color="error" fontWeight="bold">
                {metrics.stressImpactOnSuccess.toFixed(1)}%
              </Typography>
              <Typography variant="body1" sx={{ ml: 1 }}>
                success rate decrease under stress
              </Typography>
            </Box>
            <Alert severity="warning">
              <Typography variant="body2">
                Consider stress management techniques before important bids. 
                Your performance improves significantly when relaxed.
              </Typography>
            </Alert>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" fontWeight="bold">
          📊 Bid Analytics & Performance
        </Typography>
        <Stack direction="row" spacing={2}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Time Range</InputLabel>
            <Select
              value={timeRange}
              label="Time Range"
              onChange={(e) => onTimeRangeChange(e.target.value as any)}
            >
              <MenuItem value="week">This Week</MenuItem>
              <MenuItem value="month">This Month</MenuItem>
              <MenuItem value="quarter">This Quarter</MenuItem>
              <MenuItem value="year">This Year</MenuItem>
            </Select>
          </FormControl>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={loadBidAnalytics}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<Download />}
            onClick={exportAnalytics}
          >
            Export
          </Button>
        </Stack>
      </Box>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange}>
          <Tab label="📊 Overview" />
          <Tab label="🥊 Competitive" />
          <Tab label="🧠 Psychological" />
          <Tab label="📈 Trends" />
          <Tab label="💰 Financial" />
        </Tabs>
      </Box>

      {/* Tab Content */}
      {activeTab === 0 && renderOverviewTab()}
      {activeTab === 1 && renderCompetitiveTab()}
      {activeTab === 2 && renderPsychologicalTab()}
      {activeTab === 3 && (
        <Typography variant="h6">Trends analysis coming soon...</Typography>
      )}
      {activeTab === 4 && (
        <Typography variant="h6">Financial analysis coming soon...</Typography>
      )}
    </Box>
  );
};

export default BidAnalyticsDashboard;

// Export additional types for use elsewhere
export type { BidPerformanceMetrics, MonthlyTrend, CategoryPerformance, CompetitorAnalysis, BehavioralPattern };
