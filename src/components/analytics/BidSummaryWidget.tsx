import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardContent,
  Typography,
  Box,
  Grid,
  Chip,
  LinearProgress,
  <PERSON>ert,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>vider,
  IconButton,
  Tooltip,
  Collapse
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  EmojiEvents,
  MonetizationOn,
  Speed,
  Psychology,
  ExpandMore,
  ExpandLess,
  Insights,
  Timeline,
  CompareArrows
} from '@mui/icons-material';
import { useGetBidSummaryQuery } from '../../services/api/analytics.api';

interface BidSummaryProps {
  userId: string;
  compact?: boolean;
  showEconomicImpact?: boolean;
  showPsychologicalInsights?: boolean;
  onViewFullAnalytics?: () => void;
}

// Use the API types directly
import type { BidSummary } from '../../services/api/analytics.api';

// Type alias for consistency
type QuickBidSummary = BidSummary;

interface BidInsight {
  type: 'success' | 'warning' | 'info' | 'achievement';
  title: string;
  description: string;
  value?: string;
  trend?: 'up' | 'down' | 'stable';
}

interface BidAlert {
  severity: 'error' | 'warning' | 'info' | 'success';
  title: string;
  message: string;
  actionRequired: boolean;
  deadline?: string;
}

interface BidRecommendation {
  priority: 'high' | 'medium' | 'low';
  category: 'performance' | 'financial' | 'competitive' | 'psychological';
  title: string;
  description: string;
  expectedImpact: string;
}

const BidSummaryWidget: React.FC<BidSummaryProps> = ({
  userId,
  compact = false,
  showEconomicImpact = true,
  showPsychologicalInsights = true,
  onViewFullAnalytics
}) => {
  // Use RTK Query hook for real API data
  const {
    data: summary,
    isLoading: loading,
    error,
    refetch
  } = useGetBidSummaryQuery({
    userId,
    compact
  });

  const [expanded, setExpanded] = useState(!compact);

  const loadBidSummary = () => {
    refetch();
  };

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up': return <TrendingUp color="success" />;
      case 'down': return <TrendingDown color="error" />;
      default: return <Timeline color="info" />;
    }
  };

  const getMoodIcon = (mood: string) => {
    switch (mood) {
      case 'motivated': return '🚀';
      case 'confident': return '💪';
      case 'stressed': return '😰';
      case 'overwhelmed': return '🤯';
      default: return '😐';
    }
  };

  const getInsightColor = (type: string) => {
    switch (type) {
      case 'success': return 'success';
      case 'warning': return 'warning';
      case 'achievement': return 'primary';
      default: return 'info';
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent>
          <LinearProgress />
          <Typography variant="body2" sx={{ mt: 1, textAlign: 'center' }}>
            Loading bid summary...
          </Typography>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert severity="error">
        Failed to load bid summary. Please try again.
      </Alert>
    );
  }

  if (!summary) {
    return (
      <Alert severity="info">
        No bid data available yet. Start bidding to see your analytics!
      </Alert>
    );
  }

  return (
    <Card>
      <CardContent>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" fontWeight="bold">
            📊 Bid Performance Summary
          </Typography>
          <Stack direction="row" spacing={1}>
            {onViewFullAnalytics && (
              <Button
                size="small"
                variant="outlined"
                startIcon={<Insights />}
                onClick={onViewFullAnalytics}
              >
                Full Analytics
              </Button>
            )}
            {compact && (
              <IconButton
                size="small"
                onClick={() => setExpanded(!expanded)}
              >
                {expanded ? <ExpandLess /> : <ExpandMore />}
              </IconButton>
            )}
          </Stack>
        </Box>

        {/* Recent Performance */}
        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={6} md={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" fontWeight="bold" color="primary">
                {summary.recent_performance.last_30_days.success_rate.toFixed(1)}%
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <Typography variant="caption" color="text.secondary">
                  Success Rate
                </Typography>
                {getTrendIcon(summary.recent_performance.trend)}
              </Box>
            </Box>
          </Grid>
          
          <Grid item xs={6} md={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" fontWeight="bold" color="success.main">
                {summary.recent_performance.last_30_days.wins}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Wins (30 days)
              </Typography>
            </Box>
          </Grid>
          
          <Grid item xs={6} md={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" fontWeight="bold" color="info.main">
                R{(summary.recent_performance.last_30_days.value / 1000000).toFixed(1)}M
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Total Value
              </Typography>
            </Box>
          </Grid>
          
          <Grid item xs={6} md={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" fontWeight="bold" color="warning.main">
                #{summary.quick_stats.competitive_position}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Market Rank
              </Typography>
            </Box>
          </Grid>
        </Grid>

        <Collapse in={expanded}>
          {/* Top Insights */}
          <Box sx={{ mb: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 1 }}>
              🎯 Key Insights
            </Typography>
            <Stack spacing={1}>
              {summary.top_insights.slice(0, compact ? 2 : 3).map((insight, index) => (
                <Alert 
                  key={index}
                  severity={getInsightColor(insight.type) as any}
                  sx={{ py: 0.5 }}
                >
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Box>
                      <Typography variant="body2" fontWeight="bold">
                        {insight.title}
                      </Typography>
                      <Typography variant="caption">
                        {insight.description}
                      </Typography>
                    </Box>
                    {insight.value && (
                      <Chip 
                        label={insight.value}
                        size="small"
                        color={getInsightColor(insight.type) as any}
                      />
                    )}
                  </Box>
                </Alert>
              ))}
            </Stack>
          </Box>

          {/* Economic Impact */}
          {showEconomicImpact && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 1 }}>
                🌍 Economic Impact
              </Typography>
              <Card variant="outlined" sx={{ background: 'linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%)' }}>
                <CardContent sx={{ py: 1.5 }}>
                  <Grid container spacing={2}>
                    <Grid item xs={4}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h6" fontWeight="bold" color="success.main">
                          {summary.economic_impact.jobs_created}
                        </Typography>
                        <Typography variant="caption">Jobs Created</Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={4}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h6" fontWeight="bold" color="success.main">
                          R{(summary.economic_impact.economic_value / 1000000).toFixed(1)}M
                        </Typography>
                        <Typography variant="caption">Economic Value</Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={4}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Chip
                          label={summary.economic_impact.community_rank}
                          size="small"
                          color="success"
                        />
                      </Box>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Box>
          )}

          {/* Psychological Insights */}
          {showPsychologicalInsights && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 1 }}>
                🧠 Psychological State
              </Typography>
              <Card variant="outlined">
                <CardContent sx={{ py: 1.5 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Typography variant="h4" sx={{ mr: 1 }}>
                      {getMoodIcon(summary.psychological_state.current_mood)}
                    </Typography>
                    <Box>
                      <Typography variant="body1" fontWeight="bold" sx={{ textTransform: 'capitalize' }}>
                        {summary.psychological_state.current_mood}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Optimal time: {summary.psychological_state.optimal_time}
                      </Typography>
                    </Box>
                  </Box>
                  <Alert severity="info" sx={{ py: 0.5 }}>
                    <Typography variant="body2">
                      {summary.psychological_state.recommendation}
                    </Typography>
                  </Alert>
                </CardContent>
              </Card>
            </Box>
          )}

          {/* Alerts */}
          {summary.alerts.length > 0 && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 1 }}>
                🚨 Alerts
              </Typography>
              <Stack spacing={1}>
                {summary.alerts.map((alert, index) => (
                  <Alert key={index} severity={alert.severity} sx={{ py: 0.5 }}>
                    <Typography variant="body2" fontWeight="bold">
                      {alert.title}
                    </Typography>
                    <Typography variant="caption">
                      {alert.message}
                    </Typography>
                  </Alert>
                ))}
              </Stack>
            </Box>
          )}

          {/* Top Recommendations */}
          <Box>
            <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 1 }}>
              💡 Recommendations
            </Typography>
            <Stack spacing={1}>
              {summary.recommendations.slice(0, compact ? 1 : 2).map((rec, index) => (
                <Card key={index} variant="outlined">
                  <CardContent sx={{ py: 1.5 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="body2" fontWeight="bold">
                          {rec.title}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {rec.description}
                        </Typography>
                      </Box>
                      <Chip
                        label={rec.expected_impact}
                        size="small"
                        color={rec.priority === 'high' ? 'error' : rec.priority === 'medium' ? 'warning' : 'info'}
                      />
                    </Box>
                  </CardContent>
                </Card>
              ))}
            </Stack>
          </Box>
        </Collapse>
      </CardContent>
    </Card>
  );
};

export default BidSummaryWidget;
