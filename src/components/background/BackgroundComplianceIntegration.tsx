'use client';

import React, { useEffect } from 'react';
import { <PERSON>ert, <PERSON>nac<PERSON>bar, Typography, Box, Chip } from '@mui/material';
import { CheckCircle, Warning, Gavel, Assignment } from '@mui/icons-material';

interface ComplianceAlert {
  id: string;
  type: 'compliance_issue' | 'auto_task_assigned' | 'evidence_collection_started' | 'protest_opportunity';
  title: string;
  description: string;
  severity: 'info' | 'warning' | 'error' | 'success';
  autoAction?: string;
  taskId?: string;
}

interface Props {
  userId: string;
  onComplianceAlert?: (alert: ComplianceAlert) => void;
}

export default function BackgroundComplianceIntegration({ userId, onComplianceAlert }: Props) {
  const [complianceAlerts, setComplianceAlerts] = React.useState<ComplianceAlert[]>([]);
  const [currentAlert, setCurrentAlert] = React.useState<ComplianceAlert | null>(null);

  useEffect(() => {
    // Listen for background compliance alerts
    const eventSource = new EventSource(`/api/compliance/alerts/stream?userId=${userId}`);
    
    eventSource.onmessage = (event) => {
      const alert: ComplianceAlert = JSON.parse(event.data);
      handleComplianceAlert(alert);
    };

    eventSource.onerror = (error) => {
      console.error('Compliance alert stream error:', error);
    };

    return () => {
      eventSource.close();
    };
  }, [userId]);

  const handleComplianceAlert = (alert: ComplianceAlert) => {
    setComplianceAlerts(prev => [...prev, alert]);
    setCurrentAlert(alert);
    
    // Call parent handler if provided
    if (onComplianceAlert) {
      onComplianceAlert(alert);
    }

    // Auto-hide non-critical alerts after 10 seconds
    if (alert.severity !== 'error') {
      setTimeout(() => {
        setCurrentAlert(null);
      }, 10000);
    }
  };

  const getAlertIcon = (type: string) => {
    const icons = {
      'compliance_issue': <Warning />,
      'auto_task_assigned': <Assignment />,
      'evidence_collection_started': <CheckCircle />,
      'protest_opportunity': <Gavel />
    };
    return icons[type as keyof typeof icons] || <CheckCircle />;
  };

  const getAlertColor = (severity: string) => {
    const colors = {
      'info': 'info',
      'warning': 'warning', 
      'error': 'error',
      'success': 'success'
    };
    return colors[severity as keyof typeof colors] || 'info';
  };

  const handleAlertClose = () => {
    setCurrentAlert(null);
  };

  // This component renders nothing visible - it's purely for background integration
  // Alerts are shown through the Snackbar system
  return (
    <>
      {currentAlert && (
        <Snackbar
          open={true}
          onClose={handleAlertClose}
          anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
          autoHideDuration={currentAlert.severity === 'error' ? null : 10000}
        >
          <Alert 
            severity={getAlertColor(currentAlert.severity) as any}
            onClose={handleAlertClose}
            icon={getAlertIcon(currentAlert.type)}
            sx={{ minWidth: 400 }}
          >
            <Box>
              <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                {currentAlert.title}
              </Typography>
              <Typography variant="body2" sx={{ mt: 0.5 }}>
                {currentAlert.description}
              </Typography>
              
              {currentAlert.autoAction && (
                <Chip 
                  label={`AI Action: ${currentAlert.autoAction}`}
                  size="small"
                  color="primary"
                  sx={{ mt: 1 }}
                />
              )}
            </Box>
          </Alert>
        </Snackbar>
      )}
    </>
  );
}

// Hook for using background compliance in other components
export function useBackgroundCompliance(userId: string) {
  const [complianceStatus, setComplianceStatus] = React.useState({
    activeIssues: 0,
    autoAssignedTasks: 0,
    protestOpportunities: 0,
    lastCheck: null as Date | null
  });

  useEffect(() => {
    // Fetch current compliance status
    const fetchComplianceStatus = async () => {
      try {
        const response = await fetch(`/api/compliance/status?userId=${userId}`);
        if (response.ok) {
          const status = await response.json();
          setComplianceStatus(status);
        }
      } catch (error) {
        console.error('Error fetching compliance status:', error);
      }
    };

    fetchComplianceStatus();
    
    // Refresh every 5 minutes
    const interval = setInterval(fetchComplianceStatus, 300000);
    
    return () => clearInterval(interval);
  }, [userId]);

  return complianceStatus;
}

// Component for showing compliance status in dashboard
export function ComplianceStatusIndicator({ userId }: { userId: string }) {
  const status = useBackgroundCompliance(userId);

  if (status.activeIssues === 0 && status.protestOpportunities === 0) {
    return null; // Don't show anything if no issues
  }

  return (
    <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
      {status.activeIssues > 0 && (
        <Chip
          icon={<Warning />}
          label={`${status.activeIssues} Compliance Issues`}
          color="warning"
          size="small"
        />
      )}
      
      {status.protestOpportunities > 0 && (
        <Chip
          icon={<Gavel />}
          label={`${status.protestOpportunities} Protest Opportunities`}
          color="error"
          size="small"
        />
      )}
      
      {status.autoAssignedTasks > 0 && (
        <Chip
          icon={<Assignment />}
          label={`${status.autoAssignedTasks} AI Tasks Active`}
          color="info"
          size="small"
        />
      )}
    </Box>
  );
}
