'use client';

import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  IconButton,
  Divider,
  Badge,
  Tabs,
  Tab,
  Card,
  CardContent,
  Stack,
  Alert,
  Menu,
  MenuItem,
  Tooltip,
  LinearProgress
} from '@mui/material';
import {
  Send as SendIcon,
  AttachFile as AttachIcon,
  Mic as MicIcon,
  VideoCall as VideoIcon,
  Phone as PhoneIcon,
  MoreVert as MoreIcon,
  PushPin as PinIcon,
  Reply as ReplyIcon,
  EmojiEmotions as EmojiIcon,
  Schedule as ScheduleIcon,
  Assignment as TaskIcon,
  Description as DocumentIcon,
  Group as GroupIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';

import {
  TenderCommunicationWorkspace,
  TenderChannel,
  TenderMessage,
  ChannelType
} from '../../types/communication';
import { TeamActiveWorkspace } from '../../types/teamCollaboration';
import TenderCommunicationService from '../../services/TenderCommunicationService';

interface TenderCommunicationPanelProps {
  teamWorkspace: TeamActiveWorkspace;
  onClose?: () => void;
}

export default function TenderCommunicationPanel({ 
  teamWorkspace, 
  onClose 
}: TenderCommunicationPanelProps) {
  const [communicationWorkspace, setCommunicationWorkspace] = useState<TenderCommunicationWorkspace | null>(null);
  const [activeChannel, setActiveChannel] = useState<TenderChannel | null>(null);
  const [messageText, setMessageText] = useState('');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [activeTab, setActiveTab] = useState(0);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const communicationService = TenderCommunicationService.getInstance();

  useEffect(() => {
    initializeCommunication();
  }, [teamWorkspace]);

  useEffect(() => {
    scrollToBottom();
  }, [activeChannel?.messages]);

  const initializeCommunication = async () => {
    try {
      setLoading(true);
      
      // Check if communication workspace already exists
      let workspace = await communicationService.getTenderCommunicationWorkspace(
        teamWorkspace.teamBidInterest.tenderId
      );
      
      // Create workspace if it doesn't exist
      if (!workspace) {
        workspace = await communicationService.createTenderCommunicationWorkspace(teamWorkspace);
      }
      
      setCommunicationWorkspace(workspace);
      
      // Set default active channel (main discussion)
      const mainChannel = workspace.channels.find(c => c.type === 'main_discussion');
      if (mainChannel) {
        setActiveChannel(mainChannel);
      }
      
    } catch (error) {
      console.error('Error initializing communication:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSendMessage = async () => {
    if (!messageText.trim() || !activeChannel || !communicationWorkspace) return;
    
    try {
      setSending(true);
      
      await communicationService.sendMessage(
        activeChannel.id,
        'current-user-id', // Would get from auth context
        messageText,
        'text'
      );
      
      setMessageText('');
      
      // Refresh the workspace to get updated messages
      const updatedWorkspace = await communicationService.getTenderCommunicationWorkspace(
        teamWorkspace.teamBidInterest.tenderId
      );
      
      if (updatedWorkspace) {
        setCommunicationWorkspace(updatedWorkspace);
        const updatedChannel = updatedWorkspace.channels.find(c => c.id === activeChannel.id);
        if (updatedChannel) {
          setActiveChannel(updatedChannel);
        }
      }
      
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setSending(false);
    }
  };

  const handleChannelChange = (channel: TenderChannel) => {
    setActiveChannel(channel);
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const formatMessageTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('en-ZA', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getChannelIcon = (type: ChannelType) => {
    switch (type) {
      case 'main_discussion': return '💬';
      case 'technical': return '🔧';
      case 'commercial': return '💰';
      case 'compliance': return '⚖️';
      case 'project_management': return '📋';
      case 'document_review': return '📄';
      default: return '💬';
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'critical': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      default: return 'default';
    }
  };

  if (loading) {
    return (
      <Paper elevation={2} sx={{ height: 600, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Box sx={{ textAlign: 'center' }}>
          <LinearProgress sx={{ mb: 2, width: 200 }} />
          <Typography variant="body2" color="text.secondary">
            Setting up communication workspace...
          </Typography>
        </Box>
      </Paper>
    );
  }

  if (!communicationWorkspace) {
    return (
      <Paper elevation={2} sx={{ height: 600, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Alert severity="error">
          Failed to initialize communication workspace
        </Alert>
      </Paper>
    );
  }

  return (
    <Paper elevation={2} sx={{ height: 600, display: 'flex', flexDirection: 'column' }}>
      {/* Header with Tender Context */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider', bgcolor: 'primary.main', color: 'white' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              💬 Team Communication
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.9 }}>
              {communicationWorkspace.tenderTitle} ({communicationWorkspace.tenderReference})
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Voice Call">
              <IconButton size="small" sx={{ color: 'white' }}>
                <PhoneIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Video Meeting">
              <IconButton size="small" sx={{ color: 'white' }}>
                <VideoIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Settings">
              <IconButton size="small" sx={{ color: 'white' }}>
                <SettingsIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
        
        {/* Tender Quick Info */}
        <Box sx={{ display: 'flex', gap: 2, mt: 1, flexWrap: 'wrap' }}>
          <Chip 
            label={`Status: ${communicationWorkspace.tenderContext.bidStatus}`}
            size="small"
            sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}
          />
          <Chip 
            label={`Closing: ${new Date(communicationWorkspace.tenderContext.closingDate).toLocaleDateString()}`}
            size="small"
            sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}
          />
          <Chip 
            label={`Team: ${communicationWorkspace.tenderContext.teamMembers.length} members`}
            size="small"
            sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}
          />
        </Box>
      </Box>

      <Box sx={{ display: 'flex', flex: 1, overflow: 'hidden' }}>
        {/* Channel Sidebar */}
        <Box sx={{ width: 200, borderRight: 1, borderColor: 'divider', bgcolor: 'grey.50' }}>
          <Box sx={{ p: 1 }}>
            <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
              Channels
            </Typography>
            <List dense>
              {communicationWorkspace.channels.map((channel) => (
                <ListItem
                  key={channel.id}
                  button
                  selected={activeChannel?.id === channel.id}
                  onClick={() => handleChannelChange(channel)}
                  sx={{ borderRadius: 1, mb: 0.5 }}
                >
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <span>{getChannelIcon(channel.type)}</span>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {channel.name.replace(/^[💬🔧💰⚖️📋📄]\s*/, '')}
                        </Typography>
                      </Box>
                    }
                    secondary={`${channel.messages.length} messages`}
                  />
                  {channel.messages.some(m => m.tenderContext.urgencyLevel === 'critical') && (
                    <Badge color="error" variant="dot" />
                  )}
                </ListItem>
              ))}
            </List>
          </Box>
        </Box>

        {/* Chat Area */}
        <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          {activeChannel ? (
            <>
              {/* Channel Header */}
              <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      {getChannelIcon(activeChannel.type)} {activeChannel.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {activeChannel.description}
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Chip 
                      label={`${activeChannel.participants.length} members`}
                      size="small"
                      icon={<GroupIcon />}
                    />
                  </Box>
                </Box>
              </Box>

              {/* Messages */}
              <Box sx={{ flex: 1, overflow: 'auto', p: 1 }}>
                <List>
                  {activeChannel.messages.map((message) => (
                    <ListItem key={message.id} sx={{ alignItems: 'flex-start', py: 1 }}>
                      <ListItemAvatar>
                        <Avatar sx={{ width: 32, height: 32 }}>
                          {message.authorName.charAt(0)}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                            <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                              {message.authorName}
                            </Typography>
                            <Chip 
                              label={message.authorRole}
                              size="small"
                              variant="outlined"
                              sx={{ height: 20, fontSize: '0.7rem' }}
                            />
                            <Typography variant="caption" color="text.secondary">
                              {formatMessageTime(message.timestamp)}
                            </Typography>
                            {message.tenderContext.urgencyLevel !== 'medium' && (
                              <Chip
                                label={message.tenderContext.urgencyLevel}
                                size="small"
                                color={getUrgencyColor(message.tenderContext.urgencyLevel) as any}
                                sx={{ height: 20, fontSize: '0.7rem' }}
                              />
                            )}
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="body2" sx={{ mb: 1 }}>
                              {message.content}
                            </Typography>
                            
                            {/* Tender Context */}
                            {message.tenderContext.relatedSection && (
                              <Chip
                                label={`Related to: ${message.tenderContext.relatedSection}`}
                                size="small"
                                variant="outlined"
                                sx={{ mr: 1, mb: 1 }}
                              />
                            )}
                            
                            {/* Attachments */}
                            {message.attachments.length > 0 && (
                              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 1 }}>
                                {message.attachments.map((attachment, idx) => (
                                  <Chip
                                    key={idx}
                                    label={attachment.name}
                                    size="small"
                                    icon={<DocumentIcon />}
                                    clickable
                                    variant="outlined"
                                  />
                                ))}
                              </Box>
                            )}
                            
                            {/* Action Items */}
                            {message.actionItems.length > 0 && (
                              <Box sx={{ mt: 1 }}>
                                {message.actionItems.map((action, idx) => (
                                  <Alert key={idx} severity="info" sx={{ mb: 1 }}>
                                    <Typography variant="body2">
                                      <strong>Action:</strong> {action.description}
                                    </Typography>
                                  </Alert>
                                ))}
                              </Box>
                            )}
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
                <div ref={messagesEndRef} />
              </Box>

              {/* Message Input */}
              <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
                <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>
                  <TextField
                    fullWidth
                    multiline
                    maxRows={3}
                    placeholder={`Message ${activeChannel.name}...`}
                    value={messageText}
                    onChange={(e) => setMessageText(e.target.value)}
                    onKeyPress={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        handleSendMessage();
                      }
                    }}
                    disabled={sending}
                    variant="outlined"
                    size="small"
                  />
                  <IconButton size="small" disabled={sending}>
                    <AttachIcon />
                  </IconButton>
                  <IconButton size="small" disabled={sending}>
                    <EmojiIcon />
                  </IconButton>
                  <Button
                    variant="contained"
                    onClick={handleSendMessage}
                    disabled={!messageText.trim() || sending}
                    startIcon={<SendIcon />}
                    size="small"
                  >
                    Send
                  </Button>
                </Box>
                
                {/* Tender Context Reminder */}
                <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                  💡 All messages are automatically linked to {communicationWorkspace.tenderReference}
                </Typography>
              </Box>
            </>
          ) : (
            <Box sx={{ flex: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                Select a channel to start communicating
              </Typography>
            </Box>
          )}
        </Box>
      </Box>
    </Paper>
  );
}
