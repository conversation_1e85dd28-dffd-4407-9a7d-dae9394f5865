'use client';

import React from 'react';
import {
  AppBar,
  Toolbar,
  Typography,
  Button,
  Box,
  Chip
} from '@mui/material';
import {
  Home as HomeIcon,
  Dashboard as DashboardIcon,
  Analytics as AnalyticsIcon,
  WhatsApp as WhatsAppIcon,
  Security as SecurityIcon,
  Assignment as AssignmentIcon,
  Search as SearchIcon,
  Business as BusinessIcon,
  Map as MapIcon
} from '@mui/icons-material';

const quickNavItems = [
  { label: 'Home', icon: <HomeIcon />, url: '/' },
  { label: 'Dashboard', icon: <DashboardIcon />, url: '/dashboard' },
  { label: 'Tenders', icon: <SearchIcon />, url: '/tenders' },
  { label: 'Bids', icon: <AssignmentIcon />, url: '/bids' },
  { label: 'WhatsApp', icon: <WhatsAppIcon />, url: '/whatsapp' },
  { label: 'Compliance', icon: <SecurityIcon />, url: '/compliance' },
  { label: 'Analytics', icon: <AnalyticsIcon />, url: '/analytics' },
  { label: 'Supplier', icon: <BusinessIcon />, url: '/supplier' },
  { label: 'All Pages', icon: <MapIcon />, url: '/sitemap' }
];

interface QuickNavBarProps {
  currentPage?: string;
}

export default function QuickNavBar({ currentPage }: QuickNavBarProps) {
  return (
    <AppBar position="static" color="transparent" elevation={1} sx={{ mb: 2 }}>
      <Toolbar sx={{ overflowX: 'auto', py: 1 }}>
        <Typography variant="h6" sx={{ mr: 3, minWidth: 'fit-content', fontWeight: 600 }}>
          🐝 BidBeez
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 1, overflowX: 'auto', minWidth: 0 }}>
          {quickNavItems.map((item) => (
            <Button
              key={item.url}
              variant={currentPage === item.url ? 'contained' : 'outlined'}
              size="small"
              startIcon={item.icon}
              href={item.url}
              sx={{ 
                minWidth: 'fit-content',
                whiteSpace: 'nowrap',
                textTransform: 'none'
              }}
            >
              {item.label}
            </Button>
          ))}
        </Box>

        <Box sx={{ ml: 'auto', display: 'flex', alignItems: 'center' }}>
          <Chip 
            label="Live Demo"
            size="small"
            color="success"
            variant="outlined"
          />
        </Box>
      </Toolbar>
    </AppBar>
  );
}