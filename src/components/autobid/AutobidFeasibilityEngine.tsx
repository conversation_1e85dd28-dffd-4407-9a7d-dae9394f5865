import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Alert,
  LinearProgress,
  Chip,
  Grid,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  CircularProgress,
  Stack,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Psychology,
  CheckCircle,
  Warning,
  Error as ErrorIcon,
  AutoAwesome,
  Build,
  School,
  People,
  LocationOn,
  Timer,
  TrendingUp,
  Refresh,
  PlayArrow,
  Stop
} from '@mui/icons-material';
import { Tender, EnhancedTender } from '../../types/tender.types';

interface AutobidFeasibilityProps {
  tender: EnhancedTender;
  onAutobidStart?: (feasibilityReport: FeasibilityReport) => void;
  onResourcesNeeded?: (missingResources: MissingResource[]) => void;
}

interface FeasibilityReport {
  overallScore: number;
  canAutobid: boolean;
  readinessLevel: 'ready' | 'needs_resources' | 'needs_onboarding' | 'not_feasible';
  missingResources: MissingResource[];
  availableResources: AvailableResource[];
  estimatedCompletionTime: string;
  successProbability: number;
  riskFactors: RiskFactor[];
  recommendations: Recommendation[];
  economicImpact: EconomicImpact;
}

interface EconomicImpact {
  jobsCreated: number;
  economicValue: number;
  workersSupported: number;
  familiesImpacted: number;
  communityBenefit: string;
  socialImpactScore: number;
}

interface MissingResource {
  type: 'skill' | 'tool' | 'certification' | 'team_member' | 'supplier' | 'contractor' | 'bee_task';
  name: string;
  criticality: 'critical' | 'high' | 'medium' | 'low';
  canAutoOnboard: boolean;
  estimatedTime: string;
  estimatedCost: number;
  alternatives: string[];
  onboardingPath: string;
  psychTrigger: string;
  beeTaskType?: 'document_collection' | 'briefing_attendance' | 'site_visit' | 'document_submission' | 'verification';
  physicalLocation?: string;
  requiresBee?: boolean;
  deliveryMode?: string;
  urgency?: 'standard' | 'urgent' | 'critical';
}

interface AvailableResource {
  type: 'skill' | 'tool' | 'certification' | 'team_member' | 'supplier' | 'contractor';
  name: string;
  quality: 'excellent' | 'good' | 'adequate' | 'needs_improvement';
  availability: 'available' | 'limited' | 'busy' | 'unavailable';
  matchScore: number;
}

interface RiskFactor {
  category: 'resource' | 'timeline' | 'compliance' | 'technical' | 'financial';
  description: string;
  impact: 'high' | 'medium' | 'low';
  probability: number;
  mitigation: string;
}

interface Recommendation {
  type: 'immediate' | 'before_bid' | 'during_project' | 'future';
  action: string;
  benefit: string;
  effort: 'low' | 'medium' | 'high';
  priority: 'critical' | 'high' | 'medium' | 'low';
}

const AutobidFeasibilityEngine: React.FC<AutobidFeasibilityProps> = ({
  tender,
  onAutobidStart,
  onResourcesNeeded
}) => {
  const [analyzing, setAnalyzing] = useState(false);
  const [feasibilityReport, setFeasibilityReport] = useState<FeasibilityReport | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    // Auto-analyze when tender changes
    analyzeFeasibility();
  }, [tender]);

  const analyzeFeasibility = async () => {
    setAnalyzing(true);
    
    try {
      // Simulate comprehensive feasibility analysis
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const report = await performFeasibilityAnalysis(tender);
      setFeasibilityReport(report);
      
      if (report.missingResources.length > 0) {
        onResourcesNeeded?.(report.missingResources);
      }
      
    } catch (error) {
      console.error('Feasibility analysis failed:', error);
    } finally {
      setAnalyzing(false);
    }
  };

  const performFeasibilityAnalysis = async (tender: EnhancedTender): Promise<FeasibilityReport> => {
    // Analyze tender for physical requirements
    const physicalRequirements = analyzePhysicalRequirements(tender);

    // Simulate comprehensive analysis
    const missingResources: MissingResource[] = [
      // Add physical task requirements (bee tasks)
      ...physicalRequirements.map(req => ({
        type: 'bee_task' as const,
        name: req.taskName,
        criticality: req.mandatory ? 'critical' as const : 'high' as const,
        canAutoOnboard: true, // Bees can always be assigned
        estimatedTime: req.estimatedTime,
        estimatedCost: req.estimatedCost,
        alternatives: req.alternatives,
        onboardingPath: getOnboardingPath(req.taskType, determineTaskUrgency(tender.closingDate)), // Use tender.closingDate
        psychTrigger: req.psychTrigger,
        beeTaskType: req.taskType,
        physicalLocation: req.location,
        requiresBee: true,
        deliveryMode: getOptimalDeliveryMode(req.taskType, determineTaskUrgency(tender.closingDate)), // Use tender.closingDate
        urgency: determineTaskUrgency(tender.closingDate) // Use tender.closingDate
      })),
      // Add other missing resources
      {
        type: 'skill' as const,
        name: 'Project Management Professional (PMP)',
        criticality: 'critical' as const,
        canAutoOnboard: true,
        estimatedTime: '6-8 weeks',
        estimatedCost: 25000,
        alternatives: ['PRINCE2', 'Agile Certification'],
        onboardingPath: 'SkillSync → PMP Course → Certification Exam',
        psychTrigger: 'CRITICAL: Missing PMP blocks R15.6M tender entry!'
      },
      {
        type: 'tool' as const,
        name: 'AutoCAD 2024 License',
        criticality: 'high' as const,
        canAutoOnboard: true,
        estimatedTime: '24 hours',
        estimatedCost: 8500,
        alternatives: ['FreeCAD', 'DraftSight'],
        onboardingPath: 'ToolSync → License Purchase → Installation',
        psychTrigger: 'TOOL SHORTAGE: Need AutoCAD for engineering drawings!'
      },
      {
        type: 'contractor' as const,
        name: 'Electrical Subcontractor',
        criticality: 'medium' as const,
        canAutoOnboard: true,
        estimatedTime: '3-5 days',
        estimatedCost: 0,
        alternatives: ['In-house electrical team', 'Alternative contractors'],
        onboardingPath: 'ContractorSync → Partner Matching → Agreement',
        psychTrigger: 'PARTNERSHIP NEEDED: Local electrical expertise required!'
      }
    ];

    const availableResources: AvailableResource[] = [
      {
        type: 'skill',
        name: 'Construction Management',
        quality: 'excellent',
        availability: 'available',
        matchScore: 95
      },
      {
        type: 'certification',
        name: 'ISO 9001:2015',
        quality: 'excellent',
        availability: 'available',
        matchScore: 100
      },
      {
        type: 'tool',
        name: 'Microsoft Project',
        quality: 'good',
        availability: 'available',
        matchScore: 85
      }
    ];

    const overallScore = calculateOverallScore(missingResources, availableResources);
    const canAutobid = overallScore >= 70 && missingResources.filter(r => r.criticality === 'critical').length === 0;

    // Calculate economic impact of this tender
    const economicImpact = calculateEconomicImpact(tender, missingResources);

    return {
      overallScore,
      canAutobid,
      readinessLevel: canAutobid ? 'ready' :
                     missingResources.some(r => r.canAutoOnboard) ? 'needs_onboarding' : 'not_feasible',
      missingResources,
      availableResources,
      estimatedCompletionTime: canAutobid ? '2-4 hours' : '2-8 weeks',
      successProbability: Math.max(20, overallScore - 10),
      economicImpact,
      riskFactors: [
        {
          category: 'resource',
          description: 'Missing critical PMP certification',
          impact: 'high',
          probability: 0.9,
          mitigation: 'Fast-track PMP certification through SkillSync'
        }
      ],
      recommendations: [
        {
          type: 'immediate',
          action: 'Start PMP certification process',
          benefit: 'Enables bidding on R15.6M+ tenders',
          effort: 'high',
          priority: 'critical'
        },
        {
          type: 'before_bid',
          action: 'Secure AutoCAD license',
          benefit: 'Required for technical drawings',
          effort: 'low',
          priority: 'high'
        }
      ]
    };
  };

  // Analyze tender for physical requirements that need bee assignment
  const analyzePhysicalRequirements = (tender: EnhancedTender) => {
    const requirements: Array<{
      taskName: string;
      taskType: 'document_collection' | 'briefing_attendance' | 'site_visit' | 'document_submission' | 'verification';
      mandatory: boolean;
      estimatedTime: string;
      estimatedCost: number;
      alternatives: string[];
      psychTrigger: string;
      location: string;
    }> = [];

    const description = `${tender.title} ${tender.description}`.toLowerCase();
    const briefingLocation = tender.briefing_location?.toLowerCase() || '';

    // Check for mandatory briefing meeting
    if (tender.briefing_date && briefingLocation) {
      const briefingIndicators = [
        'mandatory briefing',
        'compulsory briefing',
        'briefing meeting required',
        'attendance required',
        'site briefing',
        'pre-bid meeting'
      ];

      if (briefingIndicators.some(indicator => description.includes(indicator))) {
        requirements.push({
          taskName: 'Mandatory Tender Briefing Attendance',
          taskType: 'briefing_attendance',
          mandatory: true,
          estimatedTime: '4-6 hours',
          estimatedCost: 800,
          alternatives: ['Virtual attendance (if available)', 'Detailed briefing notes'],
          psychTrigger: '🚨 CRITICAL: Mandatory briefing attendance required or DISQUALIFIED!',
          location: tender.briefing_location || 'TBD'
        });
      }
    }

    // Check for hard copy document collection
    const hardCopyIndicators = [
      'documents must be purchased',
      'hard copy only',
      'collect from office',
      'purchase at',
      'available for collection',
      'documents can be obtained from',
      'tender documents available at'
    ];

    if (hardCopyIndicators.some(indicator => description.includes(indicator))) {
      requirements.push({
        taskName: 'Physical Document Collection',
        taskType: 'document_collection',
        mandatory: true,
        estimatedTime: '2-4 hours',
        estimatedCost: 500,
        alternatives: ['Digital download (if available)', 'Email request'],
        psychTrigger: '📋 CRITICAL: Hard copy documents must be collected physically!',
        location: extractDocumentCollectionLocation(description) || tender.location || 'TBD'
      });
    }

    // Check for physical submission requirements
    const physicalSubmissionIndicators = [
      'hand delivery only',
      'physical submission required',
      'no electronic submission',
      'submit to office',
      'deliver to',
      'drop off at'
    ];

    if (physicalSubmissionIndicators.some(indicator => description.includes(indicator))) {
      requirements.push({
        taskName: 'Physical Bid Submission',
        taskType: 'document_submission',
        mandatory: true,
        estimatedTime: '2-3 hours',
        estimatedCost: 400,
        alternatives: ['Email submission (if allowed)', 'Courier service'],
        psychTrigger: '📦 CRITICAL: Physical submission required - no digital option!',
        location: extractSubmissionLocation(description) || tender.location || 'TBD'
      });
    }

    // Check for site visits
    const siteVisitIndicators = [
      'site visit required',
      'mandatory site inspection',
      'site briefing',
      'on-site evaluation',
      'facility inspection'
    ];

    if (siteVisitIndicators.some(indicator => description.includes(indicator))) {
      requirements.push({
        taskName: 'Mandatory Site Visit',
        taskType: 'site_visit',
        mandatory: true,
        estimatedTime: '4-8 hours',
        estimatedCost: 1200,
        alternatives: ['Virtual site tour (if available)', 'Detailed site documentation'],
        psychTrigger: '🏗️ CRITICAL: Site visit mandatory for technical evaluation!',
        location: extractSiteLocation(description) || tender.location || 'TBD'
      });
    }

    return requirements;
  };

  // Helper functions to extract locations from tender text
  const extractDocumentCollectionLocation = (text: string): string | null => {
    // Simple extraction - could be enhanced with NLP
    const locationPatterns = [
      /collect from (.+?)(?:\.|,|$)/i,
      /available at (.+?)(?:\.|,|$)/i,
      /purchase at (.+?)(?:\.|,|$)/i
    ];

    for (const pattern of locationPatterns) {
      const match = text.match(pattern);
      if (match) return match[1].trim();
    }
    return null;
  };

  const extractSubmissionLocation = (text: string): string | null => {
    const locationPatterns = [
      /submit to (.+?)(?:\.|,|$)/i,
      /deliver to (.+?)(?:\.|,|$)/i,
      /drop off at (.+?)(?:\.|,|$)/i
    ];

    for (const pattern of locationPatterns) {
      const match = text.match(pattern);
      if (match) return match[1].trim();
    }
    return null;
  };

  const extractSiteLocation = (text: string): string | null => {
    const locationPatterns = [
      /site visit at (.+?)(?:\.|,|$)/i,
      /located at (.+?)(?:\.|,|$)/i,
      /facility at (.+?)(?:\.|,|$)/i
    ];

    for (const pattern of locationPatterns) {
      const match = text.match(pattern);
      if (match) return match[1].trim();
    }
    return null;
  };

  // Helper functions for delivery mode optimization
  const determineTaskUrgency = (deadline: string): 'standard' | 'urgent' | 'critical' => {
    const deadlineTime = new Date(deadline).getTime();
    const currentTime = new Date().getTime();
    const hoursRemaining = (deadlineTime - currentTime) / (1000 * 60 * 60);

    if (hoursRemaining <= 4) return 'critical';
    if (hoursRemaining <= 24) return 'urgent';
    return 'standard';
  };

  const getOptimalDeliveryMode = (taskType: string, urgency: 'standard' | 'urgent' | 'critical'): string => {
    // For document tasks, use courier dispatch engine
    if (taskType === 'document_collection' || taskType === 'document_submission') {
      switch (urgency) {
        case 'critical':
          return 'bee_direct'; // Fastest option
        case 'urgent':
          return 'bee_air_bee'; // Fast but reliable
        case 'standard':
          return 'courier'; // Cost-effective
        default:
          return 'courier';
      }
    }

    // For briefings and site visits, always use direct bee assignment
    return 'bee_direct';
  };

  const getOnboardingPath = (taskType: string, deliveryMode: string): string => {
    if (taskType === 'document_collection' || taskType === 'document_submission') {
      switch (deliveryMode) {
        case 'bee_direct':
          return 'Queen Bee Assignment → Direct Bee Dispatch → Task Execution';
        case 'bee_air_bee':
          return 'Courier Dispatch → Air Transport → Bee Delivery → Task Completion';
        case 'courier':
          return 'Courier Service Assignment → Standard Delivery → Task Completion';
        case 'courier_plus_bee':
          return 'Courier Dispatch → Urban Hub → Bee Final Mile → Task Completion';
        default:
          return 'Courier Service Assignment → Task Execution';
      }
    }

    return 'Queen Bee Assignment → Professional Bee Allocation → Task Execution';
  };

  // Calculate economic impact of tender participation
  const calculateEconomicImpact = (tender: EnhancedTender, missingResources: MissingResource[]): EconomicImpact => {
    // Base jobs from bee tasks
    const beeJobs = missingResources.filter(r => r.type === 'bee_task').length;

    // Jobs from document processing (AI + human verification)
    const documentJobs = 1; // AI engine + human oversight

    // Jobs from skill/tool onboarding
    const onboardingJobs = missingResources.filter(r =>
      r.type === 'skill' || r.type === 'tool' || r.type === 'certification'
    ).length * 0.5; // Trainers, assessors, support staff

    // Jobs from contractor/supplier engagement
    const partnerJobs = missingResources.filter(r =>
      r.type === 'contractor' || r.type === 'supplier'
    ).length * 2; // Each partner creates multiple jobs

    // Project execution jobs (if tender is won)
    const tenderValue = tender.value || 0;
    const projectJobs = Math.floor(tenderValue / 500000); // 1 job per R500k of project value

    const totalJobs = beeJobs + documentJobs + onboardingJobs + partnerJobs + projectJobs;

    // Economic multiplier effect
    const economicValue = totalJobs * 45000; // Average annual salary impact
    const workersSupported = Math.floor(totalJobs * 1.2); // Including indirect support
    const familiesImpacted = Math.floor(totalJobs * 3.2); // Average family size in SA

    // Community benefit calculation
    const communityBenefit = getCommunityBenefitMessage(totalJobs, tenderValue);
    const socialImpactScore = Math.min(100, (totalJobs * 10) + (tenderValue / 100000));

    return {
      jobsCreated: totalJobs,
      economicValue,
      workersSupported,
      familiesImpacted,
      communityBenefit,
      socialImpactScore
    };
  };

  const getCommunityBenefitMessage = (jobs: number, tenderValue: number): string => {
    if (jobs >= 50) {
      return `🏘️ Major community impact - Supporting ${jobs} jobs across multiple sectors!`;
    } else if (jobs >= 20) {
      return `🏠 Significant local impact - Creating ${jobs} employment opportunities!`;
    } else if (jobs >= 10) {
      return `👥 Meaningful contribution - Generating ${jobs} jobs in your community!`;
    } else if (jobs >= 5) {
      return `💼 Direct employment - Supporting ${jobs} workers and their families!`;
    } else {
      return `🤝 Economic participation - Contributing to local job creation!`;
    }
  };

  const calculateOverallScore = (missing: MissingResource[], available: AvailableResource[]): number => {
    const totalRequired = missing.length + available.length;
    const availableScore = available.reduce((sum, res) => sum + res.matchScore, 0);
    const missingPenalty = missing.reduce((sum, res) => {
      const penalty = res.criticality === 'critical' ? 30 :
                     res.criticality === 'high' ? 20 :
                     res.criticality === 'medium' ? 10 : 5;
      return sum + penalty;
    }, 0);

    return Math.max(0, Math.min(100, (availableScore / totalRequired) - missingPenalty));
  };

  const handleStartAutobid = () => {
    if (feasibilityReport && feasibilityReport.canAutobid) {
      onAutobidStart?.(feasibilityReport);
    }
  };

  const handleAutoOnboard = async (resource: MissingResource) => {
    // Trigger automatic onboarding for missing resource
    console.log(`Starting auto-onboarding for ${resource.name}`);

    switch (resource.type) {
      case 'skill':
        // Redirect to SkillSync
        window.open('/skillsync', '_blank');
        break;
      case 'tool':
        // Redirect to ToolSync
        window.open('/toolsync', '_blank');
        break;
      case 'contractor':
        // Redirect to ContractorSync
        window.open('/contractorsync', '_blank');
        break;
      case 'bee_task':
        // Assign bee task through Queen Bee Management System
        await assignBeeTask(resource);
        break;
    }
  };

  const assignBeeTask = async (resource: MissingResource) => {
    try {
      console.log(`🐝 Assigning bee task: ${resource.name}`);

      // Determine urgency based on tender deadline
      const urgency = determineTaskUrgency(tender.closingDate);

      // For document collection/submission tasks, use courier dispatch engine
      if (resource.beeTaskType === 'document_collection' || resource.beeTaskType === 'document_submission') {
        await assignCourierTask(resource, urgency);
      } else {
        // For briefings and site visits, use direct bee assignment
        await assignDirectBeeTask(resource, urgency);
      }

    } catch (error) {
      console.error('❌ Failed to assign bee task:', error);
      alert('Failed to assign bee task. Please try again or contact support.');
    }
  };


  const assignCourierTask = async (resource: MissingResource, urgency: 'standard' | 'urgent' | 'critical') => {
    try {
      console.log(`🚚 Using Courier Dispatch Engine for ${resource.name} (${urgency} urgency)`);

      // Determine pickup and delivery addresses based on task type
      const { pickupAddress, deliveryAddress } = getTaskAddresses(resource);

      // Determine delivery priority based on urgency
      const deliveryPriority = urgency === 'critical' ? 'speed' :
                              urgency === 'urgent' ? 'speed' : 'cost';

      // Create courier request through dispatch engine
      const courierRequest = {
        tenderId: tender.tender_id,
        userId: 'current-user-id', // Should be passed from context
        pickupAddress,
        deliveryAddress,
        documentType: resource.beeTaskType === 'document_collection' ? 'tender_documents' : 'bid_submission',
        deadline: tender.closingDate,
        priority: deliveryPriority,
        urgency,
        specialRequirements: {
          documentDescription: `${resource.name} for tender: ${tender.title}`,
          requiresSignature: true,
          requiresPhoto: true,
          confidential: true,
          fragile: false
        }
      };

      // Call Courier Dispatch Engine API
      const response = await fetch('/api/courier/dispatch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(courierRequest)
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Courier task assigned successfully:', result);

        // Show success notification with delivery mode and economic impact
        const deliveryMode = getDeliveryModeDescription(result.selectedMode);
        const jobsFromThisTask = calculateJobsFromTask(resource);
        alert(`🚚 ${deliveryMode} assigned successfully!\n` +
              `Task ID: ${result.id}\n` +
              `Estimated time: ${result.estimatedTime} hours\n` +
              `Cost: R${result.estimatedCost}\n` +
              `\n🌍 ECONOMIC IMPACT:\n` +
              `💼 Jobs created: ${jobsFromThisTask}\n` +
              `👥 Workers supported: ${Math.floor(jobsFromThisTask * 1.2)}\n` +
              `👨‍👩‍👧‍👦 Families impacted: ${Math.floor(jobsFromThisTask * 3.2)}\n` +
              `\nYou're not just getting documents - you're creating jobs! 🌟\n` +
              `Track progress in your dashboard.`);

        // Optionally redirect to tracking
        // window.open(`/courier/track/${result.id}`, '_blank');
      } else {
        throw new Error('Failed to assign courier task');
      }
    } catch (error) {
      console.error('❌ Failed to assign courier task:', error);
      // Fallback to direct bee assignment
      await assignDirectBeeTask(resource, urgency);
    }
  };

  const assignDirectBeeTask = async (resource: MissingResource, urgency: 'standard' | 'urgent' | 'critical') => {
    try {
      console.log(`🐝 Using Direct Bee Assignment for ${resource.name} (${urgency} urgency)`);

      // Create bee task request
      const beeTaskRequest = {
        tenderId: tender.tender_id,
        taskType: resource.beeTaskType,
        title: resource.name,
        description: `Physical task required for tender: ${tender.title}`,
        location: resource.physicalLocation,
        deadline: tender.closingDate,
        urgency,
        estimatedCost: resource.estimatedCost,
        priority: resource.criticality === 'critical' ? 'high' : 'medium',
        requirements: [resource.name],
        psychTrigger: resource.psychTrigger,
        specialRequirements: {
          attendanceRequired: resource.beeTaskType === 'briefing_attendance',
          siteAccess: resource.beeTaskType === 'site_visit',
          professionalAppearance: true,
          reportingRequired: true
        }
      };

      // Call Queen Bee Management System API
      const response = await fetch('/api/bee/tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(beeTaskRequest)
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Bee task assigned successfully:', result);

        // Show success notification with economic impact
        const jobsFromThisTask = calculateJobsFromTask(resource);
        alert(`🐝 Professional Bee assigned successfully!\n` +
              `Task ID: ${result.taskId}\n` +
              `Bee ID: ${result.assignedBeeId}\n` +
              `Estimated time: ${resource.estimatedTime}\n` +
              `Cost: R${resource.estimatedCost}\n` +
              `\n🌍 ECONOMIC IMPACT:\n` +
              `💼 Direct job created: 1 (Professional Bee)\n` +
              `👥 Support jobs: ${Math.floor(jobsFromThisTask * 0.5)} (Coordination & Quality Control)\n` +
              `👨‍👩‍👧‍👦 Families supported: ${Math.floor(jobsFromThisTask * 3.2)}\n` +
              `💰 Economic value: R${(jobsFromThisTask * 45000).toLocaleString()}\n` +
              `\nYou're creating meaningful employment! 🌟\n` +
              `Track progress in your dashboard.`);

        // Optionally redirect to bee tracking
        // window.open(`/bee/tasks/track/${tender.tender_id}`, '_blank');
      } else {
        throw new Error('Failed to assign bee task');
      }
    } catch (error) {
      console.error('❌ Failed to assign direct bee task:', error);
      alert('Failed to assign bee task. Please try again or contact support.');
    }
  };

  const getTaskAddresses = (resource: MissingResource) => {
    // Default addresses - should be enhanced with actual location data
    const defaultPickup = {
      street: 'User Location', // Should be from user profile
      city: 'Cape Town',
      province: 'Western Cape',
      postalCode: '8001',
      country: 'South Africa',
      coordinates: { latitude: -33.9249, longitude: 18.4241 }
    };

    const taskLocation = {
      street: resource.physicalLocation || 'Task Location',
      city: extractCityFromLocation(resource.physicalLocation),
      province: extractProvinceFromLocation(resource.physicalLocation),
      postalCode: '0000',
      country: 'South Africa',
      coordinates: { latitude: -26.2041, longitude: 28.0473 } // Default to Johannesburg
    };

    if (resource.beeTaskType === 'document_collection') {
      return {
        pickupAddress: taskLocation, // Collect from tender office
        deliveryAddress: defaultPickup // Deliver to user
      };
    } else {
      return {
        pickupAddress: defaultPickup, // Pick up from user
        deliveryAddress: taskLocation // Deliver to tender office
      };
    }
  };

  const extractCityFromLocation = (location?: string): string => {
    if (!location) return 'Cape Town';
    // Simple extraction - could be enhanced with geocoding
    const cities = ['Cape Town', 'Johannesburg', 'Durban', 'Pretoria', 'Port Elizabeth'];
    const foundCity = cities.find(city => location.toLowerCase().includes(city.toLowerCase()));
    return foundCity || 'Cape Town';
  };

  const extractProvinceFromLocation = (location?: string): string => {
    if (!location) return 'Western Cape';
    const provinces = [
      'Western Cape', 'Gauteng', 'KwaZulu-Natal', 'Eastern Cape',
      'Free State', 'Limpopo', 'Mpumalanga', 'Northern Cape', 'North West'
    ];
    const foundProvince = provinces.find(province =>
      location.toLowerCase().includes(province.toLowerCase())
    );
    return foundProvince || 'Western Cape';
  };

  const getDeliveryModeDescription = (mode: string): string => {
    switch (mode) {
      case 'bee_direct': return '🐝 Direct Bee';
      case 'courier': return '🚚 Courier';
      case 'bee_air_bee': return '✈️ Air Express';
      case 'courier_plus_bee': return '🚚🐝 Hybrid';
      case 'bee_air_bee_extended': return '✈️🐝 Extended Air';
      default: return '🚚 Courier';
    }
  };

  const getUrgencyColor = (urgency?: string): string => {
    switch (urgency) {
      case 'critical': return '#d32f2f'; // Red
      case 'urgent': return '#f57c00'; // Orange
      case 'standard': return '#388e3c'; // Green
      default: return '#757575'; // Grey
    }
  };

  const getUrgencyIcon = (urgency?: string): string => {
    switch (urgency) {
      case 'critical': return '🚨';
      case 'urgent': return '⚡';
      case 'standard': return '📅';
      default: return '⏰';
    }
  };

  const calculateJobsFromTask = (resource: MissingResource): number => {
    switch (resource.type) {
      case 'bee_task':
        // Direct bee job + support (coordination, quality control)
        return resource.beeTaskType === 'site_visit' || resource.beeTaskType === 'briefing_attendance' ? 2 : 1;
      case 'skill':
        // Trainer + assessor + support staff
        return 3;
      case 'tool':
        // License provider + support + training
        return 2;
      case 'certification':
        // Assessor + administrator + support
        return 3;
      case 'contractor':
        // Multiple workers per contractor
        return 5;
      case 'supplier':
        // Supply chain jobs
        return 3;
      default:
        return 1;
    }
  };

  const getReadinessColor = (level: string) => {
    switch (level) {
      case 'ready': return 'success';
      case 'needs_resources': return 'warning';
      case 'needs_onboarding': return 'info';
      case 'not_feasible': return 'error';
      default: return 'default';
    }
  };

  const getReadinessIcon = (level: string) => {
    switch (level) {
      case 'ready': return <CheckCircle />;
      case 'needs_resources': return <Warning />;
      case 'needs_onboarding': return <AutoAwesome />;
      case 'not_feasible': return <ErrorIcon />;
      default: return <Timer />;
    }
  };

  if (analyzing) {
    return (
      <Card sx={{ border: '2px solid', borderColor: 'primary.light', bgcolor: 'primary.lighter' }}>
        <CardContent sx={{ textAlign: 'center', py: 4 }}>
          <CircularProgress sx={{ mb: 2 }} />
          <Typography variant="h6" fontWeight="bold">
            🤖 Analyzing Autobid Feasibility...
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Checking resources, compliance, and success probability
          </Typography>
        </CardContent>
      </Card>
    );
  }

  if (!feasibilityReport) {
    return (
      <Card sx={{ border: '2px solid', borderColor: 'grey.300' }}>
        <CardContent sx={{ textAlign: 'center', py: 4 }}>
          <Button
            variant="contained"
            startIcon={<Psychology />}
            onClick={analyzeFeasibility}
            size="large"
          >
            🤖 Analyze Autobid Feasibility
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card sx={{ 
      border: '2px solid', 
      borderColor: `${getReadinessColor(feasibilityReport.readinessLevel)}.main`,
      bgcolor: `${getReadinessColor(feasibilityReport.readinessLevel)}.lighter`
    }}>
      <CardContent>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {getReadinessIcon(feasibilityReport.readinessLevel)}
            <Box>
              <Typography variant="h6" fontWeight="bold">
                🤖 Autobid Feasibility: {feasibilityReport.overallScore}%
              </Typography>
              <Chip 
                label={feasibilityReport.readinessLevel.replace('_', ' ').toUpperCase()}
                color={getReadinessColor(feasibilityReport.readinessLevel) as any}
                size="small"
              />
            </Box>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <IconButton onClick={analyzeFeasibility} size="small">
              <Refresh />
            </IconButton>
            <Button
              variant="outlined"
              size="small"
              onClick={() => setShowDetails(!showDetails)}
            >
              {showDetails ? 'Hide' : 'Show'} Details
            </Button>
          </Box>
        </Box>

        {/* Progress Bar */}
        <Box sx={{ mb: 3 }}>
          <LinearProgress 
            variant="determinate" 
            value={feasibilityReport.overallScore} 
            sx={{ height: 8, borderRadius: 4 }}
            color={getReadinessColor(feasibilityReport.readinessLevel) as any}
          />
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            Success Probability: {feasibilityReport.successProbability}% | 
            Completion Time: {feasibilityReport.estimatedCompletionTime}
          </Typography>
        </Box>

        {/* Economic Impact Display */}
        <Card sx={{
          mb: 3,
          background: 'linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%)',
          border: '2px solid #4caf50'
        }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
              <Typography variant="h6" sx={{ color: '#2e7d32', fontWeight: 'bold' }}>
                🌍 YOUR ECONOMIC IMPACT
              </Typography>
              <Chip
                label={`Social Impact Score: ${feasibilityReport.economicImpact.socialImpactScore}/100`}
                sx={{
                  backgroundColor: '#4caf50',
                  color: 'white',
                  fontWeight: 'bold'
                }}
              />
            </Box>

            <Grid container spacing={2} sx={{ mb: 2 }}>
              <Grid item xs={6} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" sx={{ color: '#2e7d32', fontWeight: 'bold' }}>
                    {feasibilityReport.economicImpact.jobsCreated}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    💼 Jobs Created
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" sx={{ color: '#2e7d32', fontWeight: 'bold' }}>
                    {feasibilityReport.economicImpact.workersSupported}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    👥 Workers Supported
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" sx={{ color: '#2e7d32', fontWeight: 'bold' }}>
                    {feasibilityReport.economicImpact.familiesImpacted}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    👨‍👩‍👧‍👦 Families Impacted
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" sx={{ color: '#2e7d32', fontWeight: 'bold' }}>
                    R{(feasibilityReport.economicImpact.economicValue / 1000000).toFixed(1)}M
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    💰 Economic Value
                  </Typography>
                </Box>
              </Grid>
            </Grid>

            <Alert severity="success" sx={{ backgroundColor: '#e8f5e8' }}>
              <Typography variant="body2" fontWeight="bold">
                {feasibilityReport.economicImpact.communityBenefit}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Every task you assign creates ripple effects throughout the economy - from bee workers to document processors, trainers to suppliers. You're not just bidding, you're building communities! 🌟
              </Typography>
            </Alert>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <Stack direction="row" spacing={2} sx={{ mb: 3 }}>
          {feasibilityReport.canAutobid ? (
            <Button
              variant="contained"
              startIcon={<PlayArrow />}
              onClick={handleStartAutobid}
              color="success"
              size="large"
              sx={{ flex: 1 }}
            >
              🚀 START AUTOBID & CREATE {feasibilityReport.economicImpact.jobsCreated} JOBS!
            </Button>
          ) : (
            <Button
              variant="contained"
              startIcon={<AutoAwesome />}
              color="primary"
              size="large"
              sx={{ flex: 1 }}
              disabled
            >
              🔧 Resources Needed First
            </Button>
          )}
        </Stack>

        {/* Missing Resources Alert */}
        {feasibilityReport.missingResources.length > 0 && (
          <Alert severity="warning" sx={{ mb: 3 }}>
            <Typography variant="body2" fontWeight="bold">
              ⚠️ Missing {feasibilityReport.missingResources.length} Required Resources
            </Typography>
            <Typography variant="caption">
              Auto-onboarding available for {feasibilityReport.missingResources.filter(r => r.canAutoOnboard).length} resources
            </Typography>
          </Alert>
        )}

        {/* Detailed Analysis */}
        {showDetails && (
          <Box>
            <Divider sx={{ my: 2 }} />
            
            {/* Missing Resources */}
            {feasibilityReport.missingResources.length > 0 && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                  🔧 Missing Resources ({feasibilityReport.missingResources.length})
                </Typography>
                <Grid container spacing={2}>
                  {feasibilityReport.missingResources.map((resource, index) => (
                    <Grid item xs={12} md={6} key={index}>
                      <Card
                        variant="outlined"
                        sx={{
                          border: '2px solid',
                          borderColor: resource.type === 'bee_task' ? 'secondary.main' : 'warning.main',
                          background: resource.type === 'bee_task' ?
                            'linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%)' :
                            'linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%)'
                        }}
                      >
                        <CardContent>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                            {resource.type === 'bee_task' && (
                              <Typography variant="h6" sx={{ color: 'secondary.main' }}>
                                🐝
                              </Typography>
                            )}
                            <Typography variant="body1" fontWeight="bold">
                              {resource.name}
                            </Typography>
                          </Box>

                          {resource.type === 'bee_task' && resource.physicalLocation && (
                            <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
                              📍 Location: {resource.physicalLocation}
                            </Typography>
                          )}

                          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                            {resource.psychTrigger}
                          </Typography>

                          <Stack direction="row" spacing={1} sx={{ mb: 2, flexWrap: 'wrap', gap: 1 }}>
                            <Chip
                              label={resource.criticality}
                              size="small"
                              color={resource.type === 'bee_task' ? 'secondary' : 'warning'}
                            />
                            <Chip label={resource.estimatedTime} size="small" variant="outlined" />
                            <Chip label={`R${resource.estimatedCost.toLocaleString()}`} size="small" variant="outlined" />
                            {resource.type === 'bee_task' && (
                              <>
                                <Chip
                                  label={`🐝 ${resource.beeTaskType?.replace('_', ' ').toUpperCase()}`}
                                  size="small"
                                  sx={{
                                    backgroundColor: 'secondary.light',
                                    color: 'secondary.contrastText',
                                    fontWeight: 'bold'
                                  }}
                                />
                                {resource.deliveryMode && (
                                  <Chip
                                    label={getDeliveryModeDescription(resource.deliveryMode)}
                                    size="small"
                                    variant="outlined"
                                    sx={{
                                      borderColor: getUrgencyColor(resource.urgency),
                                      color: getUrgencyColor(resource.urgency),
                                      fontWeight: 'bold'
                                    }}
                                  />
                                )}
                                {resource.urgency && (
                                  <Chip
                                    label={`${getUrgencyIcon(resource.urgency)} ${resource.urgency.toUpperCase()}`}
                                    size="small"
                                    sx={{
                                      backgroundColor: getUrgencyColor(resource.urgency),
                                      color: 'white',
                                      fontWeight: 'bold'
                                    }}
                                  />
                                )}
                              </>
                            )}
                          </Stack>

                          {resource.canAutoOnboard && (
                            <Button
                              variant="contained"
                              size="small"
                              startIcon={resource.type === 'bee_task' ? <Typography>🐝</Typography> : <AutoAwesome />}
                              onClick={() => handleAutoOnboard(resource)}
                              fullWidth
                              color={resource.type === 'bee_task' ? 'secondary' : 'primary'}
                              sx={{
                                fontWeight: 'bold',
                                ...(resource.type === 'bee_task' && {
                                  background: 'linear-gradient(45deg, #ff9800 30%, #f57c00 90%)',
                                  '&:hover': {
                                    background: 'linear-gradient(45deg, #f57c00 30%, #ef6c00 90%)',
                                  }
                                })
                              }}
                            >
                              {resource.type === 'bee_task' ? '🐝 Assign Bee Now' : 'Auto-Onboard Now'}
                            </Button>
                          )}
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            )}

            {/* Available Resources */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                ✅ Available Resources ({feasibilityReport.availableResources.length})
              </Typography>
              <List dense>
                {feasibilityReport.availableResources.map((resource, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <CheckCircle color="success" />
                    </ListItemIcon>
                    <ListItemText
                      primary={resource.name}
                      secondary={`${resource.quality} quality • ${resource.matchScore}% match`}
                    />
                  </ListItem>
                ))}
              </List>
            </Box>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default AutobidFeasibilityEngine;
