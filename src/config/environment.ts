/**
 * Environment Configuration
 * Centralized configuration for all environment variables and API keys
 */

interface EnvironmentConfig {
  // Map Services
  mapboxToken: string;
  googleMapsApiKey: string;
  
  // API Configuration
  apiBaseUrl: string;
  apiTimeout: number;
  
  // Feature Flags
  enableBehavioralAnalytics: boolean;
  enableAdvancedAI: boolean;
  enableGamification: boolean;
  enableCollaboration: boolean;
  
  // Development
  isDevelopment: boolean;
  isProduction: boolean;
  enableDebugMode: boolean;
  
  // Security
  jwtSecret: string;
  encryptionKey: string;
  
  // External Services
  supabaseUrl: string;
  supabaseAnonKey: string;
  
  // Monitoring
  enableErrorTracking: boolean;
  enablePerformanceMonitoring: boolean;
}

// Load environment variables with fallbacks
const loadEnvironmentConfig = (): EnvironmentConfig => {
  const nodeEnv = process.env.NODE_ENV || 'development';
  
  return {
    // Map Services
    mapboxToken: process.env.REACT_APP_MAPBOX_TOKEN || 
                 process.env.MAPBOX_TOKEN || 
                 'pk.eyJ1IjoiYmlkYmVleiIsImEiOiJjbHNkZjEyM3MwMDFjMmxwYzJ6cjBxeXJoIn0.example', // Placeholder
    
    googleMapsApiKey: process.env.REACT_APP_GOOGLE_MAPS_API_KEY || 
                      process.env.GOOGLE_MAPS_API_KEY || 
                      '',
    
    // API Configuration
    apiBaseUrl: process.env.REACT_APP_API_BASE_URL || 
                process.env.API_BASE_URL || 
                'http://localhost:3001/api',
    
    apiTimeout: parseInt(process.env.REACT_APP_API_TIMEOUT || '30000'),
    
    // Feature Flags
    enableBehavioralAnalytics: process.env.REACT_APP_ENABLE_BEHAVIORAL_ANALYTICS !== 'false',
    enableAdvancedAI: process.env.REACT_APP_ENABLE_ADVANCED_AI !== 'false',
    enableGamification: process.env.REACT_APP_ENABLE_GAMIFICATION !== 'false',
    enableCollaboration: process.env.REACT_APP_ENABLE_COLLABORATION !== 'false',
    
    // Development
    isDevelopment: nodeEnv === 'development',
    isProduction: nodeEnv === 'production',
    enableDebugMode: process.env.REACT_APP_DEBUG_MODE === 'true' || nodeEnv === 'development',
    
    // Security
    jwtSecret: process.env.JWT_SECRET || 'your-jwt-secret-key',
    encryptionKey: process.env.ENCRYPTION_KEY || 'your-encryption-key',
    
    // External Services
    supabaseUrl: process.env.REACT_APP_SUPABASE_URL || 
                 process.env.SUPABASE_URL || 
                 '',
    
    supabaseAnonKey: process.env.REACT_APP_SUPABASE_ANON_KEY || 
                     process.env.SUPABASE_ANON_KEY || 
                     '',
    
    // Monitoring
    enableErrorTracking: process.env.REACT_APP_ENABLE_ERROR_TRACKING === 'true',
    enablePerformanceMonitoring: process.env.REACT_APP_ENABLE_PERFORMANCE_MONITORING === 'true'
  };
};

// Export the configuration
export const env = loadEnvironmentConfig();

// Validation function
export const validateEnvironment = (): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  // Check required map services
  if (!env.mapboxToken || env.mapboxToken.includes('example')) {
    errors.push('MAPBOX_TOKEN is required for map functionality');
  }
  
  // Check API configuration
  if (!env.apiBaseUrl) {
    errors.push('API_BASE_URL is required');
  }
  
  // Production-specific validations
  if (env.isProduction) {
    if (!env.jwtSecret || env.jwtSecret === 'your-jwt-secret-key') {
      errors.push('JWT_SECRET must be set in production');
    }
    
    if (!env.encryptionKey || env.encryptionKey === 'your-encryption-key') {
      errors.push('ENCRYPTION_KEY must be set in production');
    }
    
    if (!env.supabaseUrl) {
      errors.push('SUPABASE_URL is required in production');
    }
    
    if (!env.supabaseAnonKey) {
      errors.push('SUPABASE_ANON_KEY is required in production');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Map service configuration
export const mapConfig = {
  mapbox: {
    token: env.mapboxToken,
    styles: {
      streets: 'mapbox://styles/mapbox/streets-v12',
      light: 'mapbox://styles/mapbox/light-v11',
      dark: 'mapbox://styles/mapbox/dark-v11',
      satellite: 'mapbox://styles/mapbox/satellite-v9',
      outdoors: 'mapbox://styles/mapbox/outdoors-v12'
    },
    defaultCenter: [28.0473, -26.2041], // Johannesburg
    defaultZoom: 10
  },
  google: {
    apiKey: env.googleMapsApiKey,
    libraries: ['places', 'geometry', 'directions'] as const,
    defaultCenter: { lat: -26.2041, lng: 28.0473 }, // Johannesburg
    defaultZoom: 10
  }
};

// Feature flag helpers
export const isFeatureEnabled = (feature: keyof Pick<EnvironmentConfig, 
  'enableBehavioralAnalytics' | 'enableAdvancedAI' | 'enableGamification' | 'enableCollaboration'
>): boolean => {
  return env[feature];
};

// Debug helpers
export const debugLog = (message: string, data?: any): void => {
  if (env.enableDebugMode) {
    console.log(`[BidBeez Debug] ${message}`, data);
  }
};

export const debugError = (message: string, error?: any): void => {
  if (env.enableDebugMode) {
    console.error(`[BidBeez Error] ${message}`, error);
  }
};

// Environment info for debugging
export const getEnvironmentInfo = () => {
  return {
    nodeEnv: process.env.NODE_ENV,
    isDevelopment: env.isDevelopment,
    isProduction: env.isProduction,
    hasMapboxToken: !!env.mapboxToken && !env.mapboxToken.includes('example'),
    hasGoogleMapsKey: !!env.googleMapsApiKey,
    hasSupabaseConfig: !!(env.supabaseUrl && env.supabaseAnonKey),
    featuresEnabled: {
      behavioralAnalytics: env.enableBehavioralAnalytics,
      advancedAI: env.enableAdvancedAI,
      gamification: env.enableGamification,
      collaboration: env.enableCollaboration
    }
  };
};

export default env;
