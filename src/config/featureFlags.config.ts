// 🎛️ FEATURE FLAGS CONFIGURATION FOR SOPHISTICATED BIDBEEZ PLATFORM
// Enable all advanced psychological profiling, AI, and automation features

import { FeatureFlag, FeatureStatus, UserSegment } from '../types/featureFlags';

export const SOPHISTICATED_FEATURE_FLAGS: Record<string, FeatureFlag> = {
  
  // ===== PSYCHOLOGICAL PROFILING SYSTEM =====
  SALES_REP_CENTER: {
    id: 'sales_rep_psychological_center',
    name: 'Sales Rep Psychological Center',
    description: 'Advanced psychological profiling and behavioral optimization for sales representatives',
    category: 'PSYCHOLOGICAL',
    status: FeatureStatus.ENABLED,
    userSegments: [UserSegment.ALL],
    subscriptionTiers: ['free', 'professional', 'enterprise'],
    rolloutPercentage: 100,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: [],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['psychological', 'sales', 'ai', 'premium']
  },

  SALES_REP_ONBOARDING: {
    id: 'sales_rep_onboarding',
    name: 'Sales Rep Psychological Onboarding',
    description: 'Psychological archetype detection and personalized onboarding experience',
    category: 'PSYCHOLOGICAL',
    status: FeatureStatus.ENABLED,
    userSegments: [UserSegment.ALL],
    subscriptionTiers: ['free', 'professional', 'enterprise'],
    rolloutPercentage: 100,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: [],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['psychological', 'onboarding', 'archetype', 'premium']
  },

  // ===== AI-POWERED FEATURES =====
  AI_INSIGHTS_DASHBOARD: {
    id: 'ai_insights',
    name: 'AI Insights Dashboard',
    description: 'AI-powered recommendations, market intelligence, and win probability analysis',
    category: 'AI',
    status: FeatureStatus.ENABLED,
    userSegments: [UserSegment.ALL],
    subscriptionTiers: ['professional', 'enterprise'],
    rolloutPercentage: 100,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: [],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['ai', 'insights', 'recommendations', 'premium']
  },

  // ===== GAMIFICATION SYSTEM =====
  GAMIFICATION_HUB: {
    id: 'gamification_hub',
    name: 'Gamification Hub',
    description: 'Complete achievement system, leaderboards, and psychological rewards',
    category: 'GAMIFICATION',
    status: FeatureStatus.ENABLED,
    userSegments: [UserSegment.ALL],
    subscriptionTiers: ['free', 'professional', 'enterprise'],
    rolloutPercentage: 100,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: [],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['gamification', 'achievements', 'leaderboards', 'engagement']
  },

  // ===== ADVANCED ANALYTICS =====
  ADVANCED_ANALYTICS: {
    id: 'advanced_analytics',
    name: 'Advanced Bid Analytics',
    description: 'Comprehensive performance analytics with psychological insights',
    category: 'ANALYTICS',
    status: FeatureStatus.ENABLED,
    userSegments: [UserSegment.ALL],
    subscriptionTiers: ['professional', 'enterprise'],
    rolloutPercentage: 100,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: [],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['analytics', 'performance', 'insights', 'premium']
  },

  // ===== WHATSAPP AUTOMATION =====
  WHATSAPP_DASHBOARD: {
    id: 'whatsapp_dashboard',
    name: 'WhatsApp Automation Dashboard',
    description: 'Advanced WhatsApp auto-bidding and automation management',
    category: 'AUTOMATION',
    status: FeatureStatus.ENABLED,
    userSegments: [UserSegment.ALL],
    subscriptionTiers: ['professional', 'enterprise'],
    rolloutPercentage: 100,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: [],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['whatsapp', 'automation', 'bidding', 'premium']
  },

  // ===== COMPLIANCE TOOLS =====
  BID_PROTESTS: {
    id: 'bid_protest_management',
    name: 'Bid Protest Management',
    description: 'SA legal compliance tools for bid protests and dispute resolution',
    category: 'COMPLIANCE',
    status: FeatureStatus.ENABLED,
    userSegments: [UserSegment.ALL],
    subscriptionTiers: ['professional', 'enterprise'],
    rolloutPercentage: 100,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: [],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['compliance', 'legal', 'protests', 'sa']
  },

  // ===== SKILLSYNC INTEGRATION =====
  SKILLSYNC_MARKETPLACE: {
    id: 'skillsync_integration',
    name: 'SkillSync Marketplace',
    description: 'AI-powered skill provider discovery and B-BBEE compliant talent matching',
    category: 'ECOSYSTEM',
    status: FeatureStatus.ENABLED,
    userSegments: [UserSegment.ALL],
    subscriptionTiers: ['free', 'professional', 'enterprise'],
    rolloutPercentage: 100,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: [],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['skillsync', 'marketplace', 'bbbee', 'talent']
  },

  // ===== TOOLSYNC INTEGRATION =====
  TOOLSYNC_MANAGEMENT: {
    id: 'toolsync_integration',
    name: 'ToolSync License Management',
    description: 'Software tools, license optimization, and collaborative license sharing',
    category: 'ECOSYSTEM',
    status: FeatureStatus.ENABLED,
    userSegments: [UserSegment.ALL],
    subscriptionTiers: ['free', 'professional', 'enterprise'],
    rolloutPercentage: 100,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: [],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['toolsync', 'licenses', 'optimization', 'sharing']
  }
};

// Feature Flag Groups for Easy Management
export const FEATURE_GROUPS = {
  PSYCHOLOGICAL_SUITE: [
    'sales_rep_psychological_center',
    'sales_rep_onboarding',
    'neuromarketing_engine',
    'psychological_profiling'
  ],
  
  AI_SUITE: [
    'ai_insights',
    'ai_recommendations',
    'win_probability_calculator',
    'competitor_intelligence'
  ],
  
  AUTOMATION_SUITE: [
    'whatsapp_dashboard',
    'whatsapp_autobid',
    'whatsapp_automation'
  ],
  
  ANALYTICS_SUITE: [
    'advanced_analytics',
    'psychological_analytics',
    'performance_metrics',
    'behavioral_patterns'
  ],
  
  ECOSYSTEM_SUITE: [
    'skillsync_integration',
    'toolsync_integration',
    'gamification_hub'
  ]
};

// Quick Enable/Disable Functions
export const enableFeatureGroup = (groupName: keyof typeof FEATURE_GROUPS) => {
  const features = FEATURE_GROUPS[groupName];
  return features.reduce((config, feature) => {
    config[feature] = true;
    return config;
  }, {} as Record<string, boolean>);
};

export const disableFeatureGroup = (groupName: keyof typeof FEATURE_GROUPS) => {
  const features = FEATURE_GROUPS[groupName];
  return features.reduce((config, feature) => {
    config[feature] = false;
    return config;
  }, {} as Record<string, boolean>);
};

// Default Configuration - ALL SOPHISTICATED FEATURES ENABLED
export const DEFAULT_SOPHISTICATED_CONFIG = {
  ...enableFeatureGroup('PSYCHOLOGICAL_SUITE'),
  ...enableFeatureGroup('AI_SUITE'),
  ...enableFeatureGroup('AUTOMATION_SUITE'),
  ...enableFeatureGroup('ANALYTICS_SUITE'),
  ...enableFeatureGroup('ECOSYSTEM_SUITE'),
  
  // Additional core features
  tender_search: true,
  bid_submission: true,
  user_dashboard: true,
  sa_compliance_tools: true,
  bid_protest_management: true,
  achievement_system: true,
  leaderboards: true
};
