// 🚀 PRODUCTION CONFIGURATION FOR BIDBEEZ
// All premium features enabled for maximum competitive advantage

export const PRODUCTION_CONFIG = {
  // Environment
  isProduction: process.env.NODE_ENV === 'production' || process.env.NEXT_PUBLIC_PRODUCTION_MODE === 'true',
  
  // Feature Flags - All Premium Features Enabled
  features: {
    // Core Features (Always Enabled)
    tender_search: true,
    bid_submission: true,
    user_dashboard: true,
    
    // Premium Features (Enabled for Production)
    neuromarketing_engine: true,
    psychological_profiling: true,
    behavioral_analytics: true,

    // NEW: Psychological Profiling System
    sales_rep_psychological_center: true,
    sales_rep_onboarding: true,
    archetype_detection: true,
    behavioral_optimization: true,
    stress_monitoring: true,
    confidence_tracking: true,

    // NEW: AI-Powered Features
    ai_insights: true,
    ai_recommendations: true,
    win_probability_calculator: true,
    competitor_intelligence: true,
    market_intelligence: true,
    pricing_optimization: true,
    risk_assessment: true,

    // WhatsApp Integration (Premium Feature)
    whatsapp_autobid: true,
    whatsapp_webhooks: true,
    whatsapp_analytics: true,
    whatsapp_dashboard: true,
    whatsapp_automation: true,

    // SA Compliance Tools (Compliance Tier)
    sa_compliance_tools: true,
    bbee_integration: true,
    legal_compliance: true,
    bid_protest_management: true,
    compliance_automation: true,
    legal_templates: true,

    // Advanced Analytics (Professional Tier)
    bid_analytics: true,
    advanced_analytics: true,
    competitive_intelligence: true,
    financial_analytics: true,
    psychological_analytics: true,
    performance_metrics: true,
    behavioral_patterns: true,

    // Gamification (All Paid Users)
    achievement_system: true,
    leaderboards: true,
    reward_system: true,
    gamification_hub: true,
    xp_progression: true,
    challenge_system: true,

    // NEW: SkillSync & ToolSync Integration
    skillsync_integration: true,
    skill_matching: true,
    skill_verification: true,
    toolsync_integration: true,
    license_optimization: true,
    software_sharing: true,
    
    // Supplier Ecosystem (Network Effects)
    supplier_revenue: true,
    sales_rep_centre: true,
    contractor_supplier_access: true,
    quote_management: true,
    
    // AI Features (Professional Tier)
    ai_bid_optimization: true,
    ai_document_analysis: true,
    predictive_matching: true,
    
    // Enterprise Features
    ecosystem_integration: true,
    partner_api_access: true,
    white_label_options: true,
    team_collaboration: true,
    role_management: true,
    team_analytics: true,
    
    // Mobile Features
    mobile_app: true,
    offline_mode: true,
    
    // Notification Features
    push_notifications: true,
    email_notifications: true,
    sms_notifications: true
  },
  
  // Subscription Tiers
  subscriptionTiers: {
    free: {
      name: 'Free',
      price: 0,
      currency: 'ZAR',
      features: [
        'tender_search',
        'bid_submission',
        'user_dashboard',
        'supplier_revenue',
        'sales_rep_centre',
        'contractor_supplier_access',
        'quote_management',
        'mobile_app',
        'push_notifications',
        'email_notifications'
      ],
      limits: {
        monthly_bids: 10,
        saved_tenders: 50,
        quote_requests: 5
      }
    },
    
    professional: {
      name: 'Professional',
      price: 299,
      currency: 'ZAR',
      features: [
        'neuromarketing_engine',
        'psychological_profiling',
        'behavioral_analytics',
        'whatsapp_autobid',
        'whatsapp_webhooks',
        'whatsapp_analytics',
        'bid_analytics',
        'advanced_analytics',
        'competitive_intelligence',
        'financial_analytics',
        'psychological_analytics',
        'achievement_system',
        'leaderboards',
        'reward_system',
        'ai_bid_optimization',
        'ai_document_analysis',
        'predictive_matching',
        'offline_mode',
        'sms_notifications'
      ],
      limits: {
        monthly_bids: 100,
        saved_tenders: 500,
        quote_requests: 50,
        whatsapp_messages: 1000
      }
    },
    
    compliance_pro: {
      name: 'Compliance Pro',
      price: 499,
      currency: 'ZAR',
      features: [
        'sa_compliance_tools',
        'bbee_integration',
        'legal_compliance'
      ],
      limits: {
        monthly_bids: 200,
        saved_tenders: 1000,
        quote_requests: 100,
        whatsapp_messages: 2000,
        compliance_checks: 'unlimited'
      }
    },
    
    enterprise: {
      name: 'Enterprise',
      price: 999,
      currency: 'ZAR',
      features: [
        'ecosystem_integration',
        'partner_api_access',
        'white_label_options',
        'team_collaboration',
        'role_management',
        'team_analytics'
      ],
      limits: {
        monthly_bids: 'unlimited',
        saved_tenders: 'unlimited',
        quote_requests: 'unlimited',
        whatsapp_messages: 'unlimited',
        team_members: 'unlimited',
        api_calls: 'unlimited'
      }
    }
  },
  
  // Business Configuration
  business: {
    commission_rates: {
      supplier_commission: 0.05, // 5%
      quote_processing_fee: 25 // ZAR
    },
    
    revenue_projections: {
      year_1: {
        free_users: 1000,
        professional_users: 200,
        compliance_pro_users: 50,
        enterprise_users: 10,
        projected_revenue: 1636880 // ZAR
      }
    }
  },
  
  // API Configuration
  api: {
    base_url: process.env.NEXT_PUBLIC_API_URL || 'https://api.bidbeez.co.za/api',
    timeout: 30000,
    retry_attempts: 3
  },
  
  // Analytics
  analytics: {
    google_analytics: process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID,
    hotjar: process.env.NEXT_PUBLIC_HOTJAR_ID,
    sentry: process.env.NEXT_PUBLIC_SENTRY_DSN
  }
};

// Helper function to check if a feature is enabled
export const isFeatureEnabled = (featureName: string): boolean => {
  return PRODUCTION_CONFIG.features[featureName as keyof typeof PRODUCTION_CONFIG.features] || false;
};

// Helper function to get subscription tier features
export const getSubscriptionTierFeatures = (tier: string): string[] => {
  const tierConfig = PRODUCTION_CONFIG.subscriptionTiers[tier as keyof typeof PRODUCTION_CONFIG.subscriptionTiers];
  return tierConfig ? tierConfig.features : [];
};

// Helper function to check if user has access to feature based on subscription
export const hasFeatureAccess = (featureName: string, userTier: string): boolean => {
  const tierFeatures = getSubscriptionTierFeatures(userTier);
  return tierFeatures.includes(featureName) || isFeatureEnabled(featureName);
};

export default PRODUCTION_CONFIG;