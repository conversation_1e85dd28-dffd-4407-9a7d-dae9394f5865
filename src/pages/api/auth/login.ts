import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';
import jwt from 'jsonwebtoken';
import { z } from 'zod';

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Validation schema
const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().optional(),
});

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Validate request body
    const validatedData = loginSchema.parse(req.body);
    const { email, password, rememberMe = false } = validatedData;

    // Authenticate with Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (authError) {
      console.error('Authentication error:', authError);
      return res.status(401).json({ 
        error: 'Invalid credentials',
        message: authError.message 
      });
    }

    if (!authData.user) {
      return res.status(401).json({ error: 'Authentication failed' });
    }

    // Get user profile from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select(`
        *,
        user_profiles(*),
        organizations!organizations_owner_id_fkey(
          id,
          name,
          slug,
          subscription_tier,
          status
        ),
        team_members(
          id,
          organization_id,
          role,
          title,
          status,
          organizations(
            id,
            name,
            slug,
            subscription_tier,
            status
          )
        )
      `)
      .eq('auth_user_id', authData.user.id)
      .single();

    if (userError || !user) {
      console.error('User profile error:', userError);
      return res.status(404).json({ error: 'User profile not found' });
    }

    // Update last login timestamp
    await supabase
      .from('users')
      .update({ last_login_at: new Date().toISOString() })
      .eq('id', user.id);

    // Determine user's primary organization
    let primaryOrganization = null;
    if (user.organizations && user.organizations.length > 0) {
      // User owns an organization
      primaryOrganization = {
        id: user.organizations[0].id,
        name: user.organizations[0].name,
        slug: user.organizations[0].slug,
        role: 'owner',
        subscriptionTier: user.organizations[0].subscription_tier,
        status: user.organizations[0].status,
      };
    } else if (user.team_members && user.team_members.length > 0) {
      // User is a member of an organization
      const activeMembership = user.team_members.find(tm => tm.status === 'active');
      if (activeMembership && activeMembership.organizations) {
        primaryOrganization = {
          id: activeMembership.organizations.id,
          name: activeMembership.organizations.name,
          slug: activeMembership.organizations.slug,
          role: activeMembership.role,
          subscriptionTier: activeMembership.organizations.subscription_tier,
          status: activeMembership.organizations.status,
        };
      }
    }

    // Get user's active subscription
    const { data: subscription } = await supabase
      .from('subscriptions')
      .select(`
        *,
        subscription_plans(*)
      `)
      .eq('user_id', user.id)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    // Generate JWT token
    const tokenExpiry = rememberMe ? '30d' : '7d';
    const token = jwt.sign(
      { 
        userId: user.id,
        authUserId: authData.user.id,
        email: user.email,
        userType: user.user_type,
        organizationId: primaryOrganization?.id,
        role: primaryOrganization?.role,
        subscriptionTier: subscription?.subscription_plans?.slug || 'free',
      },
      process.env.JWT_SECRET!,
      { expiresIn: tokenExpiry }
    );

    // Prepare user response
    const userResponse = {
      id: user.id,
      email: user.email,
      fullName: user.full_name,
      avatarUrl: user.avatar_url,
      phone: user.phone,
      userType: user.user_type,
      accountStatus: user.account_status,
      profileCompleted: user.profile_completed,
      onboardingCompleted: user.onboarding_completed,
      kycVerified: user.kyc_verified,
      timezone: user.timezone,
      language: user.language,
      notificationPreferences: user.notification_preferences,
      lastLoginAt: user.last_login_at,
      
      // Profile details
      profile: user.user_profiles ? {
        jobTitle: user.user_profiles.job_title,
        companyName: user.user_profiles.company_name,
        industry: user.user_profiles.industry,
        yearsOfExperience: user.user_profiles.years_of_experience,
        skills: user.user_profiles.skills,
        specializations: user.user_profiles.specializations,
        portfolioSettings: user.user_profiles.portfolio_settings,
        bidPreferences: user.user_profiles.bid_preferences,
      } : null,
      
      // Organization details
      organization: primaryOrganization,
      
      // Subscription details
      subscription: subscription ? {
        id: subscription.id,
        status: subscription.status,
        currentPeriodEnd: subscription.current_period_end,
        plan: {
          id: subscription.subscription_plans.id,
          name: subscription.subscription_plans.name,
          slug: subscription.subscription_plans.slug,
          price: subscription.subscription_plans.price,
          features: subscription.subscription_plans.features,
          limits: subscription.subscription_plans.limits,
        },
      } : null,
    };

    // Determine redirect URL based on user type and completion status
    let redirectTo = '/dashboard';
    
    if (!user.onboarding_completed) {
      redirectTo = '/onboarding';
    } else if (!user.profile_completed) {
      redirectTo = '/profile/complete';
    } else if (user.user_type === 'organization' && primaryOrganization) {
      redirectTo = `/organization/${primaryOrganization.slug}/dashboard`;
    } else if (user.user_type === 'team_lead' && primaryOrganization) {
      redirectTo = `/organization/${primaryOrganization.slug}/dashboard`;
    }

    // Log successful login
    console.log(`User ${user.email} logged in successfully`);

    // Return success response
    res.status(200).json({
      message: 'Login successful',
      user: userResponse,
      token,
      redirectTo,
      sessionExpiry: new Date(Date.now() + (rememberMe ? 30 : 7) * 24 * 60 * 60 * 1000).toISOString(),
    });

  } catch (error) {
    console.error('Login error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Validation error',
        details: error.errors,
      });
    }

    res.status(500).json({ error: 'Internal server error' });
  }
}

// Helper function to determine user capabilities based on subscription
function getUserCapabilities(subscription: any, userType: string) {
  const defaultCapabilities = {
    maxTenderInterests: 5,
    maxTeamMembers: 1,
    advancedAnalytics: false,
    prioritySupport: false,
    customBranding: false,
    apiAccess: false,
    exportData: false,
    realTimeNotifications: true,
    documentStorage: 100, // MB
    monthlyBidLimit: 10,
  };

  if (!subscription) {
    return defaultCapabilities;
  }

  const plan = subscription.subscription_plans;
  if (!plan || !plan.features) {
    return defaultCapabilities;
  }

  return {
    maxTenderInterests: plan.limits?.max_tender_interests || defaultCapabilities.maxTenderInterests,
    maxTeamMembers: plan.limits?.max_team_members || defaultCapabilities.maxTeamMembers,
    advancedAnalytics: plan.features?.advanced_analytics || false,
    prioritySupport: plan.features?.priority_support || false,
    customBranding: plan.features?.custom_branding || false,
    apiAccess: plan.features?.api_access || false,
    exportData: plan.features?.export_data || false,
    realTimeNotifications: plan.features?.real_time_notifications !== false,
    documentStorage: plan.limits?.document_storage_mb || defaultCapabilities.documentStorage,
    monthlyBidLimit: plan.limits?.monthly_bid_limit || defaultCapabilities.monthlyBidLimit,
  };
}
