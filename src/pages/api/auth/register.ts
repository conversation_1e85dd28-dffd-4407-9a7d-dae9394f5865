import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { z } from 'zod';

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Validation schema
const registerSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  fullName: z.string().min(2, 'Full name must be at least 2 characters'),
  userType: z.enum(['individual', 'team_lead', 'organization']),
  phone: z.string().optional(),
  companyName: z.string().optional(),
  industry: z.string().optional(),
});

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Validate request body
    const validatedData = registerSchema.parse(req.body);
    const { email, password, fullName, userType, phone, companyName, industry } = validatedData;

    // Check if user already exists
    const { data: existingUser } = await supabase
      .from('users')
      .select('id')
      .eq('email', email)
      .single();

    if (existingUser) {
      return res.status(400).json({ error: 'User already exists with this email' });
    }

    // Create auth user in Supabase Auth
    const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true,
    });

    if (authError) {
      console.error('Auth creation error:', authError);
      return res.status(400).json({ error: authError.message });
    }

    if (!authUser.user) {
      return res.status(400).json({ error: 'Failed to create auth user' });
    }

    // Create user profile
    const { data: user, error: userError } = await supabase
      .from('users')
      .insert({
        auth_user_id: authUser.user.id,
        email,
        full_name: fullName,
        user_type: userType,
        phone,
      })
      .select()
      .single();

    if (userError) {
      console.error('User creation error:', userError);
      // Clean up auth user if profile creation fails
      await supabase.auth.admin.deleteUser(authUser.user.id);
      return res.status(400).json({ error: 'Failed to create user profile' });
    }

    // Create detailed user profile
    const { error: profileError } = await supabase
      .from('user_profiles')
      .insert({
        user_id: user.id,
        company_name: companyName,
        industry,
        portfolio_settings: {
          rfq_tender_ratio: 60, // 60% RFQs, 40% Tenders
          preferred_sectors: [],
          notification_preferences: {
            email: true,
            sms: false,
            whatsapp: true,
            push: true,
          },
        },
        bid_preferences: {
          auto_alerts: true,
          preferred_categories: [],
          minimum_value: 0,
          maximum_value: null,
          preferred_locations: [],
        },
      });

    if (profileError) {
      console.error('Profile creation error:', profileError);
      // Continue anyway, profile can be completed later
    }

    // If user type is organization or team_lead, create organization
    let organization = null;
    if (userType === 'organization' || userType === 'team_lead') {
      if (!companyName) {
        return res.status(400).json({ error: 'Company name is required for organization accounts' });
      }

      // Generate slug from company name
      const slug = companyName
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');

      const { data: org, error: orgError } = await supabase
        .from('organizations')
        .insert({
          name: companyName,
          slug: `${slug}-${Date.now()}`, // Add timestamp to ensure uniqueness
          owner_id: user.id,
          industry,
          subscription_tier: 'basic',
          settings: {
            defaultWorkflowTemplate: 'standard',
            autoAssignBeeWorkers: false,
            requireApprovalForBids: true,
            notificationPreferences: {
              emailEnabled: true,
              smsEnabled: false,
              whatsappEnabled: true,
              pushEnabled: true,
              digestFrequency: 'daily',
            },
            collaborationSettings: {
              allowGuestAccess: false,
              requireApprovalForDocuments: true,
              enableRealTimeChat: true,
              enableVideoMeetings: true,
              documentVersionControl: true,
            },
          },
        })
        .select()
        .single();

      if (orgError) {
        console.error('Organization creation error:', orgError);
        return res.status(400).json({ error: 'Failed to create organization' });
      }

      organization = org;

      // Add user as owner to team_members
      const { error: teamMemberError } = await supabase
        .from('team_members')
        .insert({
          user_id: user.id,
          organization_id: org.id,
          role: 'owner',
          title: 'Owner',
          permissions: ['all'],
          availability: {
            timezone: 'Africa/Johannesburg',
            workingHours: {
              monday: { start: '08:00', end: '17:00', available: true },
              tuesday: { start: '08:00', end: '17:00', available: true },
              wednesday: { start: '08:00', end: '17:00', available: true },
              thursday: { start: '08:00', end: '17:00', available: true },
              friday: { start: '08:00', end: '17:00', available: true },
              saturday: { start: '08:00', end: '12:00', available: false },
              sunday: { start: '08:00', end: '12:00', available: false },
            },
            unavailableDates: [],
            maxConcurrentBids: 10,
          },
        });

      if (teamMemberError) {
        console.error('Team member creation error:', teamMemberError);
        // Continue anyway, can be fixed later
      }
    }

    // Generate JWT token
    const token = jwt.sign(
      { 
        userId: user.id,
        authUserId: authUser.user.id,
        email: user.email,
        userType: user.user_type,
        organizationId: organization?.id,
      },
      process.env.JWT_SECRET!,
      { expiresIn: '7d' }
    );

    // Return success response
    res.status(201).json({
      message: 'User registered successfully',
      user: {
        id: user.id,
        email: user.email,
        fullName: user.full_name,
        userType: user.user_type,
        phone: user.phone,
        profileCompleted: user.profile_completed,
        onboardingCompleted: user.onboarding_completed,
        organization: organization ? {
          id: organization.id,
          name: organization.name,
          slug: organization.slug,
          role: 'owner',
        } : null,
      },
      token,
      redirectTo: userType === 'individual' ? '/dashboard' : '/organization/dashboard',
    });

  } catch (error) {
    console.error('Registration error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Validation error',
        details: error.errors,
      });
    }

    res.status(500).json({ error: 'Internal server error' });
  }
}

// Helper function to validate password strength
function validatePasswordStrength(password: string): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Helper function to generate secure slug
function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
}
