import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';
import { z } from 'zod';
import { authenticateUser } from '../../../lib/auth';

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Validation schemas
const getTendersSchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 20),
  search: z.string().optional(),
  category: z.string().optional(),
  sector: z.string().optional(),
  status: z.string().optional(),
  minValue: z.string().optional().transform(val => val ? parseFloat(val) : undefined),
  maxValue: z.string().optional().transform(val => val ? parseFloat(val) : undefined),
  location: z.string().optional(),
  sortBy: z.enum(['closing_date', 'estimated_value', 'published_date', 'title']).optional().default('closing_date'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('asc'),
  includeExpired: z.string().optional().transform(val => val === 'true'),
});

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Authenticate user
    const authResult = await authenticateUser(req);
    if (!authResult.success) {
      return res.status(401).json({ error: authResult.error });
    }

    const { user } = authResult;

    if (req.method === 'GET') {
      return await getTenders(req, res, user);
    } else {
      return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error('Tenders API error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}

async function getTenders(req: NextApiRequest, res: NextApiResponse, user: any) {
  try {
    // Validate query parameters
    const params = getTendersSchema.parse(req.query);
    const {
      page,
      limit,
      search,
      category,
      sector,
      status,
      minValue,
      maxValue,
      location,
      sortBy,
      sortOrder,
      includeExpired
    } = params;

    // Calculate pagination
    const offset = (page - 1) * limit;

    // Build query
    let query = supabase
      .from('tenders')
      .select(`
        id,
        title,
        reference,
        description,
        issuing_organization,
        tender_type,
        category,
        sector,
        estimated_value,
        currency,
        published_date,
        closing_date,
        validity_period,
        requirements,
        delivery_location,
        project_duration,
        status,
        source,
        created_at,
        updated_at
      `, { count: 'exact' });

    // Apply filters
    if (!includeExpired) {
      query = query.gte('closing_date', new Date().toISOString());
    }

    if (search) {
      query = query.or(`title.ilike.%${search}%,description.ilike.%${search}%,reference.ilike.%${search}%,issuing_organization.ilike.%${search}%`);
    }

    if (category) {
      query = query.eq('category', category);
    }

    if (sector) {
      query = query.eq('sector', sector);
    }

    if (status) {
      query = query.eq('status', status);
    } else {
      // Default to published tenders only
      query = query.eq('status', 'published');
    }

    if (minValue !== undefined) {
      query = query.gte('estimated_value', minValue);
    }

    if (maxValue !== undefined) {
      query = query.lte('estimated_value', maxValue);
    }

    if (location) {
      query = query.ilike('delivery_location', `%${location}%`);
    }

    // Apply sorting
    query = query.order(sortBy, { ascending: sortOrder === 'asc' });

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: tenders, error, count } = await query;

    if (error) {
      console.error('Database error:', error);
      return res.status(500).json({ error: 'Failed to fetch tenders' });
    }

    // Get user's tender interests for these tenders
    const tenderIds = tenders?.map(t => t.id) || [];
    let userInterests: any[] = [];
    let teamInterests: any[] = [];

    if (tenderIds.length > 0) {
      // Get individual interests
      const { data: individualInterests } = await supabase
        .from('user_tender_interests')
        .select('tender_id, interest_level, bid_status')
        .eq('user_id', user.id)
        .in('tender_id', tenderIds);

      userInterests = individualInterests || [];

      // Get team interests if user is part of organizations
      if (user.organizationId) {
        const { data: orgInterests } = await supabase
          .from('team_tender_interests')
          .select('tender_id, interest_level, bid_status')
          .eq('organization_id', user.organizationId)
          .in('tender_id', tenderIds);

        teamInterests = orgInterests || [];
      }
    }

    // Enhance tenders with user interest data
    const enhancedTenders = tenders?.map(tender => {
      const userInterest = userInterests.find(ui => ui.tender_id === tender.id);
      const teamInterest = teamInterests.find(ti => ti.tender_id === tender.id);

      return {
        ...tender,
        userInterest: userInterest ? {
          level: userInterest.interest_level,
          bidStatus: userInterest.bid_status,
        } : null,
        teamInterest: teamInterest ? {
          level: teamInterest.interest_level,
          bidStatus: teamInterest.bid_status,
        } : null,
        daysUntilClosing: tender.closing_date ? 
          Math.ceil((new Date(tender.closing_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)) : null,
        isExpired: tender.closing_date ? new Date(tender.closing_date) < new Date() : false,
      };
    });

    // Calculate pagination metadata
    const totalPages = Math.ceil((count || 0) / limit);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;

    res.status(200).json({
      tenders: enhancedTenders,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages,
        hasNextPage,
        hasPreviousPage,
      },
      filters: {
        search,
        category,
        sector,
        status,
        minValue,
        maxValue,
        location,
        includeExpired,
      },
      sorting: {
        sortBy,
        sortOrder,
      },
    });

  } catch (error) {
    console.error('Get tenders error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Validation error',
        details: error.errors,
      });
    }

    res.status(500).json({ error: 'Failed to fetch tenders' });
  }
}

// Helper function to get tender statistics
export async function getTenderStatistics() {
  try {
    const { data: stats, error } = await supabase.rpc('get_tender_statistics');
    
    if (error) {
      console.error('Statistics error:', error);
      return null;
    }

    return stats;
  } catch (error) {
    console.error('Get tender statistics error:', error);
    return null;
  }
}

// Helper function to get trending categories
export async function getTrendingCategories(limit: number = 10) {
  try {
    const { data: categories, error } = await supabase
      .from('tenders')
      .select('category')
      .eq('status', 'published')
      .gte('closing_date', new Date().toISOString())
      .not('category', 'is', null);

    if (error) {
      console.error('Trending categories error:', error);
      return [];
    }

    // Count categories
    const categoryCounts = categories.reduce((acc: Record<string, number>, tender) => {
      const category = tender.category;
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    }, {});

    // Sort by count and return top categories
    return Object.entries(categoryCounts)
      .sort(([, a], [, b]) => (b as number) - (a as number))
      .slice(0, limit)
      .map(([category, count]) => ({ category, count }));

  } catch (error) {
    console.error('Get trending categories error:', error);
    return [];
  }
}
