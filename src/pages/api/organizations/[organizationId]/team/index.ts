import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';
import { z } from 'zod';
import { authenticateOrganizationMember } from '../../../../../lib/auth';

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Validation schemas
const addTeamMemberSchema = z.object({
  userId: z.string().uuid('Invalid user ID'),
  role: z.enum(['admin', 'project_manager', 'estimator', 'technical_lead', 'legal_counsel', 'business_dev', 'finance', 'viewer', 'guest']),
  title: z.string().min(1, 'Title is required'),
  department: z.string().optional(),
  specializations: z.array(z.string()).optional().default([]),
  permissions: z.array(z.string()).optional().default([]),
});

const updateTeamMemberSchema = z.object({
  role: z.enum(['admin', 'project_manager', 'estimator', 'technical_lead', 'legal_counsel', 'business_dev', 'finance', 'viewer', 'guest']).optional(),
  title: z.string().optional(),
  department: z.string().optional(),
  specializations: z.array(z.string()).optional(),
  permissions: z.array(z.string()).optional(),
  status: z.enum(['active', 'inactive', 'suspended']).optional(),
  availability: z.object({
    timezone: z.string(),
    workingHours: z.record(z.any()),
    unavailableDates: z.array(z.string()),
    maxConcurrentBids: z.number().min(1).max(20),
  }).optional(),
});

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const organizationId = req.query.organizationId as string;

    if (req.method === 'GET') {
      return await getTeamMembers(req, res, organizationId);
    } else if (req.method === 'POST') {
      return await addTeamMember(req, res, organizationId);
    } else {
      return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error('Team API error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}

async function getTeamMembers(req: NextApiRequest, res: NextApiResponse, organizationId: string) {
  try {
    // Authenticate user as organization member
    const authResult = await authenticateOrganizationMember(req, organizationId);
    if (!authResult.success) {
      return res.status(403).json({ error: authResult.error });
    }

    // Get team members with user details
    const { data: teamMembers, error } = await supabase
      .from('team_members')
      .select(`
        id,
        role,
        title,
        department,
        specializations,
        permissions,
        availability,
        workload,
        max_concurrent_bids,
        status,
        joined_at,
        last_active,
        users!team_members_user_id_fkey(
          id,
          email,
          full_name,
          avatar_url,
          phone,
          timezone,
          user_profiles(
            job_title,
            company_name,
            industry,
            years_of_experience,
            skills,
            certifications
          )
        )
      `)
      .eq('organization_id', organizationId)
      .order('joined_at', { ascending: true });

    if (error) {
      console.error('Database error:', error);
      return res.status(500).json({ error: 'Failed to fetch team members' });
    }

    // Get team member statistics
    const { data: stats } = await supabase
      .from('team_members')
      .select('role, status')
      .eq('organization_id', organizationId);

    const statistics = {
      total: stats?.length || 0,
      active: stats?.filter(s => s.status === 'active').length || 0,
      byRole: stats?.reduce((acc: Record<string, number>, member) => {
        acc[member.role] = (acc[member.role] || 0) + 1;
        return acc;
      }, {}) || {},
    };

    // Format response
    const formattedMembers = teamMembers?.map(member => ({
      id: member.id,
      role: member.role,
      title: member.title,
      department: member.department,
      specializations: member.specializations || [],
      permissions: member.permissions || [],
      availability: member.availability || {},
      workload: member.workload,
      maxConcurrentBids: member.max_concurrent_bids,
      status: member.status,
      joinedAt: member.joined_at,
      lastActive: member.last_active,
      user: member.users ? {
        id: member.users.id,
        email: member.users.email,
        fullName: member.users.full_name,
        avatarUrl: member.users.avatar_url,
        phone: member.users.phone,
        timezone: member.users.timezone,
        profile: member.users.user_profiles ? {
          jobTitle: member.users.user_profiles.job_title,
          companyName: member.users.user_profiles.company_name,
          industry: member.users.user_profiles.industry,
          yearsOfExperience: member.users.user_profiles.years_of_experience,
          skills: member.users.user_profiles.skills || [],
          certifications: member.users.user_profiles.certifications || [],
        } : null,
      } : null,
    })) || [];

    res.status(200).json({
      teamMembers: formattedMembers,
      statistics,
    });

  } catch (error) {
    console.error('Get team members error:', error);
    res.status(500).json({ error: 'Failed to fetch team members' });
  }
}

async function addTeamMember(req: NextApiRequest, res: NextApiResponse, organizationId: string) {
  try {
    // Authenticate user as organization admin
    const authResult = await authenticateOrganizationMember(req, organizationId, ['owner', 'admin']);
    if (!authResult.success) {
      return res.status(403).json({ error: authResult.error });
    }

    // Validate request body
    const validatedData = addTeamMemberSchema.parse(req.body);
    const { userId, role, title, department, specializations, permissions } = validatedData;

    // Check if user exists
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, email, full_name, account_status')
      .eq('id', userId)
      .single();

    if (userError || !user) {
      return res.status(404).json({ error: 'User not found' });
    }

    if (user.account_status !== 'active') {
      return res.status(400).json({ error: 'User account is not active' });
    }

    // Check if user is already a member
    const { data: existingMember } = await supabase
      .from('team_members')
      .select('id')
      .eq('user_id', userId)
      .eq('organization_id', organizationId)
      .single();

    if (existingMember) {
      return res.status(400).json({ error: 'User is already a member of this organization' });
    }

    // Get default permissions for role
    const defaultPermissions = getDefaultPermissions(role);
    const finalPermissions = permissions.length > 0 ? permissions : defaultPermissions;

    // Create team member
    const { data: teamMember, error: createError } = await supabase
      .from('team_members')
      .insert({
        user_id: userId,
        organization_id: organizationId,
        role,
        title,
        department,
        specializations,
        permissions: finalPermissions,
        availability: {
          timezone: 'Africa/Johannesburg',
          workingHours: getDefaultWorkingHours(),
          unavailableDates: [],
          maxConcurrentBids: 5,
        },
        status: 'active',
      })
      .select(`
        id,
        role,
        title,
        department,
        specializations,
        permissions,
        availability,
        status,
        joined_at,
        users!team_members_user_id_fkey(
          id,
          email,
          full_name,
          avatar_url
        )
      `)
      .single();

    if (createError) {
      console.error('Team member creation error:', createError);
      return res.status(500).json({ error: 'Failed to add team member' });
    }

    // Log activity
    console.log(`User ${user.email} added to organization ${organizationId} as ${role}`);

    res.status(201).json({
      message: 'Team member added successfully',
      teamMember: {
        id: teamMember.id,
        role: teamMember.role,
        title: teamMember.title,
        department: teamMember.department,
        specializations: teamMember.specializations || [],
        permissions: teamMember.permissions || [],
        availability: teamMember.availability || {},
        status: teamMember.status,
        joinedAt: teamMember.joined_at,
        user: teamMember.users ? {
          id: teamMember.users.id,
          email: teamMember.users.email,
          fullName: teamMember.users.full_name,
          avatarUrl: teamMember.users.avatar_url,
        } : null,
      },
    });

  } catch (error) {
    console.error('Add team member error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Validation error',
        details: error.errors,
      });
    }

    res.status(500).json({ error: 'Failed to add team member' });
  }
}

// Helper function to get default permissions for role
function getDefaultPermissions(role: string): string[] {
  const rolePermissions: Record<string, string[]> = {
    admin: ['all'],
    project_manager: [
      'manage_team',
      'manage_bids',
      'view_analytics',
      'manage_communications',
      'assign_tasks',
      'approve_documents',
    ],
    estimator: [
      'view_bids',
      'create_estimates',
      'edit_estimates',
      'view_documents',
      'participate_communications',
    ],
    technical_lead: [
      'view_bids',
      'review_technical',
      'approve_technical',
      'view_documents',
      'participate_communications',
    ],
    legal_counsel: [
      'view_bids',
      'review_legal',
      'approve_legal',
      'view_documents',
      'participate_communications',
    ],
    business_dev: [
      'view_bids',
      'manage_opportunities',
      'view_analytics',
      'participate_communications',
    ],
    finance: [
      'view_bids',
      'review_financial',
      'approve_financial',
      'view_analytics',
      'participate_communications',
    ],
    viewer: [
      'view_bids',
      'view_documents',
      'participate_communications',
    ],
    guest: [
      'view_bids',
      'participate_communications',
    ],
  };

  return rolePermissions[role] || ['view_bids'];
}

// Helper function to get default working hours
function getDefaultWorkingHours() {
  return {
    monday: { start: '08:00', end: '17:00', available: true },
    tuesday: { start: '08:00', end: '17:00', available: true },
    wednesday: { start: '08:00', end: '17:00', available: true },
    thursday: { start: '08:00', end: '17:00', available: true },
    friday: { start: '08:00', end: '17:00', available: true },
    saturday: { start: '08:00', end: '12:00', available: false },
    sunday: { start: '08:00', end: '12:00', available: false },
  };
}
