/**
 * Feature Gate Hook
 * Controls access to premium features based on subscription tier
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNeuroMarketing } from './useNeuroMarketing';
import {
  useCheckFeatureAccessQuery,
  useGetFeatureUsageQuery,
  useRecordUsageMutation
} from '../services/api/subscription.api';
import {
  FeatureGate,
  FeatureUsage,
  SubscriptionTier,
  UsageType
} from '../types/subscription';

export interface FeatureGateResult {
  // Access Control
  hasAccess: boolean;
  reason?: string;
  upgradeRequired: boolean;
  requiredPlan?: SubscriptionTier;
  
  // Usage Information
  usage?: FeatureUsage;
  remainingUsage?: number;
  usagePercentage?: number;
  
  // Actions
  checkAccess: (feature: string) => Promise<boolean>;
  recordUsage: (feature: string, quantity?: number) => Promise<void>;
  showUpgradePrompt: () => void;
  
  // UI Helpers
  getAccessMessage: () => string;
  getUpgradeMessage: () => string;
  shouldShowUsageWarning: () => boolean;
}

export interface FeatureGateOptions {
  feature: string;
  autoCheck?: boolean;
  trackUsage?: boolean;
  showUpgradePrompts?: boolean;
  usageWarningThreshold?: number; // Percentage threshold for usage warnings
}

export const useFeatureGate = (options: FeatureGateOptions): FeatureGateResult => {
  const { user } = useAuth();
  const { shouldShowEncouragement } = useNeuroMarketing();
  
  const [accessResult, setAccessResult] = useState<FeatureGate | null>(null);
  const [showUpgrade, setShowUpgrade] = useState(false);

  const {
    feature,
    autoCheck = true,
    trackUsage = true,
    showUpgradePrompts = true,
    usageWarningThreshold = 80
  } = options;

  // API queries
  const {
    data: featureAccess,
    isLoading: accessLoading,
    refetch: refetchAccess
  } = useCheckFeatureAccessQuery(
    { userId: user?.id || '', feature },
    { skip: !user?.id || !autoCheck }
  );

  const {
    data: featureUsageList,
    refetch: refetchUsage
  } = useGetFeatureUsageQuery(user?.id || '', { skip: !user?.id });

  const [recordUsageMutation] = useRecordUsageMutation();

  // Get usage for this specific feature
  const usage = featureUsageList?.find(u => u.feature === feature);

  // Update access result when data changes
  useEffect(() => {
    if (featureAccess) {
      setAccessResult(featureAccess);
    }
  }, [featureAccess]);

  // Check access manually
  const checkAccess = useCallback(async (featureToCheck: string): Promise<boolean> => {
    if (!user?.id) return false;

    try {
      const result = await refetchAccess();
      if (result.data) {
        setAccessResult(result.data);
        return result.data.enabled;
      }
      return false;
    } catch (error) {
      console.error('Failed to check feature access:', error);
      return false;
    }
  }, [user?.id, refetchAccess]);

  // Record usage
  const recordUsage = useCallback(async (featureToRecord: string, quantity: number = 1): Promise<void> => {
    if (!user?.id || !trackUsage) return;

    try {
      await recordUsageMutation({
        userId: user.id,
        usageType: featureToRecord as UsageType,
        quantity,
        metadata: {
          timestamp: new Date().toISOString(),
          feature: featureToRecord
        }
      }).unwrap();

      // Refresh usage data
      await refetchUsage();
    } catch (error) {
      console.error('Failed to record usage:', error);
    }
  }, [user?.id, trackUsage, recordUsageMutation, refetchUsage]);

  // Show upgrade prompt
  const showUpgradePrompt = useCallback(() => {
    if (showUpgradePrompts) {
      setShowUpgrade(true);
    }
  }, [showUpgradePrompts]);

  // Get access message
  const getAccessMessage = useCallback((): string => {
    if (!accessResult) return 'Checking access...';

    if (accessResult.enabled) {
      if (usage && !usage.unlimited) {
        const remaining = usage.limit - usage.used;
        return `${remaining} uses remaining this month`;
      }
      return 'Full access available';
    }

    if (accessResult.upgradeRequired) {
      const planName = accessResult.requiredPlan?.replace('_', ' ').toUpperCase() || 'Premium';
      return `Upgrade to ${planName} plan required`;
    }

    return accessResult.reason || 'Access denied';
  }, [accessResult, usage]);

  // Get upgrade message
  const getUpgradeMessage = useCallback((): string => {
    if (!accessResult?.upgradeRequired) return '';

    const planName = accessResult.requiredPlan?.replace('_', ' ').toUpperCase() || 'Premium';
    
    if (shouldShowEncouragement) {
      return `Unlock this feature with our ${planName} plan - designed to help you succeed!`;
    }

    return `This feature requires a ${planName} subscription. Upgrade now to unlock full potential.`;
  }, [accessResult, shouldShowEncouragement]);

  // Check if usage warning should be shown
  const shouldShowUsageWarning = useCallback((): boolean => {
    if (!usage || usage.unlimited) return false;
    return usage.percentage >= usageWarningThreshold;
  }, [usage, usageWarningThreshold]);

  // Calculate remaining usage
  const remainingUsage = usage && !usage.unlimited ? usage.limit - usage.used : undefined;

  return {
    // Access Control
    hasAccess: accessResult?.enabled || false,
    reason: accessResult?.reason,
    upgradeRequired: accessResult?.upgradeRequired || false,
    requiredPlan: accessResult?.requiredPlan,
    
    // Usage Information
    usage,
    remainingUsage,
    usagePercentage: usage?.percentage,
    
    // Actions
    checkAccess,
    recordUsage,
    showUpgradePrompt,
    
    // UI Helpers
    getAccessMessage,
    getUpgradeMessage,
    shouldShowUsageWarning
  };
};

// Specific feature gate hooks for common features
export const useComplianceFeatureGate = () => {
  return useFeatureGate({
    feature: 'compliance_tools',
    autoCheck: true,
    trackUsage: true,
    showUpgradePrompts: true,
    usageWarningThreshold: 90
  });
};

export const useBidSubmissionGate = () => {
  return useFeatureGate({
    feature: 'bid_submission',
    autoCheck: true,
    trackUsage: true,
    showUpgradePrompts: true,
    usageWarningThreshold: 85
  });
};

export const useEcosystemGate = (service: string) => {
  return useFeatureGate({
    feature: `ecosystem_${service}`,
    autoCheck: true,
    trackUsage: true,
    showUpgradePrompts: true,
    usageWarningThreshold: 80
  });
};

export const useAnalyticsGate = () => {
  return useFeatureGate({
    feature: 'advanced_analytics',
    autoCheck: true,
    trackUsage: false, // Analytics viewing doesn't consume usage
    showUpgradePrompts: true
  });
};

export const useTeamFeaturesGate = () => {
  return useFeatureGate({
    feature: 'team_features',
    autoCheck: true,
    trackUsage: true,
    showUpgradePrompts: true,
    usageWarningThreshold: 95
  });
};

// Feature gate component wrapper
export interface FeatureGateWrapperProps {
  feature: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showUpgradePrompt?: boolean;
  trackUsage?: boolean;
  usageQuantity?: number;
}

export const FeatureGateWrapper: React.FC<FeatureGateWrapperProps> = ({
  feature,
  children,
  fallback,
  showUpgradePrompt = true,
  trackUsage = true,
  usageQuantity = 1
}) => {
  const featureGate = useFeatureGate({
    feature,
    autoCheck: true,
    trackUsage,
    showUpgradePrompts: showUpgradePrompt
  });

  // Record usage when component mounts and has access
  useEffect(() => {
    if (featureGate.hasAccess && trackUsage) {
      featureGate.recordUsage(feature, usageQuantity);
    }
  }, [featureGate.hasAccess, trackUsage, feature, usageQuantity, featureGate]);

  if (!featureGate.hasAccess) {
    if (fallback) {
      return React.createElement(React.Fragment, null, fallback);
    }

    // Default fallback with upgrade prompt
    return React.createElement('div', {
      style: {
        padding: '16px',
        textAlign: 'center',
        border: '1px dashed #ccc',
        borderRadius: '8px',
        backgroundColor: '#f9f9f9'
      }
    }, [
      React.createElement('p', { key: 'message' }, featureGate.getUpgradeMessage()),
      showUpgradePrompt && React.createElement('button', {
        key: 'button',
        onClick: featureGate.showUpgradePrompt,
        style: {
          padding: '8px 16px',
          backgroundColor: '#1976d2',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer'
        }
      }, 'Upgrade Now')
    ].filter(Boolean));
  }

  return React.createElement(React.Fragment, null, children);
};

export default useFeatureGate;
