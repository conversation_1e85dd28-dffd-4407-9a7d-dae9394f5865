/**
 * React Hook for NeuroMarketing Engine Integration
 * Provides psychological state management and adaptive interface capabilities
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import NeuroMarketingEngine, {
  UserPsychologicalState,
  PsychologicalProfile,
  AdaptiveSettings,
  UIComplexity,
  ColorScheme,
  AnimationLevel,
  InformationDensity,
  NavigationStyle,
  GamificationLevel
} from '../services/NeuroMarketingEngine';

export interface NeuroMarketingHookReturn {
  // Psychological State
  psychologicalState: UserPsychologicalState;
  psychologicalProfile: PsychologicalProfile | null;
  
  // Adaptive Settings
  adaptiveSettings: AdaptiveSettings;
  
  // Control Methods
  startTracking: () => void;
  stopTracking: () => void;
  updateProfile: () => void;
  
  // Convenience Methods
  isHighCognitiveLoad: boolean;
  isStressed: boolean;
  isEngaged: boolean;
  needsSimplification: boolean;
  needsMotivation: boolean;
  
  // Adaptive UI Helpers
  getUIComplexity: () => UIComplexity;
  getColorScheme: () => ColorScheme;
  getAnimationLevel: () => AnimationLevel;
  getInformationDensity: () => InformationDensity;
  getNavigationStyle: () => NavigationStyle;
  getGamificationLevel: () => GamificationLevel;
  
  // Behavioral Insights
  shouldShowHelp: boolean;
  shouldReduceOptions: boolean;
  shouldIncreaseMotivation: boolean;
  shouldUseCalming: boolean;
  
  // Compliance-Specific Helpers
  getComplianceUIMode: () => 'simplified' | 'standard' | 'detailed';
  getProtestStressLevel: () => 'low' | 'medium' | 'high';
  shouldShowEncouragement: boolean;
  shouldBreakIntoSteps: boolean;
}

export const useNeuroMarketing = (): NeuroMarketingHookReturn => {
  const engineRef = useRef<NeuroMarketingEngine>(NeuroMarketingEngine.getInstance());
  const [psychologicalState, setPsychologicalState] = useState<UserPsychologicalState>(
    engineRef.current.getCurrentPsychologicalState()
  );
  const [psychologicalProfile, setPsychologicalProfile] = useState<PsychologicalProfile | null>(
    engineRef.current.getPsychologicalProfile()
  );
  const [adaptiveSettings, setAdaptiveSettings] = useState<AdaptiveSettings>(
    engineRef.current.getAdaptiveSettings()
  );
  const [isTracking, setIsTracking] = useState(false);

  // Update state periodically
  useEffect(() => {
    const updateInterval = setInterval(() => {
      if (isTracking) {
        const newState = engineRef.current.getCurrentPsychologicalState();
        const newSettings = engineRef.current.getAdaptiveSettings();
        
        setPsychologicalState(newState);
        setAdaptiveSettings(newSettings);
        
        // Update adaptive settings based on current state
        engineRef.current.updateAdaptiveSettings();
      }
    }, 2000); // Update every 2 seconds

    return () => clearInterval(updateInterval);
  }, [isTracking]);

  // Listen for adaptive changes
  useEffect(() => {
    const handleAdaptiveChange = (newSettings: AdaptiveSettings) => {
      setAdaptiveSettings(newSettings);
    };

    engineRef.current.addEventListener('adaptiveChange', handleAdaptiveChange);

    return () => {
      engineRef.current.removeEventListener('adaptiveChange', handleAdaptiveChange);
    };
  }, []);

  // Control methods
  const startTracking = useCallback(() => {
    engineRef.current.startTracking();
    setIsTracking(true);
  }, []);

  const stopTracking = useCallback(() => {
    engineRef.current.stopTracking();
    setIsTracking(false);
  }, []);

  const updateProfile = useCallback(() => {
    const newProfile = engineRef.current.buildPsychologicalProfile();
    setPsychologicalProfile(newProfile);
  }, []);

  // Convenience computed values
  const isHighCognitiveLoad = psychologicalState.cognitiveLoad > 0.7;
  const isStressed = psychologicalState.stressLevel > 0.6;
  const isEngaged = psychologicalState.engagementLevel > 0.6;
  const needsSimplification = isHighCognitiveLoad || isStressed;
  const needsMotivation = psychologicalState.engagementLevel < 0.4;

  // Adaptive UI helpers
  const getUIComplexity = useCallback(() => adaptiveSettings.uiComplexity, [adaptiveSettings]);
  const getColorScheme = useCallback(() => adaptiveSettings.colorScheme, [adaptiveSettings]);
  const getAnimationLevel = useCallback(() => adaptiveSettings.animationLevel, [adaptiveSettings]);
  const getInformationDensity = useCallback(() => adaptiveSettings.informationDensity, [adaptiveSettings]);
  const getNavigationStyle = useCallback(() => adaptiveSettings.navigationStyle, [adaptiveSettings]);
  const getGamificationLevel = useCallback(() => adaptiveSettings.gamificationLevel, [adaptiveSettings]);

  // Behavioral insights
  const shouldShowHelp = psychologicalState.frustrationLevel > 0.5 || psychologicalState.cognitiveLoad > 0.8;
  const shouldReduceOptions = psychologicalState.decisionFatigue > 0.6 || psychologicalState.cognitiveLoad > 0.7;
  const shouldIncreaseMotivation = psychologicalState.engagementLevel < 0.3 || psychologicalState.confidenceLevel < 0.4;
  const shouldUseCalming = psychologicalState.stressLevel > 0.6 || psychologicalState.emotionalArousal > 0.8;

  // Compliance-specific helpers
  const getComplianceUIMode = useCallback((): 'simplified' | 'standard' | 'detailed' => {
    if (psychologicalState.cognitiveLoad > 0.7 || psychologicalState.stressLevel > 0.6) {
      return 'simplified';
    } else if (psychologicalState.cognitiveLoad < 0.3 && psychologicalState.attentionSpan > 0.7) {
      return 'detailed';
    } else {
      return 'standard';
    }
  }, [psychologicalState]);

  const getProtestStressLevel = useCallback((): 'low' | 'medium' | 'high' => {
    const combinedStress = (psychologicalState.stressLevel + psychologicalState.frustrationLevel) / 2;
    
    if (combinedStress > 0.7) {
      return 'high';
    } else if (combinedStress > 0.4) {
      return 'medium';
    } else {
      return 'low';
    }
  }, [psychologicalState]);

  const shouldShowEncouragement = psychologicalState.confidenceLevel < 0.5 || psychologicalState.stressLevel > 0.5;
  const shouldBreakIntoSteps = psychologicalState.cognitiveLoad > 0.6 || psychologicalState.decisionFatigue > 0.5;

  return {
    // State
    psychologicalState,
    psychologicalProfile,
    adaptiveSettings,
    
    // Control
    startTracking,
    stopTracking,
    updateProfile,
    
    // Convenience
    isHighCognitiveLoad,
    isStressed,
    isEngaged,
    needsSimplification,
    needsMotivation,
    
    // Adaptive UI
    getUIComplexity,
    getColorScheme,
    getAnimationLevel,
    getInformationDensity,
    getNavigationStyle,
    getGamificationLevel,
    
    // Behavioral Insights
    shouldShowHelp,
    shouldReduceOptions,
    shouldIncreaseMotivation,
    shouldUseCalming,
    
    // Compliance-Specific
    getComplianceUIMode,
    getProtestStressLevel,
    shouldShowEncouragement,
    shouldBreakIntoSteps
  };
};

// Additional specialized hooks for specific use cases

export const useComplianceOptimization = () => {
  const neuro = useNeuroMarketing();
  
  return {
    // Protest Filing Optimization
    protestUIMode: neuro.getComplianceUIMode(),
    stressLevel: neuro.getProtestStressLevel(),
    showEncouragement: neuro.shouldShowEncouragement,
    breakIntoSteps: neuro.shouldBreakIntoSteps,
    
    // Template Generation Optimization
    templateComplexity: neuro.isHighCognitiveLoad ? 'simple' : 'comprehensive',
    showGuidance: neuro.shouldShowHelp,
    useProgressIndicators: neuro.psychologicalState.decisionFatigue > 0.4,
    
    // SME-Specific Optimizations
    simplifyLegalLanguage: neuro.psychologicalState.cognitiveLoad > 0.6,
    showCostWarnings: neuro.psychologicalState.stressLevel > 0.5,
    emphasizeSupport: neuro.psychologicalState.confidenceLevel < 0.5,
    
    // Deadline Management
    urgencyVisualization: neuro.psychologicalState.attentionSpan < 0.5 ? 'prominent' : 'subtle',
    stressReduction: neuro.shouldUseCalming,
    motivationalMessaging: neuro.needsMotivation
  };
};

export const useBiddingOptimization = () => {
  const neuro = useNeuroMarketing();
  
  return {
    // Bid Creation Optimization
    showQuickBid: neuro.psychologicalState.decisionFatigue > 0.6,
    detailLevel: neuro.getInformationDensity(),
    assistanceLevel: neuro.shouldShowHelp ? 'high' : 'standard',
    
    // Tender Discovery Optimization
    filterComplexity: neuro.shouldReduceOptions ? 'simple' : 'advanced',
    recommendationEngine: neuro.needsMotivation ? 'aggressive' : 'balanced',
    visualDensity: neuro.getUIComplexity(),
    
    // Ecosystem Integration Optimization
    autoPopulation: neuro.psychologicalState.cognitiveLoad > 0.7,
    verificationLevel: neuro.psychologicalState.attentionSpan > 0.6 ? 'detailed' : 'basic',
    guidanceIntensity: neuro.shouldShowHelp ? 'comprehensive' : 'minimal'
  };
};

export const useGamificationOptimization = () => {
  const neuro = useNeuroMarketing();
  
  return {
    // Achievement System
    achievementVisibility: neuro.getGamificationLevel(),
    celebrationIntensity: neuro.psychologicalState.engagementLevel < 0.5 ? 'high' : 'moderate',
    progressVisualization: neuro.psychologicalProfile?.motivationType === 'achievement' ? 'detailed' : 'simple',
    
    // Social Features
    socialComparison: neuro.psychologicalProfile?.motivationType === 'affiliation',
    leaderboards: neuro.psychologicalProfile?.motivationType === 'power',
    collaboration: neuro.psychologicalProfile?.decisionMakingStyle === 'collaborative',
    
    // Motivation Triggers
    streakEmphasis: neuro.needsMotivation,
    rewardFrequency: neuro.psychologicalState.engagementLevel < 0.4 ? 'frequent' : 'standard',
    challengeLevel: neuro.psychologicalProfile?.riskTolerance || 'moderate'
  };
};

export default useNeuroMarketing;
