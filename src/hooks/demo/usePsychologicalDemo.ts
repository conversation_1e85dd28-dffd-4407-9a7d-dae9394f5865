/**
 * 🧠 Psychological Demo Hooks
 * Extracted from MVP for client demonstrations and psychological profiling showcases
 * Provides mock psychological data and real-time state simulation
 */

import { useState, useEffect, useCallback } from 'react';
import { PsychologicalState } from '../../components/demo/AdaptiveInterfaceDemo';

// Demo user interface
export interface DemoUser {
  id: string;
  username: string;
  email: string;
  role: 'user' | 'admin';
  subscriptionTier: 'free' | 'basic' | 'professional' | 'enterprise';
  isBetaTester: boolean;
}

// Neuro marketing state interface
export interface NeuroMarketingState {
  psychologicalState: PsychologicalState;
  adaptiveRecommendations: string[];
  realTimeMetrics: {
    attentionSpan: number;
    emotionalArousal: number;
    decisionReadiness: number;
    trustLevel: number;
  };
}

// Personality insights interface
export interface PersonalityInsights {
  bigFiveScores: {
    openness: number;
    conscientiousness: number;
    extraversion: number;
    agreeableness: number;
    neuroticism: number;
  };
  workingStyle: 'methodical' | 'spontaneous' | 'collaborative' | 'independent';
  communicationPreference: 'visual' | 'textual' | 'interactive' | 'minimal';
  stressResponse: 'fight' | 'flight' | 'freeze' | 'adapt';
}

/**
 * Mock Authentication Hook for Demo
 * Simulates user authentication with different user types
 */
export const useAuthDemo = (userType: 'free' | 'paid' | 'enterprise' | 'admin' = 'free'): { user: DemoUser } => {
  const demoUsers: Record<string, DemoUser> = {
    free: {
      id: 'demo-free-user',
      username: 'John Doe',
      email: '<EMAIL>',
      role: 'user',
      subscriptionTier: 'free',
      isBetaTester: false
    },
    paid: {
      id: 'demo-paid-user',
      username: 'Sarah Smith',
      email: '<EMAIL>',
      role: 'user',
      subscriptionTier: 'professional',
      isBetaTester: true
    },
    enterprise: {
      id: 'demo-enterprise-user',
      username: 'Michael Johnson',
      email: '<EMAIL>',
      role: 'user',
      subscriptionTier: 'enterprise',
      isBetaTester: true
    },
    admin: {
      id: 'demo-admin-user',
      username: 'Admin User',
      email: '<EMAIL>',
      role: 'admin',
      subscriptionTier: 'enterprise',
      isBetaTester: true
    }
  };

  return { user: demoUsers[userType] };
};

/**
 * Mock NeuroMarketing Hook for Demo
 * Simulates real-time psychological state monitoring and adaptive recommendations
 */
export const useNeuroMarketingDemo = (
  archetype: PsychologicalState['archetype'] = 'Achiever',
  simulateRealTime: boolean = false
): NeuroMarketingState => {
  const [psychologicalState, setPsychologicalState] = useState<PsychologicalState>(() => {
    const baseStates = {
      Achiever: {
        archetype: 'Achiever' as const,
        stressLevel: 0.3,
        confidenceLevel: 0.8,
        engagementLevel: 0.9,
        cognitiveLoad: 0.4,
        motivationFactors: ['Financial Growth', 'Recognition', 'Achievement'],
        decisionMakingStyle: 'Decisive' as const,
        riskTolerance: 'Moderate' as const
      },
      Hunter: {
        archetype: 'Hunter' as const,
        stressLevel: 0.2,
        confidenceLevel: 0.9,
        engagementLevel: 0.8,
        cognitiveLoad: 0.3,
        motivationFactors: ['Competition', 'Quick Wins', 'Opportunity'],
        decisionMakingStyle: 'Intuitive' as const,
        riskTolerance: 'High' as const
      },
      Analyst: {
        archetype: 'Analyst' as const,
        stressLevel: 0.4,
        confidenceLevel: 0.7,
        engagementLevel: 0.7,
        cognitiveLoad: 0.6,
        motivationFactors: ['Data-driven Decisions', 'Accuracy', 'Understanding'],
        decisionMakingStyle: 'Analytical' as const,
        riskTolerance: 'Low' as const
      },
      'Relationship Builder': {
        archetype: 'Relationship Builder' as const,
        stressLevel: 0.3,
        confidenceLevel: 0.6,
        engagementLevel: 0.8,
        cognitiveLoad: 0.4,
        motivationFactors: ['Collaboration', 'Trust Building', 'Long-term Success'],
        decisionMakingStyle: 'Collaborative' as const,
        riskTolerance: 'Moderate' as const
      }
    };

    return baseStates[archetype];
  });

  const [realTimeMetrics, setRealTimeMetrics] = useState({
    attentionSpan: 0.8,
    emotionalArousal: 0.6,
    decisionReadiness: 0.7,
    trustLevel: 0.8
  });

  // Simulate real-time psychological state changes
  useEffect(() => {
    if (!simulateRealTime) return;

    const interval = setInterval(() => {
      setPsychologicalState(prev => ({
        ...prev,
        stressLevel: Math.max(0, Math.min(1, prev.stressLevel + (Math.random() - 0.5) * 0.1)),
        confidenceLevel: Math.max(0, Math.min(1, prev.confidenceLevel + (Math.random() - 0.5) * 0.05)),
        engagementLevel: Math.max(0, Math.min(1, prev.engagementLevel + (Math.random() - 0.5) * 0.08)),
        cognitiveLoad: Math.max(0, Math.min(1, prev.cognitiveLoad + (Math.random() - 0.5) * 0.06))
      }));

      setRealTimeMetrics(prev => ({
        attentionSpan: Math.max(0, Math.min(1, prev.attentionSpan + (Math.random() - 0.5) * 0.1)),
        emotionalArousal: Math.max(0, Math.min(1, prev.emotionalArousal + (Math.random() - 0.5) * 0.08)),
        decisionReadiness: Math.max(0, Math.min(1, prev.decisionReadiness + (Math.random() - 0.5) * 0.06)),
        trustLevel: Math.max(0, Math.min(1, prev.trustLevel + (Math.random() - 0.5) * 0.04))
      }));
    }, 2000);

    return () => clearInterval(interval);
  }, [simulateRealTime]);

  const adaptiveRecommendations = [
    `Optimize for ${psychologicalState.archetype} archetype preferences`,
    psychologicalState.stressLevel > 0.6 
      ? 'Reduce cognitive load - simplify interface elements'
      : 'Increase engagement - add interactive elements',
    psychologicalState.confidenceLevel < 0.5
      ? 'Boost confidence with success indicators and positive feedback'
      : 'Leverage high confidence with challenging opportunities',
    `Emphasize ${psychologicalState.motivationFactors[0].toLowerCase()} in messaging`
  ];

  return {
    psychologicalState,
    adaptiveRecommendations,
    realTimeMetrics
  };
};

/**
 * Mock Psychological Systems Hook for Demo
 * Simulates personality insights and behavioral analysis
 */
export const usePsychologicalSystemsDemo = (
  archetype: PsychologicalState['archetype'] = 'Achiever'
): { personalityInsights: PersonalityInsights } => {
  const personalityProfiles = {
    Achiever: {
      bigFiveScores: {
        openness: 0.7,
        conscientiousness: 0.9,
        extraversion: 0.8,
        agreeableness: 0.6,
        neuroticism: 0.3
      },
      workingStyle: 'methodical' as const,
      communicationPreference: 'visual' as const,
      stressResponse: 'fight' as const
    },
    Hunter: {
      bigFiveScores: {
        openness: 0.8,
        conscientiousness: 0.6,
        extraversion: 0.9,
        agreeableness: 0.5,
        neuroticism: 0.2
      },
      workingStyle: 'spontaneous' as const,
      communicationPreference: 'interactive' as const,
      stressResponse: 'fight' as const
    },
    Analyst: {
      bigFiveScores: {
        openness: 0.9,
        conscientiousness: 0.8,
        extraversion: 0.4,
        agreeableness: 0.7,
        neuroticism: 0.4
      },
      workingStyle: 'methodical' as const,
      communicationPreference: 'textual' as const,
      stressResponse: 'freeze' as const
    },
    'Relationship Builder': {
      bigFiveScores: {
        openness: 0.6,
        conscientiousness: 0.7,
        extraversion: 0.8,
        agreeableness: 0.9,
        neuroticism: 0.3
      },
      workingStyle: 'collaborative' as const,
      communicationPreference: 'interactive' as const,
      stressResponse: 'adapt' as const
    }
  };

  return {
    personalityInsights: personalityProfiles[archetype]
  };
};

/**
 * Psychological Analysis Loading Hook
 * Simulates the loading state for psychological analysis
 */
export const usePsychologicalAnalysisLoading = (duration: number = 2000) => {
  const [isLoading, setIsLoading] = useState(true);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev + (100 / (duration / 100));
        return newProgress >= 100 ? 100 : newProgress;
      });
    }, 100);

    const loadingTimeout = setTimeout(() => {
      setIsLoading(false);
    }, duration);

    return () => {
      clearInterval(progressInterval);
      clearTimeout(loadingTimeout);
    };
  }, [duration]);

  const restart = useCallback(() => {
    setIsLoading(true);
    setProgress(0);
  }, []);

  return {
    isLoading,
    progress,
    restart
  };
};
