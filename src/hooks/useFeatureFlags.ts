/**
 * Feature Flag Management Hook
 * Controls progressive feature activation for <PERSON> to full platform evolution
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useAuth } from '../contexts/AuthContext';
import {
  FeatureFlag,
  FeatureFlagContext,
  FeatureFlagResult,
  FeatureStatus,
  UserSegment,
  BIDBEEZ_FEATURE_FLAGS,
  FEATURE_FLAG_GROUPS,
  MVPLevel,
  BusinessValue
} from '../types/featureFlags';

// Feature Flag Service
class FeatureFlagService {
  private static instance: FeatureFlagService;
  private flags: Record<string, FeatureFlag> = BIDBEEZ_FEATURE_FLAGS;
  private cache: Map<string, FeatureFlagResult> = new Map();
  private listeners: Set<() => void> = new Set();

  static getInstance(): FeatureFlagService {
    if (!FeatureFlagService.instance) {
      FeatureFlagService.instance = new FeatureFlagService();
    }
    return FeatureFlagService.instance;
  }

  // Evaluate feature flag for user context
  evaluateFlag(flagId: string, context: FeatureFlagContext): FeatureFlagResult {
    const cacheKey = `${flagId}_${context.userId}_${context.subscriptionTier}`;
    
    // Check cache first
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    const flag = this.flags[flagId];
    if (!flag) {
      const result = { enabled: false, reason: 'Flag not found' };
      this.cache.set(cacheKey, result);
      return result;
    }

    const result = this.evaluateFlagLogic(flag, context);
    this.cache.set(cacheKey, result);
    
    // Cache for 5 minutes
    setTimeout(() => this.cache.delete(cacheKey), 5 * 60 * 1000);
    
    return result;
  }

  private evaluateFlagLogic(flag: FeatureFlag, context: FeatureFlagContext): FeatureFlagResult {
    // Check if flag is disabled
    if (flag.status === FeatureStatus.DISABLED) {
      return { enabled: false, reason: 'Feature is disabled' };
    }

    // Check if user is explicitly disabled
    if (flag.disabledForUserIds.includes(context.userId)) {
      return { enabled: false, reason: 'User explicitly disabled' };
    }

    // Check if user is explicitly enabled
    if (flag.enabledForUserIds.includes(context.userId)) {
      return { 
        enabled: true, 
        reason: 'User explicitly enabled',
        config: flag.config 
      };
    }

    // Check admin override
    if (context.isAdmin) {
      return { 
        enabled: true, 
        reason: 'Admin access',
        config: flag.config 
      };
    }

    // Check user segment targeting
    if (!this.isUserInTargetSegment(flag, context)) {
      return { enabled: false, reason: 'User not in target segment' };
    }

    // Check subscription tier
    if (!flag.subscriptionTiers.includes(context.subscriptionTier)) {
      return { enabled: false, reason: 'Subscription tier not supported' };
    }

    // Check dependencies
    for (const depId of flag.dependencies) {
      const depResult = this.evaluateFlag(depId, context);
      if (!depResult.enabled) {
        return { enabled: false, reason: `Dependency ${depId} not enabled` };
      }
    }

    // Check conflicts
    for (const conflictId of flag.conflicts) {
      const conflictResult = this.evaluateFlag(conflictId, context);
      if (conflictResult.enabled) {
        return { enabled: false, reason: `Conflicts with ${conflictId}` };
      }
    }

    // Check rollout percentage
    if (flag.rolloutPercentage < 100) {
      const userHash = this.hashUserId(context.userId);
      const userPercentile = userHash % 100;
      
      if (userPercentile >= flag.rolloutPercentage) {
        return { enabled: false, reason: 'Not in rollout percentage' };
      }
    }

    // Beta testing check
    if (flag.status === FeatureStatus.BETA && !context.isBetaTester) {
      return { enabled: false, reason: 'Beta feature requires beta tester status' };
    }

    return { 
      enabled: true, 
      reason: 'All conditions met',
      config: flag.config 
    };
  }

  private isUserInTargetSegment(flag: FeatureFlag, context: FeatureFlagContext): boolean {
    if (flag.userSegments.includes(UserSegment.ALL)) {
      return true;
    }

    return flag.userSegments.some(segment => {
      switch (segment) {
        case UserSegment.FREE_USERS:
          return context.subscriptionTier === 'free';
        case UserSegment.PAID_USERS:
          return context.subscriptionTier !== 'free';
        case UserSegment.SME_USERS:
          return context.userSegment === UserSegment.SME_USERS;
        case UserSegment.ENTERPRISE_USERS:
          return context.subscriptionTier === 'enterprise';
        case UserSegment.BETA_TESTERS:
          return context.isBetaTester;
        case UserSegment.ADMIN_USERS:
          return context.isAdmin;
        default:
          return false;
      }
    });
  }

  private hashUserId(userId: string): number {
    let hash = 0;
    for (let i = 0; i < userId.length; i++) {
      const char = userId.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  // Update flag configuration
  updateFlag(flagId: string, updates: Partial<FeatureFlag>): void {
    if (this.flags[flagId]) {
      this.flags[flagId] = { ...this.flags[flagId], ...updates };
      this.cache.clear(); // Clear cache when flags change
      this.notifyListeners();
    }
  }

  // Get all flags for admin interface
  getAllFlags(): Record<string, FeatureFlag> {
    return { ...this.flags };
  }

  // Get flags by category
  getFlagsByCategory(category: string): FeatureFlag[] {
    return Object.values(this.flags).filter(flag => flag.category === category);
  }

  // Get MVP level flags
  getFlagsByMVPLevel(level: MVPLevel): FeatureFlag[] {
    const groups = Object.values(FEATURE_FLAG_GROUPS).filter(group => group.mvpLevel === level);
    const flagIds = groups.flatMap(group => group.features);
    return flagIds.map(id => this.flags[id]).filter(Boolean);
  }

  // Subscribe to flag changes
  subscribe(listener: () => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener());
  }
}

// React Hook for Feature Flags
export const useFeatureFlags = () => {
  const { user } = useAuth();
  const [, forceUpdate] = useState({});
  const flagService = FeatureFlagService.getInstance();

  // Subscribe to flag changes
  useEffect(() => {
    const unsubscribe = flagService.subscribe(() => {
      forceUpdate({});
    });
    return unsubscribe;
  }, [flagService]);

  // Create user context
  const context = useMemo((): FeatureFlagContext => {
    if (!user) {
      return {
        userId: 'anonymous',
        userSegment: UserSegment.ALL,
        subscriptionTier: 'free',
        isAdmin: false,
        isBetaTester: false
      };
    }

    return {
      userId: user.id,
      userSegment: user.isSME ? UserSegment.SME_USERS : UserSegment.ALL,
      subscriptionTier: (user as any).subscriptionTier || 'free',
      isAdmin: user.role === 'admin',
      isBetaTester: (user.preferences as any)?.betaTester || false,
      companySize: (user as any).companySize,
      location: (user as any).location
    };
  }, [user]);

  // Check if feature is enabled
  const isFeatureEnabled = useCallback((flagId: string): boolean => {
    const result = flagService.evaluateFlag(flagId, context);
    return result.enabled;
  }, [flagService, context]);

  // Get feature flag result with details
  const getFeatureFlag = useCallback((flagId: string): FeatureFlagResult => {
    return flagService.evaluateFlag(flagId, context);
  }, [flagService, context]);

  // Get feature config
  const getFeatureConfig = useCallback((flagId: string): Record<string, any> => {
    const result = flagService.evaluateFlag(flagId, context);
    return result.config || {};
  }, [flagService, context]);

  // Check multiple features at once
  const areAllFeaturesEnabled = useCallback((flagIds: string[]): boolean => {
    return flagIds.every(flagId => isFeatureEnabled(flagId));
  }, [isFeatureEnabled]);

  // Check if any feature is enabled
  const isAnyFeatureEnabled = useCallback((flagIds: string[]): boolean => {
    return flagIds.some(flagId => isFeatureEnabled(flagId));
  }, [isFeatureEnabled]);

  // Get enabled features by category
  const getEnabledFeaturesByCategory = useCallback((category: string): string[] => {
    const flags = flagService.getFlagsByCategory(category);
    return flags
      .filter(flag => isFeatureEnabled(flag.id))
      .map(flag => flag.id);
  }, [flagService, isFeatureEnabled]);

  // Get MVP readiness
  const getMVPReadiness = useCallback(() => {
    const coreFeatures = flagService.getFlagsByMVPLevel(MVPLevel.MVP_CORE);
    const enabledCore = coreFeatures.filter(flag => isFeatureEnabled(flag.id));
    
    return {
      total: coreFeatures.length,
      enabled: enabledCore.length,
      percentage: (enabledCore.length / coreFeatures.length) * 100,
      ready: enabledCore.length === coreFeatures.length,
      missing: coreFeatures
        .filter(flag => !isFeatureEnabled(flag.id))
        .map(flag => flag.name)
    };
  }, [flagService, isFeatureEnabled]);

  return {
    // Core methods
    isFeatureEnabled,
    getFeatureFlag,
    getFeatureConfig,
    
    // Batch operations
    areAllFeaturesEnabled,
    isAnyFeatureEnabled,
    getEnabledFeaturesByCategory,
    
    // MVP management
    getMVPReadiness,
    
    // Context info
    userContext: context,
    
    // Admin methods (only for admins)
    ...(context.isAdmin && {
      getAllFlags: () => flagService.getAllFlags(),
      updateFlag: (flagId: string, updates: Partial<FeatureFlag>) => 
        flagService.updateFlag(flagId, updates),
      getFlagsByCategory: (category: string) => flagService.getFlagsByCategory(category)
    })
  };
};

// Specific feature hooks for common features
export const useComplianceFeatures = () => {
  const { isFeatureEnabled, getFeatureConfig } = useFeatureFlags();
  
  return {
    complianceToolsEnabled: isFeatureEnabled('sa_compliance_tools'),
    protestManagementEnabled: isFeatureEnabled('bid_protest_management'),
    complianceConfig: getFeatureConfig('sa_compliance_tools')
  };
};

export const useGamificationFeatures = () => {
  const { isFeatureEnabled } = useFeatureFlags();
  
  return {
    achievementsEnabled: isFeatureEnabled('achievement_system'),
    leaderboardsEnabled: isFeatureEnabled('leaderboards'),
    gamificationEnabled: isFeatureEnabled('achievement_system') || isFeatureEnabled('leaderboards')
  };
};

export const useEcosystemFeatures = () => {
  const { isFeatureEnabled } = useFeatureFlags();
  
  return {
    skillSyncEnabled: isFeatureEnabled('skillsync_integration'),
    toolSyncEnabled: isFeatureEnabled('toolsync_integration'),
    ecosystemEnabled: isFeatureEnabled('skillsync_integration') || isFeatureEnabled('toolsync_integration')
  };
};

export const useAnalyticsFeatures = () => {
  const { isFeatureEnabled } = useFeatureFlags();

  return {
    advancedAnalyticsEnabled: isFeatureEnabled('advanced_analytics'),
    aiInsightsEnabled: isFeatureEnabled('ai_insights'),
    neuroMarketingEnabled: isFeatureEnabled('neuromarketing_engine')
  };
};

export const useDemoFeatures = () => {
  const { isFeatureEnabled, getFeatureConfig } = useFeatureFlags();

  return {
    psychologicalDemoEnabled: isFeatureEnabled('psychological_demo'),
    adaptiveInterfaceDemoEnabled: isFeatureEnabled('adaptive_interface_demo'),
    demoConfig: getFeatureConfig('psychological_demo'),
    isDemoMode: isFeatureEnabled('psychological_demo') || isFeatureEnabled('adaptive_interface_demo')
  };
};

// Feature Flag Component Wrapper
export interface FeatureGateProps {
  feature: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  loadingComponent?: React.ReactNode;
}

export const FeatureGate: React.FC<FeatureGateProps> = ({
  feature,
  children,
  fallback = null,
  loadingComponent = null
}) => {
  const { isFeatureEnabled, getFeatureFlag } = useFeatureFlags();
  
  const flagResult = getFeatureFlag(feature);
  
  if (loadingComponent && flagResult.reason === 'Loading...') {
    return React.createElement(React.Fragment, null, loadingComponent);
  }
  
  if (flagResult.enabled) {
    return React.createElement(React.Fragment, null, children);
  }
  
  return React.createElement(React.Fragment, null, fallback);
};

export default useFeatureFlags;
