/**
 * Behavioral Tender Service
 * Integrates with existing NeuroMarketing Engine for psychological optimization
 */

import { NeuroMarketingEngine } from './NeuroMarketingEngine';
import { PsychologicalProfile } from '../types/tender.types';
import { 
  Tender, 
  TenderMatch, 
  BehavioralNudge, 
  EngagementTrigger, 
  TenderStatus,
  Priority,
  TenderCategory 
} from '../types/tender.types';

class BehavioralTenderService {
  private static instance: BehavioralTenderService;
  private neuroEngine: NeuroMarketingEngine;
  private userProfile: PsychologicalProfile | null = null;
  private behavioralCache: Map<string, TenderMatch> = new Map();

  private constructor() {
    this.neuroEngine = NeuroMarketingEngine.getInstance();
    this.initializeBehavioralTracking();
  }

  public static getInstance(): BehavioralTenderService {
    if (!BehavioralTenderService.instance) {
      BehavioralTenderService.instance = new BehavioralTenderService();
    }
    return BehavioralTenderService.instance;
  }

  private initializeBehavioralTracking(): void {
    // Listen to psychological state changes from NeuroMarketing Engine
    this.neuroEngine.addEventListener('psychologicalStateChange', (state: any) => {
      this.adaptTenderRecommendations(state);
    });

    this.neuroEngine.addEventListener('profileUpdate', (profile: any) => {
      this.userProfile = profile;
      this.invalidateBehavioralCache();
    });
  }

  /**
   * Get personalized tender recommendations based on psychological profile
   */
  public async getPersonalizedTenders(
    baseTenders: Tender[],
    limit: number = 10
  ): Promise<Tender[]> {
    const psychState = this.neuroEngine.getCurrentPsychologicalState();
    const profile = this.userProfile || this.neuroEngine.getPsychologicalProfile();

    if (!profile) {
      return this.getFallbackTenders(baseTenders, limit);
    }

    // Score and rank tenders based on psychological fit
    const scoredTenders = await Promise.all(
      baseTenders.map(async (tender) => {
        const match = await this.calculateTenderMatch(tender, profile as any, psychState);
        return {
          ...tender,
          matchData: match,
          personalizedTitle: this.generatePersonalizedTitle(tender, profile),
          personalizedDescription: this.generatePersonalizedDescription(tender, profile),
          recommendationReason: this.generateRecommendationReason(match, profile),
          psychologicalTriggers: this.identifyPsychologicalTriggers(tender, profile),
          behavioralNudges: this.generateBehavioralNudges(tender, profile, psychState)
        };
      })
    );

    // Sort by psychological fit and behavioral prediction
    const rankedTenders = scoredTenders
      .sort((a, b) => {
        const scoreA = (a.matchData?.psychologicalFit || 0) + (a.matchData?.behavioralPrediction || 0);
        const scoreB = (b.matchData?.psychologicalFit || 0) + (b.matchData?.behavioralPrediction || 0);
        return scoreB - scoreA;
      })
      .slice(0, limit);

    // Apply real-time psychological adaptations
    return this.applyPsychologicalAdaptations(rankedTenders, psychState);
  }

  /**
   * Calculate psychological match between tender and user
   */
  private async calculateTenderMatch(
    tender: Tender,
    profile: PsychologicalProfile,
    psychState: any
  ): Promise<TenderMatch> {
    const cacheKey = `${tender.id}-${(profile as any).userId || 'anonymous'}-${Date.now()}`;
    
    if (this.behavioralCache.has(cacheKey)) {
      return this.behavioralCache.get(cacheKey)!;
    }

    // Calculate various psychological fit scores
    const psychologicalFit = this.calculatePsychologicalFit(tender, profile);
    const behavioralPrediction = this.calculateBehavioralPrediction(tender, profile, psychState);
    const emotionalResonance = this.calculateEmotionalResonance(tender, profile);
    const cognitiveLoad = this.calculateCognitiveLoad(tender, psychState);
    const stressLevel = this.calculateStressLevel(tender, psychState);
    
    // Overall match score (weighted combination)
    const matchScore = Math.round(
      (psychologicalFit * 0.3) +
      (behavioralPrediction * 0.25) +
      (emotionalResonance * 0.2) +
      ((100 - cognitiveLoad) * 0.15) +
      ((100 - stressLevel) * 0.1)
    );

    const motivationTriggers = this.identifyMotivationTriggers(tender, profile);
    const personalizedReasons = this.generatePersonalizedReasons(tender, profile, psychologicalFit);

    const match: TenderMatch = {
      tenderId: tender.id,
      matchScore,
      psychologicalFit,
      behavioralPrediction,
      emotionalResonance,
      cognitiveLoad,
      stressLevel,
      motivationTriggers,
      personalizedReasons
    };

    this.behavioralCache.set(cacheKey, match);
    return match;
  }

  /**
   * Calculate how well tender fits user's psychological profile
   */
  private calculatePsychologicalFit(tender: Tender, profile: PsychologicalProfile): number {
    let score = 50; // Base score

    // Personality type matching  
    switch (profile.personalityType) {
      case 'analytical':
        if (tender.category === TenderCategory.IT_SERVICES || tender.category === TenderCategory.CONSULTING) score += 20;
        if (tender.value > 1000000) score += 10; // Complex, high-value projects
        break;
      case 'driver':
        if (tender.status === TenderStatus.CLOSING_SOON) score += 15; // Urgency appeals to drivers
        if (tender.priority === Priority.HIGH) score += 10;
        break;
      case 'expressive':
        if (tender.category === TenderCategory.CATERING || tender.category === TenderCategory.EDUCATION) score += 15;
        if (tender.socialProofData?.interestedCount > 10) score += 10; // Social validation
        break;
      case 'amiable':
        if (tender.organizationType === 'government') score += 10; // Stability preference
        if (tender.category === TenderCategory.HEALTHCARE || tender.category === TenderCategory.EDUCATION) score += 15;
        break;
    }

    // Risk tolerance matching
    const riskScore = this.calculateTenderRisk(tender);
    if (profile.riskTolerance === 'aggressive' && riskScore > 70) score += 15;
    if (profile.riskTolerance === 'conservative' && riskScore < 30) score += 15;
    if (profile.riskTolerance === 'moderate' && riskScore >= 30 && riskScore <= 70) score += 10;

    // Decision making style
    if (profile.decisionMakingStyle === 'impulsive' && tender.status === TenderStatus.CLOSING_SOON) score += 10;
    if (profile.decisionMakingStyle === 'deliberate' && tender.clarifications.length > 0) score += 10;

    return Math.min(100, Math.max(0, score));
  }

  /**
   * Predict likelihood of user engagement based on behavior patterns
   */
  private calculateBehavioralPrediction(
    tender: Tender,
    profile: PsychologicalProfile,
    psychState: any
  ): number {
    let prediction = 50;

    // Current psychological state influence
    if (psychState.stressLevel > 0.7 && tender.priority === Priority.CRITICAL) {
      prediction -= 20; // High stress + high pressure = avoidance
    }

    if (psychState.engagementLevel > 0.7 && tender.difficultyLevel === 'advanced') {
      prediction += 15; // High engagement + challenge = attraction
    }

    if (psychState.cognitiveLoad > 0.8) {
      prediction -= 10; // High cognitive load = prefer simpler tenders
    }

    // Motivation factors
    if (profile.motivationTriggers.includes('ACHIEVEMENT') && tender.potentialXP > 100) {
      prediction += 15;
    }

    if (profile.motivationTriggers.includes('FINANCIAL') && tender.value > 100000) {
      prediction += 10;
    }

    // Time-based factors
    const hoursUntilDeadline = this.calculateHoursUntilDeadline(tender.closingDate);
    if (hoursUntilDeadline < 24 && profile.workingStyle === 'LAST_MINUTE') {
      prediction += 20;
    }

    return Math.min(100, Math.max(0, prediction));
  }

  /**
   * Calculate emotional resonance of tender with user
   */
  private calculateEmotionalResonance(tender: Tender, profile: PsychologicalProfile): number {
    let resonance = 50;

    // Emotional triggers based on tender content
    if (tender.emotionalTone === 'exciting' && profile.emotionalDrivers.includes('EXCITEMENT')) {
      resonance += 20;
    }

    if (tender.emotionalTone === 'prestigious' && profile.emotionalDrivers.includes('RECOGNITION')) {
      resonance += 15;
    }

    if (tender.emotionalTone === 'stable' && profile.emotionalDrivers.includes('SECURITY')) {
      resonance += 15;
    }

    // Social proof emotional impact
    if (tender.socialProofData.interestedCount > 20 && profile.socialInfluence === 'HIGH') {
      resonance += 10;
    }

    return Math.min(100, Math.max(0, resonance));
  }

  /**
   * Generate behavioral nudges for specific tender and user
   */
  private generateBehavioralNudges(
    tender: Tender,
    profile: PsychologicalProfile,
    psychState: any
  ): BehavioralNudge[] {
    const nudges: BehavioralNudge[] = [];

    // Scarcity nudge for deadline-sensitive users
    if (profile.respondToUrgency && tender.status === TenderStatus.CLOSING_SOON) {
      nudges.push({
        id: `scarcity-${tender.id}`,
        type: EngagementTrigger.SCARCITY,
        message: `Only ${this.calculateHoursUntilDeadline(tender.closingDate)} hours left to submit!`,
        intensity: 'strong',
        timing: 'immediate',
        personalizedFor: profile,
        effectivenessScore: 0.8
      });
    }

    // Social proof nudge for socially influenced users
    if (profile.socialInfluence === 'HIGH' && tender.socialProofData.interestedCount > 5) {
      nudges.push({
        id: `social-${tender.id}`,
        type: EngagementTrigger.SOCIAL_PROOF,
        message: `${tender.socialProofData.interestedCount} other bidders are interested in this opportunity`,
        intensity: 'moderate',
        timing: 'contextual',
        personalizedFor: profile,
        effectivenessScore: 0.7
      });
    }

    // Achievement nudge for achievement-oriented users
    if (profile.motivationFactors.includes('ACHIEVEMENT') && tender.potentialXP > 50) {
      nudges.push({
        id: `achievement-${tender.id}`,
        type: EngagementTrigger.ACHIEVEMENT,
        message: `Complete this bid to earn ${tender.potentialXP} XP and unlock new achievements!`,
        intensity: 'moderate',
        timing: 'delayed',
        personalizedFor: profile,
        effectivenessScore: 0.6
      });
    }

    return nudges;
  }

  /**
   * Apply real-time psychological adaptations to tender list
   */
  private applyPsychologicalAdaptations(
    tenders: Tender[],
    psychState: any
  ): Tender[] {
    return tenders.map(tender => {
      // Adapt based on current stress level
      if (psychState.stressLevel > 0.7) {
        // Reduce cognitive load by simplifying information
        tender.personalizedDescription = this.simplifyDescription(tender.description);
        tender.priority = tender.priority === Priority.CRITICAL ? Priority.HIGH : tender.priority;
      }

      // Adapt based on cognitive load
      if (psychState.cognitiveLoad > 0.8) {
        // Highlight key information only
        tender.personalizedTitle = this.highlightKeyInfo(tender.title);
      }

      // Adapt based on engagement level
      if (psychState.engagementLevel < 0.4) {
        // Add motivational elements
        tender.behavioralNudges = [
          ...tender.behavioralNudges,
          {
            id: `motivation-${tender.id}`,
            type: EngagementTrigger.CURIOSITY,
            message: "This opportunity matches your expertise perfectly!",
            intensity: 'moderate',
            timing: 'immediate',
            personalizedFor: this.userProfile!,
            effectivenessScore: 0.5
          }
        ];
      }

      return tender;
    });
  }

  // Helper methods
  private getFallbackTenders(tenders: Tender[], limit: number): Tender[] {
    return tenders
      .sort((a, b) => new Date(b.publishDate).getTime() - new Date(a.publishDate).getTime())
      .slice(0, limit);
  }

  private calculateTenderRisk(tender: Tender): number {
    let risk = 30; // Base risk
    
    if (tender.value > 5000000) risk += 20;
    if (tender.status === TenderStatus.CLOSING_SOON) risk += 15;
    if (tender.organizationType === 'private') risk += 10;
    if (tender.complianceRequirements.length > 5) risk += 15;
    
    return Math.min(100, risk);
  }

  private calculateHoursUntilDeadline(deadline: string): number {
    const now = new Date();
    const deadlineDate = new Date(deadline);
    return Math.max(0, Math.floor((deadlineDate.getTime() - now.getTime()) / (1000 * 60 * 60)));
  }

  private calculateCognitiveLoad(tender: Tender, psychState: any): number {
    let load = 30;
    
    if (tender.complianceRequirements.length > 3) load += 20;
    if (tender.technicalRequirements.length > 5) load += 15;
    if (tender.value > 2000000) load += 10;
    if (psychState.cognitiveLoad > 0.7) load += 20;
    
    return Math.min(100, load);
  }

  private calculateStressLevel(tender: Tender, psychState: any): number {
    let stress = 20;
    
    if (tender.status === TenderStatus.CLOSING_TODAY) stress += 30;
    if (tender.priority === Priority.CRITICAL) stress += 20;
    if (psychState.stressLevel > 0.6) stress += 25;
    
    return Math.min(100, stress);
  }

  private identifyPsychologicalTriggers(tender: Tender, profile: PsychologicalProfile): EngagementTrigger[] {
    const triggers: EngagementTrigger[] = [];
    
    if (tender.status === TenderStatus.CLOSING_SOON) triggers.push(EngagementTrigger.SCARCITY);
    if (tender.socialProofData.interestedCount > 10) triggers.push(EngagementTrigger.SOCIAL_PROOF);
    if (tender.organizationType === 'government') triggers.push(EngagementTrigger.AUTHORITY);
    if (tender.potentialXP > 50) triggers.push(EngagementTrigger.ACHIEVEMENT);
    
    return triggers;
  }

  private identifyMotivationTriggers(tender: Tender, profile: PsychologicalProfile): EngagementTrigger[] {
    return this.identifyPsychologicalTriggers(tender, profile);
  }

  private generatePersonalizedReasons(tender: Tender, profile: PsychologicalProfile, fitScore: number): string[] {
    const reasons: string[] = [];
    
    if (fitScore > 80) reasons.push("Perfect match for your expertise");
    if (tender.category === profile.preferredCategories?.[0]) reasons.push("In your preferred category");
    if (tender.location === profile.preferredLocation) reasons.push("In your preferred location");
    
    return reasons;
  }

  private generatePersonalizedTitle(tender: Tender, profile: PsychologicalProfile): string {
    // Add psychological hooks to title based on profile
    if (profile.respondToUrgency && tender.status === TenderStatus.CLOSING_SOON) {
      return `🔥 ${tender.title} - Closing Soon!`;
    }
    
    if (profile.motivationFactors.includes('ACHIEVEMENT') && tender.potentialXP > 100) {
      return `⭐ ${tender.title} - High XP Opportunity`;
    }
    
    return tender.title;
  }

  private generatePersonalizedDescription(tender: Tender, profile: PsychologicalProfile): string {
    // Adapt description based on psychological profile
    return tender.description; // Simplified for now
  }

  private generateRecommendationReason(match: TenderMatch, profile: PsychologicalProfile): string {
    if (match.matchScore > 90) return "Exceptional match for your profile";
    if (match.matchScore > 80) return "Strong alignment with your preferences";
    if (match.matchScore > 70) return "Good fit based on your history";
    return "Recommended based on your interests";
  }

  private adaptTenderRecommendations(state: any): void {
    // Invalidate cache when psychological state changes significantly
    if (state.stressLevel > 0.8 || state.cognitiveLoad > 0.8) {
      this.invalidateBehavioralCache();
    }
  }

  private invalidateBehavioralCache(): void {
    this.behavioralCache.clear();
  }

  private simplifyDescription(description: string): string {
    // Simplify description for high stress/cognitive load
    return description.split('.')[0] + '...'; // Show only first sentence
  }

  private highlightKeyInfo(title: string): string {
    // Highlight key information for low cognitive capacity
    return title.toUpperCase();
  }
}

export default BehavioralTenderService;
