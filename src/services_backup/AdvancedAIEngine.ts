/**
 * Advanced AI & Machine Learning Engine
 * Implements predictive analytics, bid success prediction, and advanced behavioral modeling
 */

import { NeuroMarketingEngine, PsychologicalProfile } from './NeuroMarketingEngine';
import BehavioralTenderService from './BehavioralTenderService';
import { <PERSON><PERSON>, Bid, Bid<PERSON>tatus, TenderStatus, Priority } from '../types/tender.types';

interface PredictiveModel {
  id: string;
  name: string;
  type: 'bid_success' | 'tender_match' | 'behavioral_prediction' | 'market_analysis';
  accuracy: number; // 0-1
  lastTrained: string;
  features: string[];
  version: string;
}

interface BidSuccessPrediction {
  bidId: string;
  successProbability: number; // 0-1
  confidenceInterval: [number, number];
  keyFactors: {
    factor: string;
    impact: number; // -1 to 1
    description: string;
  }[];
  recommendations: {
    action: string;
    expectedImprovement: number;
    effort: 'low' | 'medium' | 'high';
    psychologicalImpact: 'positive' | 'negative' | 'neutral';
  }[];
  riskFactors: string[];
  competitorAnalysis: {
    estimatedCompetitors: number;
    yourAdvantage: string[];
    threats: string[];
  };
}

interface TenderMatchPrediction {
  tenderId: string;
  matchScore: number; // 0-100
  winProbability: number; // 0-1
  optimalBidAmount: {
    recommended: number;
    range: [number, number];
    reasoning: string;
  };
  timeToComplete: {
    estimated: number; // hours
    confidence: number;
    factors: string[];
  };
  psychologicalFit: {
    stressLevel: number;
    engagementPotential: number;
    cognitiveLoad: number;
    motivationAlignment: number;
  };
}

interface BehavioralPrediction {
  userId: string;
  predictions: {
    optimalWorkingHours: string[];
    peakPerformanceDays: string[];
    stressTriggers: string[];
    motivationFactors: string[];
    burnoutRisk: number; // 0-1
    engagementTrend: 'increasing' | 'decreasing' | 'stable';
  };
  recommendations: {
    workloadAdjustment: string;
    breakSchedule: string[];
    taskPrioritization: string;
    stressManagement: string[];
  };
  nextWeekForecast: {
    expectedStressLevel: number;
    expectedProductivity: number;
    recommendedActions: string[];
  };
}

interface MarketAnalysis {
  sector: string;
  trends: {
    tenderVolume: number; // percentage change
    averageValue: number; // percentage change
    competitionLevel: number; // 0-1
    successRateChange: number; // percentage change
  };
  opportunities: {
    category: string;
    growth: number;
    difficulty: number;
    psychologicalFit: number;
  }[];
  threats: {
    category: string;
    risk: number;
    mitigation: string[];
  }[];
  predictions: {
    nextQuarter: {
      tenderCount: number;
      averageValue: number;
      competitionLevel: number;
    };
    yearEnd: {
      marketSize: number;
      yourProjectedShare: number;
      confidence: number;
    };
  };
}

class AdvancedAIEngine {
  private static instance: AdvancedAIEngine;
  private neuroEngine: NeuroMarketingEngine;
  private behavioralService: BehavioralTenderService;
  private models: Map<string, PredictiveModel> = new Map();
  private predictionCache: Map<string, any> = new Map();

  private constructor() {
    this.neuroEngine = NeuroMarketingEngine.getInstance();
    this.behavioralService = BehavioralTenderService.getInstance();
    this.initializeModels();
  }

  public static getInstance(): AdvancedAIEngine {
    if (!AdvancedAIEngine.instance) {
      AdvancedAIEngine.instance = new AdvancedAIEngine();
    }
    return AdvancedAIEngine.instance;
  }

  private initializeModels(): void {
    // Initialize AI models
    const models: PredictiveModel[] = [
      {
        id: 'bid-success-v2',
        name: 'Bid Success Predictor',
        type: 'bid_success',
        accuracy: 0.87,
        lastTrained: '2024-01-15T10:00:00Z',
        features: ['bid_amount', 'competitor_count', 'psychological_fit', 'past_performance', 'compliance_score'],
        version: '2.1.0'
      },
      {
        id: 'tender-match-v1',
        name: 'Tender Matching Algorithm',
        type: 'tender_match',
        accuracy: 0.92,
        lastTrained: '2024-01-20T14:30:00Z',
        features: ['category_expertise', 'location_preference', 'value_range', 'psychological_profile', 'availability'],
        version: '1.3.0'
      },
      {
        id: 'behavioral-predictor-v1',
        name: 'Behavioral Pattern Predictor',
        type: 'behavioral_prediction',
        accuracy: 0.84,
        lastTrained: '2024-01-18T09:15:00Z',
        features: ['stress_patterns', 'engagement_history', 'decision_patterns', 'performance_cycles'],
        version: '1.2.0'
      },
      {
        id: 'market-analyzer-v1',
        name: 'Market Trend Analyzer',
        type: 'market_analysis',
        accuracy: 0.79,
        lastTrained: '2024-01-22T16:45:00Z',
        features: ['tender_volume', 'sector_trends', 'economic_indicators', 'seasonal_patterns'],
        version: '1.1.0'
      }
    ];

    models.forEach(model => this.models.set(model.id, model));
  }

  /**
   * Predict bid success probability with detailed analysis
   */
  public async predictBidSuccess(bid: Bid, tender: Tender): Promise<BidSuccessPrediction> {
    const cacheKey = `bid-success-${bid.id}-${tender.id}`;
    
    if (this.predictionCache.has(cacheKey)) {
      return this.predictionCache.get(cacheKey);
    }

    // Get psychological profile for context
    const profile = this.neuroEngine.getPsychologicalProfile();
    
    // Calculate base success probability
    const baseFactors = this.calculateBaseSuccessFactors(bid, tender, profile);
    const psychologicalFactors = this.calculatePsychologicalFactors(bid, tender, profile);
    const competitiveFactors = this.calculateCompetitiveFactors(tender);
    
    // Combine factors with weighted importance
    const successProbability = this.combineSuccessFactors(baseFactors, psychologicalFactors, competitiveFactors);
    
    // Generate key factors analysis
    const keyFactors = this.identifyKeySuccessFactors(bid, tender, profile);
    
    // Generate recommendations
    const recommendations = this.generateSuccessRecommendations(bid, tender, keyFactors);
    
    // Analyze competition
    const competitorAnalysis = this.analyzeCompetition(tender, profile);
    
    // Identify risk factors
    const riskFactors = this.identifyRiskFactors(bid, tender);

    const prediction: BidSuccessPrediction = {
      bidId: bid.id,
      successProbability,
      confidenceInterval: [successProbability - 0.15, successProbability + 0.15],
      keyFactors,
      recommendations,
      riskFactors,
      competitorAnalysis
    };

    this.predictionCache.set(cacheKey, prediction);
    return prediction;
  }

  /**
   * Predict tender match quality and optimal bidding strategy
   */
  public async predictTenderMatch(tender: Tender): Promise<TenderMatchPrediction> {
    const cacheKey = `tender-match-${tender.id}`;
    
    if (this.predictionCache.has(cacheKey)) {
      return this.predictionCache.get(cacheKey);
    }

    const profile = this.neuroEngine.getPsychologicalProfile();
    
    // Calculate match score
    const matchScore = await this.calculateAdvancedMatchScore(tender, profile);
    
    // Predict win probability
    const winProbability = this.calculateWinProbability(tender, profile, matchScore);
    
    // Calculate optimal bid amount
    const optimalBidAmount = this.calculateOptimalBidAmount(tender, profile);
    
    // Estimate time to complete
    const timeToComplete = this.estimateCompletionTime(tender, profile);
    
    // Assess psychological fit
    const psychologicalFit = this.assessPsychologicalFit(tender, profile);

    const prediction: TenderMatchPrediction = {
      tenderId: tender.id,
      matchScore,
      winProbability,
      optimalBidAmount,
      timeToComplete,
      psychologicalFit
    };

    this.predictionCache.set(cacheKey, prediction);
    return prediction;
  }

  /**
   * Predict user behavioral patterns and provide recommendations
   */
  public async predictBehavioralPatterns(userId: string): Promise<BehavioralPrediction> {
    const cacheKey = `behavioral-${userId}`;
    
    if (this.predictionCache.has(cacheKey)) {
      return this.predictionCache.get(cacheKey);
    }

    const profile = this.neuroEngine.getPsychologicalProfile();
    const currentState = this.neuroEngine.getCurrentPsychologicalState();
    
    // Analyze historical patterns
    const historicalData = this.getHistoricalBehavioralData(userId);
    
    // Predict future patterns
    const predictions = this.predictFutureBehavior(historicalData, currentState);
    
    // Generate personalized recommendations
    const recommendations = this.generateBehavioralRecommendations(predictions, profile);
    
    // Forecast next week
    const nextWeekForecast = this.forecastNextWeek(predictions, currentState);

    const prediction: BehavioralPrediction = {
      userId,
      predictions,
      recommendations,
      nextWeekForecast
    };

    this.predictionCache.set(cacheKey, prediction);
    return prediction;
  }

  /**
   * Analyze market trends and opportunities
   */
  public async analyzeMarketTrends(sector: string = 'all'): Promise<MarketAnalysis> {
    const cacheKey = `market-${sector}`;
    
    if (this.predictionCache.has(cacheKey)) {
      return this.predictionCache.get(cacheKey);
    }

    // Analyze current market trends
    const trends = this.analyzeCurrentTrends(sector);
    
    // Identify opportunities
    const opportunities = this.identifyMarketOpportunities(sector);
    
    // Assess threats
    const threats = this.assessMarketThreats(sector);
    
    // Generate predictions
    const predictions = this.generateMarketPredictions(sector, trends);

    const analysis: MarketAnalysis = {
      sector,
      trends,
      opportunities,
      threats,
      predictions
    };

    this.predictionCache.set(cacheKey, analysis);
    return analysis;
  }

  // Private helper methods
  private calculateBaseSuccessFactors(bid: Bid, tender: Tender, profile: any): number {
    let score = 0.5; // Base 50%
    
    // Bid amount competitiveness (estimated)
    const estimatedOptimal = tender.value * 0.85; // Assume 15% margin is optimal
    const bidCompetitiveness = 1 - Math.abs(bid.bidAmount - estimatedOptimal) / estimatedOptimal;
    score += bidCompetitiveness * 0.2;
    
    // Completion percentage
    score += (bid.completionPercentage / 100) * 0.15;
    
    // Past performance (simulated)
    const pastSuccessRate = profile?.successRate || 0.68;
    score += (pastSuccessRate - 0.5) * 0.15;
    
    return Math.max(0, Math.min(1, score));
  }

  private calculatePsychologicalFactors(bid: Bid, tender: Tender, profile: any): number {
    let score = 0.5;
    
    // Stress level impact
    const currentStress = this.neuroEngine.getCurrentPsychologicalState()?.stressLevel || 0.5;
    score -= currentStress * 0.1; // High stress reduces performance
    
    // Engagement level impact
    const engagement = this.neuroEngine.getCurrentPsychologicalState()?.engagementLevel || 0.5;
    score += (engagement - 0.5) * 0.15;
    
    // Psychological fit with tender
    const psychFit = tender.matchData?.psychologicalFit || 70;
    score += (psychFit - 50) / 100 * 0.1;
    
    return Math.max(0, Math.min(1, score));
  }

  private calculateCompetitiveFactors(tender: Tender): number {
    let score = 0.5;
    
    // Competition level
    const competitors = tender.socialProofData?.competitorCount || 5;
    score -= Math.min(competitors / 20, 0.2); // More competitors = lower chance
    
    // Tender urgency (can be advantage or disadvantage)
    const daysLeft = this.getDaysUntilDeadline(tender.closingDate);
    if (daysLeft < 7) {
      score += 0.05; // Less competition for urgent tenders
    }
    
    return Math.max(0, Math.min(1, score));
  }

  private combineSuccessFactors(base: number, psychological: number, competitive: number): number {
    // Weighted combination
    return (base * 0.5) + (psychological * 0.3) + (competitive * 0.2);
  }

  private identifyKeySuccessFactors(bid: Bid, tender: Tender, profile: any): any[] {
    return [
      {
        factor: 'Bid Competitiveness',
        impact: 0.3,
        description: 'Your bid amount is well-positioned against estimated competition'
      },
      {
        factor: 'Psychological Fit',
        impact: 0.25,
        description: 'This tender aligns well with your working style and preferences'
      },
      {
        factor: 'Completion Progress',
        impact: 0.2,
        description: 'Your bid completion percentage demonstrates thoroughness'
      }
    ];
  }

  private generateSuccessRecommendations(bid: Bid, tender: Tender, keyFactors: any[]): any[] {
    return [
      {
        action: 'Optimize bid pricing',
        expectedImprovement: 0.15,
        effort: 'medium',
        psychologicalImpact: 'positive'
      },
      {
        action: 'Complete all compliance requirements',
        expectedImprovement: 0.1,
        effort: 'high',
        psychologicalImpact: 'neutral'
      }
    ];
  }

  private analyzeCompetition(tender: Tender, profile: any): any {
    return {
      estimatedCompetitors: tender.socialProofData?.competitorCount || 5,
      yourAdvantage: ['Strong psychological fit', 'Proven track record'],
      threats: ['Price competition', 'Larger competitors']
    };
  }

  private identifyRiskFactors(bid: Bid, tender: Tender): string[] {
    const risks: string[] = [];
    
    if (bid.completionPercentage < 80) {
      risks.push('Incomplete bid documentation');
    }
    
    const daysLeft = this.getDaysUntilDeadline(tender.closingDate);
    if (daysLeft < 3) {
      risks.push('Very tight deadline for submission');
    }
    
    return risks;
  }

  private calculateAdvancedMatchScore(tender: Tender, profile: any): Promise<number> {
    // Use behavioral service for base calculation
    return Promise.resolve(tender.matchData?.matchScore || 75);
  }

  private calculateWinProbability(tender: Tender, profile: any, matchScore: number): number {
    // Convert match score to win probability with additional factors
    let probability = matchScore / 100 * 0.7; // Base from match score
    
    // Adjust for competition
    const competitors = tender.socialProofData?.competitorCount || 5;
    probability *= Math.max(0.3, 1 - (competitors * 0.05));
    
    return Math.max(0.1, Math.min(0.9, probability));
  }

  private calculateOptimalBidAmount(tender: Tender, profile: any): any {
    const baseAmount = tender.value * 0.85; // 15% margin
    const range: [number, number] = [baseAmount * 0.9, baseAmount * 1.1];
    
    return {
      recommended: baseAmount,
      range,
      reasoning: 'Based on market analysis and competition level'
    };
  }

  private estimateCompletionTime(tender: Tender, profile: any): any {
    const baseHours = tender.estimatedEffort || 120;
    
    return {
      estimated: baseHours,
      confidence: 0.8,
      factors: ['Project complexity', 'Your experience level', 'Team availability']
    };
  }

  private assessPsychologicalFit(tender: Tender, profile: any): any {
    const currentState = this.neuroEngine.getCurrentPsychologicalState();
    
    return {
      stressLevel: currentState?.stressLevel || 0.5,
      engagementPotential: 0.8,
      cognitiveLoad: currentState?.cognitiveLoad || 0.6,
      motivationAlignment: 0.75
    };
  }

  private getHistoricalBehavioralData(userId: string): any {
    // Mock historical data - in real implementation, fetch from database
    return {
      stressPatterns: [0.3, 0.4, 0.6, 0.5, 0.7, 0.4, 0.2],
      engagementPatterns: [0.8, 0.7, 0.6, 0.8, 0.5, 0.9, 0.8],
      productivityPatterns: [0.7, 0.8, 0.6, 0.9, 0.5, 0.8, 0.9]
    };
  }

  private predictFutureBehavior(historicalData: any, currentState: any): any {
    return {
      optimalWorkingHours: ['09:00-11:00', '14:00-16:00'],
      peakPerformanceDays: ['Tuesday', 'Thursday'],
      stressTriggers: ['Complex compliance', 'Tight deadlines'],
      motivationFactors: ['Achievement', 'Financial rewards'],
      burnoutRisk: 0.3,
      engagementTrend: 'stable'
    };
  }

  private generateBehavioralRecommendations(predictions: any, profile: any): any {
    return {
      workloadAdjustment: 'Reduce complex tasks on Mondays and Fridays',
      breakSchedule: ['10:30', '15:30'],
      taskPrioritization: 'Focus on high-value activities during peak hours',
      stressManagement: ['Take breaks before stress peaks', 'Use simplified interfaces']
    };
  }

  private forecastNextWeek(predictions: any, currentState: any): any {
    return {
      expectedStressLevel: 0.4,
      expectedProductivity: 0.8,
      recommendedActions: ['Schedule important meetings on Tuesday/Thursday', 'Plan lighter workload on Friday']
    };
  }

  private analyzeCurrentTrends(sector: string): any {
    return {
      tenderVolume: 12, // 12% increase
      averageValue: 8,  // 8% increase
      competitionLevel: 0.7, // 70% competition level
      successRateChange: 5 // 5% improvement
    };
  }

  private identifyMarketOpportunities(sector: string): any[] {
    return [
      {
        category: 'IT Services',
        growth: 25,
        difficulty: 0.6,
        psychologicalFit: 0.85
      },
      {
        category: 'Construction',
        growth: 15,
        difficulty: 0.8,
        psychologicalFit: 0.7
      }
    ];
  }

  private assessMarketThreats(sector: string): any[] {
    return [
      {
        category: 'Price Competition',
        risk: 0.7,
        mitigation: ['Focus on value proposition', 'Highlight unique capabilities']
      }
    ];
  }

  private generateMarketPredictions(sector: string, trends: any): any {
    return {
      nextQuarter: {
        tenderCount: 150,
        averageValue: 2500000,
        competitionLevel: 0.75
      },
      yearEnd: {
        marketSize: 50000000,
        yourProjectedShare: 0.08,
        confidence: 0.82
      }
    };
  }

  private getDaysUntilDeadline(deadline: string): number {
    const now = new Date();
    const deadlineDate = new Date(deadline);
    return Math.ceil((deadlineDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
  }

  // Public utility methods
  public getModelAccuracy(modelId: string): number {
    return this.models.get(modelId)?.accuracy || 0;
  }

  public clearPredictionCache(): void {
    this.predictionCache.clear();
  }

  public getAvailableModels(): PredictiveModel[] {
    return Array.from(this.models.values());
  }
}

export default AdvancedAIEngine;
