/**
 * Automated Compliance Engine
 * AI-powered compliance checking and document validation for South African tenders
 * Ensures all bids meet legal and regulatory requirements automatically
 */

import { BidClass } from './BidBeezAIEngine';
import { ComplianceRequirement, DocumentStatus } from '../types/tender.types';

export interface ComplianceRule {
  id: string;
  name: string;
  description: string;
  category: 'legal' | 'technical' | 'financial' | 'administrative';
  jurisdiction: 'national' | 'provincial' | 'municipal';
  mandatory: boolean;
  applicableTo: BidClass[];
  validationCriteria: ValidationCriteria;
  penalties: CompliancePenalty[];
}

export interface ValidationCriteria {
  documentRequired: boolean;
  documentTypes: string[];
  contentValidation: ContentValidation[];
  expiryCheck: boolean;
  valueThresholds?: ValueThreshold[];
}

export interface ContentValidation {
  field: string;
  type: 'text' | 'number' | 'date' | 'boolean' | 'certificate';
  required: boolean;
  pattern?: string; // regex pattern
  minValue?: number;
  maxValue?: number;
  validValues?: string[];
}

export interface ValueThreshold {
  minValue: number;
  maxValue: number;
  additionalRequirements: string[];
}

export interface CompliancePenalty {
  severity: 'minor' | 'major' | 'critical';
  consequence: 'warning' | 'disqualification' | 'penalty_points';
  description: string;
}

export interface ComplianceCheckResult {
  ruleId: string;
  ruleName: string;
  status: 'compliant' | 'non_compliant' | 'partial' | 'not_applicable';
  score: number; // 0-100
  issues: ComplianceIssue[];
  recommendations: string[];
  evidence: ComplianceEvidence[];
}

export interface ComplianceIssue {
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  requirement: string;
  solution: string;
  autoFixable: boolean;
}

export interface ComplianceEvidence {
  type: 'document' | 'certificate' | 'declaration' | 'calculation';
  description: string;
  source: string;
  verified: boolean;
  expiryDate?: string;
}

export interface ComplianceReport {
  overallScore: number;
  overallStatus: 'compliant' | 'non_compliant' | 'partial';
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  checkResults: ComplianceCheckResult[];
  summary: ComplianceSummary;
  actionItems: ComplianceAction[];
  estimatedFixTime: number; // hours
}

export interface ComplianceSummary {
  totalRules: number;
  compliantRules: number;
  nonCompliantRules: number;
  partialRules: number;
  criticalIssues: number;
  majorIssues: number;
  minorIssues: number;
}

export interface ComplianceAction {
  priority: 'immediate' | 'high' | 'medium' | 'low';
  action: string;
  description: string;
  estimatedTime: number; // hours
  autoFixable: boolean;
  dependencies: string[];
}

class AutomatedComplianceEngine {
  private static instance: AutomatedComplianceEngine;
  private complianceRules: Map<string, ComplianceRule> = new Map();
  private jurisdictionRules: Map<string, ComplianceRule[]> = new Map();

  private constructor() {
    this.initializeComplianceRules();
  }

  public static getInstance(): AutomatedComplianceEngine {
    if (!AutomatedComplianceEngine.instance) {
      AutomatedComplianceEngine.instance = new AutomatedComplianceEngine();
    }
    return AutomatedComplianceEngine.instance;
  }

  /**
   * Main compliance checking method
   */
  public async checkCompliance(
    bidClass: BidClass,
    tenderValue: number,
    jurisdiction: string,
    documents: any[],
    bidData: any
  ): Promise<ComplianceReport> {
    console.log(`🔍 Compliance Engine: Checking ${bidClass} bid for ${jurisdiction}`);

    // Get applicable rules
    const applicableRules = this.getApplicableRules(bidClass, tenderValue, jurisdiction);
    
    // Check each rule
    const checkResults: ComplianceCheckResult[] = [];
    for (const rule of applicableRules) {
      const result = await this.checkRule(rule, documents, bidData);
      checkResults.push(result);
    }

    // Generate report
    const report = this.generateComplianceReport(checkResults);
    
    console.log(`✅ Compliance Check Complete: ${report.overallScore}% compliant`);
    return report;
  }

  /**
   * Get rules applicable to specific bid
   */
  private getApplicableRules(
    bidClass: BidClass,
    tenderValue: number,
    jurisdiction: string
  ): ComplianceRule[] {
    const rules: ComplianceRule[] = [];

    // Get all rules and filter by applicability
    for (const rule of this.complianceRules.values()) {
      if (this.isRuleApplicable(rule, bidClass, tenderValue, jurisdiction)) {
        rules.push(rule);
      }
    }

    return rules.sort((a, b) => {
      // Sort by mandatory first, then by category
      if (a.mandatory && !b.mandatory) return -1;
      if (!a.mandatory && b.mandatory) return 1;
      return a.category.localeCompare(b.category);
    });
  }

  /**
   * Check if rule applies to current bid
   */
  private isRuleApplicable(
    rule: ComplianceRule,
    bidClass: BidClass,
    tenderValue: number,
    jurisdiction: string
  ): boolean {
    // Check bid class applicability
    if (rule.applicableTo.length > 0 && !rule.applicableTo.includes(bidClass)) {
      return false;
    }

    // Check jurisdiction
    if (rule.jurisdiction !== 'national' && !jurisdiction.toLowerCase().includes(rule.jurisdiction)) {
      return false;
    }

    // Check value thresholds
    if (rule.validationCriteria.valueThresholds) {
      const applicable = rule.validationCriteria.valueThresholds.some(threshold =>
        tenderValue >= threshold.minValue && tenderValue <= threshold.maxValue
      );
      if (!applicable) return false;
    }

    return true;
  }

  /**
   * Check individual compliance rule
   */
  private async checkRule(
    rule: ComplianceRule,
    documents: any[],
    bidData: any
  ): Promise<ComplianceCheckResult> {
    const issues: ComplianceIssue[] = [];
    const recommendations: string[] = [];
    const evidence: ComplianceEvidence[] = [];
    let score = 100;

    // Check document requirements
    if (rule.validationCriteria.documentRequired) {
      const docCheck = this.checkDocumentRequirements(rule, documents);
      if (!docCheck.passed) {
        issues.push({
          severity: rule.mandatory ? 'critical' : 'medium',
          description: `Missing required document: ${rule.validationCriteria.documentTypes.join(', ')}`,
          requirement: rule.name,
          solution: `Upload the required ${rule.validationCriteria.documentTypes.join(' or ')} document`,
          autoFixable: false
        });
        score -= rule.mandatory ? 50 : 20;
      } else {
        evidence.push({
          type: 'document',
          description: `Required document provided: ${docCheck.documentName}`,
          source: docCheck.documentName || 'uploaded document',
          verified: true
        });
      }
    }

    // Check content validation
    for (const validation of rule.validationCriteria.contentValidation) {
      const contentCheck = this.validateContent(validation, bidData);
      if (!contentCheck.passed) {
        issues.push({
          severity: validation.required ? 'high' : 'medium',
          description: contentCheck.error || `Invalid ${validation.field}`,
          requirement: rule.name,
          solution: contentCheck.solution || `Provide valid ${validation.field}`,
          autoFixable: contentCheck.autoFixable || false
        });
        score -= validation.required ? 30 : 10;
      }
    }

    // Check expiry dates
    if (rule.validationCriteria.expiryCheck) {
      const expiryCheck = this.checkExpiryDates(documents);
      if (!expiryCheck.passed) {
        issues.push({
          severity: 'high',
          description: 'Document has expired or expires soon',
          requirement: rule.name,
          solution: 'Renew expired documents',
          autoFixable: false
        });
        score -= 25;
      }
    }

    // Generate recommendations
    if (issues.length > 0) {
      recommendations.push(`Address ${issues.length} compliance issue(s) for ${rule.name}`);
      if (issues.some(i => i.autoFixable)) {
        recommendations.push('Some issues can be automatically fixed');
      }
    }

    // Determine status
    let status: ComplianceCheckResult['status'];
    if (score >= 95) status = 'compliant';
    else if (score >= 70) status = 'partial';
    else if (score > 0) status = 'non_compliant';
    else status = 'not_applicable';

    return {
      ruleId: rule.id,
      ruleName: rule.name,
      status,
      score: Math.max(0, score),
      issues,
      recommendations,
      evidence
    };
  }

  /**
   * Check document requirements
   */
  private checkDocumentRequirements(
    rule: ComplianceRule,
    documents: any[]
  ): { passed: boolean; documentName?: string } {
    const requiredTypes = rule.validationCriteria.documentTypes;
    
    for (const docType of requiredTypes) {
      const found = documents.find(doc => 
        doc.name.toLowerCase().includes(docType.toLowerCase()) ||
        doc.type === docType
      );
      if (found) {
        return { passed: true, documentName: found.name };
      }
    }

    return { passed: false };
  }

  /**
   * Validate content against criteria
   */
  private validateContent(
    validation: ContentValidation,
    bidData: any
  ): { passed: boolean; error?: string; solution?: string; autoFixable?: boolean } {
    const value = bidData[validation.field];

    if (validation.required && (value === undefined || value === null || value === '')) {
      return {
        passed: false,
        error: `${validation.field} is required`,
        solution: `Provide a value for ${validation.field}`,
        autoFixable: false
      };
    }

    if (value !== undefined && value !== null) {
      // Type validation
      switch (validation.type) {
        case 'number':
          if (isNaN(Number(value))) {
            return {
              passed: false,
              error: `${validation.field} must be a number`,
              solution: `Enter a valid number for ${validation.field}`,
              autoFixable: false
            };
          }
          
          const numValue = Number(value);
          if (validation.minValue !== undefined && numValue < validation.minValue) {
            return {
              passed: false,
              error: `${validation.field} must be at least ${validation.minValue}`,
              solution: `Increase ${validation.field} to meet minimum requirement`,
              autoFixable: true
            };
          }
          
          if (validation.maxValue !== undefined && numValue > validation.maxValue) {
            return {
              passed: false,
              error: `${validation.field} must not exceed ${validation.maxValue}`,
              solution: `Reduce ${validation.field} to meet maximum requirement`,
              autoFixable: true
            };
          }
          break;

        case 'text':
          if (validation.pattern) {
            const regex = new RegExp(validation.pattern);
            if (!regex.test(value)) {
              return {
                passed: false,
                error: `${validation.field} format is invalid`,
                solution: `Ensure ${validation.field} matches required format`,
                autoFixable: false
              };
            }
          }
          break;

        case 'date':
          const date = new Date(value);
          if (isNaN(date.getTime())) {
            return {
              passed: false,
              error: `${validation.field} is not a valid date`,
              solution: `Provide a valid date for ${validation.field}`,
              autoFixable: false
            };
          }
          break;
      }

      // Valid values check
      if (validation.validValues && !validation.validValues.includes(value)) {
        return {
          passed: false,
          error: `${validation.field} must be one of: ${validation.validValues.join(', ')}`,
          solution: `Select a valid value for ${validation.field}`,
          autoFixable: true
        };
      }
    }

    return { passed: true };
  }

  /**
   * Check document expiry dates
   */
  private checkExpiryDates(documents: any[]): { passed: boolean } {
    const now = new Date();
    const warningPeriod = 30 * 24 * 60 * 60 * 1000; // 30 days

    for (const doc of documents) {
      if (doc.expiryDate) {
        const expiryDate = new Date(doc.expiryDate);
        if (expiryDate < now || (expiryDate.getTime() - now.getTime()) < warningPeriod) {
          return { passed: false };
        }
      }
    }

    return { passed: true };
  }

  /**
   * Generate compliance report
   */
  private generateComplianceReport(checkResults: ComplianceCheckResult[]): ComplianceReport {
    const summary: ComplianceSummary = {
      totalRules: checkResults.length,
      compliantRules: checkResults.filter(r => r.status === 'compliant').length,
      nonCompliantRules: checkResults.filter(r => r.status === 'non_compliant').length,
      partialRules: checkResults.filter(r => r.status === 'partial').length,
      criticalIssues: 0,
      majorIssues: 0,
      minorIssues: 0
    };

    // Count issues by severity
    checkResults.forEach(result => {
      result.issues.forEach(issue => {
        switch (issue.severity) {
          case 'critical': summary.criticalIssues++; break;
          case 'high': summary.majorIssues++; break;
          case 'medium':
          case 'low': summary.minorIssues++; break;
        }
      });
    });

    // Calculate overall score
    const totalScore = checkResults.reduce((sum, result) => sum + result.score, 0);
    const overallScore = checkResults.length > 0 ? Math.round(totalScore / checkResults.length) : 0;

    // Determine overall status
    let overallStatus: ComplianceReport['overallStatus'];
    if (overallScore >= 90) overallStatus = 'compliant';
    else if (overallScore >= 70) overallStatus = 'partial';
    else overallStatus = 'non_compliant';

    // Determine risk level
    let riskLevel: ComplianceReport['riskLevel'];
    if (summary.criticalIssues > 0) riskLevel = 'critical';
    else if (summary.majorIssues > 3) riskLevel = 'high';
    else if (summary.majorIssues > 0 || summary.minorIssues > 5) riskLevel = 'medium';
    else riskLevel = 'low';

    // Generate action items
    const actionItems: ComplianceAction[] = [];
    checkResults.forEach(result => {
      result.issues.forEach(issue => {
        actionItems.push({
          priority: issue.severity === 'critical' ? 'immediate' : 
                   issue.severity === 'high' ? 'high' : 
                   issue.severity === 'medium' ? 'medium' : 'low',
          action: issue.solution,
          description: issue.description,
          estimatedTime: issue.autoFixable ? 0.5 : 2,
          autoFixable: issue.autoFixable,
          dependencies: []
        });
      });
    });

    // Calculate estimated fix time
    const estimatedFixTime = actionItems.reduce((sum, action) => sum + action.estimatedTime, 0);

    return {
      overallScore,
      overallStatus,
      riskLevel,
      checkResults,
      summary,
      actionItems,
      estimatedFixTime
    };
  }

  /**
   * Initialize compliance rules for South African tenders
   */
  private initializeComplianceRules(): void {
    const rules: ComplianceRule[] = [
      {
        id: 'SA-TAX-001',
        name: 'Tax Compliance Certificate',
        description: 'Valid tax clearance certificate from SARS',
        category: 'legal',
        jurisdiction: 'national',
        mandatory: true,
        applicableTo: Object.values(BidClass),
        validationCriteria: {
          documentRequired: true,
          documentTypes: ['tax_clearance', 'tax_certificate'],
          contentValidation: [],
          expiryCheck: true
        },
        penalties: [
          {
            severity: 'critical',
            consequence: 'disqualification',
            description: 'Bid will be disqualified without valid tax clearance'
          }
        ]
      },
      {
        id: 'SA-BBBEE-001',
        name: 'B-BBEE Certificate',
        description: 'Valid B-BBEE certificate or sworn affidavit',
        category: 'legal',
        jurisdiction: 'national',
        mandatory: true,
        applicableTo: Object.values(BidClass),
        validationCriteria: {
          documentRequired: true,
          documentTypes: ['bbbee_certificate', 'sworn_affidavit'],
          contentValidation: [
            {
              field: 'bbbeeLevel',
              type: 'number',
              required: true,
              minValue: 1,
              maxValue: 8
            }
          ],
          expiryCheck: true
        },
        penalties: [
          {
            severity: 'major',
            consequence: 'penalty_points',
            description: 'Points deduction for invalid B-BBEE status'
          }
        ]
      },
      {
        id: 'SA-CIPC-001',
        name: 'Company Registration',
        description: 'Valid company registration certificate from CIPC',
        category: 'administrative',
        jurisdiction: 'national',
        mandatory: true,
        applicableTo: Object.values(BidClass),
        validationCriteria: {
          documentRequired: true,
          documentTypes: ['cipc_certificate', 'company_registration'],
          contentValidation: [
            {
              field: 'registrationNumber',
              type: 'text',
              required: true,
              pattern: '^[0-9]{4}/[0-9]{6}/[0-9]{2}$'
            }
          ],
          expiryCheck: false
        },
        penalties: [
          {
            severity: 'critical',
            consequence: 'disqualification',
            description: 'Invalid company registration leads to disqualification'
          }
        ]
      }
    ];

    // Store rules
    rules.forEach(rule => {
      this.complianceRules.set(rule.id, rule);
    });

    console.log(`✅ Initialized ${rules.length} compliance rules`);
  }

  /**
   * Get all compliance rules
   */
  public getComplianceRules(): ComplianceRule[] {
    return Array.from(this.complianceRules.values());
  }

  /**
   * Get rule by ID
   */
  public getRule(ruleId: string): ComplianceRule | undefined {
    return this.complianceRules.get(ruleId);
  }

  /**
   * Auto-fix compliance issues where possible
   */
  public async autoFixIssues(
    report: ComplianceReport,
    bidData: any
  ): Promise<{ fixed: number; remaining: number; updatedBidData: any }> {
    let fixed = 0;
    const updatedBidData = { ...bidData };

    for (const result of report.checkResults) {
      for (const issue of result.issues) {
        if (issue.autoFixable) {
          // Implement auto-fix logic based on issue type
          // This is a simplified example
          fixed++;
        }
      }
    }

    return {
      fixed,
      remaining: report.actionItems.length - fixed,
      updatedBidData
    };
  }
}

export default AutomatedComplianceEngine;
