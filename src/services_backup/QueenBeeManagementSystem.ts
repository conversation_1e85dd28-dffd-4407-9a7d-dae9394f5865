/**
 * Queen Bee Management System
 * Hierarchical bee management with Queen Bee coordination and supervision
 * The Queen Bee oversees worker bees, assigns tasks, and ensures quality delivery
 */

export enum BeeType {
  QUEEN_BEE = 'queen_bee',
  SENIOR_BEE = 'senior_bee',
  WORKER_BEE = 'worker_bee',
  SPECIALIST_BEE = 'specialist_bee',
  TRAINEE_BEE = 'trainee_bee'
}

export enum BeeSpecialty {
  DOCUMENT_PROCESSING = 'document_processing',
  SITE_VISITS = 'site_visits',
  BRIEFING_ATTENDANCE = 'briefing_attendance',
  COMPLIANCE_CHECKING = 'compliance_checking',
  TECHNICAL_EVALUATION = 'technical_evaluation',
  LEGAL_REVIEW = 'legal_review',
  FINANCIAL_ANALYSIS = 'financial_analysis',
  PROJECT_COORDINATION = 'project_coordination'
}

export interface QueenBee {
  id: string;
  beeId: string;
  fullName: string;
  email: string;
  phoneNumber: string;
  
  // Queen Bee Specific Properties
  beeType: BeeType.QUEEN_BEE;
  territory: GeographicTerritory;
  managedBees: string[]; // Array of worker bee IDs
  specialties: BeeSpecialty[];
  
  // Management Capabilities
  maxWorkerBees: number;
  currentWorkload: number;
  managementRating: number; // 0-5
  leadershipSkills: string[];
  
  // Performance Metrics
  teamProductivity: number; // Average productivity of managed bees
  taskCompletionRate: number; // Percentage of tasks completed successfully
  qualityScore: number; // Quality of work delivered by team
  clientSatisfactionScore: number;
  
  // Availability and Status
  isActive: boolean;
  currentLocation: BeeLocation;
  workingHours: WorkingHours;
  emergencyContact: string;
  
  // Hierarchy and Reporting
  reportsTo?: string; // Regional Queen Bee or Admin
  subordinates: BeeHierarchy[];
  
  // Timestamps
  joinedDate: string;
  lastActiveDate: string;
  lastPerformanceReview: string;
}

export interface WorkerBee {
  id: string;
  beeId: string;
  fullName: string;
  email: string;
  phoneNumber: string;
  
  // Worker Bee Properties
  beeType: BeeType;
  queenBeeId: string; // Assigned Queen Bee
  specialties: BeeSpecialty[];
  skillLevel: 'junior' | 'intermediate' | 'senior' | 'expert';
  
  // Performance Metrics
  rating: number; // 0-5
  tasksCompleted: number;
  successRate: number;
  averageTaskTime: number; // minutes
  clientFeedbackScore: number;
  
  // Current Status
  isActive: boolean;
  currentTask?: BeeTask;
  currentLocation: BeeLocation;
  availability: BeeAvailability;
  
  // Professional Development
  trainingCompleted: string[];
  certifications: string[];
  mentorshipStatus: 'mentor' | 'mentee' | 'none';
  
  // Timestamps
  joinedDate: string;
  lastActiveDate: string;
  lastTrainingDate: string;
}

export interface BeeTask {
  id: string;
  tenderId: string;
  clientId: string;
  
  // Task Details
  title: string;
  description: string;
  taskType: BeeTaskType;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  complexity: 'simple' | 'moderate' | 'complex' | 'expert';
  
  // Assignment
  assignedQueenBeeId: string;
  assignedWorkerBeeId?: string;
  assignedDate: string;
  estimatedDuration: number; // minutes
  deadline: string;
  
  // Location and Requirements
  location: BeeLocation;
  requirements: TaskRequirement[];
  documentsRequired: string[];
  skillsRequired: BeeSpecialty[];
  
  // Status and Progress
  status: BeeTaskStatus;
  progress: number; // 0-100
  startedDate?: string;
  completedDate?: string;
  
  // Quality Control
  qualityCheckRequired: boolean;
  qualityCheckBy?: string; // Queen Bee ID
  qualityScore?: number;
  clientApproval?: boolean;
  
  // Communication
  updates: TaskUpdate[];
  issues: TaskIssue[];
  clientFeedback?: string;
  
  // Financial
  payment: number;
  currency: string;
  paymentStatus: 'pending' | 'approved' | 'paid';
}

export enum BeeTaskType {
  DOCUMENT_SUBMISSION = 'document_submission',
  TENDER_BRIEFING = 'tender_briefing',
  SITE_VISIT = 'site_visit',
  COMPLIANCE_CHECK = 'compliance_check',
  TECHNICAL_EVALUATION = 'technical_evaluation',
  DOCUMENT_COLLECTION = 'document_collection',
  CLIENT_MEETING = 'client_meeting',
  VERIFICATION = 'verification',
  RESEARCH = 'research',
  COORDINATION = 'coordination'
}

export enum BeeTaskStatus {
  CREATED = 'created',
  ASSIGNED = 'assigned',
  IN_PROGRESS = 'in_progress',
  QUALITY_CHECK = 'quality_check',
  REVISION_REQUIRED = 'revision_required',
  COMPLETED = 'completed',
  APPROVED = 'approved',
  CANCELLED = 'cancelled'
}

export interface GeographicTerritory {
  name: string;
  boundaries: {
    north: number;
    south: number;
    east: number;
    west: number;
  };
  cities: string[];
  provinces: string[];
  coverage: 'urban' | 'rural' | 'mixed';
}

export interface BeeLocation {
  latitude: number;
  longitude: number;
  address: string;
  city: string;
  province: string;
  lastUpdated: string;
}

export interface WorkingHours {
  monday: TimeSlot;
  tuesday: TimeSlot;
  wednesday: TimeSlot;
  thursday: TimeSlot;
  friday: TimeSlot;
  saturday?: TimeSlot;
  sunday?: TimeSlot;
  timezone: string;
}

export interface TimeSlot {
  start: string; // HH:MM
  end: string; // HH:MM
  available: boolean;
}

export interface BeeAvailability {
  status: 'available' | 'busy' | 'offline' | 'on_break';
  availableUntil?: string;
  nextAvailable?: string;
  maxConcurrentTasks: number;
  currentTaskCount: number;
}

export interface BeeHierarchy {
  beeId: string;
  beeType: BeeType;
  reportingLevel: number;
  managementSpan: number; // Number of bees they manage
}

export interface TaskRequirement {
  id: string;
  description: string;
  mandatory: boolean;
  skillRequired?: BeeSpecialty;
  estimatedTime: number; // minutes
}

export interface TaskUpdate {
  id: string;
  timestamp: string;
  updateBy: string; // Bee ID
  updateType: 'progress' | 'issue' | 'completion' | 'quality_check';
  message: string;
  attachments?: string[];
}

export interface TaskIssue {
  id: string;
  timestamp: string;
  reportedBy: string; // Bee ID
  issueType: 'technical' | 'access' | 'client' | 'documentation' | 'other';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  resolution?: string;
  resolvedBy?: string;
  resolvedDate?: string;
}

class QueenBeeManagementSystem {
  private static instance: QueenBeeManagementSystem;
  private queenBees: Map<string, QueenBee> = new Map();
  private workerBees: Map<string, WorkerBee> = new Map();
  private activeTasks: Map<string, BeeTask> = new Map();

  private constructor() {
    this.initializeQueenBees();
    this.initializeWorkerBees();
  }

  public static getInstance(): QueenBeeManagementSystem {
    if (!QueenBeeManagementSystem.instance) {
      QueenBeeManagementSystem.instance = new QueenBeeManagementSystem();
    }
    return QueenBeeManagementSystem.instance;
  }

  /**
   * Assign task to appropriate Queen Bee based on location and specialty
   */
  public async assignTaskToQueenBee(task: Partial<BeeTask>): Promise<BeeTask> {
    const suitableQueenBee = this.findBestQueenBeeForTask(task);
    
    if (!suitableQueenBee) {
      throw new Error('No suitable Queen Bee available for this task');
    }

    const fullTask: BeeTask = {
      id: `TASK-${Date.now()}`,
      tenderId: task.tenderId!,
      clientId: task.clientId!,
      title: task.title!,
      description: task.description!,
      taskType: task.taskType!,
      priority: task.priority || 'medium',
      complexity: task.complexity || 'moderate',
      assignedQueenBeeId: suitableQueenBee.id,
      assignedDate: new Date().toISOString(),
      estimatedDuration: task.estimatedDuration || 120,
      deadline: task.deadline!,
      location: task.location!,
      requirements: task.requirements || [],
      documentsRequired: task.documentsRequired || [],
      skillsRequired: task.skillsRequired || [],
      status: BeeTaskStatus.ASSIGNED,
      progress: 0,
      qualityCheckRequired: true,
      updates: [],
      issues: [],
      payment: task.payment || 500,
      currency: 'ZAR',
      paymentStatus: 'pending'
    };

    this.activeTasks.set(fullTask.id, fullTask);
    
    // Notify Queen Bee
    await this.notifyQueenBee(suitableQueenBee.id, fullTask);
    
    return fullTask;
  }

  /**
   * Queen Bee assigns task to worker bee
   */
  public async queenBeeAssignToWorker(
    queenBeeId: string, 
    taskId: string, 
    workerBeeId: string
  ): Promise<boolean> {
    const task = this.activeTasks.get(taskId);
    const queenBee = this.queenBees.get(queenBeeId);
    const workerBee = this.workerBees.get(workerBeeId);

    if (!task || !queenBee || !workerBee) {
      return false;
    }

    // Verify Queen Bee has authority over worker bee
    if (!queenBee.managedBees.includes(workerBeeId)) {
      throw new Error('Queen Bee does not have authority over this worker bee');
    }

    // Check worker bee availability and skills
    if (!this.isWorkerBeeAvailable(workerBee, task)) {
      throw new Error('Worker bee is not available or lacks required skills');
    }

    // Assign task
    task.assignedWorkerBeeId = workerBeeId;
    task.status = BeeTaskStatus.IN_PROGRESS;
    task.startedDate = new Date().toISOString();

    // Update worker bee status
    workerBee.currentTask = task;
    workerBee.availability.status = 'busy';
    workerBee.availability.currentTaskCount++;

    // Add assignment update
    task.updates.push({
      id: `UPDATE-${Date.now()}`,
      timestamp: new Date().toISOString(),
      updateBy: queenBeeId,
      updateType: 'progress',
      message: `Task assigned to ${workerBee.fullName}`,
      attachments: []
    });

    // Notify worker bee
    await this.notifyWorkerBee(workerBeeId, task);

    return true;
  }

  /**
   * Queen Bee performs quality check
   */
  public async queenBeeQualityCheck(
    queenBeeId: string,
    taskId: string,
    qualityScore: number,
    feedback: string,
    approved: boolean
  ): Promise<boolean> {
    const task = this.activeTasks.get(taskId);
    const queenBee = this.queenBees.get(queenBeeId);

    if (!task || !queenBee || task.assignedQueenBeeId !== queenBeeId) {
      return false;
    }

    task.qualityCheckBy = queenBeeId;
    task.qualityScore = qualityScore;
    task.clientApproval = approved;

    if (approved) {
      task.status = BeeTaskStatus.COMPLETED;
      task.completedDate = new Date().toISOString();
      task.progress = 100;

      // Update worker bee metrics
      if (task.assignedWorkerBeeId) {
        const workerBee = this.workerBees.get(task.assignedWorkerBeeId);
        if (workerBee) {
          workerBee.tasksCompleted++;
          workerBee.currentTask = undefined;
          workerBee.availability.status = 'available';
          workerBee.availability.currentTaskCount--;
          
          // Update success rate
          const totalTasks = workerBee.tasksCompleted;
          const successfulTasks = Math.round(totalTasks * workerBee.successRate / 100) + 1;
          workerBee.successRate = (successfulTasks / totalTasks) * 100;
        }
      }
    } else {
      task.status = BeeTaskStatus.REVISION_REQUIRED;
    }

    // Add quality check update
    task.updates.push({
      id: `UPDATE-${Date.now()}`,
      timestamp: new Date().toISOString(),
      updateBy: queenBeeId,
      updateType: 'quality_check',
      message: `Quality check completed. Score: ${qualityScore}/5. ${approved ? 'Approved' : 'Revision required'}: ${feedback}`,
      attachments: []
    });

    return true;
  }

  /**
   * Get Queen Bee dashboard data
   */
  public getQueenBeeDashboard(queenBeeId: string): any {
    const queenBee = this.queenBees.get(queenBeeId);
    if (!queenBee) return null;

    const managedWorkerBees = queenBee.managedBees.map(id => this.workerBees.get(id)).filter(Boolean);
    const activeTasks = Array.from(this.activeTasks.values()).filter(
      task => task.assignedQueenBeeId === queenBeeId
    );

    return {
      queenBee,
      managedWorkerBees,
      activeTasks,
      metrics: {
        totalWorkerBees: managedWorkerBees.length,
        activeWorkerBees: managedWorkerBees.filter(bee => bee?.isActive).length,
        activeTasks: activeTasks.filter(task => task.status === BeeTaskStatus.IN_PROGRESS).length,
        pendingQualityChecks: activeTasks.filter(task => task.status === BeeTaskStatus.QUALITY_CHECK).length,
        completedToday: activeTasks.filter(task => 
          task.status === BeeTaskStatus.COMPLETED && 
          task.completedDate && 
          new Date(task.completedDate).toDateString() === new Date().toDateString()
        ).length,
        averageQualityScore: this.calculateAverageQualityScore(activeTasks),
        teamProductivity: this.calculateTeamProductivity(managedWorkerBees)
      }
    };
  }

  /**
   * Find best Queen Bee for task based on location, specialty, and workload
   */
  private findBestQueenBeeForTask(task: Partial<BeeTask>): QueenBee | null {
    const availableQueenBees = Array.from(this.queenBees.values()).filter(qb => qb.isActive);
    
    let bestQueenBee: QueenBee | null = null;
    let bestScore = 0;

    for (const queenBee of availableQueenBees) {
      let score = 0;

      // Location proximity (40% weight)
      if (this.isInTerritory(task.location!, queenBee.territory)) {
        score += 40;
      }

      // Specialty match (30% weight)
      if (task.skillsRequired && task.skillsRequired.some(skill => queenBee.specialties.includes(skill))) {
        score += 30;
      }

      // Workload (20% weight)
      const workloadScore = Math.max(0, 20 - (queenBee.currentWorkload * 2));
      score += workloadScore;

      // Performance (10% weight)
      score += queenBee.managementRating * 2;

      if (score > bestScore) {
        bestScore = score;
        bestQueenBee = queenBee;
      }
    }

    return bestQueenBee;
  }

  private isWorkerBeeAvailable(workerBee: WorkerBee, task: BeeTask): boolean {
    if (!workerBee.isActive || workerBee.availability.status !== 'available') {
      return false;
    }

    if (workerBee.availability.currentTaskCount >= workerBee.availability.maxConcurrentTasks) {
      return false;
    }

    // Check if worker bee has required skills
    const hasRequiredSkills = task.skillsRequired.every(skill => 
      workerBee.specialties.includes(skill)
    );

    return hasRequiredSkills;
  }

  private isInTerritory(location: BeeLocation, territory: GeographicTerritory): boolean {
    return location.latitude >= territory.boundaries.south &&
           location.latitude <= territory.boundaries.north &&
           location.longitude >= territory.boundaries.west &&
           location.longitude <= territory.boundaries.east;
  }

  private calculateAverageQualityScore(tasks: BeeTask[]): number {
    const completedTasks = tasks.filter(task => task.qualityScore !== undefined);
    if (completedTasks.length === 0) return 0;
    
    const totalScore = completedTasks.reduce((sum, task) => sum + (task.qualityScore || 0), 0);
    return totalScore / completedTasks.length;
  }

  private calculateTeamProductivity(workerBees: (WorkerBee | undefined)[]): number {
    const validBees = workerBees.filter(Boolean) as WorkerBee[];
    if (validBees.length === 0) return 0;
    
    const totalSuccessRate = validBees.reduce((sum, bee) => sum + bee.successRate, 0);
    return totalSuccessRate / validBees.length;
  }

  private async notifyQueenBee(queenBeeId: string, task: BeeTask): Promise<void> {
    // Implementation for notifying Queen Bee (WebSocket, email, SMS, etc.)
    console.log(`Notifying Queen Bee ${queenBeeId} about new task ${task.id}`);
  }

  private async notifyWorkerBee(workerBeeId: string, task: BeeTask): Promise<void> {
    // Implementation for notifying Worker Bee (WebSocket, email, SMS, etc.)
    console.log(`Notifying Worker Bee ${workerBeeId} about assigned task ${task.id}`);
  }

  private initializeQueenBees(): void {
    // Initialize with sample Queen Bees
    const sampleQueenBees: QueenBee[] = [
      {
        id: 'queen-001',
        beeId: 'QB-JHB-001',
        fullName: 'Sarah Mitchell',
        email: '<EMAIL>',
        phoneNumber: '+27 11 123 4567',
        beeType: BeeType.QUEEN_BEE,
        territory: {
          name: 'Johannesburg Metro',
          boundaries: { north: -25.7, south: -26.4, east: 28.3, west: 27.8 },
          cities: ['Johannesburg', 'Sandton', 'Randburg', 'Roodepoort'],
          provinces: ['Gauteng'],
          coverage: 'urban'
        },
        managedBees: ['worker-001', 'worker-002', 'worker-003'],
        specialties: [BeeSpecialty.PROJECT_COORDINATION, BeeSpecialty.COMPLIANCE_CHECKING],
        maxWorkerBees: 10,
        currentWorkload: 3,
        managementRating: 4.8,
        leadershipSkills: ['Team Management', 'Quality Control', 'Client Relations'],
        teamProductivity: 92,
        taskCompletionRate: 96,
        qualityScore: 4.7,
        clientSatisfactionScore: 4.9,
        isActive: true,
        currentLocation: {
          latitude: -26.2041,
          longitude: 28.0473,
          address: 'Sandton City, Johannesburg',
          city: 'Johannesburg',
          province: 'Gauteng',
          lastUpdated: new Date().toISOString()
        },
        workingHours: {
          monday: { start: '08:00', end: '17:00', available: true },
          tuesday: { start: '08:00', end: '17:00', available: true },
          wednesday: { start: '08:00', end: '17:00', available: true },
          thursday: { start: '08:00', end: '17:00', available: true },
          friday: { start: '08:00', end: '17:00', available: true },
          timezone: 'SAST'
        },
        emergencyContact: '+27 82 987 6543',
        subordinates: [
          { beeId: 'worker-001', beeType: BeeType.SENIOR_BEE, reportingLevel: 1, managementSpan: 0 },
          { beeId: 'worker-002', beeType: BeeType.WORKER_BEE, reportingLevel: 1, managementSpan: 0 },
          { beeId: 'worker-003', beeType: BeeType.SPECIALIST_BEE, reportingLevel: 1, managementSpan: 0 }
        ],
        joinedDate: '2023-01-15T00:00:00Z',
        lastActiveDate: new Date().toISOString(),
        lastPerformanceReview: '2024-01-15T00:00:00Z'
      }
    ];

    sampleQueenBees.forEach(queenBee => {
      this.queenBees.set(queenBee.id, queenBee);
    });
  }

  private initializeWorkerBees(): void {
    // Initialize with sample Worker Bees
    const sampleWorkerBees: WorkerBee[] = [
      {
        id: 'worker-001',
        beeId: 'WB-JHB-001',
        fullName: 'Michael Johnson',
        email: '<EMAIL>',
        phoneNumber: '+27 11 234 5678',
        beeType: BeeType.SENIOR_BEE,
        queenBeeId: 'queen-001',
        specialties: [BeeSpecialty.DOCUMENT_PROCESSING, BeeSpecialty.COMPLIANCE_CHECKING],
        skillLevel: 'senior',
        rating: 4.6,
        tasksCompleted: 127,
        successRate: 94,
        averageTaskTime: 85,
        clientFeedbackScore: 4.5,
        isActive: true,
        currentLocation: {
          latitude: -26.1951,
          longitude: 28.0568,
          address: 'Sandton, Johannesburg',
          city: 'Johannesburg',
          province: 'Gauteng',
          lastUpdated: new Date().toISOString()
        },
        availability: {
          status: 'available',
          maxConcurrentTasks: 3,
          currentTaskCount: 0
        },
        trainingCompleted: ['Compliance Training', 'Document Processing', 'Client Relations'],
        certifications: ['Tender Specialist', 'Compliance Officer'],
        mentorshipStatus: 'mentor',
        joinedDate: '2023-02-01T00:00:00Z',
        lastActiveDate: new Date().toISOString(),
        lastTrainingDate: '2024-01-10T00:00:00Z'
      }
    ];

    sampleWorkerBees.forEach(workerBee => {
      this.workerBees.set(workerBee.id, workerBee);
    });
  }
}

export default QueenBeeManagementSystem;
