/**
 * Psychological Archetype Detection Service
 * Analyzes user behavior patterns to determine bidder archetype for personalized experience
 */

export type BidderArchetype = 'achiever' | 'hunter' | 'analyst' | 'relationship_builder';

export interface ArchetypeProfile {
  archetype: BidderArchetype;
  confidence: number; // 0-1 confidence in the classification
  traits: {
    competitiveness: number; // 0-1
    riskTolerance: number; // 0-1
    analyticalThinking: number; // 0-1
    socialOrientation: number; // 0-1
    urgencyPreference: number; // 0-1
    detailOrientation: number; // 0-1
    collaborationTendency: number; // 0-1
    statusMotivation: number; // 0-1
  };
  behaviorPatterns: {
    averageDecisionTime: number; // seconds
    preferredBidTiming: 'early' | 'middle' | 'last_minute';
    riskLevel: 'conservative' | 'moderate' | 'aggressive';
    informationConsumption: 'minimal' | 'moderate' | 'extensive';
    competitorAwareness: 'low' | 'medium' | 'high';
    collaborationFrequency: number; // 0-1
  };
  motivationTriggers: string[];
  stressTriggers: string[];
  optimalConditions: string[];
  communicationStyle: 'direct' | 'analytical' | 'collaborative' | 'competitive';
}

export interface UserBehaviorData {
  // Bidding patterns
  totalBids: number;
  successRate: number;
  averageBidValue: number;
  averageDecisionTime: number;
  lastMinuteBids: number;
  earlyBids: number;
  
  // Interaction patterns
  timeSpentAnalyzing: number;
  documentsDownloaded: number;
  competitorChecks: number;
  collaborationRequests: number;
  helpSeeking: number;
  
  // Engagement patterns
  loginFrequency: number;
  sessionDuration: number;
  featureUsage: Record<string, number>;
  notificationResponses: number;
  
  // Risk patterns
  highValueBids: number;
  lowCompetitionBids: number;
  newCategoryBids: number;
  
  // Social patterns
  profileViews: number;
  messagesSent: number;
  networkConnections: number;
  referrals: number;
  
  // Performance patterns
  streakCount: number;
  improvementRate: number;
  consistencyScore: number;
  adaptabilityScore: number;
}

class ArchetypeDetectionService {
  private static instance: ArchetypeDetectionService;

  public static getInstance(): ArchetypeDetectionService {
    if (!ArchetypeDetectionService.instance) {
      ArchetypeDetectionService.instance = new ArchetypeDetectionService();
    }
    return ArchetypeDetectionService.instance;
  }

  /**
   * Analyze user behavior and determine their psychological archetype
   */
  public analyzeArchetype(behaviorData: UserBehaviorData): ArchetypeProfile {
    const traits = this.calculateTraits(behaviorData);
    const archetype = this.determineArchetype(traits);
    const behaviorPatterns = this.analyzeBehaviorPatterns(behaviorData);
    
    return {
      archetype,
      confidence: this.calculateConfidence(traits, behaviorData),
      traits,
      behaviorPatterns,
      motivationTriggers: this.getMotivationTriggers(archetype),
      stressTriggers: this.getStressTriggers(archetype),
      optimalConditions: this.getOptimalConditions(archetype),
      communicationStyle: this.getCommunicationStyle(archetype)
    };
  }

  /**
   * Calculate psychological traits from behavior data
   */
  private calculateTraits(data: UserBehaviorData) {
    return {
      competitiveness: Math.min(1, (data.competitorChecks / 100) + (data.highValueBids / data.totalBids)),
      riskTolerance: Math.min(1, (data.highValueBids + data.newCategoryBids) / data.totalBids),
      analyticalThinking: Math.min(1, (data.timeSpentAnalyzing / 3600) + (data.documentsDownloaded / 50)),
      socialOrientation: Math.min(1, (data.collaborationRequests + data.networkConnections) / 20),
      urgencyPreference: Math.min(1, data.lastMinuteBids / data.totalBids),
      detailOrientation: Math.min(1, data.documentsDownloaded / data.totalBids),
      collaborationTendency: Math.min(1, data.collaborationRequests / 10),
      statusMotivation: Math.min(1, (data.profileViews + data.highValueBids) / 50)
    };
  }

  /**
   * Determine archetype based on trait analysis
   */
  private determineArchetype(traits: any): BidderArchetype {
    const scores = {
      achiever: (traits.competitiveness * 0.3) + (traits.statusMotivation * 0.3) + (traits.riskTolerance * 0.2) + (traits.detailOrientation * 0.2),
      hunter: (traits.urgencyPreference * 0.3) + (traits.riskTolerance * 0.3) + (traits.competitiveness * 0.2) + ((1 - traits.analyticalThinking) * 0.2),
      analyst: (traits.analyticalThinking * 0.4) + (traits.detailOrientation * 0.3) + ((1 - traits.urgencyPreference) * 0.2) + ((1 - traits.socialOrientation) * 0.1),
      relationship_builder: (traits.socialOrientation * 0.4) + (traits.collaborationTendency * 0.3) + ((1 - traits.competitiveness) * 0.2) + ((1 - traits.urgencyPreference) * 0.1)
    };

    return Object.entries(scores).reduce((a, b) => scores[a[0] as keyof typeof scores] > scores[b[0] as keyof typeof scores] ? a : b)[0] as BidderArchetype;
  }

  /**
   * Analyze behavior patterns
   */
  private analyzeBehaviorPatterns(data: UserBehaviorData) {
    const totalBids = Math.max(1, data.totalBids);
    
    return {
      averageDecisionTime: data.averageDecisionTime,
      preferredBidTiming: this.getPreferredTiming(data),
      riskLevel: this.getRiskLevel(data),
      informationConsumption: this.getInformationConsumption(data),
      competitorAwareness: this.getCompetitorAwareness(data),
      collaborationFrequency: data.collaborationRequests / totalBids
    };
  }

  private getPreferredTiming(data: UserBehaviorData): 'early' | 'middle' | 'last_minute' {
    const totalBids = Math.max(1, data.totalBids);
    const lastMinuteRatio = data.lastMinuteBids / totalBids;
    const earlyRatio = data.earlyBids / totalBids;
    
    if (lastMinuteRatio > 0.6) return 'last_minute';
    if (earlyRatio > 0.6) return 'early';
    return 'middle';
  }

  private getRiskLevel(data: UserBehaviorData): 'conservative' | 'moderate' | 'aggressive' {
    const totalBids = Math.max(1, data.totalBids);
    const riskRatio = (data.highValueBids + data.newCategoryBids) / totalBids;
    
    if (riskRatio > 0.7) return 'aggressive';
    if (riskRatio > 0.3) return 'moderate';
    return 'conservative';
  }

  private getInformationConsumption(data: UserBehaviorData): 'minimal' | 'moderate' | 'extensive' {
    const avgDocsPerBid = data.documentsDownloaded / Math.max(1, data.totalBids);
    
    if (avgDocsPerBid > 5) return 'extensive';
    if (avgDocsPerBid > 2) return 'moderate';
    return 'minimal';
  }

  private getCompetitorAwareness(data: UserBehaviorData): 'low' | 'medium' | 'high' {
    const checksPerBid = data.competitorChecks / Math.max(1, data.totalBids);
    
    if (checksPerBid > 3) return 'high';
    if (checksPerBid > 1) return 'medium';
    return 'low';
  }

  /**
   * Calculate confidence in archetype classification
   */
  private calculateConfidence(traits: any, data: UserBehaviorData): number {
    // Base confidence on data volume and consistency
    const dataVolume = Math.min(1, data.totalBids / 20); // More bids = higher confidence
    const traitVariance = this.calculateTraitVariance(traits);
    const consistency = Math.min(1, data.consistencyScore);
    
    return (dataVolume * 0.4) + ((1 - traitVariance) * 0.3) + (consistency * 0.3);
  }

  private calculateTraitVariance(traits: any): number {
    const values = Object.values(traits) as number[];
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const variance = values.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / values.length;
    return Math.sqrt(variance);
  }

  /**
   * Get motivation triggers for each archetype
   */
  private getMotivationTriggers(archetype: BidderArchetype): string[] {
    const triggers = {
      achiever: [
        'Public recognition and rankings',
        'Achievement badges and status symbols',
        'Competitive comparisons',
        'Prestige projects and high-value contracts',
        'Performance metrics and success rates',
        'Leadership opportunities'
      ],
      hunter: [
        'Urgency and time pressure',
        'Exclusive opportunities',
        'Competitive advantages',
        'Quick wins and fast results',
        'Market opportunities and trends',
        'Risk-reward scenarios'
      ],
      analyst: [
        'Detailed data and analytics',
        'Market insights and trends',
        'Risk assessments and probabilities',
        'Comprehensive documentation',
        'Strategic planning tools',
        'Performance optimization'
      ],
      relationship_builder: [
        'Collaboration opportunities',
        'Network building and partnerships',
        'Community recognition',
        'Mentoring and helping others',
        'Long-term relationships',
        'Social impact projects'
      ]
    };

    return triggers[archetype];
  }

  /**
   * Get stress triggers for each archetype
   */
  private getStressTriggers(archetype: BidderArchetype): string[] {
    const triggers = {
      achiever: [
        'Public failure or low rankings',
        'Lack of recognition',
        'Unclear success metrics',
        'Limited competitive information',
        'Reduced status or prestige'
      ],
      hunter: [
        'Slow processes and delays',
        'Over-analysis and complexity',
        'Missed opportunities',
        'Bureaucratic procedures',
        'Risk aversion requirements'
      ],
      analyst: [
        'Insufficient data or information',
        'Time pressure and urgency',
        'Unclear requirements',
        'Forced quick decisions',
        'Incomplete documentation'
      ],
      relationship_builder: [
        'Aggressive competition',
        'Isolation and lack of collaboration',
        'Conflict and confrontation',
        'Impersonal processes',
        'Individual performance pressure'
      ]
    };

    return triggers[archetype];
  }

  /**
   * Get optimal conditions for each archetype
   */
  private getOptimalConditions(archetype: BidderArchetype): string[] {
    const conditions = {
      achiever: [
        'Clear performance metrics and rankings',
        'Public recognition opportunities',
        'Competitive environment',
        'High-stakes projects',
        'Status and prestige indicators'
      ],
      hunter: [
        'Fast-paced environment',
        'Immediate feedback and results',
        'Flexible processes',
        'Risk-taking opportunities',
        'Market volatility and change'
      ],
      analyst: [
        'Comprehensive data availability',
        'Sufficient analysis time',
        'Detailed documentation',
        'Predictable processes',
        'Risk mitigation tools'
      ],
      relationship_builder: [
        'Collaborative environment',
        'Team-based projects',
        'Networking opportunities',
        'Mentoring relationships',
        'Community involvement'
      ]
    };

    return conditions[archetype];
  }

  /**
   * Get communication style for each archetype
   */
  private getCommunicationStyle(archetype: BidderArchetype): 'direct' | 'analytical' | 'collaborative' | 'competitive' {
    const styles = {
      achiever: 'competitive' as const,
      hunter: 'direct' as const,
      analyst: 'analytical' as const,
      relationship_builder: 'collaborative' as const
    };

    return styles[archetype];
  }

  /**
   * Generate mock behavior data for testing
   */
  public generateMockBehaviorData(archetype?: BidderArchetype): UserBehaviorData {
    const baseData = {
      totalBids: 15,
      successRate: 0.68,
      averageBidValue: 3200000,
      averageDecisionTime: 1800,
      lastMinuteBids: 3,
      earlyBids: 8,
      timeSpentAnalyzing: 7200,
      documentsDownloaded: 45,
      competitorChecks: 25,
      collaborationRequests: 2,
      helpSeeking: 5,
      loginFrequency: 12,
      sessionDuration: 2400,
      featureUsage: { dashboard: 50, tenders: 30, analytics: 15 },
      notificationResponses: 8,
      highValueBids: 4,
      lowCompetitionBids: 6,
      newCategoryBids: 2,
      profileViews: 15,
      messagesSent: 8,
      networkConnections: 12,
      referrals: 3,
      streakCount: 4,
      improvementRate: 0.15,
      consistencyScore: 0.75,
      adaptabilityScore: 0.68
    };

    // Modify based on archetype if specified
    if (archetype) {
      switch (archetype) {
        case 'achiever':
          return {
            ...baseData,
            competitorChecks: 40,
            highValueBids: 8,
            profileViews: 25,
            successRate: 0.78
          };
        case 'hunter':
          return {
            ...baseData,
            lastMinuteBids: 8,
            averageDecisionTime: 900,
            highValueBids: 6,
            newCategoryBids: 5
          };
        case 'analyst':
          return {
            ...baseData,
            timeSpentAnalyzing: 14400,
            documentsDownloaded: 75,
            earlyBids: 12,
            averageDecisionTime: 3600
          };
        case 'relationship_builder':
          return {
            ...baseData,
            collaborationRequests: 8,
            networkConnections: 25,
            messagesSent: 20,
            referrals: 8
          };
      }
    }

    return baseData;
  }
}

export default ArchetypeDetectionService;
