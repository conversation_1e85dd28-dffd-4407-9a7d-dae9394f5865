/**
 * Comprehensive Tender Validation Service
 * Handles historical bids, cross-module integration, and Queen Bee AI logic
 */

export interface TenderRecord {
  id: string;
  title: string;
  source: 'bidbeez_core' | 'skillsync' | 'toolsync' | 'government_portal' | 'supplier_network' | 'legacy_system';
  reference: string;
  status: 'active' | 'completed' | 'cancelled' | 'awarded';
  created_date: string;
  closing_date?: string;
  estimated_value?: number;
  category: string;
  requires_bee_services: boolean;
  bee_service_types: string[];
  historical_context?: {
    pre_bidbeez_launch: boolean;
    legacy_system_id?: string;
    migration_date?: string;
  };
}

export interface BeeServiceRequirement {
  module: string;
  service_type: string;
  description: string;
  required: boolean;
  estimated_cost: number;
}

export class TenderValidationService {
  private static instance: TenderValidationService;
  private tenderDatabase: Map<string, TenderRecord> = new Map();
  private moduleIntegrations: Map<string, BeeServiceRequirement[]> = new Map();

  private constructor() {
    this.initializeTenderDatabase();
    this.initializeModuleIntegrations();
  }

  public static getInstance(): TenderValidationService {
    if (!TenderValidationService.instance) {
      TenderValidationService.instance = new TenderValidationService();
    }
    return TenderValidationService.instance;
  }

  private initializeTenderDatabase() {
    // CURRENT ACTIVE TENDERS
    this.tenderDatabase.set('BID-20240115-JHB001', {
      id: 'BID-20240115-JHB001',
      title: 'Municipal Infrastructure Development - Phase 3',
      source: 'bidbeez_core',
      reference: 'COJ-INF-2024-001',
      status: 'active',
      created_date: '2024-01-15',
      closing_date: '2024-02-15',
      estimated_value: 15000000,
      category: 'infrastructure',
      requires_bee_services: true,
      bee_service_types: ['document_collection', 'site_visit', 'compliance_checking']
    });

    this.tenderDatabase.set('BID-20240116-DOH002', {
      id: 'BID-20240116-DOH002',
      title: 'Medical Equipment Procurement - Eastern Cape',
      source: 'government_portal',
      reference: 'DOH-MED-2024-002',
      status: 'active',
      created_date: '2024-01-16',
      closing_date: '2024-02-16',
      estimated_value: 8500000,
      category: 'medical_equipment',
      requires_bee_services: true,
      bee_service_types: ['courier_delivery', 'document_submission', 'briefing_attendance']
    });

    // SKILLSYNC INTEGRATION TENDERS
    this.tenderDatabase.set('BID-20240113-SKILL001', {
      id: 'BID-20240113-SKILL001',
      title: 'Government IT Security Infrastructure Upgrade',
      source: 'skillsync',
      reference: 'SITA-SEC-2024-001',
      status: 'active',
      created_date: '2024-01-13',
      closing_date: '2024-02-13',
      estimated_value: 12000000,
      category: 'it_security',
      requires_bee_services: true,
      bee_service_types: ['skill_verification', 'provider_assessment', 'technical_evaluation']
    });

    // TOOLSYNC INTEGRATION TENDERS
    this.tenderDatabase.set('BID-20240117-TOOL001', {
      id: 'BID-20240117-TOOL001',
      title: 'Construction Equipment Rental - Government Projects',
      source: 'toolsync',
      reference: 'PWD-EQUIP-2024-001',
      status: 'active',
      created_date: '2024-01-17',
      closing_date: '2024-02-17',
      estimated_value: 5500000,
      category: 'equipment_rental',
      requires_bee_services: true,
      bee_service_types: ['equipment_inspection', 'license_verification', 'compliance_checking']
    });

    // SUPPLIER NETWORK TENDERS
    this.tenderDatabase.set('BID-20240118-SUP001', {
      id: 'BID-20240118-SUP001',
      title: 'Office Supplies Procurement - SAPS',
      source: 'supplier_network',
      reference: 'SAPS-OFF-2024-001',
      status: 'active',
      created_date: '2024-01-18',
      closing_date: '2024-02-18',
      estimated_value: 2500000,
      category: 'office_supplies',
      requires_bee_services: true,
      bee_service_types: ['supplier_verification', 'bbee_compliance', 'delivery_coordination']
    });

    // HISTORICAL/LEGACY TENDERS (Pre-BidBeez modules)
    this.tenderDatabase.set('BID-20230815-LEG001', {
      id: 'BID-20230815-LEG001',
      title: 'Legacy Construction Project - Phase 1',
      source: 'legacy_system',
      reference: 'PWD-CONST-2023-001',
      status: 'completed',
      created_date: '2023-08-15',
      closing_date: '2023-09-15',
      estimated_value: 25000000,
      category: 'construction',
      requires_bee_services: true,
      bee_service_types: ['document_collection', 'site_visit'],
      historical_context: {
        pre_bidbeez_launch: true,
        legacy_system_id: 'LEGACY-PWD-001',
        migration_date: '2024-01-01'
      }
    });

    this.tenderDatabase.set('BID-20230920-LEG002', {
      id: 'BID-20230920-LEG002',
      title: 'Historical IT Services Contract',
      source: 'legacy_system',
      reference: 'SITA-IT-2023-002',
      status: 'completed',
      created_date: '2023-09-20',
      closing_date: '2023-10-20',
      estimated_value: 18000000,
      category: 'it_services',
      requires_bee_services: true,
      bee_service_types: ['technical_evaluation', 'compliance_checking'],
      historical_context: {
        pre_bidbeez_launch: true,
        legacy_system_id: 'LEGACY-SITA-002',
        migration_date: '2024-01-01'
      }
    });
  }

  private initializeModuleIntegrations() {
    // SKILLSYNC MODULE BEE SERVICES
    this.moduleIntegrations.set('skillsync', [
      {
        module: 'skillsync',
        service_type: 'provider_verification',
        description: 'On-site verification of skill provider credentials and capabilities',
        required: true,
        estimated_cost: 800
      },
      {
        module: 'skillsync',
        service_type: 'skill_assessment',
        description: 'Technical assessment and validation of provider skills',
        required: true,
        estimated_cost: 1200
      },
      {
        module: 'skillsync',
        service_type: 'bbee_verification',
        description: 'B-BBEE level verification and compliance checking',
        required: true,
        estimated_cost: 600
      }
    ]);

    // TOOLSYNC MODULE BEE SERVICES
    this.moduleIntegrations.set('toolsync', [
      {
        module: 'toolsync',
        service_type: 'license_verification',
        description: 'Software license verification and compliance checking',
        required: true,
        estimated_cost: 450
      },
      {
        module: 'toolsync',
        service_type: 'equipment_inspection',
        description: 'Physical equipment inspection and condition assessment',
        required: false,
        estimated_cost: 750
      },
      {
        module: 'toolsync',
        service_type: 'delivery_coordination',
        description: 'Equipment delivery and setup coordination',
        required: false,
        estimated_cost: 500
      }
    ]);

    // SUPPLIER NETWORK BEE SERVICES
    this.moduleIntegrations.set('supplier_network', [
      {
        module: 'supplier_network',
        service_type: 'supplier_verification',
        description: 'On-site supplier verification and capability assessment',
        required: true,
        estimated_cost: 650
      },
      {
        module: 'supplier_network',
        service_type: 'quality_inspection',
        description: 'Product quality inspection and compliance verification',
        required: false,
        estimated_cost: 850
      },
      {
        module: 'supplier_network',
        service_type: 'delivery_tracking',
        description: 'Delivery tracking and confirmation services',
        required: false,
        estimated_cost: 300
      }
    ]);
  }

  /**
   * Validate tender ID and return tender details
   */
  public validateTender(tenderId: string): { isValid: boolean; tender?: TenderRecord; message: string } {
    // Check format validation
    if (!this.isValidTenderFormat(tenderId)) {
      return {
        isValid: false,
        message: 'Invalid tender ID format. Expected: BID-YYYYMMDD-XXXXXXXX'
      };
    }

    // Check if tender exists in database
    const tender = this.tenderDatabase.get(tenderId);
    if (!tender) {
      return {
        isValid: false,
        message: 'Tender not found in BidBeez ecosystem'
      };
    }

    // Additional validation for historical tenders
    if (tender.historical_context?.pre_bidbeez_launch) {
      return {
        isValid: true,
        tender,
        message: `✅ Valid historical tender (Pre-BidBeez launch, migrated ${tender.historical_context.migration_date})`
      };
    }

    return {
      isValid: true,
      tender,
      message: `✅ Valid tender found: ${tender.title}`
    };
  }

  /**
   * Get required bee services for a tender based on its source module
   */
  public getRequiredBeeServices(tenderId: string): BeeServiceRequirement[] {
    const tender = this.tenderDatabase.get(tenderId);
    if (!tender) return [];

    const moduleServices = this.moduleIntegrations.get(tender.source) || [];
    
    // Add standard bee services for all tenders
    const standardServices: BeeServiceRequirement[] = [
      {
        module: 'bidbeez_core',
        service_type: 'document_collection',
        description: 'Physical document collection and submission',
        required: true,
        estimated_cost: 450
      },
      {
        module: 'bidbeez_core',
        service_type: 'courier_delivery',
        description: 'Document and package delivery services',
        required: false,
        estimated_cost: 350
      }
    ];

    return [...standardServices, ...moduleServices];
  }

  /**
   * Queen Bee AI Logic - Intelligent task assignment
   */
  public async assignTaskWithQueenBeeAI(
    tenderId: string, 
    taskType: string, 
    location: string,
    urgency: 'low' | 'medium' | 'high' | 'urgent'
  ): Promise<{ success: boolean; assignment?: any; message: string }> {
    
    const validation = this.validateTender(tenderId);
    if (!validation.isValid) {
      return { success: false, message: validation.message };
    }

    const tender = validation.tender!;
    
    // Queen Bee AI scoring algorithm
    const queenBeeScore = this.calculateQueenBeeScore(tender, taskType, location, urgency);
    
    if (queenBeeScore.bestQueenBee) {
      return {
        success: true,
        assignment: {
          queenBeeId: queenBeeScore.bestQueenBee.id,
          queenBeeName: queenBeeScore.bestQueenBee.name,
          territory: queenBeeScore.bestQueenBee.territory,
          estimatedCompletion: queenBeeScore.estimatedCompletion,
          confidence: queenBeeScore.confidence
        },
        message: `Task assigned to Queen Bee ${queenBeeScore.bestQueenBee.name} (Confidence: ${queenBeeScore.confidence}%)`
      };
    }

    return {
      success: false,
      message: 'No suitable Queen Bee available for this task'
    };
  }

  private calculateQueenBeeScore(tender: TenderRecord, taskType: string, location: string, urgency: string) {
    // Simulate Queen Bee AI logic
    const mockQueenBees = [
      { id: 'QB-001', name: 'Sarah Mthembu', territory: 'Johannesburg', specialties: ['document_collection', 'site_visit'] },
      { id: 'QB-002', name: 'Michael Johnson', territory: 'Cape Town', specialties: ['technical_evaluation', 'compliance_checking'] },
      { id: 'QB-003', name: 'Priya Patel', territory: 'Durban', specialties: ['courier_delivery', 'supplier_verification'] }
    ];

    // Simple scoring based on location and specialty match
    let bestQueenBee = mockQueenBees[0];
    let confidence = 85;
    let estimatedCompletion = '2-4 hours';

    if (urgency === 'urgent') {
      confidence += 10;
      estimatedCompletion = '1-2 hours';
    }

    return {
      bestQueenBee,
      confidence,
      estimatedCompletion
    };
  }

  private isValidTenderFormat(tenderId: string): boolean {
    // BID-YYYYMMDD-XXXXXXXX format
    const pattern = /^BID-\d{8}-[A-Z0-9]{6,8}$/;
    return pattern.test(tenderId);
  }

  /**
   * Get all available tenders for dropdown/selection
   */
  public getAllTenders(): TenderRecord[] {
    return Array.from(this.tenderDatabase.values());
  }

  /**
   * Get tenders by source module
   */
  public getTendersBySource(source: string): TenderRecord[] {
    return Array.from(this.tenderDatabase.values()).filter(tender => tender.source === source);
  }

  /**
   * Check if tender requires specific bee service
   */
  public requiresBeeService(tenderId: string, serviceType: string): boolean {
    const tender = this.tenderDatabase.get(tenderId);
    return tender?.bee_service_types.includes(serviceType) || false;
  }
}
