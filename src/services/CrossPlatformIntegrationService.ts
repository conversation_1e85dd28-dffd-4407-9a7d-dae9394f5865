/**
 * Cross-Platform Integration Service
 * Bidirectional talent marketplace between BidBeez and SkillSync
 */

import {
  CrossPlatformUser,
  TenderTalentRequest,
  TalentApplication,
  SkillSyncOpportunityFeed,
  TenderOpportunity,
  CrossPlatformMonetization,
  CrossPlatformEvent,
  CrossPlatformEventType
} from '../types/skillsyncIntegration';
import { UserRole, TeamMember } from '../types/teamCollaboration';
import { TenderNotification } from '../types/notifications';

class CrossPlatformIntegrationService {
  private static instance: CrossPlatformIntegrationService;
  private crossPlatformUsers: Map<string, CrossPlatformUser> = new Map();
  private talentRequests: Map<string, TenderTalentRequest> = new Map();
  private talentApplications: Map<string, TalentApplication> = new Map();
  private opportunityFeeds: Map<string, SkillSyncOpportunityFeed> = new Map();
  private monetizationConfig: CrossPlatformMonetization;

  static getInstance(): CrossPlatformIntegrationService {
    if (!CrossPlatformIntegrationService.instance) {
      CrossPlatformIntegrationService.instance = new CrossPlatformIntegrationService();
      CrossPlatformIntegrationService.instance.initializeMonetization();
    }
    return CrossPlatformIntegrationService.instance;
  }

  /**
   * BIDBEEZ SIDE: TALENT RECRUITMENT
   */
  async postTalentRequest(
    organizationId: string,
    tenderId: string,
    requestData: {
      tenderTitle: string;
      tenderReference: string;
      requiredSkills: any[];
      projectDescription: string;
      responsibilities: string[];
      budgetRange: any;
      timeline: any;
      urgency: 'low' | 'medium' | 'high' | 'urgent';
    }
  ): Promise<TenderTalentRequest> {
    console.log(`🎯 Posting talent request for tender ${tenderId}`);

    const talentRequest: TenderTalentRequest = {
      id: `tr-${Date.now()}`,
      tenderId,
      tenderTitle: requestData.tenderTitle,
      tenderReference: requestData.tenderReference,
      organizationId,
      requestedBy: 'current-user-id', // Would get from auth context
      
      // Talent requirements
      requiredSkills: requestData.requiredSkills.map(skill => ({
        skill: skill.name,
        level: skill.level || 'intermediate',
        required: skill.required || true,
        weight: skill.weight || 5,
        yearsRequired: skill.yearsRequired,
        certificationRequired: skill.certificationRequired || false
      })),
      preferredExperience: 3,
      projectDuration: 30,
      workload: 40,
      
      // Compensation
      budgetRange: requestData.budgetRange,
      paymentTerms: {
        type: 'milestone',
        paymentSchedule: 'Net 30',
        currency: 'ZAR',
        invoicingRequirements: ['Tax Invoice', 'Detailed Timesheet']
      },
      
      // Project details
      projectDescription: requestData.projectDescription,
      responsibilities: requestData.responsibilities,
      deliverables: [
        'Technical analysis and recommendations',
        'Cost estimates and pricing support',
        'Documentation and reports',
        'Tender submission support'
      ],
      timeline: requestData.timeline,
      
      // Requirements
      locationRequirement: 'remote',
      travelRequired: false,
      
      // Request status
      status: 'published',
      urgency: requestData.urgency,
      
      // Responses
      applications: [],
      shortlistedCandidates: [],
      
      // Timing
      applicationDeadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      projectStartDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
      tenderSubmissionDate: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString(),
      
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    this.talentRequests.set(talentRequest.id, talentRequest);

    // Sync to SkillSync platform
    await this.syncTalentRequestToSkillSync(talentRequest);

    // Log cross-platform event
    await this.logCrossPlatformEvent('talent_request_posted', {
      sourceUserId: talentRequest.requestedBy,
      platform: 'bidbeez',
      data: {
        talentRequestId: talentRequest.id,
        tenderId,
        skills: requestData.requiredSkills.map(s => s.name),
        budget: requestData.budgetRange
      }
    });

    console.log(`✅ Talent request posted and synced to SkillSync`);
    return talentRequest;
  }

  /**
   * SKILLSYNC SIDE: OPPORTUNITY DISCOVERY
   */
  async getSkillSyncOpportunities(
    skillsyncUserId: string,
    filters?: any
  ): Promise<TenderOpportunity[]> {
    console.log(`🔍 Getting opportunities for SkillSync user ${skillsyncUserId}`);

    // Get user's SkillSync profile for matching
    const userProfile = await this.getSkillSyncProfile(skillsyncUserId);
    if (!userProfile) {
      return [];
    }

    // Get all active talent requests
    const activeTalentRequests = Array.from(this.talentRequests.values())
      .filter(tr => tr.status === 'published' || tr.status === 'receiving_applications');

    // Convert to opportunities with matching scores
    const opportunities: TenderOpportunity[] = [];

    for (const talentRequest of activeTalentRequests) {
      const matchScore = this.calculateMatchScore(userProfile, talentRequest);
      
      // Only show opportunities with decent match scores
      if (matchScore >= 30) {
        const opportunity: TenderOpportunity = {
          id: `opp-${talentRequest.id}`,
          talentRequestId: talentRequest.id,
          
          // Basic info
          title: `${this.getTalentRequestType(talentRequest)} - ${talentRequest.tenderTitle}`,
          company: await this.getOrganizationName(talentRequest.organizationId),
          location: talentRequest.locationRequirement,
          type: this.getTalentRequestType(talentRequest) as any,
          
          // Project details
          description: talentRequest.projectDescription,
          duration: `${talentRequest.projectDuration} days`,
          startDate: new Date(talentRequest.projectStartDate).toLocaleDateString(),
          workload: `${talentRequest.workload} hours/week`,
          
          // Requirements
          skills: talentRequest.requiredSkills.map(s => s.skill),
          experience: `${talentRequest.preferredExperience}+ years`,
          qualifications: talentRequest.requiredSkills
            .filter(s => s.certificationRequired)
            .map(s => `${s.skill} certification`),
          
          // Compensation
          budget: this.formatBudgetRange(talentRequest.budgetRange),
          paymentType: talentRequest.paymentTerms.type,
          
          // Matching
          matchScore,
          matchReasons: this.getMatchReasons(userProfile, talentRequest),
          
          // Status
          applicants: talentRequest.applications.length,
          timeLeft: this.calculateTimeLeft(talentRequest.applicationDeadline),
          urgency: talentRequest.urgency,
          
          // Engagement
          viewed: false,
          bookmarked: false,
          applied: this.hasUserApplied(skillsyncUserId, talentRequest.id),
          
          postedAt: talentRequest.createdAt,
          deadline: talentRequest.applicationDeadline
        };

        opportunities.push(opportunity);
      }
    }

    // Sort by match score and urgency
    opportunities.sort((a, b) => {
      if (a.urgency !== b.urgency) {
        const urgencyOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
        return urgencyOrder[b.urgency] - urgencyOrder[a.urgency];
      }
      return b.matchScore - a.matchScore;
    });

    console.log(`📋 Found ${opportunities.length} matching opportunities`);
    return opportunities;
  }

  /**
   * SKILLSYNC SIDE: APPLY FOR OPPORTUNITY
   */
  async submitTalentApplication(
    skillsyncUserId: string,
    talentRequestId: string,
    applicationData: {
      coverLetter: string;
      proposedRate: number;
      availability: any;
      portfolioSamples: any[];
      approachDescription: string;
    }
  ): Promise<TalentApplication> {
    console.log(`📝 Submitting application for talent request ${talentRequestId}`);

    const talentRequest = this.talentRequests.get(talentRequestId);
    if (!talentRequest) {
      throw new Error('Talent request not found');
    }

    const userProfile = await this.getSkillSyncProfile(skillsyncUserId);
    if (!userProfile) {
      throw new Error('SkillSync profile not found');
    }

    const application: TalentApplication = {
      id: `app-${Date.now()}`,
      talentRequestId,
      applicantId: skillsyncUserId,
      skillsyncUserId,
      
      // Application details
      coverLetter: applicationData.coverLetter,
      proposedRate: applicationData.proposedRate,
      currency: 'ZAR',
      availability: applicationData.availability,
      
      // Qualifications
      relevantExperience: this.extractRelevantExperience(userProfile, talentRequest),
      portfolioSamples: applicationData.portfolioSamples,
      references: userProfile.references || [],
      
      // Proposal
      approachDescription: applicationData.approachDescription,
      timelineProposal: `I can complete this project within ${talentRequest.projectDuration} days`,
      valueProposition: 'Bringing extensive experience in tender preparation and technical analysis',
      riskMitigation: [
        'Regular progress updates and milestone reviews',
        'Backup resources available if needed',
        'Quality assurance processes in place'
      ],
      
      // Application status
      status: 'submitted',
      submittedAt: new Date().toISOString(),
      
      // Evaluation
      skillsMatch: this.calculateSkillsMatch(userProfile, talentRequest),
      experienceMatch: this.calculateExperienceMatch(userProfile, talentRequest),
      budgetFit: this.calculateBudgetFit(applicationData.proposedRate, talentRequest.budgetRange),
      overallScore: 0, // Will be calculated
      
      // Communication
      messages: [],
      
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Calculate overall score
    application.overallScore = Math.round(
      (application.skillsMatch + application.experienceMatch + application.budgetFit) / 3
    );

    this.talentApplications.set(application.id, application);

    // Add application to talent request
    talentRequest.applications.push(application);
    talentRequest.updatedAt = new Date().toISOString();

    // Notify BidBeez side
    await this.notifyBidBeezOfApplication(application, talentRequest);

    // Log cross-platform event
    await this.logCrossPlatformEvent('application_submitted', {
      sourceUserId: skillsyncUserId,
      targetUserId: talentRequest.requestedBy,
      platform: 'skillsync',
      data: {
        applicationId: application.id,
        talentRequestId,
        proposedRate: applicationData.proposedRate,
        overallScore: application.overallScore
      }
    });

    console.log(`✅ Application submitted with score ${application.overallScore}`);
    return application;
  }

  /**
   * BIDBEEZ SIDE: REVIEW APPLICATIONS
   */
  async getTalentApplications(talentRequestId: string): Promise<TalentApplication[]> {
    const talentRequest = this.talentRequests.get(talentRequestId);
    if (!talentRequest) {
      throw new Error('Talent request not found');
    }

    // Sort applications by overall score
    const applications = talentRequest.applications.sort((a, b) => b.overallScore - a.overallScore);

    console.log(`📋 Retrieved ${applications.length} applications for talent request`);
    return applications;
  }

  /**
   * BIDBEEZ SIDE: SELECT CANDIDATE
   */
  async selectCandidate(
    talentRequestId: string,
    applicationId: string,
    selectionData: {
      startDate: string;
      finalRate: number;
      projectTerms: string;
    }
  ): Promise<{ application: TalentApplication; contract: any }> {
    console.log(`🎯 Selecting candidate for talent request ${talentRequestId}`);

    const talentRequest = this.talentRequests.get(talentRequestId);
    const application = this.talentApplications.get(applicationId);

    if (!talentRequest || !application) {
      throw new Error('Talent request or application not found');
    }

    // Update application status
    application.status = 'selected';
    application.responseAt = new Date().toISOString();

    // Update talent request
    talentRequest.selectedCandidate = applicationId;
    talentRequest.status = 'candidate_selected';
    talentRequest.updatedAt = new Date().toISOString();

    // Reject other applications
    for (const otherApp of talentRequest.applications) {
      if (otherApp.id !== applicationId && otherApp.status === 'submitted') {
        otherApp.status = 'rejected';
        otherApp.responseAt = new Date().toISOString();
      }
    }

    // Create project contract
    const contract = await this.createProjectContract(talentRequest, application, selectionData);

    // Process payment setup
    await this.setupPaymentEscrow(contract);

    // Notify SkillSync user
    await this.notifySkillSyncOfSelection(application, contract);

    // Log cross-platform event
    await this.logCrossPlatformEvent('hire_completed', {
      sourceUserId: talentRequest.requestedBy,
      targetUserId: application.skillsyncUserId,
      platform: 'bidbeez',
      data: {
        applicationId,
        talentRequestId,
        finalRate: selectionData.finalRate,
        contractValue: contract.totalValue
      }
    });

    console.log(`✅ Candidate selected and contract created`);
    return { application, contract };
  }

  /**
   * MONETIZATION IMPLEMENTATION
   */
  private initializeMonetization(): void {
    this.monetizationConfig = {
      // BidBeez side (employers)
      talentRequestFees: {
        basicPostingFee: 299, // ZAR
        featuredPostingFee: 599,
        urgentPostingFee: 999,
        hiringSuccessFee: 8, // 8% of project value
        minimumSuccessFee: 500,
        maximumSuccessFee: 25000,
        talentSearchAccess: 199, // monthly
        directMessaging: 10, // per message
        prioritySupport: 499, // monthly
        basicTier: {
          name: 'Basic Talent Access',
          monthlyFee: 299,
          features: ['Post talent requests', 'Basic matching', 'Email support'],
          talentRequestsIncluded: 3,
          additionalRequestFee: 199,
          successFeeDiscount: 0
        },
        professionalTier: {
          name: 'Professional Talent Hub',
          monthlyFee: 999,
          features: ['Unlimited posts', 'Advanced matching', 'Priority support', 'Direct messaging'],
          talentRequestsIncluded: 999,
          additionalRequestFee: 0,
          successFeeDiscount: 25 // 25% discount on success fees
        },
        enterpriseTier: {
          name: 'Enterprise Talent Solutions',
          monthlyFee: 2999,
          features: ['Everything included', 'Dedicated account manager', 'Custom integrations'],
          talentRequestsIncluded: 999,
          additionalRequestFee: 0,
          successFeeDiscount: 50 // 50% discount on success fees
        }
      },

      // SkillSync side (talent)
      platformCommission: {
        standardCommission: 12, // 12% of project value
        premiumMemberCommission: 8, // 8% for premium members
        volumeDiscounts: [
          { minimumProjects: 5, discountPercentage: 10, period: 'quarterly' },
          { minimumProjects: 10, discountPercentage: 20, period: 'annually' }
        ],
        minimumCommission: 50,
        maximumCommission: 5000,
        commissionTiming: 'milestone',
        newUserDiscount: 50, // 50% off first project commission
        loyaltyBonus: 5, // 5% bonus after 10 projects
        referralBonus: 500 // ZAR for successful referrals
      },

      // Shared revenue
      revenueSharing: {
        bidbeezShare: 60, // 60% of revenue
        skillsyncShare: 40, // 40% of revenue
        subscriptionRevenue: { bidbeezPercentage: 70, skillsyncPercentage: 30, sharedCostsPercentage: 0 },
        transactionRevenue: { bidbeezPercentage: 50, skillsyncPercentage: 50, sharedCostsPercentage: 0 },
        advertisingRevenue: { bidbeezPercentage: 60, skillsyncPercentage: 40, sharedCostsPercentage: 0 },
        settlementPeriod: 'monthly',
        minimumPayout: 1000
      },

      // Payment processing
      paymentProcessing: {
        provider: 'stripe',
        processingFees: {
          creditCardFee: 2.9, // 2.9% + R2.90
          bankTransferFee: 1.5,
          internationalFee: 3.4,
          chargebackFee: 150
        },
        payoutSchedule: {
          frequency: 'weekly',
          holdPeriod: 7,
          minimumPayout: 100,
          currency: 'ZAR'
        },
        disputeHandling: {
          mediationFee: 299,
          arbitrationFee: 999,
          resolutionTimeframe: 14,
          escrowHoldPeriod: 30
        }
      }
    };

    console.log(`💰 Initialized cross-platform monetization system`);
  }

  /**
   * HELPER METHODS
   */
  private async syncTalentRequestToSkillSync(talentRequest: TenderTalentRequest): Promise<void> {
    // In real implementation, this would sync to SkillSync API
    console.log(`🔄 Syncing talent request ${talentRequest.id} to SkillSync`);
  }

  private async getSkillSyncProfile(userId: string): Promise<any> {
    // Mock SkillSync profile - in real implementation, would fetch from SkillSync API
    return {
      userId,
      skills: ['Project Management', 'Cost Estimation', 'Technical Analysis'],
      experience: 5,
      rating: 4.8,
      completionRate: 95,
      hourlyRate: 450,
      availability: { hoursPerWeek: 40 },
      references: []
    };
  }

  private calculateMatchScore(userProfile: any, talentRequest: TenderTalentRequest): number {
    let score = 0;
    
    // Skills match (40% weight)
    const skillsMatch = this.calculateSkillsMatch(userProfile, talentRequest);
    score += skillsMatch * 0.4;
    
    // Experience match (30% weight)
    const experienceMatch = this.calculateExperienceMatch(userProfile, talentRequest);
    score += experienceMatch * 0.3;
    
    // Availability match (20% weight)
    const availabilityMatch = userProfile.availability.hoursPerWeek >= talentRequest.workload ? 100 : 50;
    score += availabilityMatch * 0.2;
    
    // Rate compatibility (10% weight)
    const rateMatch = this.calculateRateCompatibility(userProfile.hourlyRate, talentRequest.budgetRange);
    score += rateMatch * 0.1;
    
    return Math.round(score);
  }

  private calculateSkillsMatch(userProfile: any, talentRequest: TenderTalentRequest): number {
    const userSkills = userProfile.skills.map((s: string) => s.toLowerCase());
    const requiredSkills = talentRequest.requiredSkills.map(s => s.skill.toLowerCase());
    
    const matchingSkills = requiredSkills.filter(skill => 
      userSkills.some(userSkill => userSkill.includes(skill) || skill.includes(userSkill))
    );
    
    return Math.round((matchingSkills.length / requiredSkills.length) * 100);
  }

  private calculateExperienceMatch(userProfile: any, talentRequest: TenderTalentRequest): number {
    const userExperience = userProfile.experience || 0;
    const requiredExperience = talentRequest.preferredExperience || 0;
    
    if (userExperience >= requiredExperience) {
      return 100;
    } else if (userExperience >= requiredExperience * 0.7) {
      return 80;
    } else if (userExperience >= requiredExperience * 0.5) {
      return 60;
    } else {
      return 30;
    }
  }

  private calculateBudgetFit(proposedRate: number, budgetRange: any): number {
    const hourlyBudget = budgetRange.max / (budgetRange.duration || 30) / 8; // Rough hourly estimate
    
    if (proposedRate <= hourlyBudget) {
      return 100;
    } else if (proposedRate <= hourlyBudget * 1.2) {
      return 80;
    } else if (proposedRate <= hourlyBudget * 1.5) {
      return 60;
    } else {
      return 30;
    }
  }

  private calculateRateCompatibility(userRate: number, budgetRange: any): number {
    return this.calculateBudgetFit(userRate, budgetRange);
  }

  private getMatchReasons(userProfile: any, talentRequest: TenderTalentRequest): string[] {
    const reasons = [];
    
    const skillsMatch = this.calculateSkillsMatch(userProfile, talentRequest);
    if (skillsMatch >= 80) reasons.push('Strong skills match');
    
    const experienceMatch = this.calculateExperienceMatch(userProfile, talentRequest);
    if (experienceMatch >= 80) reasons.push('Relevant experience level');
    
    if (userProfile.rating >= 4.5) reasons.push('Excellent rating');
    if (userProfile.completionRate >= 90) reasons.push('High completion rate');
    
    return reasons;
  }

  private getTalentRequestType(talentRequest: TenderTalentRequest): string {
    const skills = talentRequest.requiredSkills.map(s => s.skill.toLowerCase());
    
    if (skills.some(s => s.includes('project') || s.includes('management'))) {
      return 'project_management';
    } else if (skills.some(s => s.includes('cost') || s.includes('estimation'))) {
      return 'estimation';
    } else if (skills.some(s => s.includes('technical') || s.includes('engineering'))) {
      return 'technical_review';
    } else if (skills.some(s => s.includes('legal') || s.includes('compliance'))) {
      return 'compliance';
    } else {
      return 'tender_support';
    }
  }

  private async getOrganizationName(organizationId: string): Promise<string> {
    // Mock implementation - would fetch from organization service
    return 'ABC Construction Ltd';
  }

  private formatBudgetRange(budgetRange: any): string {
    return `R${budgetRange.min.toLocaleString()} - R${budgetRange.max.toLocaleString()}`;
  }

  private calculateTimeLeft(deadline: string): string {
    const now = new Date();
    const deadlineDate = new Date(deadline);
    const diffTime = deadlineDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays > 1) {
      return `${diffDays} days left`;
    } else if (diffDays === 1) {
      return '1 day left';
    } else {
      return 'Deadline passed';
    }
  }

  private hasUserApplied(userId: string, talentRequestId: string): boolean {
    const applications = Array.from(this.talentApplications.values());
    return applications.some(app => 
      app.skillsyncUserId === userId && app.talentRequestId === talentRequestId
    );
  }

  private extractRelevantExperience(userProfile: any, talentRequest: TenderTalentRequest): any[] {
    // Mock implementation - would analyze user's experience for relevance
    return [
      {
        projectName: 'Municipal Infrastructure Tender',
        role: 'Technical Lead',
        duration: '3 months',
        skills: ['Project Management', 'Cost Analysis'],
        achievements: ['Successful tender submission', 'On-time delivery'],
        similarityScore: 85
      }
    ];
  }

  private async notifyBidBeezOfApplication(application: TalentApplication, talentRequest: TenderTalentRequest): Promise<void> {
    console.log(`🔔 Notifying BidBeez of new application for ${talentRequest.tenderTitle}`);
  }

  private async notifySkillSyncOfSelection(application: TalentApplication, contract: any): Promise<void> {
    console.log(`🎉 Notifying SkillSync user of selection for project`);
  }

  private async createProjectContract(talentRequest: TenderTalentRequest, application: TalentApplication, selectionData: any): Promise<any> {
    return {
      id: `contract-${Date.now()}`,
      talentRequestId: talentRequest.id,
      applicationId: application.id,
      totalValue: selectionData.finalRate * talentRequest.workload * (talentRequest.projectDuration / 7),
      terms: selectionData.projectTerms,
      startDate: selectionData.startDate,
      createdAt: new Date().toISOString()
    };
  }

  private async setupPaymentEscrow(contract: any): Promise<void> {
    console.log(`💰 Setting up payment escrow for contract ${contract.id}`);
  }

  private async logCrossPlatformEvent(
    type: CrossPlatformEventType,
    eventData: {
      sourceUserId: string;
      targetUserId?: string;
      platform: 'bidbeez' | 'skillsync' | 'both';
      data: Record<string, any>;
    }
  ): Promise<void> {
    const event: CrossPlatformEvent = {
      id: `evt-${Date.now()}`,
      type,
      sourceUserId: eventData.sourceUserId,
      targetUserId: eventData.targetUserId,
      platform: eventData.platform,
      data: eventData.data,
      timestamp: new Date().toISOString(),
      processed: false,
      syncRequired: true,
      notificationsSent: [],
      followUpActions: []
    };
    
    console.log(`📝 Logged cross-platform event: ${type}`);
  }

  /**
   * PUBLIC GETTERS
   */
  async getTalentRequest(id: string): Promise<TenderTalentRequest | null> {
    return this.talentRequests.get(id) || null;
  }

  async getApplication(id: string): Promise<TalentApplication | null> {
    return this.talentApplications.get(id) || null;
  }

  async getMonetizationConfig(): Promise<CrossPlatformMonetization> {
    return this.monetizationConfig;
  }
}

export default CrossPlatformIntegrationService;
