/**
 * Subscription Management Service
 * Handles subscription management, billing, feature gating, and tier recommendations
 */

import {
  SubscriptionTier,
  SubscriptionPlan,
  UserSubscription,
  OnboardingFlow,
  TierSelectionData,
  TierRecommendation,
  FeatureGate,
  FeatureUsage,
  OrganizationType
} from '../types/subscription';

class SubscriptionService {
  private static instance: SubscriptionService;
  private subscriptionPlans: Map<string, SubscriptionPlan> = new Map();
  private userSubscriptions: Map<string, UserSubscription> = new Map();
  private onboardingFlows: Map<string, OnboardingFlow> = new Map();

  static getInstance(): SubscriptionService {
    if (!SubscriptionService.instance) {
      SubscriptionService.instance = new SubscriptionService();
      SubscriptionService.instance.initializeDefaultPlans();
    }
    return SubscriptionService.instance;
  }

  /**
   * SUBSCRIPTION PLANS MANAGEMENT
   */
  private initializeDefaultPlans(): void {
    const plans: SubscriptionPlan[] = [
      {
        id: 'free',
        tier: SubscriptionTier.FREE,
        name: 'Free Starter',
        description: 'Perfect for getting started with tender discovery',
        price: { monthly: 0, yearly: 0, currency: 'ZAR' },
        features: [
          { id: 'tender_notifications', name: 'Basic Tender Notifications', description: 'Get notified of new tenders', included: true, limit: 10 },
          { id: 'tender_search', name: 'Tender Search', description: 'Search available tenders', included: true },
          { id: 'basic_analysis', name: 'Basic Analysis', description: 'Simple tender analysis', included: true, limit: 3 }
        ],
        limits: {
          monthlyBids: 3,
          tenderAlerts: 10,
          ecosystemConnections: 1,
          complianceProtests: 0,
          teamMembers: 1,
          apiCalls: 100,
          storageGB: 1,
          supportLevel: 'community'
        },
        popular: false,
        businessFocused: false,
        complianceIncluded: false
      },
      {
        id: 'individual',
        tier: SubscriptionTier.BASIC,
        name: 'Individual Pro',
        description: 'Complete solution for individual contractors and small businesses',
        price: { monthly: 299, yearly: 2990, currency: 'ZAR' },
        features: [
          { id: 'unlimited_notifications', name: 'Unlimited Notifications', description: 'All tender notifications', included: true },
          { id: 'ai_analysis', name: 'AI-Powered Analysis', description: 'Advanced AI tender analysis', included: true },
          { id: 'bee_workers', name: 'Bee Worker Tasks', description: 'Assign tasks to bee workers', included: true, limit: 20 },
          { id: 'document_management', name: 'Document Management', description: 'Advanced document handling', included: true },
          { id: 'interested_in_bid', name: 'INTERESTED IN BID Workflow', description: 'Psychological commitment funnel', included: true }
        ],
        limits: {
          monthlyBids: 25,
          tenderAlerts: 999999,
          ecosystemConnections: 5,
          complianceProtests: 5,
          teamMembers: 1,
          apiCalls: 1000,
          storageGB: 10,
          supportLevel: 'email'
        },
        popular: true,
        businessFocused: false,
        complianceIncluded: false
      },
      {
        id: 'professional',
        tier: SubscriptionTier.PROFESSIONAL,
        name: 'Team Professional',
        description: 'Advanced collaboration for small to medium teams',
        price: { monthly: 999, yearly: 9990, currency: 'ZAR' },
        features: [
          { id: 'team_collaboration', name: 'Team Collaboration', description: 'Multi-user workspace and collaboration', included: true },
          { id: 'role_based_access', name: 'Role-Based Access', description: 'Team roles and permissions', included: true },
          { id: 'approval_workflows', name: 'Approval Workflows', description: 'Multi-step approval processes', included: true },
          { id: 'team_analytics', name: 'Team Analytics', description: 'Team performance metrics', included: true },
          { id: 'advanced_bee_workers', name: 'Advanced Bee Workers', description: 'Team-based bee worker coordination', included: true, limit: 100 }
        ],
        limits: {
          monthlyBids: 100,
          tenderAlerts: 999999,
          ecosystemConnections: 15,
          complianceProtests: 20,
          teamMembers: 10,
          apiCalls: 5000,
          storageGB: 50,
          supportLevel: 'priority'
        },
        popular: true,
        businessFocused: true,
        complianceIncluded: true
      },
      {
        id: 'enterprise',
        tier: SubscriptionTier.ENTERPRISE,
        name: 'Enterprise',
        description: 'Complete solution for large organizations with unlimited features',
        price: { monthly: 2999, yearly: 29990, currency: 'ZAR' },
        features: [
          { id: 'unlimited_everything', name: 'Unlimited Features', description: 'All features with no limits', included: true },
          { id: 'custom_integrations', name: 'Custom Integrations', description: 'API access and custom integrations', included: true },
          { id: 'dedicated_support', name: 'Dedicated Support', description: '24/7 dedicated support team', included: true },
          { id: 'white_label', name: 'White Label Options', description: 'Custom branding and white-label solutions', included: true },
          { id: 'advanced_compliance', name: 'Advanced Compliance', description: 'Enterprise-grade compliance tools', included: true }
        ],
        limits: {
          monthlyBids: 999999,
          tenderAlerts: 999999,
          ecosystemConnections: 999999,
          complianceProtests: 999999,
          teamMembers: 999999,
          apiCalls: 999999,
          storageGB: 999999,
          supportLevel: 'dedicated'
        },
        popular: false,
        businessFocused: true,
        complianceIncluded: true
      }
    ];

    plans.forEach(plan => this.subscriptionPlans.set(plan.id, plan));
    console.log(`📋 Initialized ${plans.length} subscription plans`);
  }

  async getSubscriptionPlans(): Promise<SubscriptionPlan[]> {
    return Array.from(this.subscriptionPlans.values());
  }

  async getSubscriptionPlan(planId: string): Promise<SubscriptionPlan | null> {
    return this.subscriptionPlans.get(planId) || null;
  }

  /**
   * TIER RECOMMENDATION ENGINE
   */
  async recommendTier(selectionData: TierSelectionData): Promise<TierRecommendation> {
    console.log(`🎯 Analyzing tier recommendation for organization type: ${selectionData.organizationType}`);

    let recommendedTier: SubscriptionTier;
    let confidence: number;
    let reasons: string[] = [];

    // Analyze organization type
    if (selectionData.organizationType === 'individual_contractor') {
      if (selectionData.monthlyTenderVolume <= 10) {
        recommendedTier = SubscriptionTier.BASIC;
        confidence = 85;
        reasons.push('Individual contractor with moderate tender volume');
      } else {
        recommendedTier = SubscriptionTier.PROFESSIONAL;
        confidence = 75;
        reasons.push('High tender volume requires advanced features');
      }
    } else if (['small_business', 'consulting_firm'].includes(selectionData.organizationType)) {
      if (selectionData.teamSize <= 5) {
        recommendedTier = SubscriptionTier.PROFESSIONAL;
        confidence = 90;
        reasons.push('Small team benefits from collaboration features');
      } else {
        recommendedTier = SubscriptionTier.ENTERPRISE;
        confidence = 80;
        reasons.push('Larger team requires enterprise features');
      }
    } else if (['medium_enterprise', 'large_corporation', 'engineering_firm'].includes(selectionData.organizationType)) {
      recommendedTier = SubscriptionTier.ENTERPRISE;
      confidence = 95;
      reasons.push('Enterprise organization requires full feature set');
    } else {
      recommendedTier = SubscriptionTier.PROFESSIONAL;
      confidence = 70;
      reasons.push('Professional tier suitable for most organizations');
    }

    // Adjust based on budget
    if (selectionData.budget.max < 500) {
      if (recommendedTier === SubscriptionTier.ENTERPRISE) {
        recommendedTier = SubscriptionTier.PROFESSIONAL;
        confidence -= 10;
        reasons.push('Budget constraints suggest Professional tier');
      }
    }

    // Adjust based on team size
    if (selectionData.teamSize > 20 && recommendedTier !== SubscriptionTier.ENTERPRISE) {
      recommendedTier = SubscriptionTier.ENTERPRISE;
      confidence += 15;
      reasons.push('Large team size requires Enterprise features');
    }

    const plan = await this.getSubscriptionPlan(recommendedTier);
    const estimatedMonthlyCost = plan?.price.monthly || 0;
    const estimatedAnnualSavings = plan ? (plan.price.monthly * 12 - plan.price.yearly) : 0;

    return {
      recommendedTier,
      confidence,
      reasons,
      alternativeTiers: await this.getAlternativeTiers(recommendedTier, selectionData),
      estimatedMonthlyCost,
      estimatedAnnualSavings,
      features: await this.getRecommendedFeatures(recommendedTier, selectionData)
    };
  }

  private async getAlternativeTiers(recommendedTier: SubscriptionTier, selectionData: TierSelectionData) {
    const alternatives = [];
    const plans = await this.getSubscriptionPlans();
    const recommendedPlan = plans.find(p => p.tier === recommendedTier);

    for (const plan of plans) {
      if (plan.tier !== recommendedTier) {
        alternatives.push({
          tier: plan.tier,
          reason: this.getAlternativeReason(plan.tier, recommendedTier),
          costDifference: plan.price.monthly - (recommendedPlan?.price.monthly || 0),
          featureDifferences: this.getFeatureDifferences(plan.tier, recommendedTier)
        });
      }
    }

    return alternatives.slice(0, 2); // Return top 2 alternatives
  }

  private getAlternativeReason(tier: SubscriptionTier, recommended: SubscriptionTier): string {
    if (tier === SubscriptionTier.FREE) return 'Start with free tier to test the platform';
    if (tier === SubscriptionTier.BASIC) return 'Individual solution without team features';
    if (tier === SubscriptionTier.PROFESSIONAL) return 'Team collaboration with moderate limits';
    if (tier === SubscriptionTier.ENTERPRISE) return 'Unlimited features for large organizations';
    return 'Alternative option to consider';
  }

  private getFeatureDifferences(tier: SubscriptionTier, recommended: SubscriptionTier): string[] {
    // Simplified feature differences - in real implementation, this would be more comprehensive
    const differences = [];
    if (tier === SubscriptionTier.FREE) differences.push('Limited features', 'No team collaboration');
    if (tier === SubscriptionTier.ENTERPRISE) differences.push('Unlimited everything', 'Custom integrations');
    return differences;
  }

  private async getRecommendedFeatures(tier: SubscriptionTier, selectionData: TierSelectionData) {
    const plan = await this.getSubscriptionPlan(tier);
    if (!plan) return [];

    return plan.features.map(feature => ({
      featureId: feature.id,
      name: feature.name,
      importance: this.getFeatureImportance(feature.id, selectionData),
      reason: this.getFeatureReason(feature.id, selectionData),
      available: feature.included
    }));
  }

  private getFeatureImportance(featureId: string, selectionData: TierSelectionData): 'high' | 'medium' | 'low' {
    if (selectionData.teamSize > 1 && featureId.includes('team')) return 'high';
    if (selectionData.monthlyTenderVolume > 20 && featureId.includes('ai')) return 'high';
    if (selectionData.primaryUseCase.includes('compliance') && featureId.includes('compliance')) return 'high';
    return 'medium';
  }

  private getFeatureReason(featureId: string, selectionData: TierSelectionData): string {
    if (featureId.includes('team') && selectionData.teamSize > 1) {
      return `Essential for your team of ${selectionData.teamSize} members`;
    }
    if (featureId.includes('ai') && selectionData.monthlyTenderVolume > 10) {
      return `Important for handling ${selectionData.monthlyTenderVolume} tenders per month`;
    }
    return 'Recommended for your use case';
  }

  /**
   * ONBOARDING MANAGEMENT
   */
  async createOnboardingFlow(userId: string): Promise<OnboardingFlow> {
    const onboardingFlow: OnboardingFlow = {
      id: `onboarding-${Date.now()}`,
      userId,
      currentStep: 0,
      totalSteps: 6,
      steps: [
        {
          id: 'welcome',
          name: 'welcome',
          title: 'Welcome to BidBeez',
          description: 'Get started with the most advanced tender management platform',
          component: 'WelcomeStep',
          required: true,
          completed: false
        },
        {
          id: 'organization_type',
          name: 'organization_type',
          title: 'Tell us about your organization',
          description: 'Help us understand your business to recommend the best plan',
          component: 'OrganizationTypeStep',
          required: true,
          completed: false
        },
        {
          id: 'tier_selection',
          name: 'tier_selection',
          title: 'Choose your tier',
          description: 'Select between Individual or Team collaboration',
          component: 'TierSelectionStep',
          required: true,
          completed: false
        },
        {
          id: 'plan_selection',
          name: 'plan_selection',
          title: 'Select your plan',
          description: 'Choose the plan that fits your needs and budget',
          component: 'PlanSelectionStep',
          required: true,
          completed: false
        },
        {
          id: 'payment',
          name: 'payment',
          title: 'Payment setup',
          description: 'Set up your payment method and start your subscription',
          component: 'PaymentStep',
          required: false,
          completed: false
        },
        {
          id: 'completion',
          name: 'completion',
          title: 'You\'re all set!',
          description: 'Welcome to BidBeez - start discovering tender opportunities',
          component: 'CompletionStep',
          required: true,
          completed: false
        }
      ],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    this.onboardingFlows.set(onboardingFlow.id, onboardingFlow);
    console.log(`📋 Created onboarding flow for user ${userId}`);
    return onboardingFlow;
  }

  async updateOnboardingStep(
    onboardingId: string, 
    stepId: string, 
    data: Record<string, any>
  ): Promise<OnboardingFlow> {
    const flow = this.onboardingFlows.get(onboardingId);
    if (!flow) {
      throw new Error('Onboarding flow not found');
    }

    const step = flow.steps.find(s => s.id === stepId);
    if (!step) {
      throw new Error('Onboarding step not found');
    }

    step.data = { ...step.data, ...data };
    step.completed = true;
    step.completedAt = new Date().toISOString();

    // Update flow-level data
    if (stepId === 'tier_selection') {
      flow.selectedTier = data.selectedTier;
    }
    if (stepId === 'plan_selection') {
      flow.selectedPlan = data.selectedPlan;
    }
    if (stepId === 'organization_type') {
      flow.organizationType = data.organizationType;
      flow.teamSize = data.teamSize;
      flow.industry = data.industry;
    }

    // Move to next step
    const currentStepIndex = flow.steps.findIndex(s => s.id === stepId);
    if (currentStepIndex < flow.steps.length - 1) {
      flow.currentStep = currentStepIndex + 1;
    }

    flow.updatedAt = new Date().toISOString();
    this.onboardingFlows.set(onboardingId, flow);

    console.log(`✅ Updated onboarding step ${stepId} for flow ${onboardingId}`);
    return flow;
  }

  async getOnboardingFlow(onboardingId: string): Promise<OnboardingFlow | null> {
    return this.onboardingFlows.get(onboardingId) || null;
  }

  async getUserOnboardingFlow(userId: string): Promise<OnboardingFlow | null> {
    for (const flow of this.onboardingFlows.values()) {
      if (flow.userId === userId && !flow.completedAt) {
        return flow;
      }
    }
    return null;
  }

  /**
   * FEATURE ACCESS CONTROL
   */
  async checkFeatureAccess(userId: string, featureId: string): Promise<FeatureGate> {
    const subscription = this.userSubscriptions.get(userId);
    if (!subscription) {
      return {
        feature: featureId,
        enabled: false,
        reason: 'No active subscription',
        upgradeRequired: true,
        requiredPlan: SubscriptionTier.BASIC
      };
    }

    const plan = await this.getSubscriptionPlan(subscription.planId);
    if (!plan) {
      return {
        feature: featureId,
        enabled: false,
        reason: 'Invalid subscription plan',
        upgradeRequired: true
      };
    }

    const feature = plan.features.find(f => f.id === featureId);
    if (!feature || !feature.included) {
      return {
        feature: featureId,
        enabled: false,
        reason: 'Feature not included in current plan',
        upgradeRequired: true,
        requiredPlan: this.getRequiredPlanForFeature(featureId)
      };
    }

    // Check usage limits
    if (feature.limit && feature.limit > 0) {
      const usage = await this.getFeatureUsage(userId, featureId);
      if (usage.used >= feature.limit) {
        return {
          feature: featureId,
          enabled: false,
          reason: 'Feature usage limit reached',
          upgradeRequired: true
        };
      }
    }

    return {
      feature: featureId,
      enabled: true
    };
  }

  private getRequiredPlanForFeature(featureId: string): SubscriptionTier {
    // Simplified logic - in real implementation, this would be more comprehensive
    if (featureId.includes('team')) return SubscriptionTier.PROFESSIONAL;
    if (featureId.includes('enterprise')) return SubscriptionTier.ENTERPRISE;
    return SubscriptionTier.BASIC;
  }

  async getFeatureUsage(userId: string, featureId: string): Promise<FeatureUsage> {
    // Mock implementation - in real app, this would track actual usage
    return {
      feature: featureId,
      used: Math.floor(Math.random() * 10),
      limit: 25,
      percentage: 40,
      unlimited: false
    };
  }

  /**
   * SUBSCRIPTION MANAGEMENT
   */
  async createSubscription(
    userId: string, 
    planId: string, 
    paymentMethodId?: string
  ): Promise<UserSubscription> {
    const plan = await this.getSubscriptionPlan(planId);
    if (!plan) {
      throw new Error('Invalid subscription plan');
    }

    const subscription: UserSubscription = {
      id: `sub-${Date.now()}`,
      userId,
      planId,
      tier: plan.tier,
      status: 'active',
      billingCycle: 'monthly',
      currentPeriodStart: new Date().toISOString(),
      currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      trialStart: plan.tier === SubscriptionTier.FREE ? undefined : new Date().toISOString(),
      trialEnd: plan.tier === SubscriptionTier.FREE ? undefined : new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
      cancelAtPeriodEnd: false,
      usage: {
        monthlyBids: 0,
        tenderAlerts: 0,
        ecosystemConnections: 0,
        complianceProtests: 0,
        teamMembers: 1,
        apiCalls: 0,
        storageGB: 0
      },
      billing: {
        customerId: `cus-${Date.now()}`,
        subscriptionId: `sub-${Date.now()}`,
        invoicePrefix: 'BB',
        taxRate: 0.15,
        currency: 'ZAR',
        billingAddress: {
          line1: '',
          city: '',
          state: '',
          postalCode: '',
          country: 'ZA'
        },
        nextBillingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        totalPaid: 0
      },
      paymentMethods: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    this.userSubscriptions.set(userId, subscription);
    console.log(`💳 Created subscription for user ${userId} with plan ${planId}`);
    return subscription;
  }

  async getUserSubscription(userId: string): Promise<UserSubscription | null> {
    return this.userSubscriptions.get(userId) || null;
  }

  async updateSubscription(
    userId: string, 
    updates: Partial<UserSubscription>
  ): Promise<UserSubscription> {
    const subscription = this.userSubscriptions.get(userId);
    if (!subscription) {
      throw new Error('Subscription not found');
    }

    const updatedSubscription = { ...subscription, ...updates, updatedAt: new Date().toISOString() };
    this.userSubscriptions.set(userId, updatedSubscription);
    
    console.log(`📝 Updated subscription for user ${userId}`);
    return updatedSubscription;
  }
}

export default SubscriptionService;
