/**
 * Bid Workflow Service
 * Manages the complete "INTERESTED IN BID" workflow system
 */

import { 
  BidWorkflowState, 
  BidInterest, 
  TenderNotification, 
  ActiveBidWorkspace,
  BidWorkflowAction,
  EnhancedNotification
} from '../types/bidWorkflow';

class BidWorkflowService {
  private static instance: BidWorkflowService;
  private activeBids: Map<string, ActiveBidWorkspace> = new Map();
  private bidInterests: Map<string, BidInterest> = new Map();
  private notifications: Map<string, EnhancedNotification> = new Map();

  static getInstance(): BidWorkflowService {
    if (!BidWorkflowService.instance) {
      BidWorkflowService.instance = new BidWorkflowService();
    }
    return BidWorkflowService.instance;
  }

  /**
   * Express interest in a bid - moves from notifications to active workspace
   */
  async expressInterest(tenderId: string, userId: string): Promise<ActiveBidWorkspace> {
    console.log(`🎯 User ${userId} expressing interest in tender ${tenderId}`);

    // Get the notification
    const notification = this.notifications.get(tenderId);
    if (!notification) {
      throw new Error('Tender notification not found');
    }

    // Create bid interest record
    const bidInterest: BidInterest = {
      id: `interest-${Date.now()}`,
      tenderId,
      userId,
      state: BidWorkflowState.INTERESTED,
      interestedAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString(),
      commitmentLevel: 25, // Initial commitment level
      analysisCompleted: false,
      beeWorkersAssigned: [],
      notes: '',
      metadata: {}
    };

    // Store bid interest
    this.bidInterests.set(bidInterest.id, bidInterest);

    // Update notification status
    notification.status = 'interested';
    this.notifications.set(tenderId, notification);

    // Create active workspace
    const workspace = await this.createActiveWorkspace(bidInterest, notification);
    this.activeBids.set(bidInterest.id, workspace);

    // Start AI analysis
    this.startAIAnalysis(workspace);

    console.log(`✅ Created active workspace for tender ${tenderId}`);
    return workspace;
  }

  /**
   * Get all active bid workspaces for a user
   */
  async getActiveBids(userId: string): Promise<ActiveBidWorkspace[]> {
    const userBids: ActiveBidWorkspace[] = [];
    
    for (const workspace of this.activeBids.values()) {
      if (workspace.bidInterest.userId === userId) {
        userBids.push(workspace);
      }
    }

    return userBids.sort((a, b) => 
      new Date(b.bidInterest.lastUpdated).getTime() - 
      new Date(a.bidInterest.lastUpdated).getTime()
    );
  }

  /**
   * Get notifications for a user (excluding interested bids)
   */
  async getNotifications(userId: string): Promise<EnhancedNotification[]> {
    const userNotifications: EnhancedNotification[] = [];
    
    for (const notification of this.notifications.values()) {
      if (notification.status !== 'interested') {
        userNotifications.push(notification);
      }
    }

    return userNotifications.sort((a, b) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
  }

  /**
   * Update bid workflow state
   */
  async updateBidState(
    bidInterestId: string, 
    newState: BidWorkflowState, 
    metadata?: Record<string, any>
  ): Promise<void> {
    const bidInterest = this.bidInterests.get(bidInterestId);
    if (!bidInterest) {
      throw new Error('Bid interest not found');
    }

    const oldState = bidInterest.state;
    bidInterest.state = newState;
    bidInterest.lastUpdated = new Date().toISOString();
    
    // Update commitment level based on state progression
    bidInterest.commitmentLevel = this.calculateCommitmentLevel(newState);
    
    if (metadata) {
      bidInterest.metadata = { ...bidInterest.metadata, ...metadata };
    }

    this.bidInterests.set(bidInterestId, bidInterest);

    // Update workspace
    const workspace = this.activeBids.get(bidInterestId);
    if (workspace) {
      workspace.bidInterest = bidInterest;
      this.activeBids.set(bidInterestId, workspace);
    }

    console.log(`🔄 Updated bid ${bidInterestId} from ${oldState} to ${newState}`);
  }

  /**
   * Assign bee workers to a bid
   */
  async assignBeeWorkers(
    bidInterestId: string, 
    beeWorkerIds: string[], 
    tasks: string[]
  ): Promise<void> {
    const bidInterest = this.bidInterests.get(bidInterestId);
    if (!bidInterest) {
      throw new Error('Bid interest not found');
    }

    bidInterest.beeWorkersAssigned = [...bidInterest.beeWorkersAssigned, ...beeWorkerIds];
    bidInterest.lastUpdated = new Date().toISOString();
    bidInterest.commitmentLevel = Math.min(bidInterest.commitmentLevel + 20, 100);

    this.bidInterests.set(bidInterestId, bidInterest);

    // Update workspace with bee assignments
    const workspace = this.activeBids.get(bidInterestId);
    if (workspace) {
      // Add bee tasks to workspace
      workspace.bidInterest = bidInterest;
      this.activeBids.set(bidInterestId, workspace);
    }

    console.log(`🐝 Assigned ${beeWorkerIds.length} bee workers to bid ${bidInterestId}`);
  }

  /**
   * Create active workspace with all analysis components
   */
  private async createActiveWorkspace(
    bidInterest: BidInterest, 
    notification: EnhancedNotification
  ): Promise<ActiveBidWorkspace> {
    
    const workspace: ActiveBidWorkspace = {
      bidInterest,
      tender: notification,
      
      tenderAnalysis: {
        aiInsights: {
          keyRequirements: [],
          technicalSpecs: [],
          complianceItems: [],
          extractedData: {},
          confidence: 0
        },
        riskAssessment: {
          overallRisk: 'medium',
          riskFactors: [],
          mitigationStrategies: [],
          contingencyPlan: '',
          riskScore: 0
        },
        complianceStatus: {
          overallScore: 0,
          mandatoryItems: [],
          optionalItems: [],
          missingDocuments: [],
          riskLevel: 'medium',
          complianceGaps: []
        },
        competitiveAnalysis: {
          competitorCount: 0,
          threatLevel: 'medium',
          marketPosition: '',
          winProbability: 0,
          competitiveAdvantages: [],
          recommendations: []
        },
        feasibilityScore: 0,
        recommendedActions: []
      },
      
      governanceEngine: {
        complianceChecklist: [],
        riskMitigation: [],
        approvalWorkflow: [],
        governanceScore: 0
      },
      
      beeWorkerTasks: {
        documentCollection: [],
        siteVisits: [],
        complianceVerification: [],
        marketResearch: [],
        availableBees: []
      },
      
      documentWorkspace: {
        originalDocs: notification.documents,
        workingDrafts: [],
        submissionPackage: [],
        processingStatus: {
          totalDocuments: notification.documents.length,
          processed: 0,
          analyzing: 0,
          failed: 0,
          lastUpdate: new Date().toISOString()
        }
      },
      
      progressTracking: {
        overallProgress: 10, // Just started
        milestones: [],
        deadlines: [],
        criticalPath: []
      }
    };

    return workspace;
  }

  /**
   * Start AI analysis for a workspace
   */
  private async startAIAnalysis(workspace: ActiveBidWorkspace): Promise<void> {
    console.log(`🤖 Starting AI analysis for tender ${workspace.tender.tenderId}`);
    
    // Update state to analyzing
    await this.updateBidState(workspace.bidInterest.id, BidWorkflowState.ANALYZING);
    
    // Simulate AI analysis (in real implementation, this would call actual AI services)
    setTimeout(async () => {
      await this.completeAIAnalysis(workspace);
    }, 3000);
  }

  /**
   * Complete AI analysis and populate workspace
   */
  private async completeAIAnalysis(workspace: ActiveBidWorkspace): Promise<void> {
    console.log(`✅ Completing AI analysis for tender ${workspace.tender.tenderId}`);
    
    // Update analysis results (mock data for now)
    workspace.tenderAnalysis.feasibilityScore = 75;
    workspace.tenderAnalysis.aiInsights.confidence = 85;
    workspace.tenderAnalysis.riskAssessment.riskScore = 35;
    workspace.tenderAnalysis.complianceStatus.overallScore = 80;
    workspace.tenderAnalysis.competitiveAnalysis.winProbability = 68;
    
    workspace.bidInterest.analysisCompleted = true;
    workspace.progressTracking.overallProgress = 30;
    
    // Update state to preparing
    await this.updateBidState(workspace.bidInterest.id, BidWorkflowState.PREPARING);
    
    console.log(`🎯 AI analysis complete for tender ${workspace.tender.tenderId}`);
  }

  /**
   * Calculate commitment level based on workflow state
   */
  private calculateCommitmentLevel(state: BidWorkflowState): number {
    switch (state) {
      case BidWorkflowState.DISCOVERED: return 0;
      case BidWorkflowState.INTERESTED: return 25;
      case BidWorkflowState.ANALYZING: return 40;
      case BidWorkflowState.PREPARING: return 60;
      case BidWorkflowState.READY: return 85;
      case BidWorkflowState.SUBMITTED: return 100;
      default: return 0;
    }
  }

  /**
   * Add a new tender notification
   */
  async addNotification(notification: TenderNotification): Promise<EnhancedNotification> {
    const enhanced: EnhancedNotification = {
      ...notification,
      psychologicalTriggers: {
        scarcity: `Only ${Math.floor(Math.random() * 5) + 1} days left to submit!`,
        urgency: `${Math.floor(Math.random() * 10) + 5} competitors already viewing`,
        social_proof: `${Math.floor(Math.random() * 20) + 10} similar bids won this month`,
        authority: `Government tender - verified opportunity`,
        commitment: `${notification.matchScore}% match with your profile`
      },
      actionButtons: {
        primary: 'INTERESTED_IN_BID',
        secondary: 'REMIND_LATER'
      }
    };

    this.notifications.set(notification.tenderId, enhanced);
    return enhanced;
  }

  /**
   * Dismiss a notification
   */
  async dismissNotification(tenderId: string): Promise<void> {
    const notification = this.notifications.get(tenderId);
    if (notification) {
      notification.status = 'dismissed';
      this.notifications.set(tenderId, notification);
    }
  }
}

export default BidWorkflowService;
