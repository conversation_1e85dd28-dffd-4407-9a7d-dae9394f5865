/**
 * Feature Access Control Service
 * Manages feature gating based on subscription tiers and usage limits
 */

import { FeatureGate, FeatureUsage, SubscriptionTier } from '../types/subscription';
import SubscriptionService from './SubscriptionService';

interface FeatureDefinition {
  id: string;
  name: string;
  description: string;
  category: string;
  requiredTier: SubscriptionTier;
  hasUsageLimit: boolean;
  defaultLimit?: number;
  resetPeriod?: 'daily' | 'monthly' | 'yearly';
}

class FeatureAccessService {
  private static instance: FeatureAccessService;
  private featureDefinitions: Map<string, FeatureDefinition> = new Map();
  private userUsage: Map<string, Map<string, FeatureUsage>> = new Map(); // userId -> featureId -> usage

  static getInstance(): FeatureAccessService {
    if (!FeatureAccessService.instance) {
      FeatureAccessService.instance = new FeatureAccessService();
      FeatureAccessService.instance.initializeFeatureDefinitions();
    }
    return FeatureAccessService.instance;
  }

  private initializeFeatureDefinitions(): void {
    const features: FeatureDefinition[] = [
      // Core Features
      {
        id: 'tender_notifications',
        name: 'Tender Notifications',
        description: 'Receive tender notifications',
        category: 'core',
        requiredTier: SubscriptionTier.FREE,
        hasUsageLimit: true,
        defaultLimit: 10,
        resetPeriod: 'monthly'
      },
      {
        id: 'tender_search',
        name: 'Tender Search',
        description: 'Search available tenders',
        category: 'core',
        requiredTier: SubscriptionTier.FREE,
        hasUsageLimit: false
      },
      {
        id: 'interested_in_bid',
        name: 'INTERESTED IN BID Workflow',
        description: 'Psychological commitment funnel',
        category: 'workflow',
        requiredTier: SubscriptionTier.BASIC,
        hasUsageLimit: false
      },

      // AI Features
      {
        id: 'ai_analysis',
        name: 'AI-Powered Analysis',
        description: 'Advanced AI tender analysis',
        category: 'ai',
        requiredTier: SubscriptionTier.BASIC,
        hasUsageLimit: true,
        defaultLimit: 25,
        resetPeriod: 'monthly'
      },
      {
        id: 'ai_insights',
        name: 'AI Market Insights',
        description: 'AI-powered market intelligence',
        category: 'ai',
        requiredTier: SubscriptionTier.PROFESSIONAL,
        hasUsageLimit: true,
        defaultLimit: 50,
        resetPeriod: 'monthly'
      },

      // Team Features
      {
        id: 'team_collaboration',
        name: 'Team Collaboration',
        description: 'Multi-user workspace and collaboration',
        category: 'team',
        requiredTier: SubscriptionTier.PROFESSIONAL,
        hasUsageLimit: false
      },
      {
        id: 'role_based_access',
        name: 'Role-Based Access Control',
        description: 'Team roles and permissions',
        category: 'team',
        requiredTier: SubscriptionTier.PROFESSIONAL,
        hasUsageLimit: false
      },
      {
        id: 'approval_workflows',
        name: 'Approval Workflows',
        description: 'Multi-step approval processes',
        category: 'team',
        requiredTier: SubscriptionTier.PROFESSIONAL,
        hasUsageLimit: false
      },
      {
        id: 'team_analytics',
        name: 'Team Analytics',
        description: 'Team performance metrics',
        category: 'analytics',
        requiredTier: SubscriptionTier.PROFESSIONAL,
        hasUsageLimit: false
      },

      // Bee Worker Features
      {
        id: 'bee_workers',
        name: 'Bee Worker Tasks',
        description: 'Assign tasks to bee workers',
        category: 'bee_workers',
        requiredTier: SubscriptionTier.BASIC,
        hasUsageLimit: true,
        defaultLimit: 20,
        resetPeriod: 'monthly'
      },
      {
        id: 'advanced_bee_workers',
        name: 'Advanced Bee Workers',
        description: 'Team-based bee worker coordination',
        category: 'bee_workers',
        requiredTier: SubscriptionTier.PROFESSIONAL,
        hasUsageLimit: true,
        defaultLimit: 100,
        resetPeriod: 'monthly'
      },

      // Document Features
      {
        id: 'document_management',
        name: 'Document Management',
        description: 'Advanced document handling',
        category: 'documents',
        requiredTier: SubscriptionTier.BASIC,
        hasUsageLimit: false
      },
      {
        id: 'collaborative_documents',
        name: 'Collaborative Documents',
        description: 'Real-time collaborative editing',
        category: 'documents',
        requiredTier: SubscriptionTier.PROFESSIONAL,
        hasUsageLimit: false
      },

      // Compliance Features
      {
        id: 'compliance_basic',
        name: 'Basic Compliance',
        description: 'Basic compliance checking',
        category: 'compliance',
        requiredTier: SubscriptionTier.BASIC,
        hasUsageLimit: false
      },
      {
        id: 'compliance_advanced',
        name: 'Advanced Compliance',
        description: 'Enterprise-grade compliance tools',
        category: 'compliance',
        requiredTier: SubscriptionTier.ENTERPRISE,
        hasUsageLimit: false
      },

      // Integration Features
      {
        id: 'api_access',
        name: 'API Access',
        description: 'REST API access for integrations',
        category: 'integrations',
        requiredTier: SubscriptionTier.PROFESSIONAL,
        hasUsageLimit: true,
        defaultLimit: 5000,
        resetPeriod: 'monthly'
      },
      {
        id: 'custom_integrations',
        name: 'Custom Integrations',
        description: 'Custom API integrations and webhooks',
        category: 'integrations',
        requiredTier: SubscriptionTier.ENTERPRISE,
        hasUsageLimit: false
      },

      // Support Features
      {
        id: 'email_support',
        name: 'Email Support',
        description: 'Email customer support',
        category: 'support',
        requiredTier: SubscriptionTier.BASIC,
        hasUsageLimit: false
      },
      {
        id: 'priority_support',
        name: 'Priority Support',
        description: 'Priority customer support',
        category: 'support',
        requiredTier: SubscriptionTier.PROFESSIONAL,
        hasUsageLimit: false
      },
      {
        id: 'dedicated_support',
        name: 'Dedicated Support',
        description: '24/7 dedicated support team',
        category: 'support',
        requiredTier: SubscriptionTier.ENTERPRISE,
        hasUsageLimit: false
      }
    ];

    features.forEach(feature => this.featureDefinitions.set(feature.id, feature));
    console.log(`🔐 Initialized ${features.length} feature definitions`);
  }

  /**
   * Check if user has access to a specific feature
   */
  async checkFeatureAccess(userId: string, featureId: string): Promise<FeatureGate> {
    const subscriptionService = SubscriptionService.getInstance();
    
    // Get feature definition
    const featureDefinition = this.featureDefinitions.get(featureId);
    if (!featureDefinition) {
      return {
        feature: featureId,
        enabled: false,
        reason: 'Feature not found',
        upgradeRequired: false
      };
    }

    // Get user subscription
    const subscription = await subscriptionService.getUserSubscription(userId);
    if (!subscription) {
      return {
        feature: featureId,
        enabled: false,
        reason: 'No active subscription',
        upgradeRequired: true,
        requiredPlan: featureDefinition.requiredTier
      };
    }

    // Check tier requirement
    if (!this.hasTierAccess(subscription.tier, featureDefinition.requiredTier)) {
      return {
        feature: featureId,
        enabled: false,
        reason: `Requires ${featureDefinition.requiredTier} tier or higher`,
        upgradeRequired: true,
        requiredPlan: featureDefinition.requiredTier
      };
    }

    // Check usage limits
    if (featureDefinition.hasUsageLimit) {
      const usage = await this.getFeatureUsage(userId, featureId);
      const limit = this.getFeatureLimit(subscription.tier, featureDefinition);
      
      if (limit > 0 && usage.used >= limit) {
        return {
          feature: featureId,
          enabled: false,
          reason: 'Usage limit reached for current billing period',
          upgradeRequired: true
        };
      }
    }

    return {
      feature: featureId,
      enabled: true
    };
  }

  /**
   * Get feature usage for a user
   */
  async getFeatureUsage(userId: string, featureId: string): Promise<FeatureUsage> {
    const userUsageMap = this.userUsage.get(userId) || new Map();
    const existingUsage = userUsageMap.get(featureId);

    if (existingUsage) {
      return existingUsage;
    }

    // Initialize usage if not exists
    const featureDefinition = this.featureDefinitions.get(featureId);
    const subscriptionService = SubscriptionService.getInstance();
    const subscription = await subscriptionService.getUserSubscription(userId);
    
    const limit = featureDefinition && subscription 
      ? this.getFeatureLimit(subscription.tier, featureDefinition)
      : 0;

    const usage: FeatureUsage = {
      feature: featureId,
      used: 0,
      limit,
      percentage: 0,
      unlimited: limit === 0 || limit >= 999999
    };

    userUsageMap.set(featureId, usage);
    this.userUsage.set(userId, userUsageMap);

    return usage;
  }

  /**
   * Record feature usage
   */
  async recordFeatureUsage(userId: string, featureId: string, amount: number = 1): Promise<void> {
    const usage = await this.getFeatureUsage(userId, featureId);
    
    usage.used += amount;
    usage.percentage = usage.unlimited ? 0 : Math.min((usage.used / usage.limit) * 100, 100);

    const userUsageMap = this.userUsage.get(userId) || new Map();
    userUsageMap.set(featureId, usage);
    this.userUsage.set(userId, userUsageMap);

    console.log(`📊 Recorded usage for ${featureId}: ${usage.used}/${usage.limit}`);
  }

  /**
   * Get all feature access for a user
   */
  async getUserFeatureAccess(userId: string): Promise<FeatureGate[]> {
    const features: FeatureGate[] = [];
    
    for (const featureId of this.featureDefinitions.keys()) {
      const access = await this.checkFeatureAccess(userId, featureId);
      features.push(access);
    }

    return features;
  }

  /**
   * Get features by category
   */
  getFeaturesByCategory(category: string): FeatureDefinition[] {
    return Array.from(this.featureDefinitions.values())
      .filter(feature => feature.category === category);
  }

  /**
   * Get all feature categories
   */
  getFeatureCategories(): string[] {
    const categories = new Set<string>();
    this.featureDefinitions.forEach(feature => categories.add(feature.category));
    return Array.from(categories);
  }

  /**
   * Helper: Check if user tier has access to required tier
   */
  private hasTierAccess(userTier: SubscriptionTier, requiredTier: SubscriptionTier): boolean {
    const tierHierarchy = {
      [SubscriptionTier.FREE]: 0,
      [SubscriptionTier.BASIC]: 1,
      [SubscriptionTier.PROFESSIONAL]: 2,
      [SubscriptionTier.ENTERPRISE]: 3,
      [SubscriptionTier.COMPLIANCE_PRO]: 3
    };

    return tierHierarchy[userTier] >= tierHierarchy[requiredTier];
  }

  /**
   * Helper: Get feature limit based on tier and feature definition
   */
  private getFeatureLimit(tier: SubscriptionTier, feature: FeatureDefinition): number {
    if (!feature.hasUsageLimit) return 0; // No limit

    // Tier-specific limits (could be moved to configuration)
    const tierMultipliers = {
      [SubscriptionTier.FREE]: 1,
      [SubscriptionTier.BASIC]: 2,
      [SubscriptionTier.PROFESSIONAL]: 5,
      [SubscriptionTier.ENTERPRISE]: 999999, // Unlimited
      [SubscriptionTier.COMPLIANCE_PRO]: 999999
    };

    const baseLimit = feature.defaultLimit || 0;
    const multiplier = tierMultipliers[tier] || 1;
    
    return baseLimit * multiplier;
  }

  /**
   * Reset usage for a specific period
   */
  async resetUsageForPeriod(period: 'daily' | 'monthly' | 'yearly'): Promise<void> {
    console.log(`🔄 Resetting ${period} usage limits`);
    
    for (const [userId, userUsageMap] of this.userUsage.entries()) {
      for (const [featureId, usage] of userUsageMap.entries()) {
        const featureDefinition = this.featureDefinitions.get(featureId);
        
        if (featureDefinition?.resetPeriod === period) {
          usage.used = 0;
          usage.percentage = 0;
          userUsageMap.set(featureId, usage);
        }
      }
      this.userUsage.set(userId, userUsageMap);
    }
  }

  /**
   * Get upgrade recommendations based on usage patterns
   */
  async getUpgradeRecommendations(userId: string): Promise<{
    shouldUpgrade: boolean;
    reasons: string[];
    recommendedTier: SubscriptionTier;
    blockedFeatures: string[];
  }> {
    const subscriptionService = SubscriptionService.getInstance();
    const subscription = await subscriptionService.getUserSubscription(userId);
    
    if (!subscription) {
      return {
        shouldUpgrade: true,
        reasons: ['No active subscription'],
        recommendedTier: SubscriptionTier.BASIC,
        blockedFeatures: []
      };
    }

    const reasons: string[] = [];
    const blockedFeatures: string[] = [];
    let recommendedTier = subscription.tier;

    // Check for features hitting limits
    const userUsageMap = this.userUsage.get(userId) || new Map();
    
    for (const [featureId, usage] of userUsageMap.entries()) {
      if (usage.percentage >= 80) {
        reasons.push(`${featureId} usage is at ${Math.round(usage.percentage)}%`);
      }
      if (usage.percentage >= 100) {
        blockedFeatures.push(featureId);
      }
    }

    // Check for blocked higher-tier features
    for (const feature of this.featureDefinitions.values()) {
      if (!this.hasTierAccess(subscription.tier, feature.requiredTier)) {
        const access = await this.checkFeatureAccess(userId, feature.id);
        if (!access.enabled && access.upgradeRequired) {
          blockedFeatures.push(feature.id);
          
          // Suggest upgrade to required tier
          if (this.hasTierAccess(feature.requiredTier, recommendedTier)) {
            recommendedTier = feature.requiredTier;
          }
        }
      }
    }

    const shouldUpgrade = reasons.length > 0 || blockedFeatures.length > 0;

    return {
      shouldUpgrade,
      reasons,
      recommendedTier,
      blockedFeatures
    };
  }
}

export default FeatureAccessService;
