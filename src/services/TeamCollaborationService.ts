/**
 * Team Collaboration Service
 * Manages multi-user/enterprise team collaboration for tender management
 */

import {
  Organization,
  TeamMember,
  UserRole,
  TeamBidInterest,
  TeamActiveWorkspace,
  TeamTaskBoard,
  TeamTask,
  TeamDiscussion,
  TeamMeeting,
  ApprovalWorkflow,
  TeamDecision,
  CollaborativeDocument
} from '../types/teamCollaboration';
import { TenderNotification, BidWorkflowState } from '../types/bidWorkflow';
import BidWorkflowService from './BidWorkflowService';

class TeamCollaborationService {
  private static instance: TeamCollaborationService;
  private organizations: Map<string, Organization> = new Map();
  private teamMembers: Map<string, TeamMember[]> = new Map(); // organizationId -> members
  private teamBidInterests: Map<string, TeamBidInterest> = new Map();
  private teamWorkspaces: Map<string, TeamActiveWorkspace> = new Map();
  private teamTaskBoards: Map<string, TeamTaskBoard> = new Map();
  private teamDiscussions: Map<string, TeamDiscussion> = new Map();
  private teamMeetings: Map<string, TeamMeeting[]> = new Map();

  static getInstance(): TeamCollaborationService {
    if (!TeamCollaborationService.instance) {
      TeamCollaborationService.instance = new TeamCollaborationService();
    }
    return TeamCollaborationService.instance;
  }

  /**
   * ORGANIZATION MANAGEMENT
   */
  async createOrganization(orgData: Partial<Organization>): Promise<Organization> {
    const organization: Organization = {
      id: `org-${Date.now()}`,
      name: orgData.name || '',
      type: orgData.type || 'small_company',
      industry: orgData.industry || [],
      cidbGrade: orgData.cidbGrade || '',
      bbbeeLevel: orgData.bbbeeLevel || 8,
      registrationNumber: orgData.registrationNumber || '',
      taxNumber: orgData.taxNumber || '',
      address: orgData.address || {
        street: '',
        city: '',
        province: '',
        postalCode: '',
        country: 'South Africa'
      },
      contactInfo: orgData.contactInfo || {
        primaryEmail: '',
        primaryPhone: '',
        emergencyContact: {
          name: '',
          phone: '',
          email: '',
          relationship: ''
        }
      },
      settings: orgData.settings || {
        defaultWorkflowTemplate: 'standard',
        autoAssignBeeWorkers: false,
        requireApprovalForBids: true,
        notificationPreferences: {
          emailEnabled: true,
          smsEnabled: false,
          whatsappEnabled: true,
          pushEnabled: true,
          digestFrequency: 'daily'
        },
        collaborationSettings: {
          allowGuestAccess: false,
          requireApprovalForDocuments: true,
          enableRealTimeChat: true,
          enableVideoMeetings: true,
          documentVersionControl: true
        }
      },
      subscription: orgData.subscription || 'professional',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    this.organizations.set(organization.id, organization);
    this.teamMembers.set(organization.id, []);
    
    console.log(`🏢 Created organization: ${organization.name}`);
    return organization;
  }

  async getOrganization(organizationId: string): Promise<Organization | null> {
    return this.organizations.get(organizationId) || null;
  }

  /**
   * TEAM MEMBER MANAGEMENT
   */
  async addTeamMember(
    organizationId: string,
    userId: string,
    role: UserRole,
    memberData: Partial<TeamMember>
  ): Promise<TeamMember> {
    const teamMember: TeamMember = {
      id: `member-${Date.now()}`,
      userId,
      organizationId,
      role,
      permissions: this.getDefaultPermissions(role),
      department: memberData.department,
      title: memberData.title || '',
      specializations: memberData.specializations || [],
      availability: memberData.availability || {
        timezone: 'Africa/Johannesburg',
        workingHours: this.getDefaultWorkingHours(),
        unavailableDates: [],
        maxConcurrentBids: 5
      },
      workload: 'available',
      joinedAt: new Date().toISOString(),
      lastActive: new Date().toISOString(),
      status: 'active'
    };

    const members = this.teamMembers.get(organizationId) || [];
    members.push(teamMember);
    this.teamMembers.set(organizationId, members);

    console.log(`👥 Added team member: ${teamMember.title} (${role})`);
    return teamMember;
  }

  async getTeamMembers(organizationId: string): Promise<TeamMember[]> {
    return this.teamMembers.get(organizationId) || [];
  }

  async getTeamMembersByRole(organizationId: string, role: UserRole): Promise<TeamMember[]> {
    const members = await this.getTeamMembers(organizationId);
    return members.filter(member => member.role === role);
  }

  /**
   * TEAM BID INTEREST WORKFLOW
   */
  async expressTeamInterest(
    tenderId: string,
    organizationId: string,
    initiatedBy: string
  ): Promise<TeamActiveWorkspace> {
    console.log(`🎯 Team ${organizationId} expressing interest in tender ${tenderId}`);

    // Get organization and check if approval is required
    const organization = await this.getOrganization(organizationId);
    if (!organization) {
      throw new Error('Organization not found');
    }

    // Create team bid interest
    const teamBidInterest: TeamBidInterest = {
      id: `team-interest-${Date.now()}`,
      tenderId,
      userId: initiatedBy, // Keep for compatibility
      organizationId,
      state: organization.settings.requireApprovalForBids 
        ? BidWorkflowState.DISCOVERED 
        : BidWorkflowState.INTERESTED,
      interestedAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString(),
      commitmentLevel: 15, // Lower initial commitment for teams
      analysisCompleted: false,
      beeWorkersAssigned: [],
      notes: '',
      metadata: { initiatedBy },
      teamMembers: [],
      approvalWorkflow: await this.createApprovalWorkflow(organizationId, 'bid_interest'),
      collaborationStatus: {
        activeMembers: 0,
        totalMembers: 0,
        lastActivity: new Date().toISOString(),
        unreadMessages: 0,
        pendingTasks: 0,
        completedTasks: 0,
        overallProgress: 0
      },
      teamDecision: {
        decision: 'pending',
        finalDecisionBy: '',
        finalDecisionAt: '',
        reasoning: ''
      },
      budgetAllocation: {
        totalBudget: 0,
        allocatedBudget: 0,
        remainingBudget: 0,
        departmentAllocations: [],
        approvedBy: '',
        approvedAt: '',
        budgetStatus: 'draft'
      }
    };

    this.teamBidInterests.set(teamBidInterest.id, teamBidInterest);

    // Create team workspace
    const workspace = await this.createTeamWorkspace(teamBidInterest);
    this.teamWorkspaces.set(teamBidInterest.id, workspace);

    // If no approval required, start team analysis
    if (!organization.settings.requireApprovalForBids) {
      await this.startTeamAnalysis(workspace);
    }

    console.log(`✅ Created team workspace for tender ${tenderId}`);
    return workspace;
  }

  /**
   * TEAM WORKSPACE MANAGEMENT
   */
  async getTeamActiveWorkspaces(organizationId: string): Promise<TeamActiveWorkspace[]> {
    const workspaces: TeamActiveWorkspace[] = [];
    
    for (const workspace of this.teamWorkspaces.values()) {
      if (workspace.teamBidInterest.organizationId === organizationId) {
        workspaces.push(workspace);
      }
    }

    return workspaces.sort((a, b) => 
      new Date(b.teamBidInterest.lastUpdated).getTime() - 
      new Date(a.teamBidInterest.lastUpdated).getTime()
    );
  }

  async assignTeamMembers(
    workspaceId: string,
    memberAssignments: { memberId: string; responsibilities: string[] }[]
  ): Promise<void> {
    const workspace = this.teamWorkspaces.get(workspaceId);
    if (!workspace) {
      throw new Error('Team workspace not found');
    }

    for (const assignment of memberAssignments) {
      const member = await this.getTeamMember(assignment.memberId);
      if (member) {
        workspace.teamBidInterest.teamMembers.push({
          memberId: assignment.memberId,
          role: member.role,
          responsibilities: assignment.responsibilities,
          assignedAt: new Date().toISOString(),
          assignedBy: 'system', // TODO: Get from context
          status: 'assigned',
          workloadImpact: 20 // Default 20%
        });
      }
    }

    workspace.teamBidInterest.lastUpdated = new Date().toISOString();
    workspace.teamBidInterest.commitmentLevel += 15; // Increase commitment

    this.teamWorkspaces.set(workspaceId, workspace);
    console.log(`👥 Assigned ${memberAssignments.length} team members to workspace`);
  }

  /**
   * TEAM TASK MANAGEMENT
   */
  async createTeamTaskBoard(
    workspaceId: string,
    name: string
  ): Promise<TeamTaskBoard> {
    const taskBoard: TeamTaskBoard = {
      id: `taskboard-${Date.now()}`,
      name,
      columns: [
        { id: 'col-1', name: 'To Do', position: 1, color: '#e3f2fd', isCompleted: false },
        { id: 'col-2', name: 'In Progress', position: 2, color: '#fff3e0', isCompleted: false },
        { id: 'col-3', name: 'Review', position: 3, color: '#f3e5f5', isCompleted: false },
        { id: 'col-4', name: 'Done', position: 4, color: '#e8f5e8', isCompleted: true }
      ],
      tasks: [],
      members: [],
      settings: {
        allowGuestAccess: false,
        requireApprovalForTaskCreation: false,
        enableTimeTracking: true,
        enableNotifications: true,
        autoArchiveCompleted: true
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    this.teamTaskBoards.set(workspaceId, taskBoard);
    console.log(`📋 Created team task board: ${name}`);
    return taskBoard;
  }

  async addTeamTask(
    workspaceId: string,
    taskData: Partial<TeamTask>
  ): Promise<TeamTask> {
    const taskBoard = this.teamTaskBoards.get(workspaceId);
    if (!taskBoard) {
      throw new Error('Team task board not found');
    }

    const task: TeamTask = {
      id: `task-${Date.now()}`,
      title: taskData.title || '',
      description: taskData.description || '',
      columnId: taskData.columnId || taskBoard.columns[0].id,
      assigneeIds: taskData.assigneeIds || [],
      creatorId: taskData.creatorId || '',
      priority: taskData.priority || 'medium',
      status: 'not_started',
      tags: taskData.tags || [],
      dueDate: taskData.dueDate,
      estimatedHours: taskData.estimatedHours,
      dependencies: taskData.dependencies || [],
      attachments: [],
      comments: [],
      checklist: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    taskBoard.tasks.push(task);
    taskBoard.updatedAt = new Date().toISOString();
    this.teamTaskBoards.set(workspaceId, taskBoard);

    console.log(`✅ Added team task: ${task.title}`);
    return task;
  }

  /**
   * TEAM COMMUNICATION
   */
  async createTeamDiscussion(
    workspaceId: string,
    organizationId: string
  ): Promise<TeamDiscussion> {
    const discussion: TeamDiscussion = {
      id: `discussion-${Date.now()}`,
      tenderId: workspaceId,
      organizationId,
      channels: [
        {
          id: 'general',
          name: 'General Discussion',
          description: 'General tender discussion',
          type: 'general',
          messages: [],
          participants: [],
          settings: {
            isPrivate: false,
            allowInvites: true,
            requireApprovalToJoin: false,
            muteNotifications: false
          },
          createdAt: new Date().toISOString()
        }
      ],
      participants: [],
      settings: {
        allowFileSharing: true,
        allowVoiceMessages: true,
        requireModeration: false,
        retentionPeriod: 365,
        enableNotifications: true
      },
      createdAt: new Date().toISOString()
    };

    this.teamDiscussions.set(workspaceId, discussion);
    console.log(`💬 Created team discussion for workspace ${workspaceId}`);
    return discussion;
  }

  /**
   * APPROVAL WORKFLOW
   */
  private async createApprovalWorkflow(
    organizationId: string,
    type: 'bid_interest' | 'budget_allocation' | 'document_approval'
  ): Promise<ApprovalWorkflow> {
    const workflow: ApprovalWorkflow = {
      id: `approval-${Date.now()}`,
      steps: [],
      currentStep: 0,
      status: 'pending',
      createdAt: new Date().toISOString()
    };

    // Add approval steps based on organization settings and type
    if (type === 'bid_interest') {
      workflow.steps = [
        {
          id: 'step-1',
          name: 'Project Manager Review',
          description: 'Initial review by project manager',
          approverRole: 'project_manager',
          approverIds: [],
          requiredApprovals: 1,
          currentApprovals: [],
          status: 'pending',
          dependencies: []
        },
        {
          id: 'step-2',
          name: 'Financial Approval',
          description: 'Budget and financial review',
          approverRole: 'finance',
          approverIds: [],
          requiredApprovals: 1,
          currentApprovals: [],
          status: 'pending',
          dependencies: ['step-1']
        }
      ];
    }

    return workflow;
  }

  /**
   * HELPER METHODS
   */
  private async createTeamWorkspace(teamBidInterest: TeamBidInterest): Promise<TeamActiveWorkspace> {
    // Get the original tender notification (would come from BidWorkflowService)
    const bidWorkflowService = BidWorkflowService.getInstance();
    
    // Create base workspace structure
    const workspace: TeamActiveWorkspace = {
      bidInterest: teamBidInterest, // Keep for compatibility
      tender: {} as TenderNotification, // Would be populated from actual tender data
      teamBidInterest,
      
      // Enhanced team analysis
      teamAnalysis: {
        individualAnalyses: [],
        consensusAnalysis: {
          averageFeasibilityScore: 0,
          consensusLevel: 0,
          majorityRecommendation: 'needs_more_info',
          keyAgreements: [],
          keyDisagreements: [],
          finalRecommendation: ''
        },
        conflictingViews: [],
        recommendationSummary: {
          overallRecommendation: 'needs_more_info',
          confidence: 0,
          keyFactors: [],
          riskMitigation: [],
          nextSteps: [],
          timelineEstimate: ''
        }
      },

      // Team task management
      teamTasks: await this.createTeamTaskBoard(teamBidInterest.id, 'Tender Preparation'),
      
      // Team communication
      teamDiscussion: await this.createTeamDiscussion(teamBidInterest.id, teamBidInterest.organizationId),
      
      // Team meetings
      teamMeetings: [],
      
      // Team documents
      teamDocuments: {
        id: `docs-${Date.now()}`,
        documents: [],
        folders: [],
        permissions: [],
        versionControl: {
          enableAutoSave: true,
          saveInterval: 5,
          maxVersions: 10,
          enableBranching: false,
          requireApprovalForMerge: true
        },
        collaborationSettings: {
          enableRealTimeEditing: true,
          enableComments: true,
          enableSuggestions: true,
          requireApprovalForChanges: false,
          notifyOnChanges: true
        }
      },

      // Team progress tracking
      teamProgress: {
        overallProgress: 5,
        departmentProgress: [],
        milestones: [],
        deadlines: [],
        criticalPath: [],
        performanceMetrics: {
          velocityTrend: [],
          qualityMetrics: [],
          collaborationScore: 0,
          communicationFrequency: 0,
          decisionMakingSpeed: 0,
          memberSatisfaction: 0
        },
        riskIndicators: []
      },

      // Base workspace properties (for compatibility)
      tenderAnalysis: {
        aiInsights: { keyRequirements: [], technicalSpecs: [], complianceItems: [], extractedData: {}, confidence: 0 },
        riskAssessment: { overallRisk: 'medium', riskFactors: [], mitigationStrategies: [], contingencyPlan: '', riskScore: 0 },
        complianceStatus: { overallScore: 0, mandatoryItems: [], optionalItems: [], missingDocuments: [], riskLevel: 'medium', complianceGaps: [] },
        competitiveAnalysis: { competitorCount: 0, threatLevel: 'medium', marketPosition: '', winProbability: 0, competitiveAdvantages: [], recommendations: [] },
        feasibilityScore: 0,
        recommendedActions: []
      },
      governanceEngine: { complianceChecklist: [], riskMitigation: [], approvalWorkflow: [], governanceScore: 0 },
      beeWorkerTasks: { documentCollection: [], siteVisits: [], complianceVerification: [], marketResearch: [], availableBees: [] },
      documentWorkspace: { originalDocs: [], workingDrafts: [], submissionPackage: [], processingStatus: { totalDocuments: 0, processed: 0, analyzing: 0, failed: 0, lastUpdate: new Date().toISOString() } },
      progressTracking: { overallProgress: 5, milestones: [], deadlines: [], criticalPath: [] }
    };

    return workspace;
  }

  private async startTeamAnalysis(workspace: TeamActiveWorkspace): Promise<void> {
    console.log(`🤖 Starting team analysis for tender ${workspace.teamBidInterest.tenderId}`);
    
    // Update state to analyzing
    workspace.teamBidInterest.state = BidWorkflowState.ANALYZING;
    workspace.teamBidInterest.lastUpdated = new Date().toISOString();
    
    // Simulate team analysis process
    setTimeout(async () => {
      await this.completeTeamAnalysis(workspace);
    }, 5000);
  }

  private async completeTeamAnalysis(workspace: TeamActiveWorkspace): Promise<void> {
    console.log(`✅ Completing team analysis for tender ${workspace.teamBidInterest.tenderId}`);
    
    workspace.teamBidInterest.analysisCompleted = true;
    workspace.teamBidInterest.state = BidWorkflowState.PREPARING;
    workspace.teamBidInterest.commitmentLevel = 40;
    workspace.teamProgress.overallProgress = 25;
    
    console.log(`🎯 Team analysis complete for tender ${workspace.teamBidInterest.tenderId}`);
  }

  private getDefaultPermissions(role: UserRole) {
    // Return default permissions based on role
    // This would be more comprehensive in a real implementation
    return [];
  }

  private getDefaultWorkingHours() {
    const defaultDay = {
      available: true,
      startTime: '08:00',
      endTime: '17:00',
      breaks: [{ startTime: '12:00', endTime: '13:00', description: 'Lunch' }]
    };

    return {
      monday: defaultDay,
      tuesday: defaultDay,
      wednesday: defaultDay,
      thursday: defaultDay,
      friday: defaultDay,
      saturday: { ...defaultDay, available: false },
      sunday: { ...defaultDay, available: false }
    };
  }

  private async getTeamMember(memberId: string): Promise<TeamMember | null> {
    for (const members of this.teamMembers.values()) {
      const member = members.find(m => m.id === memberId);
      if (member) return member;
    }
    return null;
  }
}

export default TeamCollaborationService;
