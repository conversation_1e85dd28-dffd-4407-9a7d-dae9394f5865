/**
 * Team Member Onboarding Service
 * Complete system for inviting, registering, and onboarding team members
 */

import {
  TeamMemberInvitation,
  TeamMemberOnboarding,
  InvitationStatus,
  InvitationResponse,
  OnboardingStep,
  OnboardingStepType,
  ApprovalWorkflow,
  BuddyAssignment,
  OnboardingEvent,
  OnboardingEventType
} from '../types/teamOnboarding';
import { UserRole, TeamMember, Organization } from '../types/teamCollaboration';
import TeamCollaborationService from './TeamCollaborationService';

class TeamMemberOnboardingService {
  private static instance: TeamMemberOnboardingService;
  private invitations: Map<string, TeamMemberInvitation> = new Map();
  private onboardings: Map<string, TeamMemberOnboarding> = new Map();
  private approvalWorkflows: Map<string, ApprovalWorkflow> = new Map();
  private buddyAssignments: Map<string, BuddyAssignment> = new Map();

  static getInstance(): TeamMemberOnboardingService {
    if (!TeamMemberOnboardingService.instance) {
      TeamMemberOnboardingService.instance = new TeamMemberOnboardingService();
    }
    return TeamMemberOnboardingService.instance;
  }

  /**
   * TEAM MEMBER INVITATION SYSTEM
   */
  async inviteTeamMember(
    organizationId: string,
    inviterUserId: string,
    invitationData: {
      email: string;
      role: UserRole;
      personalMessage?: string;
      suggestedTitle: string;
      department?: string;
      responsibilities: string[];
    }
  ): Promise<TeamMemberInvitation> {
    const teamService = TeamCollaborationService.getInstance();
    const organization = await teamService.getOrganization(organizationId);
    const inviter = await teamService.getTeamMember(inviterUserId);

    if (!organization || !inviter) {
      throw new Error('Organization or inviter not found');
    }

    // Generate invitation token
    const invitationToken = this.generateInvitationToken();
    
    const invitation: TeamMemberInvitation = {
      id: `inv-${Date.now()}`,
      organizationId,
      organizationName: organization.name,
      invitedEmail: invitationData.email,
      invitedRole: invitationData.role,
      invitedBy: inviterUserId,
      inviterName: inviter.title || 'Team Member',
      inviterRole: inviter.role,
      personalMessage: invitationData.personalMessage,
      suggestedTitle: invitationData.suggestedTitle,
      suggestedDepartment: invitationData.department,
      expectedResponsibilities: invitationData.responsibilities,
      status: 'pending',
      sentAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
      invitationToken,
      remindersSent: 0,
      onboardingTemplate: this.getOnboardingTemplate(invitationData.role),
      requiredDocuments: this.getRequiredDocuments(invitationData.role),
      accessLevel: this.getAccessLevel(invitationData.role),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    this.invitations.set(invitation.id, invitation);

    // Send invitation email
    await this.sendInvitationEmail(invitation);

    // Log event
    await this.logOnboardingEvent('invitation_sent', {
      invitationId: invitation.id,
      organizationId,
      data: { email: invitationData.email, role: invitationData.role }
    });

    console.log(`📧 Invitation sent to ${invitationData.email} for role ${invitationData.role}`);
    return invitation;
  }

  /**
   * RESPOND TO INVITATION
   */
  async respondToInvitation(
    invitationToken: string,
    response: InvitationResponse
  ): Promise<{ invitation: TeamMemberInvitation; onboarding?: TeamMemberOnboarding }> {
    const invitation = this.findInvitationByToken(invitationToken);
    if (!invitation) {
      throw new Error('Invalid invitation token');
    }

    if (invitation.status !== 'pending' && invitation.status !== 'sent') {
      throw new Error('Invitation has already been responded to');
    }

    if (new Date() > new Date(invitation.expiresAt)) {
      throw new Error('Invitation has expired');
    }

    // Update invitation
    invitation.status = response.response === 'accept' ? 'accepted' : 'declined';
    invitation.respondedAt = response.respondedAt;
    
    if (response.response === 'accept') {
      invitation.acceptedAt = response.respondedAt;
    } else {
      invitation.declinedAt = response.respondedAt;
    }

    invitation.updatedAt = new Date().toISOString();

    // Log event
    await this.logOnboardingEvent(
      response.response === 'accept' ? 'invitation_accepted' : 'invitation_declined',
      {
        invitationId: invitation.id,
        organizationId: invitation.organizationId,
        data: { response: response.response, message: response.message }
      }
    );

    let onboarding: TeamMemberOnboarding | undefined;

    // If accepted, create onboarding flow
    if (response.response === 'accept') {
      onboarding = await this.createOnboardingFlow(invitation, response);
    }

    console.log(`✅ Invitation ${response.response}ed by ${invitation.invitedEmail}`);
    return { invitation, onboarding };
  }

  /**
   * CREATE ONBOARDING FLOW
   */
  async createOnboardingFlow(
    invitation: TeamMemberInvitation,
    invitationResponse: InvitationResponse
  ): Promise<TeamMemberOnboarding> {
    // Create user account first (would integrate with auth system)
    const userId = `user-${Date.now()}`;

    const onboardingSteps = this.generateOnboardingSteps(invitation.invitedRole);

    const onboarding: TeamMemberOnboarding = {
      id: `onb-${Date.now()}`,
      invitationId: invitation.id,
      userId,
      organizationId: invitation.organizationId,
      currentStep: 0,
      totalSteps: onboardingSteps.length,
      steps: onboardingSteps,
      role: invitation.invitedRole,
      personalInfo: {
        fullName: invitationResponse.personalInfo?.fullName || '',
        email: invitation.invitedEmail,
        phone: invitationResponse.personalInfo?.phone,
        bio: invitationResponse.personalInfo?.bio,
        preferredContactMethod: 'email',
        timezone: invitationResponse.availabilityInfo?.timezone || 'Africa/Johannesburg',
        language: 'en'
      },
      professionalInfo: {
        title: invitation.suggestedTitle,
        department: invitation.suggestedDepartment,
        startDate: invitationResponse.availabilityInfo?.startDate || new Date().toISOString(),
        yearsOfExperience: parseInt(invitationResponse.professionalInfo?.experience || '0'),
        specializations: invitationResponse.professionalInfo?.specializations || [],
        certifications: [],
        skills: [],
        workingHours: invitationResponse.availabilityInfo?.workingHours || this.getDefaultWorkingHours(),
        maxConcurrentBids: 5,
        preferredProjectTypes: [],
        previousCompanies: [],
        education: [],
        performanceGoals: [],
        kpis: []
      },
      organizationInfo: {
        organizationId: invitation.organizationId,
        organizationName: invitation.organizationName,
        role: invitation.invitedRole,
        department: invitation.suggestedDepartment,
        accessLevel: invitation.accessLevel,
        permissions: [],
        systemAccess: [],
        integrationAccounts: []
      },
      completedSteps: [],
      skippedSteps: [],
      documentsUploaded: [],
      requiresApproval: this.requiresApproval(invitation.invitedRole),
      approvalStatus: this.requiresApproval(invitation.invitedRole) ? 'pending' : 'not_required',
      onboardingScore: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    this.onboardings.set(onboarding.id, onboarding);

    // Create approval workflow if required
    if (onboarding.requiresApproval) {
      await this.createApprovalWorkflow(onboarding);
    }

    // Assign buddy if applicable
    await this.assignBuddy(onboarding);

    // Log event
    await this.logOnboardingEvent('onboarding_started', {
      onboardingId: onboarding.id,
      organizationId: invitation.organizationId,
      data: { role: invitation.invitedRole, userId }
    });

    console.log(`🚀 Onboarding flow created for ${invitation.invitedEmail}`);
    return onboarding;
  }

  /**
   * COMPLETE ONBOARDING STEP
   */
  async completeOnboardingStep(
    onboardingId: string,
    stepId: string,
    stepData: Record<string, any>
  ): Promise<TeamMemberOnboarding> {
    const onboarding = this.onboardings.get(onboardingId);
    if (!onboarding) {
      throw new Error('Onboarding not found');
    }

    const step = onboarding.steps.find(s => s.id === stepId);
    if (!step) {
      throw new Error('Onboarding step not found');
    }

    // Validate step data
    const validationErrors = this.validateStepData(step, stepData);
    if (validationErrors.length > 0) {
      step.errors = validationErrors;
      throw new Error(`Validation failed: ${validationErrors.join(', ')}`);
    }

    // Mark step as completed
    step.completed = true;
    step.completedAt = new Date().toISOString();
    step.data = stepData;
    step.errors = undefined;

    // Add to completed steps
    if (!onboarding.completedSteps.includes(stepId)) {
      onboarding.completedSteps.push(stepId);
    }

    // Update onboarding data based on step type
    await this.processStepCompletion(onboarding, step, stepData);

    // Move to next step
    const currentStepIndex = onboarding.steps.findIndex(s => s.id === stepId);
    if (currentStepIndex < onboarding.steps.length - 1) {
      onboarding.currentStep = currentStepIndex + 1;
    }

    // Calculate onboarding score
    onboarding.onboardingScore = this.calculateOnboardingScore(onboarding);

    // Check if onboarding is complete
    if (onboarding.completedSteps.length === onboarding.totalSteps) {
      await this.completeOnboarding(onboarding);
    }

    onboarding.updatedAt = new Date().toISOString();

    // Log event
    await this.logOnboardingEvent('step_completed', {
      onboardingId,
      organizationId: onboarding.organizationId,
      data: { stepId, stepType: step.type }
    });

    console.log(`✅ Completed onboarding step: ${step.title}`);
    return onboarding;
  }

  /**
   * COMPLETE ONBOARDING
   */
  async completeOnboarding(onboarding: TeamMemberOnboarding): Promise<TeamMember> {
    const teamService = TeamCollaborationService.getInstance();

    // Create team member
    const teamMember = await teamService.addTeamMember(
      onboarding.organizationId,
      onboarding.userId,
      onboarding.role,
      {
        title: onboarding.personalInfo.fullName,
        department: onboarding.professionalInfo.department,
        specializations: onboarding.professionalInfo.specializations,
        availability: {
          timezone: onboarding.personalInfo.timezone,
          workingHours: onboarding.professionalInfo.workingHours,
          unavailableDates: [],
          maxConcurrentBids: onboarding.professionalInfo.maxConcurrentBids
        }
      }
    );

    // Update onboarding
    onboarding.memberId = teamMember.id;
    onboarding.completedAt = new Date().toISOString();
    onboarding.activatedAt = new Date().toISOString();

    // Update invitation status
    const invitation = this.invitations.get(onboarding.invitationId);
    if (invitation) {
      invitation.status = 'completed';
      invitation.updatedAt = new Date().toISOString();
    }

    // Log event
    await this.logOnboardingEvent('onboarding_completed', {
      onboardingId: onboarding.id,
      organizationId: onboarding.organizationId,
      data: { memberId: teamMember.id, role: onboarding.role }
    });

    console.log(`🎉 Onboarding completed for ${onboarding.personalInfo.fullName}`);
    return teamMember;
  }

  /**
   * HELPER METHODS
   */
  private generateInvitationToken(): string {
    return `inv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private findInvitationByToken(token: string): TeamMemberInvitation | undefined {
    for (const invitation of this.invitations.values()) {
      if (invitation.invitationToken === token) {
        return invitation;
      }
    }
    return undefined;
  }

  private getOnboardingTemplate(role: UserRole): string {
    const templates = {
      owner: 'executive_onboarding',
      admin: 'admin_onboarding',
      project_manager: 'pm_onboarding',
      estimator: 'technical_onboarding',
      technical_lead: 'technical_onboarding',
      legal_counsel: 'compliance_onboarding',
      business_dev: 'sales_onboarding',
      finance: 'finance_onboarding',
      viewer: 'basic_onboarding',
      guest: 'guest_onboarding'
    };
    return templates[role] || 'standard_onboarding';
  }

  private getRequiredDocuments(role: UserRole): string[] {
    const baseDocuments = ['identity_document', 'tax_certificate'];
    const roleDocuments = {
      legal_counsel: ['law_degree', 'bar_admission'],
      technical_lead: ['engineering_degree', 'professional_registration'],
      estimator: ['quantity_surveying_qualification'],
      finance: ['accounting_qualification']
    };
    return [...baseDocuments, ...(roleDocuments[role as keyof typeof roleDocuments] || [])];
  }

  private getAccessLevel(role: UserRole): 'immediate' | 'pending_approval' | 'restricted' {
    const immediateAccess = ['viewer', 'guest'];
    const restrictedAccess = ['owner', 'admin'];
    
    if (immediateAccess.includes(role)) return 'immediate';
    if (restrictedAccess.includes(role)) return 'pending_approval';
    return 'pending_approval';
  }

  private requiresApproval(role: UserRole): boolean {
    const noApprovalRoles = ['viewer', 'guest'];
    return !noApprovalRoles.includes(role);
  }

  private generateOnboardingSteps(role: UserRole): OnboardingStep[] {
    const baseSteps: Partial<OnboardingStep>[] = [
      {
        name: 'welcome',
        title: 'Welcome to BidBeez',
        description: 'Introduction to the platform and your role',
        type: 'welcome',
        required: true,
        order: 1
      },
      {
        name: 'personal_info',
        title: 'Personal Information',
        description: 'Complete your personal profile',
        type: 'personal_info',
        required: true,
        order: 2
      },
      {
        name: 'professional_info',
        title: 'Professional Information',
        description: 'Add your professional background and skills',
        type: 'professional_info',
        required: true,
        order: 3
      },
      {
        name: 'role_training',
        title: 'Role-Specific Training',
        description: 'Learn about your specific role and responsibilities',
        type: 'role_training',
        required: true,
        order: 4
      },
      {
        name: 'document_upload',
        title: 'Document Upload',
        description: 'Upload required documents and certifications',
        type: 'document_upload',
        required: true,
        order: 5
      },
      {
        name: 'team_introduction',
        title: 'Meet Your Team',
        description: 'Introduction to team members and communication channels',
        type: 'team_introduction',
        required: false,
        order: 6
      },
      {
        name: 'system_access',
        title: 'System Access Setup',
        description: 'Set up access to systems and tools',
        type: 'system_access',
        required: true,
        order: 7
      },
      {
        name: 'completion',
        title: 'Onboarding Complete',
        description: 'Final steps and activation',
        type: 'completion',
        required: true,
        order: 8
      }
    ];

    return baseSteps.map((step, index) => ({
      id: `step-${index + 1}`,
      name: step.name!,
      title: step.title!,
      description: step.description!,
      type: step.type!,
      required: step.required!,
      order: step.order!,
      config: {
        component: `${step.type}Component`,
        timeEstimate: 15
      },
      completed: false,
      validationRules: []
    }));
  }

  private validateStepData(step: OnboardingStep, data: Record<string, any>): string[] {
    const errors: string[] = [];
    
    for (const rule of step.validationRules) {
      const value = data[rule.field];
      
      switch (rule.rule) {
        case 'required':
          if (!value || (typeof value === 'string' && value.trim() === '')) {
            errors.push(rule.message);
          }
          break;
        case 'email':
          if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
            errors.push(rule.message);
          }
          break;
        // Add more validation rules as needed
      }
    }
    
    return errors;
  }

  private async processStepCompletion(
    onboarding: TeamMemberOnboarding,
    step: OnboardingStep,
    stepData: Record<string, any>
  ): Promise<void> {
    switch (step.type) {
      case 'personal_info':
        Object.assign(onboarding.personalInfo, stepData);
        break;
      case 'professional_info':
        Object.assign(onboarding.professionalInfo, stepData);
        break;
      case 'document_upload':
        // Handle document uploads
        break;
      // Add more step processing as needed
    }
  }

  private calculateOnboardingScore(onboarding: TeamMemberOnboarding): number {
    const completedSteps = onboarding.completedSteps.length;
    const totalSteps = onboarding.totalSteps;
    const baseScore = (completedSteps / totalSteps) * 70;
    
    // Add bonus points for quality
    let bonusPoints = 0;
    if (onboarding.documentsUploaded.length > 0) bonusPoints += 10;
    if (onboarding.professionalInfo.specializations.length > 0) bonusPoints += 10;
    if (onboarding.personalInfo.bio) bonusPoints += 5;
    if (onboarding.professionalInfo.certifications.length > 0) bonusPoints += 5;
    
    return Math.min(100, baseScore + bonusPoints);
  }

  private async createApprovalWorkflow(onboarding: TeamMemberOnboarding): Promise<void> {
    // Implementation for approval workflow
    console.log(`📋 Creating approval workflow for ${onboarding.personalInfo.fullName}`);
  }

  private async assignBuddy(onboarding: TeamMemberOnboarding): Promise<void> {
    // Implementation for buddy assignment
    console.log(`👥 Assigning buddy for ${onboarding.personalInfo.fullName}`);
  }

  private async sendInvitationEmail(invitation: TeamMemberInvitation): Promise<void> {
    // Implementation for sending invitation email
    console.log(`📧 Sending invitation email to ${invitation.invitedEmail}`);
  }

  private async logOnboardingEvent(
    type: OnboardingEventType,
    eventData: {
      invitationId?: string;
      onboardingId?: string;
      organizationId: string;
      data: Record<string, any>;
    }
  ): Promise<void> {
    const event: OnboardingEvent = {
      id: `evt-${Date.now()}`,
      type,
      invitationId: eventData.invitationId,
      onboardingId: eventData.onboardingId,
      organizationId: eventData.organizationId,
      data: eventData.data,
      timestamp: new Date().toISOString(),
      processed: false,
      notificationsSent: [],
      followUpRequired: false
    };
    
    console.log(`📝 Logged onboarding event: ${type}`);
  }

  private getDefaultWorkingHours(): any {
    return {
      monday: { start: '08:00', end: '17:00', available: true },
      tuesday: { start: '08:00', end: '17:00', available: true },
      wednesday: { start: '08:00', end: '17:00', available: true },
      thursday: { start: '08:00', end: '17:00', available: true },
      friday: { start: '08:00', end: '17:00', available: true },
      saturday: { start: '08:00', end: '12:00', available: false },
      sunday: { start: '08:00', end: '12:00', available: false }
    };
  }

  /**
   * PUBLIC GETTERS
   */
  async getInvitation(invitationId: string): Promise<TeamMemberInvitation | null> {
    return this.invitations.get(invitationId) || null;
  }

  async getOnboarding(onboardingId: string): Promise<TeamMemberOnboarding | null> {
    return this.onboardings.get(onboardingId) || null;
  }

  async getOrganizationInvitations(organizationId: string): Promise<TeamMemberInvitation[]> {
    return Array.from(this.invitations.values())
      .filter(inv => inv.organizationId === organizationId);
  }

  async getOrganizationOnboardings(organizationId: string): Promise<TeamMemberOnboarding[]> {
    return Array.from(this.onboardings.values())
      .filter(onb => onb.organizationId === organizationId);
  }
}

export default TeamMemberOnboardingService;
