/**
 * SkillSync Integration API Service - Real Backend Integration
 * Cross-platform talent marketplace integration
 */

import {
  TenderTalentRequest,
  TalentApplication,
  TenderOpportunity,
  CrossPlatformUser
} from '../../types/skillsyncIntegration';

class SkillSyncApiService {
  private static instance: SkillSyncApiService;
  private baseUrl = '/api';

  static getInstance(): SkillSyncApiService {
    if (!SkillSyncApiService.instance) {
      SkillSyncApiService.instance = new SkillSyncApiService();
    }
    return SkillSyncApiService.instance;
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const token = localStorage.getItem('token');
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    const response = await fetch(`${this.baseUrl}${endpoint}`, config);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * CROSS-PLATFORM USER MANAGEMENT
   */

  /**
   * Link BidBeez account with SkillSync
   */
  async linkSkillSyncAccount(skillsyncCredentials: {
    email: string;
    password: string;
  }): Promise<CrossPlatformUser> {
    return this.makeRequest<CrossPlatformUser>('/skillsync/link-account', {
      method: 'POST',
      body: JSON.stringify(skillsyncCredentials),
    });
  }

  /**
   * Unlink SkillSync account
   */
  async unlinkSkillSyncAccount(): Promise<void> {
    return this.makeRequest<void>('/skillsync/unlink-account', {
      method: 'POST',
    });
  }

  /**
   * Get cross-platform user profile
   */
  async getCrossPlatformProfile(): Promise<CrossPlatformUser | null> {
    return this.makeRequest<CrossPlatformUser | null>('/skillsync/profile');
  }

  /**
   * Update data sharing settings
   */
  async updateDataSharingSettings(settings: {
    shareProfile: boolean;
    shareRatings: boolean;
    sharePortfolio: boolean;
    shareAvailability: boolean;
    allowDirectContact: boolean;
  }): Promise<CrossPlatformUser> {
    return this.makeRequest<CrossPlatformUser>('/skillsync/data-sharing', {
      method: 'PUT',
      body: JSON.stringify(settings),
    });
  }

  /**
   * TALENT REQUEST MANAGEMENT (BIDBEEZ SIDE)
   */

  /**
   * Post talent request to SkillSync
   */
  async postTalentRequest(
    organizationId: string,
    tenderId: string,
    requestData: {
      title: string;
      description: string;
      requiredSkills: Array<{
        skill: string;
        level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
        required: boolean;
        yearsRequired?: number;
      }>;
      budgetMin: number;
      budgetMax: number;
      projectDuration: number;
      workload: number;
      urgency: 'low' | 'medium' | 'high' | 'urgent';
      locationRequirement: 'remote' | 'hybrid' | 'onsite';
      applicationDeadline: string;
      projectStartDate: string;
    }
  ): Promise<TenderTalentRequest> {
    return this.makeRequest<TenderTalentRequest>(`/organizations/${organizationId}/talent-requests`, {
      method: 'POST',
      body: JSON.stringify({
        ...requestData,
        tenderId,
      }),
    });
  }

  /**
   * Get organization's talent requests
   */
  async getTalentRequests(organizationId: string): Promise<TenderTalentRequest[]> {
    return this.makeRequest<TenderTalentRequest[]>(`/organizations/${organizationId}/talent-requests`);
  }

  /**
   * Update talent request
   */
  async updateTalentRequest(
    organizationId: string,
    requestId: string,
    updateData: Partial<TenderTalentRequest>
  ): Promise<TenderTalentRequest> {
    return this.makeRequest<TenderTalentRequest>(`/organizations/${organizationId}/talent-requests/${requestId}`, {
      method: 'PUT',
      body: JSON.stringify(updateData),
    });
  }

  /**
   * Cancel talent request
   */
  async cancelTalentRequest(organizationId: string, requestId: string): Promise<void> {
    return this.makeRequest<void>(`/organizations/${organizationId}/talent-requests/${requestId}`, {
      method: 'DELETE',
    });
  }

  /**
   * TALENT APPLICATION MANAGEMENT
   */

  /**
   * Get applications for talent request
   */
  async getTalentApplications(
    organizationId: string,
    requestId: string
  ): Promise<TalentApplication[]> {
    return this.makeRequest<TalentApplication[]>(`/organizations/${organizationId}/talent-requests/${requestId}/applications`);
  }

  /**
   * Get specific application
   */
  async getTalentApplication(
    organizationId: string,
    applicationId: string
  ): Promise<TalentApplication> {
    return this.makeRequest<TalentApplication>(`/organizations/${organizationId}/talent-applications/${applicationId}`);
  }

  /**
   * Update application status
   */
  async updateApplicationStatus(
    organizationId: string,
    applicationId: string,
    status: 'under_review' | 'shortlisted' | 'interview_scheduled' | 'selected' | 'rejected',
    notes?: string
  ): Promise<TalentApplication> {
    return this.makeRequest<TalentApplication>(`/organizations/${organizationId}/talent-applications/${applicationId}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status, notes }),
    });
  }

  /**
   * Schedule interview
   */
  async scheduleInterview(
    organizationId: string,
    applicationId: string,
    interviewData: {
      scheduledAt: string;
      duration: number;
      type: 'phone' | 'video' | 'in_person';
      interviewers: Array<{ userId: string; focus: string[] }>;
      agenda: string[];
      meetingLink?: string;
      location?: string;
    }
  ): Promise<TalentApplication> {
    return this.makeRequest<TalentApplication>(`/organizations/${organizationId}/talent-applications/${applicationId}/interview`, {
      method: 'POST',
      body: JSON.stringify(interviewData),
    });
  }

  /**
   * Submit interview feedback
   */
  async submitInterviewFeedback(
    organizationId: string,
    applicationId: string,
    feedback: {
      technicalSkills: number;
      communication: number;
      culturalFit: number;
      experience: number;
      overallRating: number;
      strengths: string[];
      concerns: string[];
      recommendation: 'strong_hire' | 'hire' | 'maybe' | 'no_hire';
      notes: string;
    }
  ): Promise<TalentApplication> {
    return this.makeRequest<TalentApplication>(`/organizations/${organizationId}/talent-applications/${applicationId}/feedback`, {
      method: 'POST',
      body: JSON.stringify(feedback),
    });
  }

  /**
   * Select candidate
   */
  async selectCandidate(
    organizationId: string,
    applicationId: string,
    selectionData: {
      startDate: string;
      finalRate: number;
      projectTerms: string;
      contractDetails?: any;
    }
  ): Promise<{
    application: TalentApplication;
    contract: any;
  }> {
    return this.makeRequest<any>(`/organizations/${organizationId}/talent-applications/${applicationId}/select`, {
      method: 'POST',
      body: JSON.stringify(selectionData),
    });
  }

  /**
   * SKILLSYNC OPPORTUNITY FEED (SKILLSYNC SIDE)
   */

  /**
   * Get tender opportunities for SkillSync user
   */
  async getSkillSyncOpportunities(filters?: {
    skills?: string[];
    experienceLevel?: string[];
    projectType?: string[];
    budget?: { min: number; max: number };
    location?: string[];
    urgency?: string[];
    minimumMatchScore?: number;
  }): Promise<TenderOpportunity[]> {
    const queryParams = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            value.forEach(v => queryParams.append(key, v.toString()));
          } else if (typeof value === 'object') {
            queryParams.append(key, JSON.stringify(value));
          } else {
            queryParams.append(key, value.toString());
          }
        }
      });
    }

    const endpoint = `/skillsync/opportunities${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return this.makeRequest<TenderOpportunity[]>(endpoint);
  }

  /**
   * Get specific opportunity details
   */
  async getOpportunityDetails(opportunityId: string): Promise<TenderOpportunity> {
    return this.makeRequest<TenderOpportunity>(`/skillsync/opportunities/${opportunityId}`);
  }

  /**
   * Apply for opportunity
   */
  async applyForOpportunity(
    opportunityId: string,
    applicationData: {
      coverLetter: string;
      proposedRate: number;
      availability: {
        startDate: string;
        endDate: string;
        hoursPerWeek: number;
        timezone: string;
      };
      portfolioSamples: Array<{
        title: string;
        description: string;
        url?: string;
        skills: string[];
      }>;
      approachDescription: string;
      timelineProposal: string;
      valueProposition: string;
    }
  ): Promise<TalentApplication> {
    return this.makeRequest<TalentApplication>(`/skillsync/opportunities/${opportunityId}/apply`, {
      method: 'POST',
      body: JSON.stringify(applicationData),
    });
  }

  /**
   * Get user's applications
   */
  async getMyApplications(): Promise<TalentApplication[]> {
    return this.makeRequest<TalentApplication[]>('/skillsync/my-applications');
  }

  /**
   * Update application
   */
  async updateApplication(
    applicationId: string,
    updateData: Partial<TalentApplication>
  ): Promise<TalentApplication> {
    return this.makeRequest<TalentApplication>(`/skillsync/applications/${applicationId}`, {
      method: 'PUT',
      body: JSON.stringify(updateData),
    });
  }

  /**
   * Withdraw application
   */
  async withdrawApplication(applicationId: string): Promise<void> {
    return this.makeRequest<void>(`/skillsync/applications/${applicationId}/withdraw`, {
      method: 'POST',
    });
  }

  /**
   * COMMUNICATION & MESSAGING
   */

  /**
   * Send message to employer
   */
  async sendMessageToEmployer(
    applicationId: string,
    message: string
  ): Promise<void> {
    return this.makeRequest<void>(`/skillsync/applications/${applicationId}/messages`, {
      method: 'POST',
      body: JSON.stringify({ message }),
    });
  }

  /**
   * Get application messages
   */
  async getApplicationMessages(applicationId: string): Promise<Array<{
    id: string;
    senderId: string;
    senderType: 'applicant' | 'employer';
    message: string;
    timestamp: string;
    read: boolean;
  }>> {
    return this.makeRequest<any>(`/skillsync/applications/${applicationId}/messages`);
  }

  /**
   * ANALYTICS & INSIGHTS
   */

  /**
   * Get talent marketplace analytics
   */
  async getTalentMarketplaceAnalytics(organizationId: string): Promise<{
    totalRequests: number;
    totalApplications: number;
    averageApplicationsPerRequest: number;
    hiringSuccessRate: number;
    averageTimeToHire: number;
    topSkillsRequested: Array<{ skill: string; count: number }>;
    budgetAnalysis: {
      averageBudget: number;
      budgetRanges: Record<string, number>;
    };
    performanceMetrics: {
      qualityScore: number;
      responseTime: number;
      candidateSatisfaction: number;
    };
  }> {
    return this.makeRequest<any>(`/organizations/${organizationId}/talent-analytics`);
  }

  /**
   * Get SkillSync user analytics
   */
  async getSkillSyncUserAnalytics(): Promise<{
    totalApplications: number;
    successRate: number;
    averageProjectValue: number;
    skillsInDemand: string[];
    marketPosition: {
      rank: number;
      percentile: number;
      competitiveAdvantages: string[];
    };
    earningsProjection: {
      monthly: number;
      quarterly: number;
      annual: number;
    };
    recommendations: Array<{
      type: 'skill_development' | 'portfolio_improvement' | 'rate_optimization';
      title: string;
      description: string;
      impact: string;
    }>;
  }> {
    return this.makeRequest<any>('/skillsync/user-analytics');
  }
}

export default SkillSyncApiService;
