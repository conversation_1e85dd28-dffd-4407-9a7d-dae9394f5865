/**
 * API Service Manager - Centralized API Management
 * Coordinates all API services and provides unified interface
 */

import TenderApiService from './TenderApiService';
import TeamApiService from './TeamApiService';
import CommunicationApiService from './CommunicationApiService';
import SubscriptionApiService from './SubscriptionApiService';
import SkillSyncApiService from './SkillSyncApiService';

class ApiServiceManager {
  private static instance: ApiServiceManager;
  
  // Service instances
  public readonly tender: TenderApiService;
  public readonly team: TeamApiService;
  public readonly communication: CommunicationApiService;
  public readonly subscription: SubscriptionApiService;
  public readonly skillsync: SkillSyncApiService;

  // Global configuration
  private baseUrl: string;
  private defaultHeaders: Record<string, string>;
  private requestInterceptors: Array<(config: RequestInit) => RequestInit> = [];
  private responseInterceptors: Array<(response: Response) => Response | Promise<Response>> = [];

  private constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || '/api';
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    };

    // Initialize service instances
    this.tender = TenderApiService.getInstance();
    this.team = TeamApiService.getInstance();
    this.communication = CommunicationApiService.getInstance();
    this.subscription = SubscriptionApiService.getInstance();
    this.skillsync = SkillSyncApiService.getInstance();

    // Setup global interceptors
    this.setupGlobalInterceptors();
  }

  static getInstance(): ApiServiceManager {
    if (!ApiServiceManager.instance) {
      ApiServiceManager.instance = new ApiServiceManager();
    }
    return ApiServiceManager.instance;
  }

  /**
   * Setup global request/response interceptors
   */
  private setupGlobalInterceptors(): void {
    // Request interceptor for authentication
    this.addRequestInterceptor((config) => {
      const token = localStorage.getItem('token');
      if (token) {
        config.headers = {
          ...config.headers,
          Authorization: `Bearer ${token}`,
        };
      }
      return config;
    });

    // Request interceptor for rate limiting
    this.addRequestInterceptor((config) => {
      // Add rate limiting headers if needed
      config.headers = {
        ...config.headers,
        'X-Client-Version': process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
        'X-Client-Platform': 'web',
      };
      return config;
    });

    // Response interceptor for error handling
    this.addResponseInterceptor(async (response) => {
      if (response.status === 401) {
        // Handle unauthorized - redirect to login
        this.handleUnauthorized();
      } else if (response.status === 429) {
        // Handle rate limiting
        this.handleRateLimit(response);
      } else if (response.status >= 500) {
        // Handle server errors
        this.handleServerError(response);
      }
      return response;
    });
  }

  /**
   * Add request interceptor
   */
  addRequestInterceptor(interceptor: (config: RequestInit) => RequestInit): void {
    this.requestInterceptors.push(interceptor);
  }

  /**
   * Add response interceptor
   */
  addResponseInterceptor(interceptor: (response: Response) => Response | Promise<Response>): void {
    this.responseInterceptors.push(interceptor);
  }

  /**
   * Make authenticated request with interceptors
   */
  async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    // Apply request interceptors
    let config = {
      headers: { ...this.defaultHeaders },
      ...options,
    };

    for (const interceptor of this.requestInterceptors) {
      config = interceptor(config);
    }

    // Make request
    let response = await fetch(`${this.baseUrl}${endpoint}`, config);

    // Apply response interceptors
    for (const interceptor of this.responseInterceptors) {
      response = await interceptor(response);
    }

    // Handle response
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Handle unauthorized responses
   */
  private handleUnauthorized(): void {
    // Clear stored authentication
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('sessionExpiry');

    // Redirect to login
    if (typeof window !== 'undefined') {
      window.location.href = '/login?redirect=' + encodeURIComponent(window.location.pathname);
    }
  }

  /**
   * Handle rate limiting
   */
  private handleRateLimit(response: Response): void {
    const retryAfter = response.headers.get('Retry-After');
    const message = `Rate limit exceeded. ${retryAfter ? `Please try again in ${retryAfter} seconds.` : 'Please try again later.'}`;
    
    // Show user notification
    this.showNotification('warning', message);
  }

  /**
   * Handle server errors
   */
  private handleServerError(response: Response): void {
    const message = 'Server error occurred. Please try again later.';
    this.showNotification('error', message);
    
    // Log error for monitoring
    console.error('Server error:', {
      status: response.status,
      statusText: response.statusText,
      url: response.url,
    });
  }

  /**
   * Show notification to user
   */
  private showNotification(type: 'success' | 'warning' | 'error' | 'info', message: string): void {
    // This would integrate with your notification system
    // For now, just console log
    console.log(`[${type.toUpperCase()}] ${message}`);
    
    // You could dispatch to a global notification store here
    // Example: store.dispatch(showNotification({ type, message }));
  }

  /**
   * HEALTH CHECK & MONITORING
   */

  /**
   * Check API health
   */
  async checkHealth(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    services: Record<string, 'up' | 'down' | 'degraded'>;
    timestamp: string;
    version: string;
  }> {
    return this.makeRequest<any>('/health');
  }

  /**
   * Get API status
   */
  async getApiStatus(): Promise<{
    uptime: number;
    requestsPerMinute: number;
    averageResponseTime: number;
    errorRate: number;
    activeConnections: number;
  }> {
    return this.makeRequest<any>('/status');
  }

  /**
   * BATCH OPERATIONS
   */

  /**
   * Execute multiple requests in parallel
   */
  async batchRequests<T>(requests: Array<{
    endpoint: string;
    options?: RequestInit;
  }>): Promise<Array<T | Error>> {
    const promises = requests.map(({ endpoint, options }) =>
      this.makeRequest<T>(endpoint, options).catch(error => error)
    );

    return Promise.all(promises);
  }

  /**
   * Execute requests with retry logic
   */
  async requestWithRetry<T>(
    endpoint: string,
    options: RequestInit = {},
    maxRetries: number = 3,
    retryDelay: number = 1000
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await this.makeRequest<T>(endpoint, options);
      } catch (error) {
        lastError = error as Error;
        
        if (attempt < maxRetries) {
          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, retryDelay * Math.pow(2, attempt)));
        }
      }
    }

    throw lastError!;
  }

  /**
   * CACHING
   */

  private cache: Map<string, { data: any; timestamp: number; ttl: number }> = new Map();

  /**
   * Make cached request
   */
  async cachedRequest<T>(
    endpoint: string,
    options: RequestInit = {},
    ttl: number = 300000 // 5 minutes default
  ): Promise<T> {
    const cacheKey = `${endpoint}:${JSON.stringify(options)}`;
    const cached = this.cache.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached.data;
    }

    const data = await this.makeRequest<T>(endpoint, options);
    this.cache.set(cacheKey, {
      data,
      timestamp: Date.now(),
      ttl,
    });

    return data;
  }

  /**
   * Clear cache
   */
  clearCache(pattern?: string): void {
    if (pattern) {
      for (const key of this.cache.keys()) {
        if (key.includes(pattern)) {
          this.cache.delete(key);
        }
      }
    } else {
      this.cache.clear();
    }
  }

  /**
   * REAL-TIME CONNECTIONS
   */

  /**
   * Initialize real-time connections
   */
  initializeRealTime(organizationId: string, channelIds: string[]): void {
    this.communication.connectRealTime(organizationId, channelIds);
  }

  /**
   * Cleanup real-time connections
   */
  cleanupRealTime(): void {
    this.communication.disconnectRealTime();
  }

  /**
   * UTILITY METHODS
   */

  /**
   * Upload file with progress
   */
  async uploadFile(
    endpoint: string,
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      const formData = new FormData();
      formData.append('file', file);

      const xhr = new XMLHttpRequest();

      if (onProgress) {
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const progress = (event.loaded / event.total) * 100;
            onProgress(progress);
          }
        });
      }

      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText);
            resolve(response);
          } catch (error) {
            resolve(xhr.responseText);
          }
        } else {
          reject(new Error(`Upload failed: ${xhr.statusText}`));
        }
      });

      xhr.addEventListener('error', () => {
        reject(new Error('Upload failed'));
      });

      const token = localStorage.getItem('token');
      if (token) {
        xhr.setRequestHeader('Authorization', `Bearer ${token}`);
      }

      xhr.open('POST', `${this.baseUrl}${endpoint}`);
      xhr.send(formData);
    });
  }

  /**
   * Download file
   */
  async downloadFile(endpoint: string, filename?: string): Promise<void> {
    const token = localStorage.getItem('token');
    
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      headers: {
        ...(token && { Authorization: `Bearer ${token}` }),
      },
    });

    if (!response.ok) {
      throw new Error(`Download failed: ${response.statusText}`);
    }

    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename || 'download';
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  }
}

export default ApiServiceManager;
