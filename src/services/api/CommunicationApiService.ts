/**
 * Communication API Service - Real Backend Integration
 * Real-time communication and collaboration
 */

import {
  CommunicationWorkspace,
  CommunicationChannel,
  Message,
  MessageAttachment,
  ChannelParticipant
} from '../../types/communication';

class CommunicationApiService {
  private static instance: CommunicationApiService;
  private baseUrl = '/api';
  private websocket: WebSocket | null = null;
  private messageHandlers: Map<string, Function[]> = new Map();

  static getInstance(): CommunicationApiService {
    if (!CommunicationApiService.instance) {
      CommunicationApiService.instance = new CommunicationApiService();
    }
    return CommunicationApiService.instance;
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const token = localStorage.getItem('token');
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    const response = await fetch(`${this.baseUrl}${endpoint}`, config);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * WORKSPACE MANAGEMENT
   */

  /**
   * Get communication workspaces for organization
   */
  async getWorkspaces(organizationId: string): Promise<CommunicationWorkspace[]> {
    return this.makeRequest<CommunicationWorkspace[]>(`/organizations/${organizationId}/communication/workspaces`);
  }

  /**
   * Create communication workspace
   */
  async createWorkspace(
    organizationId: string,
    workspaceData: {
      name: string;
      description?: string;
      tenderId?: string;
      workspaceType: 'tender' | 'project' | 'general';
      settings?: any;
    }
  ): Promise<CommunicationWorkspace> {
    return this.makeRequest<CommunicationWorkspace>(`/organizations/${organizationId}/communication/workspaces`, {
      method: 'POST',
      body: JSON.stringify(workspaceData),
    });
  }

  /**
   * Update workspace
   */
  async updateWorkspace(
    organizationId: string,
    workspaceId: string,
    updateData: Partial<CommunicationWorkspace>
  ): Promise<CommunicationWorkspace> {
    return this.makeRequest<CommunicationWorkspace>(`/organizations/${organizationId}/communication/workspaces/${workspaceId}`, {
      method: 'PUT',
      body: JSON.stringify(updateData),
    });
  }

  /**
   * CHANNEL MANAGEMENT
   */

  /**
   * Get channels in workspace
   */
  async getChannels(organizationId: string, workspaceId: string): Promise<CommunicationChannel[]> {
    return this.makeRequest<CommunicationChannel[]>(`/organizations/${organizationId}/communication/workspaces/${workspaceId}/channels`);
  }

  /**
   * Create channel
   */
  async createChannel(
    organizationId: string,
    workspaceId: string,
    channelData: {
      name: string;
      description?: string;
      channelType: 'general' | 'technical' | 'commercial' | 'compliance' | 'coordination' | 'announcements';
      isPrivate?: boolean;
      inviteOnly?: boolean;
    }
  ): Promise<CommunicationChannel> {
    return this.makeRequest<CommunicationChannel>(`/organizations/${organizationId}/communication/workspaces/${workspaceId}/channels`, {
      method: 'POST',
      body: JSON.stringify(channelData),
    });
  }

  /**
   * Update channel
   */
  async updateChannel(
    organizationId: string,
    channelId: string,
    updateData: Partial<CommunicationChannel>
  ): Promise<CommunicationChannel> {
    return this.makeRequest<CommunicationChannel>(`/organizations/${organizationId}/communication/channels/${channelId}`, {
      method: 'PUT',
      body: JSON.stringify(updateData),
    });
  }

  /**
   * Join channel
   */
  async joinChannel(organizationId: string, channelId: string): Promise<ChannelParticipant> {
    return this.makeRequest<ChannelParticipant>(`/organizations/${organizationId}/communication/channels/${channelId}/join`, {
      method: 'POST',
    });
  }

  /**
   * Leave channel
   */
  async leaveChannel(organizationId: string, channelId: string): Promise<void> {
    return this.makeRequest<void>(`/organizations/${organizationId}/communication/channels/${channelId}/leave`, {
      method: 'POST',
    });
  }

  /**
   * Get channel participants
   */
  async getChannelParticipants(organizationId: string, channelId: string): Promise<ChannelParticipant[]> {
    return this.makeRequest<ChannelParticipant[]>(`/organizations/${organizationId}/communication/channels/${channelId}/participants`);
  }

  /**
   * MESSAGE MANAGEMENT
   */

  /**
   * Get messages in channel
   */
  async getMessages(
    organizationId: string,
    channelId: string,
    options: {
      limit?: number;
      before?: string;
      after?: string;
    } = {}
  ): Promise<{
    messages: Message[];
    hasMore: boolean;
    nextCursor?: string;
  }> {
    const queryParams = new URLSearchParams();
    if (options.limit) queryParams.append('limit', options.limit.toString());
    if (options.before) queryParams.append('before', options.before);
    if (options.after) queryParams.append('after', options.after);

    const endpoint = `/organizations/${organizationId}/communication/channels/${channelId}/messages${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return this.makeRequest<any>(endpoint);
  }

  /**
   * Send message
   */
  async sendMessage(
    organizationId: string,
    channelId: string,
    messageData: {
      content: string;
      messageType?: 'text' | 'file' | 'image' | 'voice' | 'video';
      attachments?: File[];
      mentions?: string[];
      parentMessageId?: string;
      tenderContext?: any;
    }
  ): Promise<Message> {
    // Handle file uploads if attachments exist
    if (messageData.attachments && messageData.attachments.length > 0) {
      const formData = new FormData();
      formData.append('content', messageData.content);
      formData.append('messageType', messageData.messageType || 'text');
      
      messageData.attachments.forEach((file, index) => {
        formData.append(`attachment_${index}`, file);
      });

      if (messageData.mentions) {
        formData.append('mentions', JSON.stringify(messageData.mentions));
      }
      if (messageData.parentMessageId) {
        formData.append('parentMessageId', messageData.parentMessageId);
      }
      if (messageData.tenderContext) {
        formData.append('tenderContext', JSON.stringify(messageData.tenderContext));
      }

      const token = localStorage.getItem('token');
      const response = await fetch(`${this.baseUrl}/organizations/${organizationId}/communication/channels/${channelId}/messages`, {
        method: 'POST',
        headers: {
          ...(token && { Authorization: `Bearer ${token}` }),
        },
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to send message');
      }

      return response.json();
    } else {
      // Text-only message
      return this.makeRequest<Message>(`/organizations/${organizationId}/communication/channels/${channelId}/messages`, {
        method: 'POST',
        body: JSON.stringify({
          content: messageData.content,
          messageType: messageData.messageType || 'text',
          mentions: messageData.mentions,
          parentMessageId: messageData.parentMessageId,
          tenderContext: messageData.tenderContext,
        }),
      });
    }
  }

  /**
   * Update message
   */
  async updateMessage(
    organizationId: string,
    messageId: string,
    updateData: {
      content: string;
    }
  ): Promise<Message> {
    return this.makeRequest<Message>(`/organizations/${organizationId}/communication/messages/${messageId}`, {
      method: 'PUT',
      body: JSON.stringify(updateData),
    });
  }

  /**
   * Delete message
   */
  async deleteMessage(organizationId: string, messageId: string): Promise<void> {
    return this.makeRequest<void>(`/organizations/${organizationId}/communication/messages/${messageId}`, {
      method: 'DELETE',
    });
  }

  /**
   * Add reaction to message
   */
  async addReaction(
    organizationId: string,
    messageId: string,
    emoji: string
  ): Promise<void> {
    return this.makeRequest<void>(`/organizations/${organizationId}/communication/messages/${messageId}/reactions`, {
      method: 'POST',
      body: JSON.stringify({ emoji }),
    });
  }

  /**
   * Remove reaction from message
   */
  async removeReaction(
    organizationId: string,
    messageId: string,
    emoji: string
  ): Promise<void> {
    return this.makeRequest<void>(`/organizations/${organizationId}/communication/messages/${messageId}/reactions/${emoji}`, {
      method: 'DELETE',
    });
  }

  /**
   * REAL-TIME COMMUNICATION
   */

  /**
   * Connect to real-time communication
   */
  connectRealTime(organizationId: string, channelIds: string[]): void {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('Authentication required for real-time communication');
    }

    const wsUrl = `${process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3000'}/ws/communication?token=${token}&organizationId=${organizationId}&channels=${channelIds.join(',')}`;
    
    this.websocket = new WebSocket(wsUrl);

    this.websocket.onopen = () => {
      console.log('Real-time communication connected');
    };

    this.websocket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        this.handleRealTimeMessage(data);
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };

    this.websocket.onclose = () => {
      console.log('Real-time communication disconnected');
      // Attempt to reconnect after 3 seconds
      setTimeout(() => {
        if (this.websocket?.readyState === WebSocket.CLOSED) {
          this.connectRealTime(organizationId, channelIds);
        }
      }, 3000);
    };

    this.websocket.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
  }

  /**
   * Disconnect from real-time communication
   */
  disconnectRealTime(): void {
    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }
  }

  /**
   * Subscribe to real-time events
   */
  onRealTimeEvent(eventType: string, handler: Function): void {
    if (!this.messageHandlers.has(eventType)) {
      this.messageHandlers.set(eventType, []);
    }
    this.messageHandlers.get(eventType)!.push(handler);
  }

  /**
   * Unsubscribe from real-time events
   */
  offRealTimeEvent(eventType: string, handler: Function): void {
    const handlers = this.messageHandlers.get(eventType);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  /**
   * Handle incoming real-time messages
   */
  private handleRealTimeMessage(data: any): void {
    const { type, payload } = data;
    const handlers = this.messageHandlers.get(type);
    
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(payload);
        } catch (error) {
          console.error('Error in real-time message handler:', error);
        }
      });
    }
  }

  /**
   * Send typing indicator
   */
  sendTypingIndicator(channelId: string, isTyping: boolean): void {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify({
        type: 'typing',
        payload: {
          channelId,
          isTyping,
        },
      }));
    }
  }

  /**
   * Mark channel as read
   */
  async markChannelAsRead(organizationId: string, channelId: string): Promise<void> {
    return this.makeRequest<void>(`/organizations/${organizationId}/communication/channels/${channelId}/read`, {
      method: 'POST',
    });
  }

  /**
   * Get unread message count
   */
  async getUnreadCount(organizationId: string): Promise<{
    totalUnread: number;
    channelUnread: Record<string, number>;
  }> {
    return this.makeRequest<any>(`/organizations/${organizationId}/communication/unread`);
  }
}

export default CommunicationApiService;
