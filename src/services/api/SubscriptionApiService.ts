/**
 * Subscription API Service - Real Backend Integration
 * Subscription management and billing
 */

import {
  SubscriptionPlan,
  UserSubscription,
  BillingInfo,
  PaymentMethod,
  Invoice,
  UsageMetrics
} from '../../types/subscription';

class SubscriptionApiService {
  private static instance: SubscriptionApiService;
  private baseUrl = '/api';

  static getInstance(): SubscriptionApiService {
    if (!SubscriptionApiService.instance) {
      SubscriptionApiService.instance = new SubscriptionApiService();
    }
    return SubscriptionApiService.instance;
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const token = localStorage.getItem('token');
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    const response = await fetch(`${this.baseUrl}${endpoint}`, config);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * SUBSCRIPTION PLANS
   */

  /**
   * Get all available subscription plans
   */
  async getSubscriptionPlans(): Promise<SubscriptionPlan[]> {
    return this.makeRequest<SubscriptionPlan[]>('/subscriptions/plans');
  }

  /**
   * Get specific subscription plan
   */
  async getSubscriptionPlan(planId: string): Promise<SubscriptionPlan> {
    return this.makeRequest<SubscriptionPlan>(`/subscriptions/plans/${planId}`);
  }

  /**
   * USER SUBSCRIPTIONS
   */

  /**
   * Get user's current subscription
   */
  async getCurrentSubscription(): Promise<UserSubscription | null> {
    return this.makeRequest<UserSubscription | null>('/subscriptions/current');
  }

  /**
   * Subscribe to a plan
   */
  async subscribeToPlan(
    planId: string,
    paymentMethodId: string,
    billingInfo?: Partial<BillingInfo>
  ): Promise<{
    subscription: UserSubscription;
    clientSecret?: string; // For 3D Secure authentication
  }> {
    return this.makeRequest<any>('/subscriptions/subscribe', {
      method: 'POST',
      body: JSON.stringify({
        planId,
        paymentMethodId,
        billingInfo,
      }),
    });
  }

  /**
   * Change subscription plan
   */
  async changeSubscriptionPlan(
    newPlanId: string,
    prorationBehavior: 'create_prorations' | 'none' = 'create_prorations'
  ): Promise<UserSubscription> {
    return this.makeRequest<UserSubscription>('/subscriptions/change-plan', {
      method: 'POST',
      body: JSON.stringify({
        newPlanId,
        prorationBehavior,
      }),
    });
  }

  /**
   * Cancel subscription
   */
  async cancelSubscription(
    cancelAtPeriodEnd: boolean = true,
    cancellationReason?: string
  ): Promise<UserSubscription> {
    return this.makeRequest<UserSubscription>('/subscriptions/cancel', {
      method: 'POST',
      body: JSON.stringify({
        cancelAtPeriodEnd,
        cancellationReason,
      }),
    });
  }

  /**
   * Reactivate cancelled subscription
   */
  async reactivateSubscription(): Promise<UserSubscription> {
    return this.makeRequest<UserSubscription>('/subscriptions/reactivate', {
      method: 'POST',
    });
  }

  /**
   * PAYMENT METHODS
   */

  /**
   * Get user's payment methods
   */
  async getPaymentMethods(): Promise<PaymentMethod[]> {
    return this.makeRequest<PaymentMethod[]>('/subscriptions/payment-methods');
  }

  /**
   * Add payment method
   */
  async addPaymentMethod(paymentMethodId: string): Promise<PaymentMethod> {
    return this.makeRequest<PaymentMethod>('/subscriptions/payment-methods', {
      method: 'POST',
      body: JSON.stringify({ paymentMethodId }),
    });
  }

  /**
   * Set default payment method
   */
  async setDefaultPaymentMethod(paymentMethodId: string): Promise<void> {
    return this.makeRequest<void>(`/subscriptions/payment-methods/${paymentMethodId}/default`, {
      method: 'POST',
    });
  }

  /**
   * Remove payment method
   */
  async removePaymentMethod(paymentMethodId: string): Promise<void> {
    return this.makeRequest<void>(`/subscriptions/payment-methods/${paymentMethodId}`, {
      method: 'DELETE',
    });
  }

  /**
   * BILLING & INVOICES
   */

  /**
   * Get billing information
   */
  async getBillingInfo(): Promise<BillingInfo> {
    return this.makeRequest<BillingInfo>('/subscriptions/billing-info');
  }

  /**
   * Update billing information
   */
  async updateBillingInfo(billingInfo: Partial<BillingInfo>): Promise<BillingInfo> {
    return this.makeRequest<BillingInfo>('/subscriptions/billing-info', {
      method: 'PUT',
      body: JSON.stringify(billingInfo),
    });
  }

  /**
   * Get invoices
   */
  async getInvoices(limit: number = 20): Promise<{
    invoices: Invoice[];
    hasMore: boolean;
  }> {
    return this.makeRequest<any>(`/subscriptions/invoices?limit=${limit}`);
  }

  /**
   * Get specific invoice
   */
  async getInvoice(invoiceId: string): Promise<Invoice> {
    return this.makeRequest<Invoice>(`/subscriptions/invoices/${invoiceId}`);
  }

  /**
   * Download invoice PDF
   */
  async downloadInvoice(invoiceId: string): Promise<Blob> {
    const token = localStorage.getItem('token');
    
    const response = await fetch(`${this.baseUrl}/subscriptions/invoices/${invoiceId}/download`, {
      headers: {
        ...(token && { Authorization: `Bearer ${token}` }),
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to download invoice: ${response.statusText}`);
    }

    return response.blob();
  }

  /**
   * USAGE & METRICS
   */

  /**
   * Get current usage metrics
   */
  async getUsageMetrics(): Promise<UsageMetrics> {
    return this.makeRequest<UsageMetrics>('/subscriptions/usage');
  }

  /**
   * Get usage history
   */
  async getUsageHistory(period: 'month' | 'quarter' | 'year' = 'month'): Promise<{
    period: string;
    usage: UsageMetrics[];
  }> {
    return this.makeRequest<any>(`/subscriptions/usage/history?period=${period}`);
  }

  /**
   * Check feature access
   */
  async checkFeatureAccess(feature: string): Promise<{
    hasAccess: boolean;
    limit?: number;
    usage?: number;
    upgradeRequired?: boolean;
  }> {
    return this.makeRequest<any>(`/subscriptions/features/${feature}/access`);
  }

  /**
   * STRIPE INTEGRATION
   */

  /**
   * Create Stripe setup intent for payment method
   */
  async createSetupIntent(): Promise<{
    clientSecret: string;
    setupIntentId: string;
  }> {
    return this.makeRequest<any>('/subscriptions/stripe/setup-intent', {
      method: 'POST',
    });
  }

  /**
   * Create Stripe payment intent for one-time payment
   */
  async createPaymentIntent(
    amount: number,
    currency: string = 'ZAR',
    description?: string
  ): Promise<{
    clientSecret: string;
    paymentIntentId: string;
  }> {
    return this.makeRequest<any>('/subscriptions/stripe/payment-intent', {
      method: 'POST',
      body: JSON.stringify({
        amount,
        currency,
        description,
      }),
    });
  }

  /**
   * Confirm payment intent
   */
  async confirmPaymentIntent(
    paymentIntentId: string,
    paymentMethodId: string
  ): Promise<{
    status: string;
    requiresAction?: boolean;
    clientSecret?: string;
  }> {
    return this.makeRequest<any>('/subscriptions/stripe/confirm-payment', {
      method: 'POST',
      body: JSON.stringify({
        paymentIntentId,
        paymentMethodId,
      }),
    });
  }

  /**
   * SUBSCRIPTION ANALYTICS
   */

  /**
   * Get subscription analytics
   */
  async getSubscriptionAnalytics(): Promise<{
    subscriptionValue: number;
    renewalDate: string;
    daysUntilRenewal: number;
    usageEfficiency: number;
    costPerFeature: Record<string, number>;
    recommendations: Array<{
      type: 'upgrade' | 'downgrade' | 'feature';
      title: string;
      description: string;
      potentialSavings?: number;
      potentialValue?: number;
    }>;
  }> {
    return this.makeRequest<any>('/subscriptions/analytics');
  }

  /**
   * TRIAL MANAGEMENT
   */

  /**
   * Start free trial
   */
  async startFreeTrial(planId: string): Promise<UserSubscription> {
    return this.makeRequest<UserSubscription>('/subscriptions/trial/start', {
      method: 'POST',
      body: JSON.stringify({ planId }),
    });
  }

  /**
   * Extend trial period
   */
  async extendTrial(days: number): Promise<UserSubscription> {
    return this.makeRequest<UserSubscription>('/subscriptions/trial/extend', {
      method: 'POST',
      body: JSON.stringify({ days }),
    });
  }

  /**
   * Convert trial to paid subscription
   */
  async convertTrialToPaid(paymentMethodId: string): Promise<UserSubscription> {
    return this.makeRequest<UserSubscription>('/subscriptions/trial/convert', {
      method: 'POST',
      body: JSON.stringify({ paymentMethodId }),
    });
  }

  /**
   * ORGANIZATION SUBSCRIPTIONS
   */

  /**
   * Get organization subscription
   */
  async getOrganizationSubscription(organizationId: string): Promise<UserSubscription | null> {
    return this.makeRequest<UserSubscription | null>(`/organizations/${organizationId}/subscription`);
  }

  /**
   * Transfer subscription to organization
   */
  async transferSubscriptionToOrganization(organizationId: string): Promise<UserSubscription> {
    return this.makeRequest<UserSubscription>(`/subscriptions/transfer-to-organization`, {
      method: 'POST',
      body: JSON.stringify({ organizationId }),
    });
  }
}

export default SubscriptionApiService;
