/**
 * Tender API Service - Real Backend Integration
 * Replaces simulation with actual API calls
 */

import { Tender, TenderInterest, TenderFilters } from '../../types/tender';

class TenderApiService {
  private static instance: TenderApiService;
  private baseUrl = '/api';

  static getInstance(): TenderApiService {
    if (!TenderApiService.instance) {
      TenderApiService.instance = new TenderApiService();
    }
    return TenderApiService.instance;
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const token = localStorage.getItem('token');
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    const response = await fetch(`${this.baseUrl}${endpoint}`, config);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Get tenders with filtering and pagination
   */
  async getTenders(filters: TenderFilters = {}): Promise<{
    tenders: Tender[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNextPage: boolean;
      hasPreviousPage: boolean;
    };
  }> {
    const queryParams = new URLSearchParams();
    
    // Add filters to query params
    if (filters.page) queryParams.append('page', filters.page.toString());
    if (filters.limit) queryParams.append('limit', filters.limit.toString());
    if (filters.search) queryParams.append('search', filters.search);
    if (filters.category) queryParams.append('category', filters.category);
    if (filters.sector) queryParams.append('sector', filters.sector);
    if (filters.status) queryParams.append('status', filters.status);
    if (filters.minValue) queryParams.append('minValue', filters.minValue.toString());
    if (filters.maxValue) queryParams.append('maxValue', filters.maxValue.toString());
    if (filters.location) queryParams.append('location', filters.location);
    if (filters.sortBy) queryParams.append('sortBy', filters.sortBy);
    if (filters.sortOrder) queryParams.append('sortOrder', filters.sortOrder);
    if (filters.includeExpired) queryParams.append('includeExpired', filters.includeExpired.toString());

    const endpoint = `/tenders${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return this.makeRequest<any>(endpoint);
  }

  /**
   * Get single tender by ID
   */
  async getTender(tenderId: string): Promise<Tender> {
    return this.makeRequest<Tender>(`/tenders/${tenderId}`);
  }

  /**
   * Express interest in a tender (individual)
   */
  async expressInterest(
    tenderId: string,
    interestData: {
      interestLevel: 'watching' | 'interested' | 'committed';
      notes?: string;
      analysis?: any;
    }
  ): Promise<TenderInterest> {
    return this.makeRequest<TenderInterest>(`/tenders/${tenderId}/interest`, {
      method: 'POST',
      body: JSON.stringify(interestData),
    });
  }

  /**
   * Update tender interest
   */
  async updateInterest(
    tenderId: string,
    interestData: Partial<TenderInterest>
  ): Promise<TenderInterest> {
    return this.makeRequest<TenderInterest>(`/tenders/${tenderId}/interest`, {
      method: 'PUT',
      body: JSON.stringify(interestData),
    });
  }

  /**
   * Withdraw interest from tender
   */
  async withdrawInterest(tenderId: string): Promise<void> {
    return this.makeRequest<void>(`/tenders/${tenderId}/interest`, {
      method: 'DELETE',
    });
  }

  /**
   * Get user's tender interests
   */
  async getUserInterests(): Promise<TenderInterest[]> {
    return this.makeRequest<TenderInterest[]>('/user/tender-interests');
  }

  /**
   * Get tender statistics
   */
  async getTenderStatistics(): Promise<{
    totalTenders: number;
    activeTenders: number;
    totalValue: number;
    averageValue: number;
    categoriesCount: Record<string, number>;
    sectorsCount: Record<string, number>;
    monthlyTrends: Array<{ month: string; count: number; value: number }>;
  }> {
    return this.makeRequest<any>('/tenders/statistics');
  }

  /**
   * Get trending categories
   */
  async getTrendingCategories(limit: number = 10): Promise<Array<{
    category: string;
    count: number;
    growth: number;
  }>> {
    return this.makeRequest<any>(`/tenders/trending-categories?limit=${limit}`);
  }

  /**
   * Search tenders with advanced filters
   */
  async searchTenders(searchQuery: {
    query: string;
    filters?: TenderFilters;
    facets?: string[];
  }): Promise<{
    tenders: Tender[];
    facets: Record<string, Array<{ value: string; count: number }>>;
    suggestions: string[];
    total: number;
  }> {
    return this.makeRequest<any>('/tenders/search', {
      method: 'POST',
      body: JSON.stringify(searchQuery),
    });
  }

  /**
   * Get tender recommendations for user
   */
  async getTenderRecommendations(limit: number = 10): Promise<{
    tenders: Tender[];
    reasons: Record<string, string[]>;
  }> {
    return this.makeRequest<any>(`/tenders/recommendations?limit=${limit}`);
  }

  /**
   * Get tender alerts/notifications
   */
  async getTenderAlerts(): Promise<Array<{
    id: string;
    tenderId: string;
    type: 'new_tender' | 'closing_soon' | 'amendment' | 'award';
    message: string;
    createdAt: string;
    read: boolean;
  }>> {
    return this.makeRequest<any>('/tenders/alerts');
  }

  /**
   * Mark tender alert as read
   */
  async markAlertAsRead(alertId: string): Promise<void> {
    return this.makeRequest<void>(`/tenders/alerts/${alertId}/read`, {
      method: 'POST',
    });
  }

  /**
   * Subscribe to tender alerts
   */
  async subscribeTenderAlerts(preferences: {
    categories: string[];
    sectors: string[];
    minValue?: number;
    maxValue?: number;
    locations: string[];
    keywords: string[];
    emailNotifications: boolean;
    smsNotifications: boolean;
    pushNotifications: boolean;
  }): Promise<void> {
    return this.makeRequest<void>('/tenders/alerts/subscribe', {
      method: 'POST',
      body: JSON.stringify(preferences),
    });
  }

  /**
   * Get tender documents
   */
  async getTenderDocuments(tenderId: string): Promise<Array<{
    id: string;
    name: string;
    type: string;
    size: number;
    url: string;
    uploadedAt: string;
  }>> {
    return this.makeRequest<any>(`/tenders/${tenderId}/documents`);
  }

  /**
   * Download tender document
   */
  async downloadTenderDocument(tenderId: string, documentId: string): Promise<Blob> {
    const token = localStorage.getItem('token');
    
    const response = await fetch(`${this.baseUrl}/tenders/${tenderId}/documents/${documentId}/download`, {
      headers: {
        ...(token && { Authorization: `Bearer ${token}` }),
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to download document: ${response.statusText}`);
    }

    return response.blob();
  }

  /**
   * Get tender timeline/history
   */
  async getTenderTimeline(tenderId: string): Promise<Array<{
    id: string;
    type: 'published' | 'amended' | 'clarification' | 'deadline_extended' | 'awarded' | 'cancelled';
    title: string;
    description: string;
    date: string;
    documents?: Array<{ name: string; url: string }>;
  }>> {
    return this.makeRequest<any>(`/tenders/${tenderId}/timeline`);
  }

  /**
   * Submit tender question/clarification
   */
  async submitTenderQuestion(
    tenderId: string,
    question: {
      subject: string;
      question: string;
      category: string;
    }
  ): Promise<{
    id: string;
    status: 'submitted' | 'answered' | 'rejected';
    submittedAt: string;
  }> {
    return this.makeRequest<any>(`/tenders/${tenderId}/questions`, {
      method: 'POST',
      body: JSON.stringify(question),
    });
  }

  /**
   * Get tender questions and answers
   */
  async getTenderQuestions(tenderId: string): Promise<Array<{
    id: string;
    question: string;
    answer?: string;
    category: string;
    submittedAt: string;
    answeredAt?: string;
    isPublic: boolean;
  }>> {
    return this.makeRequest<any>(`/tenders/${tenderId}/questions`);
  }

  /**
   * Get similar tenders
   */
  async getSimilarTenders(tenderId: string, limit: number = 5): Promise<Tender[]> {
    return this.makeRequest<Tender[]>(`/tenders/${tenderId}/similar?limit=${limit}`);
  }

  /**
   * Export tenders data
   */
  async exportTenders(
    filters: TenderFilters,
    format: 'csv' | 'excel' | 'pdf'
  ): Promise<Blob> {
    const queryParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });
    queryParams.append('format', format);

    const token = localStorage.getItem('token');
    
    const response = await fetch(`${this.baseUrl}/tenders/export?${queryParams.toString()}`, {
      headers: {
        ...(token && { Authorization: `Bearer ${token}` }),
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to export tenders: ${response.statusText}`);
    }

    return response.blob();
  }
}

export default TenderApiService;
