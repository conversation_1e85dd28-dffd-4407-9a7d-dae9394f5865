/**
 * Authentication Context with SA Compliance Integration
 * Manages user authentication and SME profile data
 */

import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { setCredentials, logout as logoutAction } from '../store/store';
import { SMEClassification } from '../types/compliance';

// User interface with compliance extensions
export interface User {
  id: string;
  email: string;
  username: string;
  companyName: string;
  role: 'bidder' | 'admin' | 'bee' | 'supplier';
  
  // SME-specific fields
  isSME: boolean;
  smeClassification?: SMEClassification;
  annualTurnover?: number;
  employeeCount?: number;
  yearsInBusiness?: number;
  
  // Compliance status
  beeLevel?: string;
  cidbGrade?: string;
  taxCompliant?: boolean;
  
  // Profile completion
  profileComplete: boolean;
  complianceProfileComplete: boolean;
  
  // Preferences
  preferences: {
    language: string;
    notifications: boolean;
    adaptiveInterface: boolean;
    neuroMarketingOptOut: boolean;
  };
  
  // Timestamps
  createdAt: string;
  lastLogin: string;
  lastComplianceUpdate?: string;
}

// Auth state interface
interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// Auth actions
type AuthAction =
  | { type: 'LOGIN_START' }
  | { type: 'LOGIN_SUCCESS'; payload: { user: User; token: string } }
  | { type: 'LOGIN_FAILURE'; payload: string }
  | { type: 'LOGOUT' }
  | { type: 'UPDATE_USER'; payload: Partial<User> }
  | { type: 'CLEAR_ERROR' }
  | { type: 'SET_LOADING'; payload: boolean };

// Initial state
const initialState: AuthState = {
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

// Auth reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'LOGIN_START':
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };
    
    case 'LOGIN_FAILURE':
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload,
      };
    
    case 'LOGOUT':
      return {
        ...initialState,
      };
    
    case 'UPDATE_USER':
      return {
        ...state,
        user: state.user ? { ...state.user, ...action.payload } : null,
      };
    
    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };
    
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
    
    default:
      return state;
  }
};

// Auth context interface
interface AuthContextType {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => void;
  updateUser: (userData: Partial<User>) => void;
  clearError: () => void;
  refreshToken: () => Promise<void>;
  
  // SME-specific methods
  updateSMEProfile: (smeData: SMEProfileData) => Promise<void>;
  checkComplianceStatus: () => Promise<ComplianceStatus>;
}

// Registration data interface
interface RegisterData {
  email: string;
  password: string;
  username: string;
  companyName: string;
  role: 'bidder' | 'bee' | 'supplier';
  
  // Optional SME data
  isSME?: boolean;
  smeClassification?: SMEClassification;
  annualTurnover?: number;
  employeeCount?: number;
  yearsInBusiness?: number;
}

// SME profile data interface
interface SMEProfileData {
  smeClassification: SMEClassification;
  annualTurnover: number;
  employeeCount: number;
  yearsInBusiness: number;
  industry: string;
  primaryServices: string[];
  geographicFocus: string[];
}

// Compliance status interface
interface ComplianceStatus {
  overall: 'compliant' | 'partial' | 'non-compliant';
  beeStatus: 'valid' | 'expired' | 'missing';
  taxStatus: 'compliant' | 'non-compliant';
  cidbStatus: 'valid' | 'expired' | 'missing';
  lastUpdated: string;
  expiryDates: {
    bee?: string;
    tax?: string;
    cidb?: string;
  };
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);
  const reduxDispatch = useDispatch();

  // Load user from localStorage on mount
  useEffect(() => {
    const loadStoredAuth = () => {
      try {
        const storedToken = localStorage.getItem('token');
        const storedUser = localStorage.getItem('user');
        
        if (storedToken && storedUser) {
          const user = JSON.parse(storedUser);
          dispatch({
            type: 'LOGIN_SUCCESS',
            payload: { user, token: storedToken }
          });
          
          // Update Redux store
          reduxDispatch(setCredentials({ user, token: storedToken }));
        }
      } catch (error) {
        console.error('Error loading stored auth:', error);
        localStorage.removeItem('token');
        localStorage.removeItem('user');
      }
    };

    loadStoredAuth();
  }, [reduxDispatch]);

  // Login function
  const login = async (email: string, password: string, rememberMe: boolean = false): Promise<void> => {
    dispatch({ type: 'LOGIN_START' });

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password, rememberMe }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Login failed');
      }

      const data = await response.json();
      const { user, token, redirectTo, sessionExpiry } = data;

      // Store in localStorage
      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify(user));
      localStorage.setItem('sessionExpiry', sessionExpiry);

      // Update local state
      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: { user, token }
      });

      // Update Redux store
      reduxDispatch(setCredentials({ user, token }));

      // Redirect to appropriate page
      if (redirectTo) {
        window.location.href = redirectTo;
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      dispatch({
        type: 'LOGIN_FAILURE',
        payload: errorMessage
      });
      throw error;
    }
  };

  // Register function
  const register = async (userData: RegisterData): Promise<void> => {
    dispatch({ type: 'LOGIN_START' });

    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: userData.email,
          password: userData.password,
          fullName: userData.fullName,
          userType: userData.userType || 'individual',
          phone: userData.phone,
          companyName: userData.companyName,
          industry: userData.industry,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Registration failed');
      }

      const data = await response.json();
      const { user, token, redirectTo } = data;

      // Store in localStorage
      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify(user));

      // Update local state
      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: { user, token }
      });

      // Update Redux store
      reduxDispatch(setCredentials({ user, token }));

      // Redirect to appropriate page
      if (redirectTo) {
        window.location.href = redirectTo;
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Registration failed';
      dispatch({
        type: 'LOGIN_FAILURE',
        payload: errorMessage
      });
      throw error;
    }
  };

  // Logout function
  const logout = (): void => {
    // Clear localStorage
    localStorage.removeItem('token');
    localStorage.removeItem('user');

    // Update local state
    dispatch({ type: 'LOGOUT' });

    // Update Redux store
    reduxDispatch(logoutAction());
  };

  // Update user function
  const updateUser = (userData: Partial<User>): void => {
    if (state.user) {
      const updatedUser = { ...state.user, ...userData };
      
      // Update localStorage
      localStorage.setItem('user', JSON.stringify(updatedUser));
      
      // Update local state
      dispatch({
        type: 'UPDATE_USER',
        payload: userData
      });

      // Update Redux store
      if (state.token) {
        reduxDispatch(setCredentials({ user: updatedUser, token: state.token }));
      }
    }
  };

  // Clear error function
  const clearError = (): void => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  // Refresh token function
  const refreshToken = async (): Promise<void> => {
    if (!state.token) return;

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${state.token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Token refresh failed');
      }

      const data = await response.json();
      const { token: newToken } = data;

      // Update localStorage
      localStorage.setItem('token', newToken);

      // Update local state
      if (state.user) {
        dispatch({
          type: 'LOGIN_SUCCESS',
          payload: { user: state.user, token: newToken }
        });

        // Update Redux store
        reduxDispatch(setCredentials({ user: state.user, token: newToken }));
      }

    } catch (error) {
      console.error('Token refresh failed:', error);
      logout();
    }
  };

  // Update SME profile function
  const updateSMEProfile = async (smeData: SMEProfileData): Promise<void> => {
    if (!state.token || !state.user) {
      throw new Error('User not authenticated');
    }

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/compliance/sme/profile/${state.user.id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${state.token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(smeData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'SME profile update failed');
      }

      const updatedProfile = await response.json();
      
      // Update user with SME data
      updateUser({
        isSME: true,
        smeClassification: smeData.smeClassification,
        annualTurnover: smeData.annualTurnover,
        employeeCount: smeData.employeeCount,
        yearsInBusiness: smeData.yearsInBusiness,
        complianceProfileComplete: true,
        lastComplianceUpdate: new Date().toISOString()
      });

    } catch (error) {
      console.error('SME profile update failed:', error);
      throw error;
    }
  };

  // Check compliance status function
  const checkComplianceStatus = async (): Promise<ComplianceStatus> => {
    if (!state.token || !state.user) {
      throw new Error('User not authenticated');
    }

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/compliance/status/${state.user.id}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${state.token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Compliance status check failed');
      }

      const complianceStatus = await response.json();
      return complianceStatus;

    } catch (error) {
      console.error('Compliance status check failed:', error);
      throw error;
    }
  };

  // Context value
  const value: AuthContextType = {
    user: state.user,
    token: state.token,
    isAuthenticated: state.isAuthenticated,
    isLoading: state.isLoading,
    error: state.error,
    login,
    register,
    logout,
    updateUser,
    clearError,
    refreshToken,
    updateSMEProfile,
    checkComplianceStatus,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
