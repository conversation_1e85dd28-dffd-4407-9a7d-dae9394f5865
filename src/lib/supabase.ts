/**
 * Supabase Configuration and Client Setup
 * Real-time database and authentication integration
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../types/database';

// Environment variables validation
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

// Create Supabase client
export const supabase: SupabaseClient<Database> = createClient(
  supabaseUrl,
  supabaseAnonKey,
  {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true,
    },
    realtime: {
      params: {
        eventsPerSecond: 10,
      },
    },
    global: {
      headers: {
        'X-Client-Info': 'bidbeez-web',
      },
    },
  }
);

// Service role client for server-side operations
export const supabaseAdmin = createClient(
  supabaseUrl,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

/**
 * Database table type definitions
 */
export type Tables = Database['public']['Tables'];
export type User = Tables['users']['Row'];
export type UserInsert = Tables['users']['Insert'];
export type UserUpdate = Tables['users']['Update'];

export type Organization = Tables['organizations']['Row'];
export type OrganizationInsert = Tables['organizations']['Insert'];
export type OrganizationUpdate = Tables['organizations']['Update'];

export type TeamMember = Tables['team_members']['Row'];
export type TeamMemberInsert = Tables['team_members']['Insert'];
export type TeamMemberUpdate = Tables['team_members']['Update'];

export type Tender = Tables['tenders']['Row'];
export type TenderInsert = Tables['tenders']['Insert'];
export type TenderUpdate = Tables['tenders']['Update'];

export type Subscription = Tables['subscriptions']['Row'];
export type SubscriptionInsert = Tables['subscriptions']['Insert'];
export type SubscriptionUpdate = Tables['subscriptions']['Update'];

export type Message = Tables['messages']['Row'];
export type MessageInsert = Tables['messages']['Insert'];
export type MessageUpdate = Tables['messages']['Update'];

/**
 * Real-time subscription management
 */
class SupabaseRealtimeManager {
  private subscriptions: Map<string, any> = new Map();

  /**
   * Subscribe to table changes
   */
  subscribeToTable<T>(
    table: string,
    callback: (payload: {
      eventType: 'INSERT' | 'UPDATE' | 'DELETE';
      new: T | null;
      old: T | null;
    }) => void,
    filter?: string
  ): string {
    const subscriptionId = `${table}_${Date.now()}_${Math.random()}`;
    
    let subscription = supabase
      .channel(subscriptionId)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table,
        ...(filter && { filter }),
      }, callback);

    subscription.subscribe();
    this.subscriptions.set(subscriptionId, subscription);
    
    return subscriptionId;
  }

  /**
   * Subscribe to user's tender interests
   */
  subscribeToUserTenderInterests(
    userId: string,
    callback: (payload: any) => void
  ): string {
    return this.subscribeToTable(
      'user_tender_interests',
      callback,
      `user_id=eq.${userId}`
    );
  }

  /**
   * Subscribe to organization's team tender interests
   */
  subscribeToTeamTenderInterests(
    organizationId: string,
    callback: (payload: any) => void
  ): string {
    return this.subscribeToTable(
      'team_tender_interests',
      callback,
      `organization_id=eq.${organizationId}`
    );
  }

  /**
   * Subscribe to communication messages
   */
  subscribeToMessages(
    channelId: string,
    callback: (payload: any) => void
  ): string {
    return this.subscribeToTable(
      'messages',
      callback,
      `channel_id=eq.${channelId}`
    );
  }

  /**
   * Subscribe to team member changes
   */
  subscribeToTeamMembers(
    organizationId: string,
    callback: (payload: any) => void
  ): string {
    return this.subscribeToTable(
      'team_members',
      callback,
      `organization_id=eq.${organizationId}`
    );
  }

  /**
   * Subscribe to talent applications
   */
  subscribeToTalentApplications(
    organizationId: string,
    callback: (payload: any) => void
  ): string {
    return this.subscribeToTable(
      'talent_applications',
      callback,
      `talent_request_id=in.(select id from talent_requests where organization_id=eq.${organizationId})`
    );
  }

  /**
   * Unsubscribe from specific subscription
   */
  unsubscribe(subscriptionId: string): void {
    const subscription = this.subscriptions.get(subscriptionId);
    if (subscription) {
      subscription.unsubscribe();
      this.subscriptions.delete(subscriptionId);
    }
  }

  /**
   * Unsubscribe from all subscriptions
   */
  unsubscribeAll(): void {
    for (const [id, subscription] of this.subscriptions) {
      subscription.unsubscribe();
    }
    this.subscriptions.clear();
  }
}

export const realtimeManager = new SupabaseRealtimeManager();

/**
 * Authentication helpers
 */
export const auth = {
  /**
   * Get current user
   */
  async getCurrentUser() {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) throw error;
    return user;
  },

  /**
   * Sign in with email and password
   */
  async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    if (error) throw error;
    return data;
  },

  /**
   * Sign up with email and password
   */
  async signUp(email: string, password: string, metadata?: any) {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata,
      },
    });
    if (error) throw error;
    return data;
  },

  /**
   * Sign out
   */
  async signOut() {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  },

  /**
   * Reset password
   */
  async resetPassword(email: string) {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`,
    });
    if (error) throw error;
  },

  /**
   * Update password
   */
  async updatePassword(password: string) {
    const { error } = await supabase.auth.updateUser({ password });
    if (error) throw error;
  },

  /**
   * Listen to auth state changes
   */
  onAuthStateChange(callback: (event: string, session: any) => void) {
    return supabase.auth.onAuthStateChange(callback);
  },
};

/**
 * Database helpers
 */
export const db = {
  /**
   * Get user profile
   */
  async getUserProfile(userId: string) {
    const { data, error } = await supabase
      .from('users')
      .select(`
        *,
        user_profiles(*),
        organizations!organizations_owner_id_fkey(*),
        team_members(
          *,
          organizations(*)
        )
      `)
      .eq('id', userId)
      .single();

    if (error) throw error;
    return data;
  },

  /**
   * Get organization with team members
   */
  async getOrganizationWithMembers(organizationId: string) {
    const { data, error } = await supabase
      .from('organizations')
      .select(`
        *,
        team_members(
          *,
          users(*)
        )
      `)
      .eq('id', organizationId)
      .single();

    if (error) throw error;
    return data;
  },

  /**
   * Get tenders with user interests
   */
  async getTendersWithInterests(userId: string, organizationId?: string) {
    let query = supabase
      .from('tenders')
      .select(`
        *,
        user_tender_interests!left(*)
      `);

    if (organizationId) {
      query = query.select(`
        *,
        user_tender_interests!left(*),
        team_tender_interests!left(*)
      `);
    }

    const { data, error } = await query
      .eq('status', 'published')
      .gte('closing_date', new Date().toISOString())
      .order('closing_date', { ascending: true });

    if (error) throw error;
    return data;
  },

  /**
   * Get communication workspace with channels
   */
  async getCommunicationWorkspace(workspaceId: string) {
    const { data, error } = await supabase
      .from('communication_workspaces')
      .select(`
        *,
        communication_channels(
          *,
          channel_participants(
            *,
            users(*)
          )
        )
      `)
      .eq('id', workspaceId)
      .single();

    if (error) throw error;
    return data;
  },

  /**
   * Get messages with sender info
   */
  async getMessages(channelId: string, limit: number = 50) {
    const { data, error } = await supabase
      .from('messages')
      .select(`
        *,
        users(*),
        message_attachments(*)
      `)
      .eq('channel_id', channelId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data?.reverse() || [];
  },
};

/**
 * Storage helpers
 */
export const storage = {
  /**
   * Upload file
   */
  async uploadFile(bucket: string, path: string, file: File) {
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(path, file);

    if (error) throw error;
    return data;
  },

  /**
   * Get file URL
   */
  getFileUrl(bucket: string, path: string) {
    const { data } = supabase.storage
      .from(bucket)
      .getPublicUrl(path);

    return data.publicUrl;
  },

  /**
   * Delete file
   */
  async deleteFile(bucket: string, path: string) {
    const { error } = await supabase.storage
      .from(bucket)
      .remove([path]);

    if (error) throw error;
  },
};

export default supabase;
