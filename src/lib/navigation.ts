// Navigation utility to replace react-router-dom with Next.js navigation
'use client';

import { useRouter } from 'next/navigation';
import React from 'react';

export const useNavigate = () => {
  const router = useRouter();
  
  return (path: string) => {
    router.push(path);
  };
};

interface LinkProps {
  to: string;
  children: React.ReactNode;
  className?: string;
  [key: string]: any;
}

export const Link: React.FC<LinkProps> = ({ to, children, className, ...props }) => {
  return React.createElement('a', { href: to, className, ...props }, children);
};

export { useRouter };