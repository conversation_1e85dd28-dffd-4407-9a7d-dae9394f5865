import { NextApiRequest } from 'next';
import { createClient } from '@supabase/supabase-js';
import jwt from 'jsonwebtoken';

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export interface AuthUser {
  id: string;
  authUserId: string;
  email: string;
  fullName: string;
  userType: 'individual' | 'team_lead' | 'organization';
  organizationId?: string;
  role?: string;
  subscriptionTier?: string;
}

export interface AuthResult {
  success: boolean;
  user?: AuthUser;
  error?: string;
}

/**
 * Authenticate user from request headers
 */
export async function authenticateUser(req: NextApiRequest): Promise<AuthResult> {
  try {
    // Get token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return { success: false, error: 'No authorization token provided' };
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Verify JWT token
    let decoded: any;
    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET!);
    } catch (jwtError) {
      return { success: false, error: 'Invalid or expired token' };
    }

    // Get user from database
    const { data: user, error } = await supabase
      .from('users')
      .select(`
        id,
        auth_user_id,
        email,
        full_name,
        user_type,
        account_status,
        profile_completed,
        onboarding_completed
      `)
      .eq('id', decoded.userId)
      .eq('account_status', 'active')
      .single();

    if (error || !user) {
      return { success: false, error: 'User not found or inactive' };
    }

    // Verify auth user still exists in Supabase Auth
    const { data: authUser, error: authError } = await supabase.auth.admin.getUserById(user.auth_user_id);
    if (authError || !authUser.user) {
      return { success: false, error: 'Authentication session invalid' };
    }

    return {
      success: true,
      user: {
        id: user.id,
        authUserId: user.auth_user_id,
        email: user.email,
        fullName: user.full_name,
        userType: user.user_type,
        organizationId: decoded.organizationId,
        role: decoded.role,
        subscriptionTier: decoded.subscriptionTier,
      },
    };

  } catch (error) {
    console.error('Authentication error:', error);
    return { success: false, error: 'Authentication failed' };
  }
}

/**
 * Authenticate user and check organization membership
 */
export async function authenticateOrganizationMember(
  req: NextApiRequest,
  organizationId: string,
  requiredRoles?: string[]
): Promise<AuthResult> {
  const authResult = await authenticateUser(req);
  if (!authResult.success) {
    return authResult;
  }

  const { user } = authResult;

  // Check if user is member of the organization
  const { data: membership, error } = await supabase
    .from('team_members')
    .select('role, status')
    .eq('user_id', user.id)
    .eq('organization_id', organizationId)
    .eq('status', 'active')
    .single();

  if (error || !membership) {
    return { success: false, error: 'Not a member of this organization' };
  }

  // Check role requirements
  if (requiredRoles && !requiredRoles.includes(membership.role)) {
    return { success: false, error: 'Insufficient permissions' };
  }

  return {
    success: true,
    user: {
      ...user,
      organizationId,
      role: membership.role,
    },
  };
}

/**
 * Check if user has specific permission
 */
export async function checkUserPermission(
  userId: string,
  permission: string,
  organizationId?: string
): Promise<boolean> {
  try {
    if (organizationId) {
      // Check organization-level permission
      const { data: membership } = await supabase
        .from('team_members')
        .select('role, permissions')
        .eq('user_id', userId)
        .eq('organization_id', organizationId)
        .eq('status', 'active')
        .single();

      if (!membership) return false;

      // Check if user has 'all' permissions or specific permission
      const permissions = membership.permissions || [];
      return permissions.includes('all') || permissions.includes(permission);
    } else {
      // Check individual user permission
      // For now, individual users have basic permissions
      const basicPermissions = [
        'view_tenders',
        'express_interest',
        'manage_profile',
        'view_notifications',
      ];
      return basicPermissions.includes(permission);
    }
  } catch (error) {
    console.error('Permission check error:', error);
    return false;
  }
}

/**
 * Get user's subscription limits
 */
export async function getUserSubscriptionLimits(userId: string): Promise<any> {
  try {
    const { data: subscription } = await supabase
      .from('subscriptions')
      .select(`
        status,
        subscription_plans(
          limits,
          features
        )
      `)
      .eq('user_id', userId)
      .eq('status', 'active')
      .single();

    if (!subscription || !subscription.subscription_plans) {
      // Return default free tier limits
      return {
        maxTenderInterests: 5,
        maxTeamMembers: 1,
        maxDocumentStorage: 100, // MB
        monthlyBidLimit: 10,
        advancedAnalytics: false,
        prioritySupport: false,
        apiAccess: false,
      };
    }

    return {
      ...subscription.subscription_plans.limits,
      ...subscription.subscription_plans.features,
    };
  } catch (error) {
    console.error('Get subscription limits error:', error);
    return null;
  }
}

/**
 * Check if user has reached subscription limit
 */
export async function checkSubscriptionLimit(
  userId: string,
  limitType: string,
  currentUsage: number
): Promise<{ allowed: boolean; limit: number; usage: number }> {
  try {
    const limits = await getUserSubscriptionLimits(userId);
    if (!limits) {
      return { allowed: false, limit: 0, usage: currentUsage };
    }

    const limit = limits[limitType];
    if (limit === undefined || limit === -1) {
      // Unlimited
      return { allowed: true, limit: -1, usage: currentUsage };
    }

    return {
      allowed: currentUsage < limit,
      limit,
      usage: currentUsage,
    };
  } catch (error) {
    console.error('Check subscription limit error:', error);
    return { allowed: false, limit: 0, usage: currentUsage };
  }
}

/**
 * Middleware to require authentication
 */
export function requireAuth(handler: Function) {
  return async (req: NextApiRequest, res: any) => {
    const authResult = await authenticateUser(req);
    if (!authResult.success) {
      return res.status(401).json({ error: authResult.error });
    }

    // Add user to request object
    (req as any).user = authResult.user;
    return handler(req, res);
  };
}

/**
 * Middleware to require organization membership
 */
export function requireOrganizationMember(requiredRoles?: string[]) {
  return function(handler: Function) {
    return async (req: NextApiRequest, res: any) => {
      const organizationId = req.query.organizationId as string;
      if (!organizationId) {
        return res.status(400).json({ error: 'Organization ID required' });
      }

      const authResult = await authenticateOrganizationMember(req, organizationId, requiredRoles);
      if (!authResult.success) {
        return res.status(403).json({ error: authResult.error });
      }

      // Add user to request object
      (req as any).user = authResult.user;
      return handler(req, res);
    };
  };
}

/**
 * Middleware to require specific permission
 */
export function requirePermission(permission: string) {
  return function(handler: Function) {
    return async (req: NextApiRequest, res: any) => {
      const authResult = await authenticateUser(req);
      if (!authResult.success) {
        return res.status(401).json({ error: authResult.error });
      }

      const hasPermission = await checkUserPermission(
        authResult.user.id,
        permission,
        authResult.user.organizationId
      );

      if (!hasPermission) {
        return res.status(403).json({ error: 'Insufficient permissions' });
      }

      // Add user to request object
      (req as any).user = authResult.user;
      return handler(req, res);
    };
  };
}

/**
 * Rate limiting helper
 */
export class RateLimiter {
  private requests: Map<string, number[]> = new Map();

  isAllowed(identifier: string, maxRequests: number, windowMs: number): boolean {
    const now = Date.now();
    const windowStart = now - windowMs;

    // Get existing requests for this identifier
    const userRequests = this.requests.get(identifier) || [];

    // Filter out requests outside the window
    const recentRequests = userRequests.filter(time => time > windowStart);

    // Check if under limit
    if (recentRequests.length >= maxRequests) {
      return false;
    }

    // Add current request
    recentRequests.push(now);
    this.requests.set(identifier, recentRequests);

    return true;
  }

  cleanup(): void {
    const now = Date.now();
    const oneHourAgo = now - 60 * 60 * 1000;

    for (const [identifier, requests] of this.requests.entries()) {
      const recentRequests = requests.filter(time => time > oneHourAgo);
      if (recentRequests.length === 0) {
        this.requests.delete(identifier);
      } else {
        this.requests.set(identifier, recentRequests);
      }
    }
  }
}

// Global rate limiter instance
export const globalRateLimiter = new RateLimiter();

// Cleanup rate limiter every hour
if (typeof window === 'undefined') {
  setInterval(() => {
    globalRateLimiter.cleanup();
  }, 60 * 60 * 1000);
}
