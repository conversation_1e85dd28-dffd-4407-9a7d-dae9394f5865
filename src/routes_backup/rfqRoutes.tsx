import React from 'react';
import { Routes, Route } from 'react-router-dom';
import RFQCreationWizard from '../components/rfq/RFQCreationWizard';
import RFQManagement from '../pages/rfq/RFQManagement';
import BidderOnboarding from '../components/onboarding/BidderOnboarding';

const RFQRoutes: React.FC = () => {
  return (
    <Routes>
      {/* RFQ Creation Wizard - 90-second challenge */}
      <Route path="/create" element={<RFQCreationWizard />} />
      
      {/* RFQ Management Dashboard */}
      <Route path="/manage" element={<RFQManagement />} />
      <Route path="/" element={<RFQManagement />} />
      
      {/* Bidder Onboarding with RFQ/Tender Mix */}
      <Route path="/onboarding" element={<BidderOnboarding />} />
      
      {/* Individual RFQ Views */}
      <Route path="/:rfqId" element={<RFQManagement />} />
      <Route path="/edit/:rfqId" element={<RFQCreationWizard />} />
    </Routes>
  );
};

export default RFQRoutes;
