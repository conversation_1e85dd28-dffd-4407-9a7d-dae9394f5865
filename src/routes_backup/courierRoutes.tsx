/**
 * Courier Routes Configuration
 * Routing for Courier Dispatch System with Queen Bee integration
 */

import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useNeuroMarketing } from '../hooks/useNeuroMarketing';

// Courier Pages
import CourierManagement from '../pages/courier/CourierManagement';
import CourierDashboard from '../pages/courier/CourierDashboard';
import DeliveryTracking from '../pages/courier/DeliveryTracking';
import QueenBeeDashboard from '../pages/queen-bee/QueenBeeDashboard';

// Lazy load components for better performance
const CourierAnalytics = React.lazy(() => import('../pages/courier/CourierAnalytics'));
const DeliveryHistory = React.lazy(() => import('../pages/courier/DeliveryHistory'));
const BeeManagement = React.lazy(() => import('../pages/courier/BeeManagement'));

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: string;
  requiresQueenBee?: boolean;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requiredRole,
  requiresQueenBee = false 
}) => {
  const { user, isAuthenticated } = useAuth();
  const { psychologicalState, startTracking } = useNeuroMarketing();

  React.useEffect(() => {
    // Start psychological tracking for courier features
    if (isAuthenticated) {
      startTracking();
    }
  }, [isAuthenticated, startTracking]);

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (requiredRole && user?.role !== requiredRole) {
    return <Navigate to="/unauthorized" replace />;
  }

  if (requiresQueenBee && user?.role !== 'queen_bee') {
    return <Navigate to="/courier/unauthorized" replace />;
  }

  return <>{children}</>;
};

// Courier Routes Component
const CourierRoutes: React.FC = () => {
  return (
    <Routes>
      {/* Main Courier Dashboard */}
      <Route 
        path="/" 
        element={
          <ProtectedRoute>
            <CourierDashboard />
          </ProtectedRoute>
        } 
      />

      {/* Courier Management */}
      <Route 
        path="/management" 
        element={
          <ProtectedRoute>
            <CourierManagement />
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/dashboard" 
        element={
          <ProtectedRoute>
            <CourierDashboard />
          </ProtectedRoute>
        } 
      />

      {/* Delivery Management */}
      <Route 
        path="/deliveries" 
        element={
          <ProtectedRoute>
            <CourierManagement />
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/deliveries/create" 
        element={
          <ProtectedRoute>
            <CourierManagement />
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/deliveries/:id" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading...</div>}>
              <DeliveryTracking />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/deliveries/track/:id" 
        element={
          <ProtectedRoute>
            <DeliveryTracking />
          </ProtectedRoute>
        } 
      />

      {/* Queen Bee Management - Restricted Access */}
      <Route 
        path="/queen-bee" 
        element={
          <ProtectedRoute requiresQueenBee>
            <QueenBeeDashboard queenBeeId="queen-001" />
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/queen-bee/dashboard" 
        element={
          <ProtectedRoute requiresQueenBee>
            <QueenBeeDashboard queenBeeId="queen-001" />
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/queen-bee/:queenBeeId" 
        element={
          <ProtectedRoute requiresQueenBee>
            <QueenBeeDashboard queenBeeId="queen-001" />
          </ProtectedRoute>
        } 
      />

      {/* Bee Management */}
      <Route 
        path="/bees" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading...</div>}>
              <BeeManagement />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/bees/management" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading...</div>}>
              <BeeManagement />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Analytics and Reporting */}
      <Route 
        path="/analytics" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading...</div>}>
              <CourierAnalytics />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/reports" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading...</div>}>
              <CourierAnalytics />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* History and Tracking */}
      <Route 
        path="/history" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading...</div>}>
              <DeliveryHistory />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />
      
      <Route 
        path="/tracking" 
        element={
          <ProtectedRoute>
            <DeliveryTracking />
          </ProtectedRoute>
        } 
      />

      {/* Error Routes */}
      <Route 
        path="/unauthorized" 
        element={
          <div style={{ padding: '2rem', textAlign: 'center' }}>
            <h2>🚫 Access Denied</h2>
            <p>You don't have permission to access courier management features.</p>
            <p>Contact your administrator for Queen Bee access.</p>
          </div>
        } 
      />

      {/* Fallback Route */}
      <Route path="*" element={<Navigate to="/courier" replace />} />
    </Routes>
  );
};

export default CourierRoutes;
