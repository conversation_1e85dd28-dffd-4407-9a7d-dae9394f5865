/**
 * WhatsApp Auto-Bidding Routes Configuration
 * Routing for WhatsApp integration and auto-bidding features
 */

import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useFeatureFlags } from '../hooks/useFeatureFlags';

// WhatsApp Components
import WhatsAppStatusWidget from '../components/whatsapp/WhatsAppStatusWidget';

// Lazy load WhatsApp pages
const WhatsAppDashboard = React.lazy(() => import('../pages/whatsapp/WhatsAppDashboard'));
const WhatsAppSettings = React.lazy(() => import('../pages/whatsapp/WhatsAppSettings'));
const AutoBidSettings = React.lazy(() => import('../pages/whatsapp/AutoBidSettings'));
const MessageHistory = React.lazy(() => import('../pages/whatsapp/MessageHistory'));
const WhatsAppAnalytics = React.lazy(() => import('../pages/whatsapp/WhatsAppAnalytics'));
const WhatsAppSetup = React.lazy(() => import('../pages/whatsapp/WhatsAppSetup'));

// Protected Route Component
interface ProtectedRouteProps {
  children: React.ReactNode;
  requiresFeature?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requiresFeature = 'whatsapp_autobid' 
}) => {
  const { user } = useAuth();
  const { isFeatureEnabled } = useFeatureFlags();

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  if (!isFeatureEnabled(requiresFeature)) {
    return (
      <div style={{ 
        padding: '2rem', 
        textAlign: 'center',
        background: 'linear-gradient(135deg, #25D366 0%, #128C7E 100%)',
        color: 'white',
        borderRadius: '12px',
        margin: '2rem'
      }}>
        <h2>📱 WhatsApp Auto-Bidding Not Available</h2>
        <p>WhatsApp integration is not enabled for your account.</p>
        <p>Contact support to enable this premium feature.</p>
      </div>
    );
  }

  return <>{children}</>;
};

// WhatsApp Routes Component
const WhatsAppRoutes: React.FC = () => {
  const { user } = useAuth();

  return (
    <Routes>
      {/* Main WhatsApp Dashboard */}
      <Route 
        path="/" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading WhatsApp Dashboard...</div>}>
              <WhatsAppDashboard />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* WhatsApp Dashboard */}
      <Route 
        path="/dashboard" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading WhatsApp Dashboard...</div>}>
              <WhatsAppDashboard />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* WhatsApp Setup/Onboarding */}
      <Route 
        path="/setup" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading WhatsApp Setup...</div>}>
              <WhatsAppSetup />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* WhatsApp Settings */}
      <Route 
        path="/settings" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading WhatsApp Settings...</div>}>
              <WhatsAppSettings />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Auto-Bid Settings */}
      <Route 
        path="/auto-bid" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading Auto-Bid Settings...</div>}>
              <AutoBidSettings />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Auto-Bid Configuration */}
      <Route 
        path="/auto-bid/settings" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading Auto-Bid Settings...</div>}>
              <AutoBidSettings />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Message History */}
      <Route 
        path="/messages" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading Message History...</div>}>
              <MessageHistory />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* WhatsApp Analytics */}
      <Route 
        path="/analytics" 
        element={
          <ProtectedRoute>
            <React.Suspense fallback={<div>Loading WhatsApp Analytics...</div>}>
              <WhatsAppAnalytics />
            </React.Suspense>
          </ProtectedRoute>
        } 
      />

      {/* Status Widget (for embedding) */}
      <Route 
        path="/status" 
        element={
          <ProtectedRoute>
            <WhatsAppStatusWidget
              userId={user?.id || 'demo'}
              onConfigureSettings={() => window.location.href = '/whatsapp/settings'}
            />
          </ProtectedRoute>
        } 
      />

      {/* Test Auto-Bid */}
      <Route 
        path="/test" 
        element={
          <ProtectedRoute>
            <div style={{ padding: '2rem' }}>
              <h2>🧪 Test Auto-Bid</h2>
              <p>Test your WhatsApp auto-bidding configuration with sample messages.</p>
              <p>Coming soon...</p>
            </div>
          </ProtectedRoute>
        } 
      />

      {/* Webhook Management */}
      <Route 
        path="/webhooks" 
        element={
          <ProtectedRoute requiresFeature="whatsapp_webhooks">
            <div style={{ padding: '2rem' }}>
              <h2>🔗 Webhook Management</h2>
              <p>Configure and monitor WhatsApp webhook endpoints.</p>
              <p>Coming soon...</p>
            </div>
          </ProtectedRoute>
        } 
      />

      {/* Templates Management */}
      <Route 
        path="/templates" 
        element={
          <ProtectedRoute>
            <div style={{ padding: '2rem' }}>
              <h2>📝 Message Templates</h2>
              <p>Manage WhatsApp message templates for auto-responses.</p>
              <p>Coming soon...</p>
            </div>
          </ProtectedRoute>
        } 
      />

      {/* Verification */}
      <Route 
        path="/verify" 
        element={
          <ProtectedRoute>
            <div style={{ padding: '2rem' }}>
              <h2>✅ Number Verification</h2>
              <p>Verify your WhatsApp number for auto-bidding.</p>
              <p>Coming soon...</p>
            </div>
          </ProtectedRoute>
        } 
      />

      {/* Help & Documentation */}
      <Route 
        path="/help" 
        element={
          <ProtectedRoute>
            <div style={{ padding: '2rem' }}>
              <h2>❓ WhatsApp Auto-Bidding Help</h2>
              <p>Learn how to set up and use WhatsApp auto-bidding effectively.</p>
              <p>Coming soon...</p>
            </div>
          </ProtectedRoute>
        } 
      />

      {/* Redirect unknown routes to main dashboard */}
      <Route path="*" element={<Navigate to="/whatsapp" replace />} />
    </Routes>
  );
};

export default WhatsAppRoutes;
