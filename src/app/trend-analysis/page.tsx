'use client';

import React from 'react';
import { Container, Typography, Card, CardContent, Grid, Box, Avatar, Chip } from '@mui/material';
import { Timeline, TrendingUp, TrendingDown, Assessment } from '@mui/icons-material';

export default function TrendAnalysisPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
        Trend Analysis
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        Comprehensive trend analysis and market forecasting
      </Typography>
      
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="success.main">
                    +18%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Market Growth
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <TrendingUp />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="primary">
                    94.7%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Prediction Accuracy
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <Assessment />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Sector Trends</Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Box sx={{ p: 2, bgcolor: 'success.50', borderRadius: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <TrendingUp sx={{ color: 'success.main' }} />
                  <Typography variant="h6" color="success.main">Construction</Typography>
                </Box>
                <Typography variant="h4" fontWeight="bold">+25%</Typography>
                <Chip label="Strong Growth" color="success" size="small" />
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ p: 2, bgcolor: 'primary.50', borderRadius: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <TrendingUp sx={{ color: 'primary.main' }} />
                  <Typography variant="h6" color="primary.main">IT Services</Typography>
                </Box>
                <Typography variant="h4" fontWeight="bold">+32%</Typography>
                <Chip label="Rapid Growth" color="primary" size="small" />
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ p: 2, bgcolor: 'warning.50', borderRadius: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <TrendingDown sx={{ color: 'warning.main' }} />
                  <Typography variant="h6" color="warning.main">Consulting</Typography>
                </Box>
                <Typography variant="h4" fontWeight="bold">-5%</Typography>
                <Chip label="Declining" color="warning" size="small" />
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Container>
  );
}
