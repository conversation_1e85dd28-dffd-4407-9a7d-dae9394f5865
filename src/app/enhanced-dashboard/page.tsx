'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Container,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  LinearProgress,
  Chip,
  Alert,
  Stack,
  IconButton,
  Tooltip,
  Avatar,
  Paper,
  Slide
} from '@mui/material';
import {
  TrendingUp,
  FlashOn,
  Psychology,
  EmojiEvents,
  Warning,
  VolumeUp,
  Refresh,
  Celebration,
  AttachMoney,
  Close
} from '@mui/icons-material';
import { PieChart, Pie, Cell, ResponsiveContainer, LineChart, Line, XAxis, YAxis, Tooltip as RechartsTooltip } from 'recharts';

interface DashboardMetrics {
  currentMix: {
    rfqPercentage: number;
    tenderPercentage: number;
    onTrack: boolean;
  };
  targetTurnover: number;
  currentEarnings: number;
  missedEarnings: number;
  projectedShortfall: number;
  questProgress: {
    current: number;
    target: number;
    percentage: number;
  };
  winStreak: {
    current: number;
    trend: 'up' | 'down' | 'flat';
    encouragement: string;
  };
  victoryReel: {
    userWins: string;
    socialProof: string;
  };
}

interface ProcurementNewsItem {
  id: string;
  type: 'award' | 'new_tender' | 'new_rfq' | 'cancelled' | 'deadline' | 'compliance' | 'market_intel';
  message: string;
  value?: string;
  entity: string;
  timestamp: Date;
  urgency: 'breaking' | 'urgent' | 'normal';
  sector: string;
}

export default function EnhancedDashboardPage() {
  const [metrics, setMetrics] = useState<DashboardMetrics>({
    currentMix: {
      rfqPercentage: 60,
      tenderPercentage: 40,
      onTrack: true
    },
    targetTurnover: 5000000,
    currentEarnings: 1250000,
    missedEarnings: 350000,
    projectedShortfall: 500000,
    questProgress: {
      current: 173,
      target: 8650,
      percentage: 2
    },
    winStreak: {
      current: 2,
      trend: 'up',
      encouragement: 'Your 200% win streak suggests boldness!'
    },
    victoryReel: {
      userWins: '2 in a row!',
      socialProof: 'John from Gauteng won R500k!'
    }
  });

  const [procurementNews, setProcurementNews] = useState<ProcurementNewsItem[]>([
    {
      id: '1',
      type: 'award',
      message: 'Stefanutti Stocks awarded R12.8M road construction contract',
      value: 'R12.8M',
      entity: 'City of Cape Town',
      timestamp: new Date(),
      urgency: 'breaking',
      sector: 'Construction'
    },
    {
      id: '2',
      type: 'new_tender',
      message: 'NEW TENDER: IT infrastructure upgrade for provincial offices',
      value: 'R8.4M',
      entity: 'Gauteng Provincial Government',
      timestamp: new Date(Date.now() - 900000),
      urgency: 'breaking',
      sector: 'IT'
    },
    {
      id: '3',
      type: 'cancelled',
      message: 'CANCELLED: Office furniture supply tender withdrawn',
      value: 'R3.2M',
      entity: 'Department of Health',
      timestamp: new Date(Date.now() - 1800000),
      urgency: 'normal',
      sector: 'Supplies'
    },
    {
      id: '4',
      type: 'new_rfq',
      message: 'URGENT RFQ: Security services for government buildings',
      value: 'R1.6M',
      entity: 'Department of Public Works',
      timestamp: new Date(Date.now() - 2700000),
      urgency: 'urgent',
      sector: 'Security'
    },
    {
      id: '5',
      type: 'deadline',
      message: 'DEADLINE ALERT: Cleaning services tender closes in 4 hours',
      value: 'R2.1M',
      entity: 'eThekwini Municipality',
      timestamp: new Date(Date.now() - 3600000),
      urgency: 'urgent',
      sector: 'Services'
    },
    {
      id: '6',
      type: 'award',
      message: 'Group Five Construction wins municipal infrastructure project',
      value: 'R15.7M',
      entity: 'City of Johannesburg',
      timestamp: new Date(Date.now() - 4500000),
      urgency: 'normal',
      sector: 'Infrastructure'
    },
    {
      id: '7',
      type: 'compliance',
      message: 'NEW REQUIREMENT: BEE Level 4+ mandatory for all municipal contracts above R5M',
      entity: 'National Treasury',
      timestamp: new Date(Date.now() - 5400000),
      urgency: 'breaking',
      sector: 'Compliance'
    },
    {
      id: '8',
      type: 'new_tender',
      message: 'PUBLISHED: Medical equipment procurement for public hospitals',
      value: 'R22.3M',
      entity: 'Western Cape Health Department',
      timestamp: new Date(Date.now() - 6300000),
      urgency: 'breaking',
      sector: 'Healthcare'
    },
    {
      id: '9',
      type: 'market_intel',
      message: 'MARKET UPDATE: Construction tender activity up 34% in KwaZulu-Natal',
      entity: 'BidBeez Intelligence',
      timestamp: new Date(Date.now() - 7200000),
      urgency: 'normal',
      sector: 'Market Analysis'
    },
    {
      id: '10',
      type: 'new_rfq',
      message: 'FAST TRACK RFQ: Emergency road repairs - 24hr deadline',
      value: 'R4.8M',
      entity: 'Mpumalanga Provincial Roads',
      timestamp: new Date(Date.now() - 8100000),
      urgency: 'urgent',
      sector: 'Emergency Services'
    }
  ]);

  const [currentNewsIndex, setCurrentNewsIndex] = useState(0);
  const [showRibbon, setShowRibbon] = useState(true);

  // Auto-rotate through procurement news items
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentNewsIndex((prev) => (prev + 1) % procurementNews.length);
    }, 4000);

    return () => clearInterval(interval);
  }, [procurementNews.length]);

  const currentNewsItem = procurementNews[currentNewsIndex];

  const pieData = [
    { name: 'RFQ', value: metrics.currentMix.rfqPercentage, color: '#4caf50' },
    { name: 'Tender', value: metrics.currentMix.tenderPercentage, color: '#2196f3' }
  ];

  const winStreakData = [
    { week: 'W1', wins: 1 },
    { week: 'W2', wins: 1 },
    { week: 'W3', wins: 2 },
    { week: 'W4', wins: 3 },
    { week: 'W5', wins: 2 }
  ];

  const handleClaimOpportunities = () => {
    window.location.href = '/tender-search';
  };

  const handleGetSupplierQuotes = () => {
    window.location.href = '/rfq-creation';
  };

  const handleRefreshMetrics = () => {
    // Simulate refresh
    console.log('Refreshing metrics...');
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* CNN-Style Moving News Ribbon */}
      {showRibbon && (
        <Slide direction="down" in={showRibbon} mountOnEnter unmountOnExit>
          <Paper
            elevation={3}
            sx={{
              mb: 3,
              background: 'linear-gradient(135deg, #c41e3a 0%, #8b0000 100%)', // CNN red
              color: 'white',
              borderRadius: 0,
              position: 'relative',
              overflow: 'hidden',
              height: 60
            }}
          >
            {/* CNN-style "BREAKING" label */}
            <Box
              sx={{
                position: 'absolute',
                left: 0,
                top: 0,
                bottom: 0,
                width: 140,
                background: '#ffffff',
                color: '#c41e3a',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontWeight: 'bold',
                fontSize: '12px',
                zIndex: 2,
                borderRight: '3px solid #c41e3a'
              }}
            >
              🏛️ SA PROCUREMENT
            </Box>

            {/* Scrolling news content */}
            <Box
              sx={{
                position: 'absolute',
                left: 140,
                right: 60,
                top: 0,
                bottom: 0,
                overflow: 'hidden',
                display: 'flex',
                alignItems: 'center'
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  whiteSpace: 'nowrap',
                  animation: 'scroll-left 45s linear infinite',
                  gap: 4
                }}
              >
                {/* Create a continuous loop of procurement news */}
                {[...procurementNews, ...procurementNews].map((item, index) => {
                  const getBadgeConfig = (type: string, urgency: string) => {
                    switch (type) {
                      case 'award':
                        return { label: 'AWARDED', color: '#4caf50', bgColor: '#ffffff' };
                      case 'new_tender':
                        return { label: 'NEW TENDER', color: '#c41e3a', bgColor: '#ffff00' };
                      case 'new_rfq':
                        return { label: 'NEW RFQ', color: '#ffffff', bgColor: '#ff9800' };
                      case 'cancelled':
                        return { label: 'CANCELLED', color: '#ffffff', bgColor: '#f44336' };
                      case 'deadline':
                        return { label: 'DEADLINE', color: '#c41e3a', bgColor: '#ffff00' };
                      case 'compliance':
                        return { label: 'COMPLIANCE', color: '#ffffff', bgColor: '#9c27b0' };
                      case 'market_intel':
                        return { label: 'MARKET', color: '#ffffff', bgColor: '#2196f3' };
                      default:
                        return { label: 'UPDATE', color: '#c41e3a', bgColor: '#ffffff' };
                    }
                  };

                  const badgeConfig = getBadgeConfig(item.type, item.urgency);

                  return (
                    <Box key={`${item.id}-${index}`} sx={{ display: 'flex', alignItems: 'center', gap: 2, minWidth: 'fit-content' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Chip
                          label={badgeConfig.label}
                          size="small"
                          sx={{
                            bgcolor: badgeConfig.bgColor,
                            color: badgeConfig.color,
                            fontWeight: 'bold',
                            fontSize: '10px',
                            animation: item.urgency === 'breaking' ? 'flash 1s infinite' :
                                     item.urgency === 'urgent' ? 'pulse 2s infinite' : 'none'
                          }}
                        />
                        <Typography variant="body1" fontWeight="bold" sx={{ fontSize: '15px' }}>
                          {item.message}
                        </Typography>
                        {item.value && (
                          <Typography variant="body1" sx={{ color: '#ffff00', fontWeight: 'bold' }}>
                            {item.value}
                          </Typography>
                        )}
                        <Typography variant="body2" sx={{ opacity: 0.8, fontSize: '13px' }}>
                          • {item.entity}
                        </Typography>
                        <Typography variant="body2" sx={{ opacity: 0.6, fontSize: '12px' }}>
                          • {item.timestamp.toLocaleTimeString()}
                        </Typography>
                      </Box>
                      <Box sx={{ width: 3, height: 20, bgcolor: 'rgba(255,255,255,0.5)', mx: 2 }} />
                    </Box>
                  );
                })}
              </Box>
            </Box>

            {/* Close button */}
            <Box
              sx={{
                position: 'absolute',
                right: 0,
                top: 0,
                bottom: 0,
                width: 60,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                background: 'rgba(0,0,0,0.2)'
              }}
            >
              <IconButton
                size="small"
                onClick={() => setShowRibbon(false)}
                sx={{ color: 'white' }}
              >
                <Close />
              </IconButton>
            </Box>
          </Paper>
        </Slide>
      )}

      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
          🏆 Enhanced Dashboard with Victory Reel
        </Typography>
        <Typography variant="h6" color="text.secondary">
          Real-time wins, psychological insights, and business intelligence
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Main Dashboard Overview Card */}
        <Grid item xs={12} md={8}>
          <Card 
            sx={{ 
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              position: 'relative',
              overflow: 'visible'
            }}
          >
            <CardContent sx={{ p: 4 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 3 }}>
                <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                  Dashboard Overview
                </Typography>
                <IconButton onClick={handleRefreshMetrics} sx={{ color: 'white' }}>
                  <Refresh />
                </IconButton>
              </Box>

              <Grid container spacing={4}>
                {/* RFQ/Tender Mix Visualization */}
                <Grid item xs={12} md={6}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Box sx={{ height: 200, mb: 2 }}>
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={pieData}
                            cx="50%"
                            cy="50%"
                            innerRadius={50}
                            outerRadius={90}
                            dataKey="value"
                          >
                            {pieData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Pie>
                          <RechartsTooltip />
                        </PieChart>
                      </ResponsiveContainer>
                    </Box>
                    <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                      Current Mix: {metrics.currentMix.onTrack ? '✅ On Track' : '⚠️ Needs Adjustment'}
                    </Typography>
                  </Box>
                </Grid>

                {/* Financial Tracking */}
                <Grid item xs={12} md={6}>
                  <Box>
                    <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 3 }}>
                      Welcome to Your Opportunity Journey!
                    </Typography>
                    
                    <Grid container spacing={2}>
                      <Grid item xs={6}>
                        <Box>
                          <Typography variant="body2" sx={{ opacity: 0.9 }}>RFQ/Tender Mix</Typography>
                          <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                            {metrics.currentMix.rfqPercentage}% RFQ
                          </Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={6}>
                        <Box>
                          <Typography variant="body2" sx={{ opacity: 0.9 }}>Missed Earnings</Typography>
                          <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#f44336' }}>
                            R {(metrics.missedEarnings / 1000).toFixed(0)}k
                          </Typography>
                        </Box>
                      </Grid>
                    </Grid>
                  </Box>
                </Grid>
              </Grid>

              {/* Action Buttons */}
              <Box sx={{ mt: 4, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Button
                  variant="contained"
                  size="large"
                  onClick={handleClaimOpportunities}
                  sx={{
                    background: 'linear-gradient(45deg, #ff6b6b, #ee5a24)',
                    fontWeight: 'bold',
                    px: 4
                  }}
                >
                  🎯 Claim Your Opportunities
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Bidder Tools & Actions */}
        <Grid item xs={12} md={4}>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                🔧 Bidder Tools
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Essential tools for winning and delivering contracts
              </Typography>

              <Stack spacing={2}>
                <Button
                  variant="contained"
                  startIcon={<AttachMoney />}
                  onClick={handleGetSupplierQuotes}
                  fullWidth
                  sx={{
                    background: 'linear-gradient(45deg, #4caf50, #8bc34a)',
                    color: 'white',
                    fontWeight: 'bold',
                    py: 1.5,
                    '&:hover': {
                      background: 'linear-gradient(45deg, #388e3c, #689f38)'
                    }
                  }}
                >
                  🏗️ Get Supplier Quotes
                </Button>

                <Button
                  variant="outlined"
                  startIcon={<Psychology />}
                  href="/psychological-assessment"
                  fullWidth
                  sx={{ py: 1.5 }}
                >
                  🧠 Psychological Assessment
                </Button>

                <Button
                  variant="outlined"
                  startIcon={<TrendingUp />}
                  href="/ai-engine"
                  fullWidth
                  sx={{ py: 1.5 }}
                >
                  🤖 AI Bidding Engine
                </Button>
              </Stack>

              <Alert severity="info" sx={{ mt: 3 }}>
                <Typography variant="body2" fontWeight="bold">
                  💡 Pro Tip: Use "Get Supplier Quotes" when you win a contract and need subcontractors or suppliers for project delivery.
                </Typography>
              </Alert>
            </CardContent>
          </Card>
        </Grid>

        {/* Quest Progress & Victory Reel */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              {/* Quest Progress */}
              <Box sx={{ mb: 4 }}>
                <Typography variant="h6" gutterBottom>Quest Progress</Typography>
                <Box sx={{ mb: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Typography variant="body2" color="text.secondary">
                      Level 3 Progress
                    </Typography>
                    <Chip 
                      label={`${metrics.questProgress.percentage}%`}
                      size="small"
                      color="warning"
                    />
                  </Box>
                  
                  <LinearProgress 
                    variant="determinate" 
                    value={metrics.questProgress.percentage} 
                    sx={{ 
                      height: 12, 
                      borderRadius: 6,
                      backgroundColor: '#e0e0e0',
                      '& .MuiLinearProgress-bar': {
                        backgroundColor: '#ff9800',
                        borderRadius: 6
                      }
                    }} 
                  />
                  
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    {metrics.questProgress.current} / {metrics.questProgress.target} points
                  </Typography>
                </Box>
              </Box>

              {/* Win Streak */}
              <Box sx={{ mb: 4 }}>
                <Typography variant="h6" gutterBottom>Win Streak</Typography>
                <Box sx={{ height: 100, mb: 2 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={winStreakData}>
                      <XAxis dataKey="week" />
                      <YAxis />
                      <RechartsTooltip />
                      <Line type="monotone" dataKey="wins" stroke="#4caf50" strokeWidth={3} />
                    </LineChart>
                  </ResponsiveContainer>
                </Box>
                <Typography variant="body2" color="success.main" fontWeight="bold">
                  {metrics.winStreak.encouragement}
                </Typography>
              </Box>
              
              {/* Procurement News Feed */}
              <Box>
                <Typography variant="h6" gutterBottom>Live Procurement Feed</Typography>
                <Stack spacing={2}>
                  {procurementNews.slice(0, 6).map((item) => {
                    const getItemColor = (type: string) => {
                      switch (type) {
                        case 'award': return '#4caf50';
                        case 'new_tender': return '#2196f3';
                        case 'new_rfq': return '#ff9800';
                        case 'cancelled': return '#f44336';
                        case 'deadline': return '#ff5722';
                        case 'compliance': return '#9c27b0';
                        case 'market_intel': return '#607d8b';
                        default: return '#757575';
                      }
                    };

                    const getItemIcon = (type: string) => {
                      switch (type) {
                        case 'award': return '🏆';
                        case 'new_tender': return '📋';
                        case 'new_rfq': return '⚡';
                        case 'cancelled': return '❌';
                        case 'deadline': return '⏰';
                        case 'compliance': return '⚖️';
                        case 'market_intel': return '📊';
                        default: return '📢';
                      }
                    };

                    return (
                      <Box
                        key={item.id}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 2,
                          p: 1.5,
                          borderRadius: 2,
                          backgroundColor: item.urgency === 'breaking' ? 'rgba(244, 67, 54, 0.1)' :
                                         item.urgency === 'urgent' ? 'rgba(255, 152, 0, 0.1)' : 'rgba(0, 0, 0, 0.02)',
                          border: item.urgency === 'breaking' ? '1px solid rgba(244, 67, 54, 0.3)' :
                                item.urgency === 'urgent' ? '1px solid rgba(255, 152, 0, 0.3)' : '1px solid transparent'
                        }}
                      >
                        <Avatar
                          sx={{
                            width: 32,
                            height: 32,
                            backgroundColor: getItemColor(item.type)
                          }}
                        >
                          {getItemIcon(item.type)}
                        </Avatar>

                        <Box sx={{ flex: 1 }}>
                          <Typography
                            variant="body2"
                            fontWeight={item.urgency === 'breaking' ? 'bold' : 'normal'}
                            color={item.urgency === 'breaking' ? 'error.main' : 'text.primary'}
                            sx={{ fontSize: '13px' }}
                          >
                            {item.message}
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 1, mt: 0.5 }}>
                            {item.value && (
                              <Typography variant="caption" color="success.main" fontWeight="bold">
                                {item.value}
                              </Typography>
                            )}
                            <Typography variant="caption" color="text.secondary">
                              {item.entity}
                            </Typography>
                          </Box>
                        </Box>
                      </Box>
                    );
                  })}
                </Stack>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* CSS Animations */}
      <style jsx global>{`
        @keyframes pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.7; }
        }

        @keyframes scroll-left {
          0% { transform: translateX(100%); }
          100% { transform: translateX(-100%); }
        }

        @keyframes flash {
          0%, 50% { opacity: 1; }
          25%, 75% { opacity: 0.5; }
        }

        @keyframes glow {
          0%, 100% { box-shadow: 0 0 5px rgba(255, 255, 0, 0.5); }
          50% { box-shadow: 0 0 20px rgba(255, 255, 0, 0.8); }
        }
      `}</style>
    </Container>
  );
}
