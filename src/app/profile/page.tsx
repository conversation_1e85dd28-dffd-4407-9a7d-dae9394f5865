'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Container,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Switch,
  FormControlLabel,
  <PERSON><PERSON>r,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>r,
  Autocomplete,
  Tab,
  Tabs,
  LinearProgress
} from '@mui/material';
import {
  Person,
  Business,
  Notifications,
  TrendingUp,
  Psychology,
  LocationOn,
  AttachMoney,
  Category,
  Save,
  Refresh
} from '@mui/icons-material';

interface BidderProfile {
  // Personal Information
  personalInfo: {
    fullName: string;
    email: string;
    phone: string;
    company: string;
    registrationNumber: string;
    bbbeeLevel: number;
    taxNumber: string;
  };
  
  // Business Preferences
  businessPreferences: {
    preferredCategories: string[];
    preferredProvinces: string[];
    minimumValue: number;
    maximumValue: number;
    riskTolerance: 'low' | 'medium' | 'high';
    targetTurnover: number;
    currentCapacity: number;
  };
  
  // Opportunity Configuration
  opportunityConfig: {
    tenderNotifications: boolean;
    governmentRfqNotifications: boolean;
    bidderRfqNotifications: boolean;
    urgentOpportunities: boolean;
    portfolioBalanceAlerts: boolean;
    competitionAlerts: boolean;
    deadlineReminders: number; // days before
  };
  
  // Notification Preferences
  notificationPreferences: {
    email: boolean;
    sms: boolean;
    whatsapp: boolean;
    push: boolean;
    frequency: 'immediate' | 'hourly' | 'daily' | 'weekly';
    quietHours: {
      enabled: boolean;
      start: string;
      end: string;
    };
  };
  
  // Portfolio Settings
  portfolioSettings: {
    targetRfqRatio: number; // 60% default
    targetTenderRatio: number; // 40% default
    autoOptimization: boolean;
    balanceThreshold: number; // deviation percentage
    psychologicalProfile: 'achiever' | 'hunter' | 'analyst' | 'relationship_builder';
  };
}

const BidderProfilePage: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [profile, setProfile] = useState<BidderProfile>({
    personalInfo: {
      fullName: 'John Smith',
      email: '<EMAIL>',
      phone: '+27 82 123 4567',
      company: 'Smith Construction (Pty) Ltd',
      registrationNumber: '2020/123456/07',
      bbbeeLevel: 4,
      taxNumber: '9876543210'
    },
    businessPreferences: {
      preferredCategories: ['Construction', 'IT Services'],
      preferredProvinces: ['Gauteng', 'Western Cape'],
      minimumValue: 100000,
      maximumValue: 10000000,
      riskTolerance: 'medium',
      targetTurnover: 5000000,
      currentCapacity: 80
    },
    opportunityConfig: {
      tenderNotifications: true,
      governmentRfqNotifications: true,
      bidderRfqNotifications: true,
      urgentOpportunities: true,
      portfolioBalanceAlerts: true,
      competitionAlerts: false,
      deadlineReminders: 7
    },
    notificationPreferences: {
      email: true,
      sms: true,
      whatsapp: false,
      push: true,
      frequency: 'immediate',
      quietHours: {
        enabled: true,
        start: '18:00',
        end: '08:00'
      }
    },
    portfolioSettings: {
      targetRfqRatio: 60,
      targetTenderRatio: 40,
      autoOptimization: true,
      balanceThreshold: 10,
      psychologicalProfile: 'achiever'
    }
  });

  // Available options
  const categories = [
    'Construction', 'IT Services', 'Office Supplies', 'Security Services',
    'Cleaning Services', 'Catering', 'Transport & Logistics', 'Professional Services',
    'Engineering', 'Healthcare', 'Education', 'Agriculture', 'Mining',
    'Manufacturing', 'Energy', 'Water & Sanitation', 'Telecommunications'
  ];

  const provinces = [
    'Gauteng', 'Western Cape', 'KwaZulu-Natal', 'Eastern Cape',
    'Free State', 'Limpopo', 'Mpumalanga', 'North West', 'Northern Cape'
  ];

  const handleSaveProfile = async () => {
    setSaving(true);
    try {
      // Save profile to backend
      const response = await fetch('/api/profile/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(profile)
      });

      if (response.ok) {
        // Show success message
        console.log('Profile saved successfully');
      }
    } catch (error) {
      console.error('Error saving profile:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0
    }).format(value);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold' }}>
          👤 Bidder Profile Configuration
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Configure your preferences for tenders, RFQs, and notifications
        </Typography>
      </Box>

      {/* Portfolio Balance Alert */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>Current Portfolio Balance:</strong> Your configuration affects which opportunities you'll receive. 
          Target: {profile.portfolioSettings.targetRfqRatio}% RFQ / {profile.portfolioSettings.targetTenderRatio}% Tender activities.
        </Typography>
      </Alert>

      {/* Tabs */}
      <Card sx={{ mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange} variant="scrollable" scrollButtons="auto">
          <Tab icon={<Person />} label="Personal Info" />
          <Tab icon={<Business />} label="Business Preferences" />
          <Tab icon={<Category />} label="Opportunity Config" />
          <Tab icon={<Notifications />} label="Notifications" />
          <Tab icon={<Psychology />} label="Portfolio Settings" />
        </Tabs>
      </Card>

      {/* Tab Content */}
      <Card>
        <CardContent>
          {/* Personal Information Tab */}
          {activeTab === 0 && (
            <Box>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Person color="primary" />
                Personal Information
              </Typography>
              
              <Grid container spacing={3} sx={{ mt: 2 }}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Full Name"
                    value={profile.personalInfo.fullName}
                    onChange={(e) => setProfile(prev => ({
                      ...prev,
                      personalInfo: { ...prev.personalInfo, fullName: e.target.value }
                    }))}
                  />
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Email Address"
                    type="email"
                    value={profile.personalInfo.email}
                    onChange={(e) => setProfile(prev => ({
                      ...prev,
                      personalInfo: { ...prev.personalInfo, email: e.target.value }
                    }))}
                  />
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Phone Number"
                    value={profile.personalInfo.phone}
                    onChange={(e) => setProfile(prev => ({
                      ...prev,
                      personalInfo: { ...prev.personalInfo, phone: e.target.value }
                    }))}
                  />
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Company Name"
                    value={profile.personalInfo.company}
                    onChange={(e) => setProfile(prev => ({
                      ...prev,
                      personalInfo: { ...prev.personalInfo, company: e.target.value }
                    }))}
                  />
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Company Registration Number"
                    value={profile.personalInfo.registrationNumber}
                    onChange={(e) => setProfile(prev => ({
                      ...prev,
                      personalInfo: { ...prev.personalInfo, registrationNumber: e.target.value }
                    }))}
                  />
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>B-BBEE Level</InputLabel>
                    <Select
                      value={profile.personalInfo.bbbeeLevel}
                      onChange={(e) => setProfile(prev => ({
                        ...prev,
                        personalInfo: { ...prev.personalInfo, bbbeeLevel: e.target.value as number }
                      }))}
                    >
                      {[1, 2, 3, 4, 5, 6, 7, 8].map(level => (
                        <MenuItem key={level} value={level}>Level {level}</MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </Box>
          )}

          {/* Business Preferences Tab */}
          {activeTab === 1 && (
            <Box>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Business color="primary" />
                Business Preferences
              </Typography>
              
              <Grid container spacing={3} sx={{ mt: 2 }}>
                <Grid item xs={12}>
                  <Autocomplete
                    multiple
                    options={categories}
                    value={profile.businessPreferences.preferredCategories}
                    onChange={(event, newValue) => setProfile(prev => ({
                      ...prev,
                      businessPreferences: { ...prev.businessPreferences, preferredCategories: newValue }
                    }))}
                    renderTags={(value, getTagProps) =>
                      value.map((option, index) => (
                        <Chip variant="outlined" label={option} {...getTagProps({ index })} key={option} />
                      ))
                    }
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Preferred Categories"
                        placeholder="Select categories you specialize in"
                      />
                    )}
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <Autocomplete
                    multiple
                    options={provinces}
                    value={profile.businessPreferences.preferredProvinces}
                    onChange={(event, newValue) => setProfile(prev => ({
                      ...prev,
                      businessPreferences: { ...prev.businessPreferences, preferredProvinces: newValue }
                    }))}
                    renderTags={(value, getTagProps) =>
                      value.map((option, index) => (
                        <Chip variant="outlined" label={option} {...getTagProps({ index })} key={option} />
                      ))
                    }
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Preferred Provinces"
                        placeholder="Select provinces where you operate"
                      />
                    )}
                  />
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Minimum Project Value"
                    type="number"
                    value={profile.businessPreferences.minimumValue}
                    onChange={(e) => setProfile(prev => ({
                      ...prev,
                      businessPreferences: { ...prev.businessPreferences, minimumValue: parseInt(e.target.value) }
                    }))}
                    InputProps={{
                      startAdornment: <Typography sx={{ mr: 1 }}>R</Typography>
                    }}
                  />
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Maximum Project Value"
                    type="number"
                    value={profile.businessPreferences.maximumValue}
                    onChange={(e) => setProfile(prev => ({
                      ...prev,
                      businessPreferences: { ...prev.businessPreferences, maximumValue: parseInt(e.target.value) }
                    }))}
                    InputProps={{
                      startAdornment: <Typography sx={{ mr: 1 }}>R</Typography>
                    }}
                  />
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>Risk Tolerance</InputLabel>
                    <Select
                      value={profile.businessPreferences.riskTolerance}
                      onChange={(e) => setProfile(prev => ({
                        ...prev,
                        businessPreferences: { ...prev.businessPreferences, riskTolerance: e.target.value as any }
                      }))}
                    >
                      <MenuItem value="low">Low Risk (Prefer guaranteed wins)</MenuItem>
                      <MenuItem value="medium">Medium Risk (Balanced approach)</MenuItem>
                      <MenuItem value="high">High Risk (High reward opportunities)</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Annual Target Turnover"
                    type="number"
                    value={profile.businessPreferences.targetTurnover}
                    onChange={(e) => setProfile(prev => ({
                      ...prev,
                      businessPreferences: { ...prev.businessPreferences, targetTurnover: parseInt(e.target.value) }
                    }))}
                    InputProps={{
                      startAdornment: <Typography sx={{ mr: 1 }}>R</Typography>
                    }}
                    helperText={`Target: ${formatCurrency(profile.businessPreferences.targetTurnover)}`}
                  />
                </Grid>
              </Grid>
            </Box>
          )}

          {/* Opportunity Configuration Tab */}
          {activeTab === 2 && (
            <Box>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Category color="primary" />
                Opportunity Configuration
              </Typography>
              
              <Alert severity="info" sx={{ mt: 2, mb: 3 }}>
                <Typography variant="body2">
                  Configure which types of opportunities you want to receive notifications for. 
                  This affects your portfolio balance and AI recommendations.
                </Typography>
              </Alert>
              
              <Stack spacing={3} sx={{ mt: 2 }}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold' }}>
                      Opportunity Types
                    </Typography>
                    
                    <Stack spacing={2}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={profile.opportunityConfig.tenderNotifications}
                            onChange={(e) => setProfile(prev => ({
                              ...prev,
                              opportunityConfig: { ...prev.opportunityConfig, tenderNotifications: e.target.checked }
                            }))}
                          />
                        }
                        label={
                          <Box>
                            <Typography variant="body1">Government Tenders</Typography>
                            <Typography variant="caption" color="text.secondary">
                              Traditional tender opportunities (75% success rate, counts toward 40% tender target)
                            </Typography>
                          </Box>
                        }
                      />
                      
                      <FormControlLabel
                        control={
                          <Switch
                            checked={profile.opportunityConfig.governmentRfqNotifications}
                            onChange={(e) => setProfile(prev => ({
                              ...prev,
                              opportunityConfig: { ...prev.opportunityConfig, governmentRfqNotifications: e.target.checked }
                            }))}
                          />
                        }
                        label={
                          <Box>
                            <Typography variant="body1">Government RFQs</Typography>
                            <Typography variant="caption" color="text.secondary">
                              Quote-based government opportunities (88% success rate, counts toward 60% RFQ target)
                            </Typography>
                          </Box>
                        }
                      />
                      
                      <FormControlLabel
                        control={
                          <Switch
                            checked={profile.opportunityConfig.bidderRfqNotifications}
                            onChange={(e) => setProfile(prev => ({
                              ...prev,
                              opportunityConfig: { ...prev.opportunityConfig, bidderRfqNotifications: e.target.checked }
                            }))}
                          />
                        }
                        label={
                          <Box>
                            <Typography variant="body1">Bidder RFQs</Typography>
                            <Typography variant="caption" color="text.secondary">
                              RFQs created by other bidders (92% success rate, counts toward 60% RFQ target)
                            </Typography>
                          </Box>
                        }
                      />
                    </Stack>
                  </CardContent>
                </Card>
                
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold' }}>
                      Alert Preferences
                    </Typography>
                    
                    <Stack spacing={2}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={profile.opportunityConfig.urgentOpportunities}
                            onChange={(e) => setProfile(prev => ({
                              ...prev,
                              opportunityConfig: { ...prev.opportunityConfig, urgentOpportunities: e.target.checked }
                            }))}
                          />
                        }
                        label="Urgent Opportunities (Closing within 3 days)"
                      />
                      
                      <FormControlLabel
                        control={
                          <Switch
                            checked={profile.opportunityConfig.portfolioBalanceAlerts}
                            onChange={(e) => setProfile(prev => ({
                              ...prev,
                              opportunityConfig: { ...prev.opportunityConfig, portfolioBalanceAlerts: e.target.checked }
                            }))}
                          />
                        }
                        label="Portfolio Balance Alerts (When ratio is off target)"
                      />
                      
                      <FormControlLabel
                        control={
                          <Switch
                            checked={profile.opportunityConfig.competitionAlerts}
                            onChange={(e) => setProfile(prev => ({
                              ...prev,
                              opportunityConfig: { ...prev.opportunityConfig, competitionAlerts: e.target.checked }
                            }))}
                          />
                        }
                        label="Competition Alerts (When competitors are active)"
                      />
                    </Stack>
                    
                    <Box sx={{ mt: 3 }}>
                      <Typography variant="body2" gutterBottom>
                        Deadline Reminder: {profile.opportunityConfig.deadlineReminders} days before closing
                      </Typography>
                      <Slider
                        value={profile.opportunityConfig.deadlineReminders}
                        onChange={(e, value) => setProfile(prev => ({
                          ...prev,
                          opportunityConfig: { ...prev.opportunityConfig, deadlineReminders: value as number }
                        }))}
                        min={1}
                        max={14}
                        marks={[
                          { value: 1, label: '1 day' },
                          { value: 7, label: '1 week' },
                          { value: 14, label: '2 weeks' }
                        ]}
                        valueLabelDisplay="auto"
                      />
                    </Box>
                  </CardContent>
                </Card>
              </Stack>
            </Box>
          )}

          {/* Notification Preferences Tab */}
          {activeTab === 3 && (
            <Box>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Notifications color="primary" />
                Notification Preferences
              </Typography>

              <Grid container spacing={3} sx={{ mt: 2 }}>
                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold' }}>
                        Notification Channels
                      </Typography>

                      <Stack spacing={2}>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={profile.notificationPreferences.email}
                              onChange={(e) => setProfile(prev => ({
                                ...prev,
                                notificationPreferences: { ...prev.notificationPreferences, email: e.target.checked }
                              }))}
                            />
                          }
                          label="Email Notifications"
                        />

                        <FormControlLabel
                          control={
                            <Switch
                              checked={profile.notificationPreferences.sms}
                              onChange={(e) => setProfile(prev => ({
                                ...prev,
                                notificationPreferences: { ...prev.notificationPreferences, sms: e.target.checked }
                              }))}
                            />
                          }
                          label="SMS Notifications"
                        />

                        <FormControlLabel
                          control={
                            <Switch
                              checked={profile.notificationPreferences.whatsapp}
                              onChange={(e) => setProfile(prev => ({
                                ...prev,
                                notificationPreferences: { ...prev.notificationPreferences, whatsapp: e.target.checked }
                              }))}
                            />
                          }
                          label="WhatsApp Notifications"
                        />

                        <FormControlLabel
                          control={
                            <Switch
                              checked={profile.notificationPreferences.push}
                              onChange={(e) => setProfile(prev => ({
                                ...prev,
                                notificationPreferences: { ...prev.notificationPreferences, push: e.target.checked }
                              }))}
                            />
                          }
                          label="Push Notifications"
                        />
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold' }}>
                        Frequency & Timing
                      </Typography>

                      <FormControl fullWidth sx={{ mb: 3 }}>
                        <InputLabel>Notification Frequency</InputLabel>
                        <Select
                          value={profile.notificationPreferences.frequency}
                          onChange={(e) => setProfile(prev => ({
                            ...prev,
                            notificationPreferences: { ...prev.notificationPreferences, frequency: e.target.value as any }
                          }))}
                        >
                          <MenuItem value="immediate">Immediate</MenuItem>
                          <MenuItem value="hourly">Hourly Digest</MenuItem>
                          <MenuItem value="daily">Daily Summary</MenuItem>
                          <MenuItem value="weekly">Weekly Report</MenuItem>
                        </Select>
                      </FormControl>

                      <FormControlLabel
                        control={
                          <Switch
                            checked={profile.notificationPreferences.quietHours.enabled}
                            onChange={(e) => setProfile(prev => ({
                              ...prev,
                              notificationPreferences: {
                                ...prev.notificationPreferences,
                                quietHours: { ...prev.notificationPreferences.quietHours, enabled: e.target.checked }
                              }
                            }))}
                          />
                        }
                        label="Enable Quiet Hours"
                        sx={{ mb: 2 }}
                      />

                      {profile.notificationPreferences.quietHours.enabled && (
                        <Grid container spacing={2}>
                          <Grid item xs={6}>
                            <TextField
                              fullWidth
                              label="Quiet Start"
                              type="time"
                              value={profile.notificationPreferences.quietHours.start}
                              onChange={(e) => setProfile(prev => ({
                                ...prev,
                                notificationPreferences: {
                                  ...prev.notificationPreferences,
                                  quietHours: { ...prev.notificationPreferences.quietHours, start: e.target.value }
                                }
                              }))}
                              InputLabelProps={{ shrink: true }}
                            />
                          </Grid>
                          <Grid item xs={6}>
                            <TextField
                              fullWidth
                              label="Quiet End"
                              type="time"
                              value={profile.notificationPreferences.quietHours.end}
                              onChange={(e) => setProfile(prev => ({
                                ...prev,
                                notificationPreferences: {
                                  ...prev.notificationPreferences,
                                  quietHours: { ...prev.notificationPreferences.quietHours, end: e.target.value }
                                }
                              }))}
                              InputLabelProps={{ shrink: true }}
                            />
                          </Grid>
                        </Grid>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          )}

          {/* Portfolio Settings Tab */}
          {activeTab === 4 && (
            <Box>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Psychology color="primary" />
                Portfolio Settings & AI Optimization
              </Typography>

              <Alert severity="warning" sx={{ mt: 2, mb: 3 }}>
                <Typography variant="body2">
                  <strong>Portfolio Balance Psychology:</strong> These settings control how the AI optimizes your bidding portfolio
                  for maximum success. The 60/40 RFQ/Tender ratio is scientifically optimized for South African markets.
                </Typography>
              </Alert>

              <Grid container spacing={3} sx={{ mt: 2 }}>
                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold' }}>
                        Portfolio Balance Targets
                      </Typography>

                      <Box sx={{ mb: 3 }}>
                        <Typography variant="body2" gutterBottom>
                          RFQ Activities Target: {profile.portfolioSettings.targetRfqRatio}%
                        </Typography>
                        <Slider
                          value={profile.portfolioSettings.targetRfqRatio}
                          onChange={(e, value) => setProfile(prev => ({
                            ...prev,
                            portfolioSettings: {
                              ...prev.portfolioSettings,
                              targetRfqRatio: value as number,
                              targetTenderRatio: 100 - (value as number)
                            }
                          }))}
                          min={40}
                          max={80}
                          marks={[
                            { value: 40, label: '40%' },
                            { value: 60, label: '60% (Optimal)' },
                            { value: 80, label: '80%' }
                          ]}
                          valueLabelDisplay="auto"
                        />
                        <Typography variant="caption" color="text.secondary">
                          Includes: Bidder RFQs + Government RFQ responses
                        </Typography>
                      </Box>

                      <Box sx={{ mb: 3 }}>
                        <Typography variant="body2" gutterBottom>
                          Tender Activities Target: {profile.portfolioSettings.targetTenderRatio}%
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Automatically calculated: {100 - profile.portfolioSettings.targetRfqRatio}%
                        </Typography>
                      </Box>

                      <Box>
                        <Typography variant="body2" gutterBottom>
                          Balance Alert Threshold: {profile.portfolioSettings.balanceThreshold}%
                        </Typography>
                        <Slider
                          value={profile.portfolioSettings.balanceThreshold}
                          onChange={(e, value) => setProfile(prev => ({
                            ...prev,
                            portfolioSettings: { ...prev.portfolioSettings, balanceThreshold: value as number }
                          }))}
                          min={5}
                          max={25}
                          marks={[
                            { value: 5, label: '5%' },
                            { value: 10, label: '10%' },
                            { value: 25, label: '25%' }
                          ]}
                          valueLabelDisplay="auto"
                        />
                        <Typography variant="caption" color="text.secondary">
                          Get alerts when your portfolio deviates by this percentage
                        </Typography>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold' }}>
                        AI Optimization Settings
                      </Typography>

                      <FormControl fullWidth sx={{ mb: 3 }}>
                        <InputLabel>Psychological Profile</InputLabel>
                        <Select
                          value={profile.portfolioSettings.psychologicalProfile}
                          onChange={(e) => setProfile(prev => ({
                            ...prev,
                            portfolioSettings: { ...prev.portfolioSettings, psychologicalProfile: e.target.value as any }
                          }))}
                        >
                          <MenuItem value="achiever">🏆 Achiever (Competition-driven)</MenuItem>
                          <MenuItem value="hunter">🎯 Hunter (Opportunity-focused)</MenuItem>
                          <MenuItem value="analyst">📊 Analyst (Data-driven)</MenuItem>
                          <MenuItem value="relationship_builder">🤝 Relationship Builder (Network-focused)</MenuItem>
                        </Select>
                      </FormControl>

                      <FormControlLabel
                        control={
                          <Switch
                            checked={profile.portfolioSettings.autoOptimization}
                            onChange={(e) => setProfile(prev => ({
                              ...prev,
                              portfolioSettings: { ...prev.portfolioSettings, autoOptimization: e.target.checked }
                            }))}
                          />
                        }
                        label={
                          <Box>
                            <Typography variant="body2">Auto Portfolio Optimization</Typography>
                            <Typography variant="caption" color="text.secondary">
                              Let AI automatically suggest opportunities to balance your portfolio
                            </Typography>
                          </Box>
                        }
                      />

                      <Alert severity="info" sx={{ mt: 2 }}>
                        <Typography variant="caption">
                          <strong>Success Rates by Type:</strong><br />
                          • Bidder RFQs: 92% success rate<br />
                          • Government RFQs: 88% success rate<br />
                          • Government Tenders: 75% success rate
                        </Typography>
                      </Alert>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          )}

          {/* Save Button */}
          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Button
              variant="outlined"
              startIcon={<Refresh />}
              onClick={() => window.location.reload()}
            >
              Reset Changes
            </Button>

            <Button
              variant="contained"
              size="large"
              startIcon={saving ? <LinearProgress /> : <Save />}
              onClick={handleSaveProfile}
              disabled={saving}
              sx={{
                px: 4,
                py: 1.5,
                fontWeight: 'bold'
              }}
            >
              {saving ? 'Saving...' : 'Save Profile'}
            </Button>
          </Box>
        </CardContent>
      </Card>
    </Container>
  );
};

export default BidderProfilePage;
