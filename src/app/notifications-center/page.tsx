'use client';

import React, { useState } from 'react';
import {
  <PERSON>,
  Container,
  Card,
  CardContent,
  <PERSON>pography,
  Button,
  Grid,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Tooltip,
  Chip,
  Stack,
  Alert,
  Paper,
  Switch,
  FormControlLabel,
  Divider,
  Badge,
  Avatar,
  Menu,
  MenuItem
} from '@mui/material';
import {
  Notifications,
  NotificationsActive,
  Schedule,
  Warning,
  Info,
  CheckCircle,
  Error,
  TrendingUp,
  Assignment,
  Group,
  MonetizationOn,
  Psychology,
  MoreVert,
  MarkAsUnread,
  Delete,
  Archive,
  Settings,
  FilterList,
  Refresh,
  VolumeOff,
  VolumeUp
} from '@mui/icons-material';

interface Notification {
  id: string;
  type: 'deadline' | 'tender_match' | 'bid_update' | 'team_activity' | 'system' | 'achievement' | 'psychological';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  actionUrl?: string;
  actionLabel?: string;
  category: string;
  relatedId?: string;
  psychologicalTrigger?: 'urgency' | 'social_proof' | 'achievement' | 'fear_of_loss';
}

const mockNotifications: Notification[] = [
  {
    id: 'notif-001',
    type: 'deadline',
    title: '⏰ Tender Deadline Approaching',
    message: 'Municipal Infrastructure Development tender closes in 2 days. Your bid is 85% complete.',
    timestamp: '2024-01-16T14:30:00Z',
    read: false,
    priority: 'urgent',
    actionUrl: '/bid-management',
    actionLabel: 'Complete Bid',
    category: 'Deadlines',
    relatedId: 'tender-001',
    psychologicalTrigger: 'urgency'
  },
  {
    id: 'notif-002',
    type: 'tender_match',
    title: '🎯 Perfect Tender Match Found',
    message: 'New IT Services tender matches your profile with 94% compatibility. 12 others are viewing.',
    timestamp: '2024-01-16T13:15:00Z',
    read: false,
    priority: 'high',
    actionUrl: '/tenders',
    actionLabel: 'View Tender',
    category: 'Opportunities',
    relatedId: 'tender-002',
    psychologicalTrigger: 'social_proof'
  },
  {
    id: 'notif-003',
    type: 'achievement',
    title: '🏆 Achievement Unlocked: Bid Master',
    message: 'Congratulations! You\'ve successfully submitted 10 bids this month. Your win rate is 47%.',
    timestamp: '2024-01-16T12:00:00Z',
    read: true,
    priority: 'medium',
    category: 'Achievements',
    psychologicalTrigger: 'achievement'
  },
  {
    id: 'notif-004',
    type: 'psychological',
    title: '🧠 Stress Level Alert',
    message: 'Your stress indicators are elevated. Consider taking a 15-minute break for optimal performance.',
    timestamp: '2024-01-16T11:45:00Z',
    read: false,
    priority: 'medium',
    actionUrl: '/stress-monitoring',
    actionLabel: 'View Insights',
    category: 'Wellness',
    psychologicalTrigger: 'fear_of_loss'
  },
  {
    id: 'notif-005',
    type: 'team_activity',
    title: '👥 Team Member Added Comment',
    message: 'Sarah Johnson commented on the technical proposal for Municipal Infrastructure tender.',
    timestamp: '2024-01-16T10:30:00Z',
    read: true,
    priority: 'low',
    actionUrl: '/team-active-bids',
    actionLabel: 'View Comment',
    category: 'Team',
    relatedId: 'tender-001'
  }
];

export default function NotificationsCenterPage() {
  const [activeTab, setActiveTab] = useState(0);
  const [notifications, setNotifications] = useState(mockNotifications);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedNotification, setSelectedNotification] = useState<Notification | null>(null);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'deadline': return <Schedule color="error" />;
      case 'tender_match': return <TrendingUp color="success" />;
      case 'bid_update': return <Assignment color="primary" />;
      case 'team_activity': return <Group color="info" />;
      case 'achievement': return <CheckCircle color="warning" />;
      case 'psychological': return <Psychology color="secondary" />;
      case 'system': return <Info color="action" />;
      default: return <Notifications color="action" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      default: return 'default';
    }
  };

  const getPsychTriggerIcon = (trigger?: string) => {
    switch (trigger) {
      case 'urgency': return '⚡';
      case 'social_proof': return '👥';
      case 'achievement': return '🏆';
      case 'fear_of_loss': return '⚠️';
      default: return '';
    }
  };

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === id ? { ...notif, read: true } : notif
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notif => ({ ...notif, read: true }))
    );
  };

  const deleteNotification = (id: string) => {
    setNotifications(prev => prev.filter(notif => notif.id !== id));
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, notification: Notification) => {
    setAnchorEl(event.currentTarget);
    setSelectedNotification(notification);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedNotification(null);
  };

  const unreadCount = notifications.filter(n => !n.read).length;
  const urgentCount = notifications.filter(n => n.priority === 'urgent' && !n.read).length;

  const filteredNotifications = notifications.filter(notification => {
    switch (activeTab) {
      case 0: return true; // All
      case 1: return !notification.read; // Unread
      case 2: return notification.priority === 'urgent' || notification.priority === 'high'; // Important
      case 3: return notification.type === 'psychological'; // Psychological
      default: return true;
    }
  });

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
            🔔 Notifications Center
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Real-time alerts, psychological insights, and intelligent notifications
          </Typography>
        </Box>
        
        <Stack direction="row" spacing={2}>
          <Button
            variant="outlined"
            startIcon={<FilterList />}
          >
            Filter
          </Button>
          <Button
            variant="outlined"
            startIcon={<Settings />}
          >
            Settings
          </Button>
          <Button
            variant="contained"
            startIcon={<Refresh />}
          >
            Refresh
          </Button>
        </Stack>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" color="primary" gutterBottom>
                    {unreadCount}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Unread Notifications
                  </Typography>
                </Box>
                <Badge badgeContent={unreadCount} color="primary">
                  <NotificationsActive color="primary" sx={{ fontSize: 40 }} />
                </Badge>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" color="error.main" gutterBottom>
                    {urgentCount}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Urgent Items
                  </Typography>
                </Box>
                <Warning color="error" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" color="success.main" gutterBottom>
                    3
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    New Opportunities
                  </Typography>
                </Box>
                <TrendingUp color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" color="warning.main" gutterBottom>
                    2
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Psychological Alerts
                  </Typography>
                </Box>
                <Psychology color="warning" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Notification Settings */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
          <Typography variant="body2">
            <strong>🧠 Psychological Optimization:</strong> Notifications are timed and personalized based on your stress levels and optimal working hours.
          </Typography>
          <FormControlLabel
            control={<Switch defaultChecked />}
            label="Smart Timing"
          />
        </Box>
      </Alert>

      {/* Main Content */}
      <Grid container spacing={4}>
        {/* Notifications List */}
        <Grid item xs={12} md={8}>
          <Card>
            <Box sx={{ borderBottom: 1, borderColor: 'divider', display: 'flex', justifyContent: 'space-between', alignItems: 'center', px: 2 }}>
              <Tabs value={activeTab} onChange={handleTabChange}>
                <Tab label={`All (${notifications.length})`} />
                <Tab label={`Unread (${unreadCount})`} />
                <Tab label="Important" />
                <Tab label="Psychological" />
              </Tabs>
              
              {unreadCount > 0 && (
                <Button size="small" onClick={markAllAsRead}>
                  Mark All Read
                </Button>
              )}
            </Box>

            <CardContent sx={{ p: 0 }}>
              <List>
                {filteredNotifications.map((notification, index) => (
                  <React.Fragment key={notification.id}>
                    <ListItem
                      sx={{
                        bgcolor: notification.read ? 'transparent' : 'action.hover',
                        '&:hover': { bgcolor: 'action.selected' }
                      }}
                    >
                      <ListItemIcon>
                        <Badge 
                          variant="dot" 
                          color={getPriorityColor(notification.priority)}
                          invisible={notification.read}
                        >
                          {getNotificationIcon(notification.type)}
                        </Badge>
                      </ListItemIcon>
                      
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography 
                              variant="subtitle2" 
                              fontWeight={notification.read ? 'normal' : 'bold'}
                            >
                              {notification.title}
                            </Typography>
                            {notification.psychologicalTrigger && (
                              <Tooltip title={`Psychological Trigger: ${notification.psychologicalTrigger}`}>
                                <Typography variant="caption">
                                  {getPsychTriggerIcon(notification.psychologicalTrigger)}
                                </Typography>
                              </Tooltip>
                            )}
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography 
                              variant="body2" 
                              color="text.secondary"
                              sx={{ mb: 1 }}
                            >
                              {notification.message}
                            </Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Chip 
                                label={notification.category} 
                                size="small" 
                                variant="outlined"
                              />
                              <Chip 
                                label={notification.priority} 
                                size="small" 
                                color={getPriorityColor(notification.priority)}
                              />
                              <Typography variant="caption" color="text.secondary">
                                {new Date(notification.timestamp).toLocaleString()}
                              </Typography>
                            </Box>
                          </Box>
                        }
                        onClick={() => !notification.read && markAsRead(notification.id)}
                      />
                      
                      <ListItemSecondaryAction>
                        <Stack direction="row" spacing={1}>
                          {notification.actionUrl && (
                            <Button 
                              size="small" 
                              variant="outlined"
                              href={notification.actionUrl}
                            >
                              {notification.actionLabel}
                            </Button>
                          )}
                          <IconButton 
                            size="small"
                            onClick={(e) => handleMenuClick(e, notification)}
                          >
                            <MoreVert />
                          </IconButton>
                        </Stack>
                      </ListItemSecondaryAction>
                    </ListItem>
                    {index < filteredNotifications.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Sidebar */}
        <Grid item xs={12} md={4}>
          <Stack spacing={3}>
            {/* Quick Actions */}
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  🚀 Quick Actions
                </Typography>
                
                <Stack spacing={2}>
                  <Button 
                    variant="outlined" 
                    fullWidth 
                    startIcon={<VolumeOff />}
                  >
                    Pause Notifications (1h)
                  </Button>
                  
                  <Button 
                    variant="outlined" 
                    fullWidth 
                    startIcon={<Settings />}
                  >
                    Notification Preferences
                  </Button>
                  
                  <Button 
                    variant="outlined" 
                    fullWidth 
                    startIcon={<Psychology />}
                  >
                    Psychological Settings
                  </Button>
                </Stack>
              </CardContent>
            </Card>

            {/* Notification Categories */}
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  📂 Categories
                </Typography>
                
                <List dense>
                  {['Deadlines', 'Opportunities', 'Team', 'Achievements', 'Wellness', 'System'].map((category) => (
                    <ListItem key={category} sx={{ px: 0 }}>
                      <ListItemText primary={category} />
                      <Chip 
                        label={notifications.filter(n => n.category === category).length} 
                        size="small" 
                        variant="outlined"
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>

            {/* Psychological Insights */}
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  🧠 Psychological Insights
                </Typography>
                
                <Stack spacing={2}>
                  <Paper sx={{ p: 2, bgcolor: 'success.50' }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Optimal Notification Time
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Your peak attention is between 9-11 AM. Important notifications are queued for this window.
                    </Typography>
                  </Paper>
                  
                  <Paper sx={{ p: 2, bgcolor: 'warning.50' }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Stress Level: Moderate
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Non-urgent notifications are being delayed to reduce cognitive load.
                    </Typography>
                  </Paper>
                </Stack>
              </CardContent>
            </Card>
          </Stack>
        </Grid>
      </Grid>

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => {
          if (selectedNotification) markAsRead(selectedNotification.id);
          handleMenuClose();
        }}>
          <MarkAsUnread sx={{ mr: 1 }} /> Mark as Read
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <Archive sx={{ mr: 1 }} /> Archive
        </MenuItem>
        <MenuItem 
          onClick={() => {
            if (selectedNotification) deleteNotification(selectedNotification.id);
            handleMenuClose();
          }}
          sx={{ color: 'error.main' }}
        >
          <Delete sx={{ mr: 1 }} /> Delete
        </MenuItem>
      </Menu>
    </Container>
  );
}
