'use client';

import React from 'react';
import { Container, Typo<PERSON>, Card, CardContent, Grid, Button, Box, Chip, MenuItem, TextField } from '@mui/material';
import { Download, Schedule, FileDownload } from '@mui/icons-material';

export default function DataExportPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
        Data Export
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        Export your data in various formats for analysis
      </Typography>
      
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Export Configuration</Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField fullWidth select label="Data Type" variant="outlined" defaultValue="bids">
                    <MenuItem value="bids">Bid Data</MenuItem>
                    <MenuItem value="tenders">Tender Data</MenuItem>
                    <MenuItem value="analytics">Analytics Data</MenuItem>
                  </TextField>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField fullWidth select label="Format" variant="outlined" defaultValue="csv">
                    <MenuItem value="csv">CSV</MenuItem>
                    <MenuItem value="excel">Excel</MenuItem>
                    <MenuItem value="pdf">PDF</MenuItem>
                  </TextField>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField fullWidth label="Date From" type="date" variant="outlined" InputLabelProps={{ shrink: true }} />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField fullWidth label="Date To" type="date" variant="outlined" InputLabelProps={{ shrink: true }} />
                </Grid>
              </Grid>
              <Button variant="contained" startIcon={<Download />} sx={{ mt: 2 }}>
                Export Data
              </Button>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Export History</Typography>
              <Box sx={{ mt: 2 }}>
                <Box sx={{ p: 2, border: '1px solid', borderColor: 'divider', borderRadius: 1, mb: 2 }}>
                  <Typography variant="body2" gutterBottom>Bid Data Export</Typography>
                  <Chip label="CSV" size="small" sx={{ mr: 1 }} />
                  <Typography variant="caption" color="text.secondary">2024-01-10</Typography>
                </Box>
                <Box sx={{ p: 2, border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
                  <Typography variant="body2" gutterBottom>Analytics Report</Typography>
                  <Chip label="PDF" size="small" sx={{ mr: 1 }} />
                  <Typography variant="caption" color="text.secondary">2024-01-08</Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
}
