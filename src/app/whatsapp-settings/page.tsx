'use client';

import React from 'react';
import { Container, Typography, Card, CardContent, Grid, Button, Box, Switch, FormControlLabel } from '@mui/material';
import { WhatsApp, Settings, Notifications, Security } from '@mui/icons-material';

export default function WhatsAppSettingsPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
        WhatsApp Settings
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        Configure WhatsApp integration and automation settings
      </Typography>
      
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Connection Settings</Typography>
              <Box sx={{ mt: 2 }}>
                <FormControlLabel control={<Switch defaultChecked />} label="Enable WhatsApp Integration" />
                <FormControlLabel control={<Switch defaultChecked />} label="Auto-respond to Messages" />
                <FormControlLabel control={<Switch />} label="Send Read Receipts" />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Notification Settings</Typography>
              <Box sx={{ mt: 2 }}>
                <FormControlLabel control={<Switch defaultChecked />} label="New Tender Alerts" />
                <FormControlLabel control={<Switch defaultChecked />} label="Bid Status Updates" />
                <FormControlLabel control={<Switch />} label="Daily Summary" />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
}
