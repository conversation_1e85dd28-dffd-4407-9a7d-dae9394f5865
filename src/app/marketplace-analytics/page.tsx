'use client';

import React from 'react';
import { Container, Typography, Card, CardContent, Grid, Box, Avatar, LinearProgress } from '@mui/material';
import { TrendingUp, Assessment, Business, AttachMoney } from '@mui/icons-material';

export default function MarketplaceAnalyticsPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
        Marketplace Analytics
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        Comprehensive marketplace intelligence and trends
      </Typography>
      
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="primary">
                    R45.2M
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Market Volume
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <AttachMoney />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="success.main">
                    234
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active Providers
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <Business />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Market Trends</Typography>
              <Box sx={{ mt: 2 }}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" gutterBottom>IT Services: +18%</Typography>
                  <LinearProgress variant="determinate" value={75} color="success" />
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" gutterBottom>Construction: +12%</Typography>
                  <LinearProgress variant="determinate" value={60} color="primary" />
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" gutterBottom>Consulting: +8%</Typography>
                  <LinearProgress variant="determinate" value={45} color="warning" />
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
}
