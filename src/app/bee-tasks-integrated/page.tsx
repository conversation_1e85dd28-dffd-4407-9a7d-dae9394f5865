'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  Button,
  Stack,
  Tab,
  Tabs,
  LinearProgress,
  Alert,
  Avatar,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  TextField
} from '@mui/material';
import {
  Assignment,
  LocationOn,
  Schedule,
  AttachMoney,
  Star,
  Navigation,
  Phone,
  CheckCircle,
  Cancel,
  PlayArrow,
  Pause,
  Stop,
  Camera,
  Upload
} from '@mui/icons-material';

// Import comprehensive tender validation service
import { TenderValidationService } from '../../services/TenderValidationService';

// Database-aligned interfaces with tender validation
interface BeeTask {
  id: number;
  bee_id: number;
  title: string;
  description: string;
  task_type: 'document_collection' | 'site_visit' | 'briefing_attendance' | 'form_completion' | 'courier_delivery' | 'courier_pickup' | 'compliance_checking' | 'technical_evaluation' | 'ad_hoc';
  status: 'pending' | 'accepted' | 'in_progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';

  // MANDATORY TENDER ASSOCIATION
  tender_id: string; // Must be valid BidBeez tender ID (BID-YYYYMMDD-XXXXXXXX)
  tender_title: string;
  tender_source: 'bidbeez_core' | 'skillsync' | 'toolsync' | 'government_portal' | 'supplier_network';
  tender_reference: string; // Original tender reference number

  location_address: string;
  location_coordinates: string | null; // JSON string
  deadline: string;
  estimated_duration_hours: number;
  payment_amount: string;
  payment_terms: string;
  requirements: string; // JSON string
  evidence_requirements: string; // JSON string
  completion_criteria: string; // JSON string
  workflow_type: string;
  assignment_method: 'manual' | 'auto' | 'bid';
  auto_assignment_criteria: string | null; // JSON string
  required_verification_level: string;
  assigned_by: string;
  assigned_at: string;
  started_at: string | null;
  completed_at: string | null;
  created_at: string;
  updated_at: string;

  // COURIER SPECIFIC FIELDS
  courier_details?: {
    pickup_address?: string;
    delivery_address?: string;
    delivery_mode: 'bee_direct' | 'courier' | 'bee_air_bee' | 'bee_air_bee_extended' | 'courier_plus_bee';
    document_type: string;
    special_requirements: string[];
    tracking_number?: string;
  };
}

interface TaskProgress {
  task_id: number;
  progress_percentage: number;
  current_step: string;
  steps_completed: string; // JSON array
  evidence_submitted: string; // JSON array
  notes: string;
  last_update: string;
}

export default function BeeTasksIntegratedPage() {
  const [currentTab, setCurrentTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [tasks, setTasks] = useState<BeeTask[]>([]);
  const [taskProgress, setTaskProgress] = useState<Record<number, TaskProgress>>({});
  const [selectedTask, setSelectedTask] = useState<BeeTask | null>(null);
  const [actionDialogOpen, setActionDialogOpen] = useState(false);
  const [actionType, setActionType] = useState<'accept' | 'start' | 'complete' | 'cancel'>('accept');
  const [actionNotes, setActionNotes] = useState('');

  // Initialize tender validation service
  const tenderValidationService = TenderValidationService.getInstance();
  const [availableTenders] = useState(tenderValidationService.getAllTenders());

  useEffect(() => {
    // Simulate API calls to Supabase
    setTimeout(() => {
      setTasks([
        {
          id: 101,
          bee_id: 1,
          title: 'Municipal Tender Document Collection',
          description: 'Collect comprehensive tender documents from City of Johannesburg Municipal Offices for road maintenance contract RFQ-2024-001.',
          task_type: 'document_collection',
          status: 'in_progress',
          priority: 'high',

          // MANDATORY TENDER ASSOCIATION
          tender_id: 'BID-20240115-JHB001',
          tender_title: 'Municipal Infrastructure Development - Phase 3',
          tender_source: 'government_portal',
          tender_reference: 'COJ-INF-2024-001',

          location_address: 'City of Johannesburg Offices, 158 Loveday Street, Braamfontein, Johannesburg, 2001',
          location_coordinates: '{"latitude": -26.1929, "longitude": 28.0344}',
          deadline: '2024-01-15T16:00:00Z',
          estimated_duration_hours: 2,
          payment_amount: '450.00',
          payment_terms: 'payment_on_completion',
          requirements: '["Valid ID", "BidBeez Authorization Letter", "Transport to location"]',
          evidence_requirements: '["Photo of documents collected", "Receipt/acknowledgment", "GPS check-in/out"]',
          completion_criteria: '["All documents collected", "Evidence submitted", "Client confirmation"]',
          workflow_type: 'standard_collection',
          assignment_method: 'manual',
          auto_assignment_criteria: null,
          required_verification_level: 'standard',
          assigned_by: 'Queen Bee Sarah',
          assigned_at: '2024-01-15T08:00:00Z',
          started_at: '2024-01-15T09:30:00Z',
          completed_at: null,
          created_at: '2024-01-15T07:00:00Z',
          updated_at: '2024-01-15T09:30:00Z'
        },
        {
          id: 102,
          bee_id: 1,
          title: 'Courier Delivery - Bid Submission',
          description: 'Urgent courier delivery of completed bid documents to Department of Health for medical equipment tender. Requires signature confirmation and photo evidence.',
          task_type: 'courier_delivery',
          status: 'pending',
          priority: 'urgent',

          // MANDATORY TENDER ASSOCIATION
          tender_id: 'BID-20240116-DOH002',
          tender_title: 'Medical Equipment Procurement - Eastern Cape',
          tender_source: 'government_portal',
          tender_reference: 'DOH-MED-2024-002',

          location_address: 'Department of Health, Bhisho, Eastern Cape',
          location_coordinates: '{"latitude": -32.8518, "longitude": 27.4398}',
          deadline: '2024-01-16T12:00:00Z',
          estimated_duration_hours: 4,
          payment_amount: '850.00',
          payment_terms: 'payment_on_completion',
          requirements: '["Valid driver license", "Insured vehicle", "Professional appearance", "Smartphone for tracking"]',
          evidence_requirements: '["Delivery confirmation photo", "Recipient signature", "GPS tracking log", "Timestamp verification"]',
          completion_criteria: '["Documents delivered on time", "Signature obtained", "Photo evidence submitted", "GPS tracking complete"]',
          workflow_type: 'courier_delivery',
          assignment_method: 'auto',
          auto_assignment_criteria: '{"max_distance_km": 50, "required_skills": ["courier_delivery"], "min_rating": 4.5, "vehicle_required": true}',
          required_verification_level: 'premium',
          assigned_by: 'Courier Dispatch Engine',
          assigned_at: '2024-01-15T15:45:00Z',
          started_at: null,
          completed_at: null,
          created_at: '2024-01-15T15:00:00Z',
          updated_at: '2024-01-15T15:45:00Z',

          // COURIER SPECIFIC DETAILS
          courier_details: {
            pickup_address: 'BidBeez Office, Sandton City, Johannesburg',
            delivery_address: 'Department of Health, Bhisho, Eastern Cape',
            delivery_mode: 'bee_direct',
            document_type: 'bid_submission',
            special_requirements: ['signature_required', 'photo_evidence', 'time_sensitive', 'confidential'],
            tracking_number: 'BEE-DEL-20240115-001'
          }
        },
        {
          id: 103,
          bee_id: 1,
          title: 'SkillSync Provider Verification - Cybersecurity Specialist',
          description: 'Verify cybersecurity specialist credentials and conduct on-site assessment for SkillSync marketplace integration. Required for upcoming government IT security tender.',
          task_type: 'ad_hoc',
          status: 'completed',
          priority: 'medium',

          // MANDATORY TENDER ASSOCIATION (SkillSync Integration)
          tender_id: 'BID-20240113-SKILL001',
          tender_title: 'Government IT Security Infrastructure Upgrade',
          tender_source: 'skillsync',
          tender_reference: 'SITA-SEC-2024-001',

          location_address: 'CyberTech Solutions, Waterfall City, Midrand, 1685',
          location_coordinates: '{"latitude": -25.9953, "longitude": 28.1294}',
          deadline: '2024-01-13T17:00:00Z',
          estimated_duration_hours: 4,
          payment_amount: '800.00',
          payment_terms: 'payment_on_completion',
          requirements: '["Technical assessment skills", "Cybersecurity knowledge", "Verification protocols", "Professional appearance"]',
          evidence_requirements: '["Verification photos", "Completed assessment form", "Skills validation checklist", "Provider interview notes"]',
          completion_criteria: '["Provider assessment completed", "Skills verified", "SkillSync profile updated", "Tender readiness confirmed"]',
          workflow_type: 'skillsync_verification',
          assignment_method: 'auto',
          auto_assignment_criteria: '{"max_distance_km": 30, "required_skills": ["technical_assessment", "verification"], "min_rating": 4.5}',
          required_verification_level: 'premium',
          assigned_by: 'SkillSync Integration System',
          assigned_at: '2024-01-13T08:00:00Z',
          started_at: '2024-01-13T10:00:00Z',
          completed_at: '2024-01-13T15:30:00Z',
          created_at: '2024-01-13T07:30:00Z',
          updated_at: '2024-01-13T15:30:00Z'
        },

        // HISTORICAL/LEGACY TENDER TASK EXAMPLE
        {
          id: 104,
          bee_id: 1,
          title: 'Historical Tender Follow-up - Legacy Construction Project',
          description: 'Follow-up documentation and compliance verification for legacy construction project completed before BidBeez module launch. Required for audit trail completion.',
          task_type: 'ad_hoc',
          status: 'pending',
          priority: 'medium',

          // HISTORICAL TENDER ASSOCIATION
          tender_id: 'BID-20230815-LEG001',
          tender_title: 'Legacy Construction Project - Phase 1',
          tender_source: 'legacy_system',
          tender_reference: 'PWD-CONST-2023-001',

          location_address: 'Legacy Project Site, Pretoria West, 0183',
          location_coordinates: '{"latitude": -25.7479, "longitude": 28.1893}',
          deadline: '2024-01-20T16:00:00Z',
          estimated_duration_hours: 3,
          payment_amount: '650.00',
          payment_terms: 'payment_on_completion',
          requirements: '["Historical project knowledge", "Audit documentation skills", "Legacy system access"]',
          evidence_requirements: '["Audit trail documentation", "Compliance verification photos", "Legacy system screenshots"]',
          completion_criteria: '["Historical documentation complete", "Audit trail verified", "Legacy system updated"]',
          workflow_type: 'historical_audit',
          assignment_method: 'manual',
          auto_assignment_criteria: null,
          required_verification_level: 'premium',
          assigned_by: 'Legacy System Migration Team',
          assigned_at: '2024-01-15T16:00:00Z',
          started_at: null,
          completed_at: null,
          created_at: '2024-01-15T16:00:00Z',
          updated_at: '2024-01-15T16:00:00Z'
        },

        // TOOLSYNC INTEGRATION TASK EXAMPLE
        {
          id: 105,
          bee_id: 1,
          title: 'ToolSync License Verification - Government Software Audit',
          description: 'Verify software licenses and compliance for government tender requiring specific tools. ToolSync integration for license validation and cost optimization.',
          task_type: 'ad_hoc',
          status: 'pending',
          priority: 'high',

          // TOOLSYNC TENDER ASSOCIATION
          tender_id: 'BID-20240117-TOOL001',
          tender_title: 'Construction Equipment Rental - Government Projects',
          tender_source: 'toolsync',
          tender_reference: 'PWD-EQUIP-2024-001',

          location_address: 'Government IT Center, Hatfield, Pretoria, 0083',
          location_coordinates: '{"latitude": -25.7545, "longitude": 28.2314}',
          deadline: '2024-01-19T14:00:00Z',
          estimated_duration_hours: 4,
          payment_amount: '750.00',
          payment_terms: 'payment_on_completion',
          requirements: '["Software license expertise", "ToolSync platform access", "Government compliance knowledge"]',
          evidence_requirements: '["License verification screenshots", "Compliance checklist", "ToolSync integration report"]',
          completion_criteria: '["All licenses verified", "ToolSync integration complete", "Compliance report submitted"]',
          workflow_type: 'toolsync_integration',
          assignment_method: 'auto',
          auto_assignment_criteria: '{"required_skills": ["license_verification", "toolsync"], "min_rating": 4.5}',
          required_verification_level: 'premium',
          assigned_by: 'ToolSync Integration Engine',
          assigned_at: '2024-01-15T17:00:00Z',
          started_at: null,
          completed_at: null,
          created_at: '2024-01-15T17:00:00Z',
          updated_at: '2024-01-15T17:00:00Z'
        }
      ]);

      setTaskProgress({
        101: {
          task_id: 101,
          progress_percentage: 65,
          current_step: 'Document Collection',
          steps_completed: '["Check-in at location", "Meet contact person", "Collect initial documents"]',
          evidence_submitted: '["GPS check-in photo", "Contact person meeting photo"]',
          notes: 'Successfully met with Ms. Nomsa Dlamini. Collected 80% of required documents. Waiting for final compliance certificates.',
          last_update: '2024-01-15T12:30:00Z'
        },
        103: {
          task_id: 103,
          progress_percentage: 100,
          current_step: 'Completed',
          steps_completed: '["Site arrival", "Safety briefing", "Site inspection", "Photo documentation", "Report completion"]',
          evidence_submitted: '["Site photos (15)", "Completed inspection checklist", "GPS verification", "Final report"]',
          notes: 'Site inspection completed successfully. All safety protocols followed. Comprehensive photo documentation provided.',
          last_update: '2024-01-13T15:30:00Z'
        }
      });

      setLoading(false);
    }, 1000);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'in_progress': return 'info';
      case 'pending': return 'warning';
      case 'cancelled': return 'error';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  const getTaskIcon = (type: string) => {
    switch (type) {
      case 'document_collection': return '📄';
      case 'site_visit': return '🏗️';
      case 'form_completion': return '📝';
      case 'briefing_attendance': return '👥';
      case 'compliance_checking': return '✅';
      default: return '📋';
    }
  };

  const parseJsonField = (jsonString: string | null): any[] => {
    if (!jsonString) return [];
    try {
      return JSON.parse(jsonString);
    } catch {
      return [];
    }
  };

  const parseCoordinates = (coordString: string | null): { latitude: number; longitude: number } | null => {
    if (!coordString) return null;
    try {
      return JSON.parse(coordString);
    } catch {
      return null;
    }
  };

  const validateTaskTender = (task: BeeTask): { isValid: boolean; message: string; isHistorical: boolean } => {
    const validation = tenderValidationService.validateTender(task.tender_id);
    const isHistorical = validation.tender?.historical_context?.pre_bidbeez_launch || false;

    return {
      isValid: validation.isValid,
      message: validation.message,
      isHistorical
    };
  };

  const handleTaskAction = (task: BeeTask, action: 'accept' | 'start' | 'complete' | 'cancel') => {
    // Validate tender before allowing action
    const tenderValidation = validateTaskTender(task);
    if (!tenderValidation.isValid) {
      alert(`Cannot perform action: ${tenderValidation.message}`);
      return;
    }

    setSelectedTask(task);
    setActionType(action);
    setActionDialogOpen(true);
  };

  const executeTaskAction = async () => {
    if (!selectedTask) return;

    // In real app, this would update Supabase
    const updatedTasks = tasks.map(task => {
      if (task.id === selectedTask.id) {
        const now = new Date().toISOString();
        switch (actionType) {
          case 'accept':
            return { ...task, status: 'accepted' as const, updated_at: now };
          case 'start':
            return { ...task, status: 'in_progress' as const, started_at: now, updated_at: now };
          case 'complete':
            return { ...task, status: 'completed' as const, completed_at: now, updated_at: now };
          case 'cancel':
            return { ...task, status: 'cancelled' as const, updated_at: now };
          default:
            return task;
        }
      }
      return task;
    });

    setTasks(updatedTasks);
    setActionDialogOpen(false);
    setActionNotes('');
    setSelectedTask(null);
  };

  const filteredTasks = tasks.filter(task => {
    switch (currentTab) {
      case 0: return task.status === 'pending';
      case 1: return ['accepted', 'in_progress'].includes(task.status);
      case 2: return ['completed', 'cancelled'].includes(task.status);
      default: return true;
    }
  });

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 3 }}>
        <LinearProgress />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Loading tasks from database...
        </Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" fontWeight="bold" gutterBottom>
          📋 Task Management (Integrated)
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Real-time task management connected to database
        </Typography>
      </Box>

      {/* Task Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={currentTab} onChange={(e, newValue) => setCurrentTab(newValue)}>
          <Tab label={`Pending (${tasks.filter(t => t.status === 'pending').length})`} />
          <Tab label={`Active (${tasks.filter(t => ['accepted', 'in_progress'].includes(t.status)).length})`} />
          <Tab label={`Completed (${tasks.filter(t => ['completed', 'cancelled'].includes(t.status)).length})`} />
        </Tabs>
      </Box>

      {/* Tasks List */}
      <Grid container spacing={3}>
        {filteredTasks.map((task) => {
          const progress = taskProgress[task.id];
          const coordinates = parseCoordinates(task.location_coordinates);
          const requirements = parseJsonField(task.requirements);
          const evidenceReqs = parseJsonField(task.evidence_requirements);

          return (
            <Grid item xs={12} key={task.id}>
              <Card>
                <CardContent>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={8}>
                      <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2, mb: 2 }}>
                        <Typography variant="h2" sx={{ fontSize: '2rem' }}>
                          {getTaskIcon(task.task_type)}
                        </Typography>
                        <Box sx={{ flex: 1 }}>
                          <Typography variant="h6" fontWeight="bold" gutterBottom>
                            {task.title}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                            {task.description}
                          </Typography>
                          
                          <Stack direction="row" spacing={1} sx={{ mb: 2 }} flexWrap="wrap" gap={1}>
                            <Chip
                              label={task.status.toUpperCase()}
                              color={getStatusColor(task.status) as any}
                              size="small"
                            />
                            <Chip
                              label={task.priority.toUpperCase()}
                              color={getPriorityColor(task.priority) as any}
                              variant="outlined"
                              size="small"
                            />
                            <Chip
                              label={task.task_type.replace('_', ' ').toUpperCase()}
                              variant="outlined"
                              size="small"
                            />
                            <Chip
                              label={`TENDER: ${task.tender_id}`}
                              color="primary"
                              size="small"
                            />
                            <Chip
                              label={task.tender_source.toUpperCase()}
                              color="secondary"
                              variant="outlined"
                              size="small"
                            />
                            {task.courier_details && (
                              <Chip
                                label={`🚚 ${task.courier_details.delivery_mode.replace('_', ' ').toUpperCase()}`}
                                color="warning"
                                size="small"
                              />
                            )}
                            {task.tender_source === 'legacy_system' && (
                              <Chip
                                label="📜 HISTORICAL"
                                color="info"
                                variant="outlined"
                                size="small"
                              />
                            )}
                            {['skillsync', 'toolsync', 'supplier_network'].includes(task.tender_source) && (
                              <Chip
                                label={`🔗 ${task.tender_source.toUpperCase()}`}
                                color="secondary"
                                size="small"
                              />
                            )}
                          </Stack>

                          <Grid container spacing={2} sx={{ mb: 2 }}>
                            <Grid item xs={12}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                                <Assignment sx={{ fontSize: 16, color: 'primary.main' }} />
                                <Typography variant="body2" fontWeight="bold" color="primary.main">
                                  Tender: {task.tender_title}
                                </Typography>
                              </Box>
                              <Typography variant="body2" color="text.secondary" sx={{ ml: 3 }}>
                                Reference: {task.tender_reference} | Source: {task.tender_source}
                              </Typography>
                            </Grid>

                            <Grid item xs={12} sm={6}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <LocationOn sx={{ fontSize: 16, color: 'text.secondary' }} />
                                <Typography variant="body2" color="text.secondary">
                                  {task.location_address}
                                </Typography>
                              </Box>
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Schedule sx={{ fontSize: 16, color: 'text.secondary' }} />
                                <Typography variant="body2" color="text.secondary">
                                  Due: {new Date(task.deadline).toLocaleString()}
                                </Typography>
                              </Box>
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <AttachMoney sx={{ fontSize: 16, color: 'text.secondary' }} />
                                <Typography variant="body2" color="text.secondary">
                                  Payment: R{parseFloat(task.payment_amount).toLocaleString()}
                                </Typography>
                              </Box>
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Star sx={{ fontSize: 16, color: 'text.secondary' }} />
                                <Typography variant="body2" color="text.secondary">
                                  Assigned by: {task.assigned_by}
                                </Typography>
                              </Box>
                            </Grid>

                            {task.courier_details && (
                              <Grid item xs={12}>
                                <Box sx={{ mt: 1, p: 2, bgcolor: 'warning.light', borderRadius: 1 }}>
                                  <Typography variant="body2" fontWeight="bold" sx={{ mb: 1 }}>
                                    🚚 Courier Details:
                                  </Typography>
                                  <Typography variant="body2" color="text.secondary">
                                    Pickup: {task.courier_details.pickup_address}
                                  </Typography>
                                  <Typography variant="body2" color="text.secondary">
                                    Delivery: {task.courier_details.delivery_address}
                                  </Typography>
                                  <Typography variant="body2" color="text.secondary">
                                    Mode: {task.courier_details.delivery_mode.replace('_', ' ')} |
                                    Type: {task.courier_details.document_type} |
                                    Tracking: {task.courier_details.tracking_number}
                                  </Typography>
                                </Box>
                              </Grid>
                            )}
                          </Grid>

                          {progress && (
                            <Box sx={{ mb: 2 }}>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                <Typography variant="body2" color="text.secondary">
                                  Progress: {progress.current_step}
                                </Typography>
                                <Typography variant="body2" color="text.secondary">
                                  {progress.progress_percentage}%
                                </Typography>
                              </Box>
                              <LinearProgress 
                                variant="determinate" 
                                value={progress.progress_percentage}
                                sx={{ height: 8, borderRadius: 4 }}
                              />
                            </Box>
                          )}
                        </Box>
                      </Box>
                    </Grid>

                    <Grid item xs={12} md={4}>
                      <Stack spacing={2}>
                        {task.status === 'pending' && (
                          <Button
                            variant="contained"
                            color="success"
                            startIcon={<CheckCircle />}
                            onClick={() => handleTaskAction(task, 'accept')}
                            fullWidth
                          >
                            Accept Task
                          </Button>
                        )}

                        {task.status === 'accepted' && (
                          <Button
                            variant="contained"
                            color="primary"
                            startIcon={<PlayArrow />}
                            onClick={() => handleTaskAction(task, 'start')}
                            fullWidth
                          >
                            Start Task
                          </Button>
                        )}

                        {task.status === 'in_progress' && (
                          <>
                            <Button
                              variant="contained"
                              color="success"
                              startIcon={<CheckCircle />}
                              onClick={() => handleTaskAction(task, 'complete')}
                              fullWidth
                            >
                              Complete Task
                            </Button>
                            <Button
                              variant="outlined"
                              startIcon={<Navigation />}
                              href={coordinates ? 
                                `https://www.google.com/maps/dir//${coordinates.latitude},${coordinates.longitude}` : 
                                '#'
                              }
                              target="_blank"
                              fullWidth
                            >
                              Navigate
                            </Button>
                          </>
                        )}

                        {['pending', 'accepted'].includes(task.status) && (
                          <Button
                            variant="outlined"
                            color="error"
                            startIcon={<Cancel />}
                            onClick={() => handleTaskAction(task, 'cancel')}
                            fullWidth
                          >
                            Cancel Task
                          </Button>
                        )}

                        <Button
                          variant="outlined"
                          startIcon={<Assignment />}
                          fullWidth
                        >
                          View Details
                        </Button>
                      </Stack>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          );
        })}
      </Grid>

      {filteredTasks.length === 0 && (
        <Alert severity="info" sx={{ mt: 3 }}>
          <Typography variant="body1">
            No tasks found in this category.
          </Typography>
        </Alert>
      )}

      {/* Action Dialog */}
      <Dialog open={actionDialogOpen} onClose={() => setActionDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {actionType === 'accept' && '✅ Accept Task'}
          {actionType === 'start' && '▶️ Start Task'}
          {actionType === 'complete' && '🎉 Complete Task'}
          {actionType === 'cancel' && '❌ Cancel Task'}
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" gutterBottom>
            {selectedTask?.title}
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={3}
            placeholder="Add notes (optional)..."
            value={actionNotes}
            onChange={(e) => setActionNotes(e.target.value)}
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setActionDialogOpen(false)}>
            Cancel
          </Button>
          <Button 
            variant="contained" 
            onClick={executeTaskAction}
            color={actionType === 'cancel' ? 'error' : 'primary'}
          >
            Confirm {actionType.charAt(0).toUpperCase() + actionType.slice(1)}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
}
