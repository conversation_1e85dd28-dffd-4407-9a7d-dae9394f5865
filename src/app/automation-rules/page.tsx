'use client';

import React from 'react';
import { Container, Typo<PERSON>, Card, CardContent, Grid, Button, Box, Chip, Switch, FormControlLabel } from '@mui/material';
import { SmartToy, Add, Edit, PlayArrow } from '@mui/icons-material';

const automationRules = [
  { id: 1, name: "Auto-bid on IT Tenders", status: true, triggers: 12, success: "89%" },
  { id: 2, name: "Price Alert Notifications", status: true, triggers: 45, success: "94%" },
  { id: 3, name: "Deadline Reminders", status: false, triggers: 0, success: "N/A" }
];

export default function AutomationRulesPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
            Automation Rules
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Configure intelligent automation workflows
          </Typography>
        </Box>
        <Button variant="contained" startIcon={<Add />}>
          Create Rule
        </Button>
      </Box>
      
      <Grid container spacing={3}>
        {automationRules.map((rule) => (
          <Grid item xs={12} md={4} key={rule.id}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6">{rule.name}</Typography>
                  <FormControlLabel
                    control={<Switch checked={rule.status} />}
                    label=""
                  />
                </Box>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Triggered {rule.triggers} times this month
                </Typography>
                <Chip 
                  label={`${rule.success} success rate`}
                  color={rule.status ? 'success' : 'default'}
                  size="small"
                  sx={{ mb: 2 }}
                />
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button size="small" startIcon={<Edit />}>Edit</Button>
                  <Button size="small" startIcon={<PlayArrow />}>Test</Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Container>
  );
}
