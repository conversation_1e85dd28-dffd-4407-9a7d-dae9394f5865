'use client';

import React from 'react';
import { Container, Typo<PERSON>, Card, CardContent, Grid, Button, Box, Switch, FormControlLabel } from '@mui/material';
import { Integration, Api, Cloud, Security } from '@mui/icons-material';

export default function IntegrationSettingsPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
        Integration Settings
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        Configure third-party integrations and API connections
      </Typography>
      
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>API Integrations</Typography>
              <Box sx={{ mt: 2 }}>
                <FormControlLabel control={<Switch defaultChecked />} label="Government Tender Portal" />
                <FormControlLabel control={<Switch defaultChecked />} label="Banking API" />
                <FormControlLabel control={<Switch />} label="CRM Integration" />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Cloud Services</Typography>
              <Box sx={{ mt: 2 }}>
                <FormControlLabel control={<Switch defaultChecked />} label="Document Storage" />
                <FormControlLabel control={<Switch defaultChecked />} label="Backup Services" />
                <FormControlLabel control={<Switch />} label="Analytics Platform" />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
}
