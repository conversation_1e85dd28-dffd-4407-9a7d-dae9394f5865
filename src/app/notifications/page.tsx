'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Alert,
  Grid,
  Card,
  CardContent,
  Chip,
  Stack,
  IconButton,
  Menu,
  MenuItem,
  Divider
} from '@mui/material';
import {
  Notifications as NotificationsIcon,
  FilterList as FilterIcon,
  Sort as SortIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  TrendingUp as TrendingUpIcon,
  Schedule as ScheduleIcon,
  LocationOn as LocationIcon
} from '@mui/icons-material';
import { useRouter } from 'next/navigation';

import { EnhancedNotification } from '../../types/bidWorkflow';
import BidWorkflowService from '../../services/BidWorkflowService';
import TenderNotificationCard from '../../components/notifications/TenderNotificationCard';

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState<EnhancedNotification[]>([]);
  const [loading, setLoading] = useState(true);
  const [filterAnchor, setFilterAnchor] = useState<null | HTMLElement>(null);
  const [sortAnchor, setSortAnchor] = useState<null | HTMLElement>(null);
  const [selectedFilter, setSelectedFilter] = useState<string>('all');
  const [selectedSort, setSelectedSort] = useState<string>('newest');

  const router = useRouter();
  const workflowService = BidWorkflowService.getInstance();

  useEffect(() => {
    loadNotifications();
    // Add some mock notifications for demo
    addMockNotifications();
  }, []);

  const loadNotifications = async () => {
    try {
      setLoading(true);
      const userNotifications = await workflowService.getNotifications('user-123');
      setNotifications(userNotifications);
    } catch (error) {
      console.error('Error loading notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const addMockNotifications = async () => {
    // Add some mock notifications for demonstration
    const mockNotifications = [
      {
        id: 'notif-001',
        tenderId: 'tender-001',
        title: 'Municipal Infrastructure Development Project',
        organization: 'City of Johannesburg',
        estimatedValue: 15600000,
        closingDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days from now
        location: 'Johannesburg, GP',
        category: 'Infrastructure',
        matchScore: 87,
        urgencyLevel: 'high' as const,
        description: 'Comprehensive infrastructure development including road construction, water systems, and electrical installations for new municipal development.',
        requirements: [
          'CIDB Grade 7 or higher certification required',
          'Minimum 5 years municipal infrastructure experience',
          'B-BBEE Level 4 or better compliance',
          'Valid tax clearance certificate',
          'Professional indemnity insurance minimum R10M'
        ],
        documents: [
          { id: 'doc-001', name: 'Technical Specifications.pdf', type: 'specification' as const, url: '', size: 2500000, processed: false },
          { id: 'doc-002', name: 'Terms and Conditions.pdf', type: 'terms' as const, url: '', size: 1200000, processed: false }
        ],
        createdAt: new Date().toISOString(),
        status: 'new' as const
      },
      {
        id: 'notif-002',
        tenderId: 'tender-002',
        title: 'Highway Construction and Maintenance',
        organization: 'Department of Transport',
        estimatedValue: 28900000,
        closingDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days from now
        location: 'Pretoria, GP',
        category: 'Construction',
        matchScore: 92,
        urgencyLevel: 'critical' as const,
        description: 'Major highway construction project including new lanes, bridge construction, and comprehensive maintenance systems.',
        requirements: [
          'CIDB Grade 9 certification mandatory',
          'Minimum 10 years highway construction experience',
          'B-BBEE Level 2 or better compliance',
          'Environmental compliance certification'
        ],
        documents: [
          { id: 'doc-003', name: 'Engineering Drawings.pdf', type: 'drawings' as const, url: '', size: 5500000, processed: false }
        ],
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
        status: 'new' as const
      },
      {
        id: 'notif-003',
        tenderId: 'tender-003',
        title: 'School Building Renovation Program',
        organization: 'Department of Education',
        estimatedValue: 8900000,
        closingDate: new Date(Date.now() + 12 * 24 * 60 * 60 * 1000).toISOString(), // 12 days from now
        location: 'Cape Town, WC',
        category: 'Building',
        matchScore: 74,
        urgencyLevel: 'medium' as const,
        description: 'Comprehensive renovation of multiple school buildings including structural improvements, electrical upgrades, and accessibility enhancements.',
        requirements: [
          'CIDB Grade 6 or higher certification',
          'Educational facility experience preferred',
          'B-BBEE compliance required'
        ],
        documents: [
          { id: 'doc-004', name: 'Renovation Specifications.pdf', type: 'specification' as const, url: '', size: 3200000, processed: false }
        ],
        createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
        status: 'viewed' as const
      }
    ];

    for (const notification of mockNotifications) {
      await workflowService.addNotification(notification);
    }

    // Reload notifications to include the mock data
    await loadNotifications();
  };

  const handleInterestExpressed = (tenderId: string) => {
    // Remove notification from list since it's now in active bids
    setNotifications(prev => prev.filter(n => n.tenderId !== tenderId));
    
    // Navigate to active bids page
    router.push('/my-active-bids');
  };

  const handleNotificationDismissed = (tenderId: string) => {
    setNotifications(prev => prev.filter(n => n.tenderId !== tenderId));
  };

  const filteredNotifications = notifications.filter(notification => {
    if (selectedFilter === 'all') return true;
    if (selectedFilter === 'urgent') return notification.urgencyLevel === 'critical' || notification.urgencyLevel === 'high';
    if (selectedFilter === 'high_match') return notification.matchScore >= 80;
    if (selectedFilter === 'closing_soon') {
      const daysRemaining = Math.ceil((new Date(notification.closingDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
      return daysRemaining <= 5;
    }
    return true;
  });

  const sortedNotifications = [...filteredNotifications].sort((a, b) => {
    switch (selectedSort) {
      case 'newest':
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      case 'closing_date':
        return new Date(a.closingDate).getTime() - new Date(b.closingDate).getTime();
      case 'match_score':
        return b.matchScore - a.matchScore;
      case 'value':
        return b.estimatedValue - a.estimatedValue;
      default:
        return 0;
    }
  });

  return (
    <Box sx={{ p: 3, minHeight: '100vh', bgcolor: 'grey.50' }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main', mb: 1 }}>
            🔔 Tender Notifications
          </Typography>
          <Typography variant="body1" color="text.secondary">
            New tender opportunities matched to your profile and preferences
          </Typography>
        </Box>
        <Stack direction="row" spacing={1}>
          <IconButton onClick={loadNotifications}>
            <RefreshIcon />
          </IconButton>
          <Button
            variant="outlined"
            startIcon={<FilterIcon />}
            onClick={(e) => setFilterAnchor(e.currentTarget)}
          >
            Filter
          </Button>
          <Button
            variant="outlined"
            startIcon={<SortIcon />}
            onClick={(e) => setSortAnchor(e.currentTarget)}
          >
            Sort
          </Button>
          <Button
            variant="outlined"
            startIcon={<SettingsIcon />}
            href="/notification-settings"
          >
            Settings
          </Button>
        </Stack>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={1}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <NotificationsIcon color="primary" />
                <Typography variant="h6">Total</Typography>
              </Box>
              <Typography variant="h3" sx={{ fontWeight: 600 }}>
                {notifications.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                New opportunities
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={1}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <TrendingUpIcon color="success" />
                <Typography variant="h6">High Match</Typography>
              </Box>
              <Typography variant="h3" sx={{ fontWeight: 600 }}>
                {notifications.filter(n => n.matchScore >= 80).length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                80%+ match score
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={1}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <ScheduleIcon color="warning" />
                <Typography variant="h6">Urgent</Typography>
              </Box>
              <Typography variant="h3" sx={{ fontWeight: 600 }}>
                {notifications.filter(n => n.urgencyLevel === 'critical' || n.urgencyLevel === 'high').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Closing soon
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={1}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <LocationIcon color="info" />
                <Typography variant="h6">Local</Typography>
              </Box>
              <Typography variant="h3" sx={{ fontWeight: 600 }}>
                {notifications.filter(n => n.location.includes('Johannesburg') || n.location.includes('GP')).length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                In your area
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Active Filters */}
      {(selectedFilter !== 'all' || selectedSort !== 'newest') && (
        <Box sx={{ mb: 2 }}>
          <Stack direction="row" spacing={1} alignItems="center">
            <Typography variant="body2" color="text.secondary">
              Active filters:
            </Typography>
            {selectedFilter !== 'all' && (
              <Chip
                label={`Filter: ${selectedFilter.replace('_', ' ')}`}
                size="small"
                onDelete={() => setSelectedFilter('all')}
              />
            )}
            {selectedSort !== 'newest' && (
              <Chip
                label={`Sort: ${selectedSort.replace('_', ' ')}`}
                size="small"
                onDelete={() => setSelectedSort('newest')}
              />
            )}
          </Stack>
        </Box>
      )}

      {/* Notifications List */}
      {loading ? (
        <Alert severity="info">Loading your personalized tender notifications...</Alert>
      ) : sortedNotifications.length === 0 ? (
        <Alert severity="info">
          <Typography variant="body2">
            No tender notifications match your current filters. Try adjusting your filters or check back later for new opportunities.
          </Typography>
        </Alert>
      ) : (
        <Box>
          {sortedNotifications.map((notification) => (
            <TenderNotificationCard
              key={notification.id}
              notification={notification}
              onInterestExpressed={handleInterestExpressed}
              onDismissed={handleNotificationDismissed}
            />
          ))}
        </Box>
      )}

      {/* Filter Menu */}
      <Menu
        anchorEl={filterAnchor}
        open={Boolean(filterAnchor)}
        onClose={() => setFilterAnchor(null)}
      >
        <MenuItem onClick={() => { setSelectedFilter('all'); setFilterAnchor(null); }}>
          All Notifications
        </MenuItem>
        <MenuItem onClick={() => { setSelectedFilter('urgent'); setFilterAnchor(null); }}>
          Urgent (Closing Soon)
        </MenuItem>
        <MenuItem onClick={() => { setSelectedFilter('high_match'); setFilterAnchor(null); }}>
          High Match (80%+)
        </MenuItem>
        <MenuItem onClick={() => { setSelectedFilter('closing_soon'); setFilterAnchor(null); }}>
          Closing in 5 Days
        </MenuItem>
      </Menu>

      {/* Sort Menu */}
      <Menu
        anchorEl={sortAnchor}
        open={Boolean(sortAnchor)}
        onClose={() => setSortAnchor(null)}
      >
        <MenuItem onClick={() => { setSelectedSort('newest'); setSortAnchor(null); }}>
          Newest First
        </MenuItem>
        <MenuItem onClick={() => { setSelectedSort('closing_date'); setSortAnchor(null); }}>
          Closing Date
        </MenuItem>
        <MenuItem onClick={() => { setSelectedSort('match_score'); setSortAnchor(null); }}>
          Match Score
        </MenuItem>
        <MenuItem onClick={() => { setSelectedSort('value'); setSortAnchor(null); }}>
          Contract Value
        </MenuItem>
      </Menu>
    </Box>
  );
}
