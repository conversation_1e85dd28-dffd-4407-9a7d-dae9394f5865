'use client';

import React, { useState } from 'react';
import {
  Box,
  Container,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  LinearProgress,
  Stack,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Alert,
  Avatar,
  Divider
} from '@mui/material';
import {
  AttachMoney,
  TrendingUp,
  TrendingDown,
  AccountBalance,
  Receipt,
  Schedule,
  Star,
  Assignment,
  Payment,
  Download,
  Visibility
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Bar<PERSON>hart, Bar, PieChart, Pie, Cell } from 'recharts';

interface EarningsData {
  summary: {
    totalEarnings: number;
    thisMonth: number;
    thisWeek: number;
    today: number;
    pendingPayments: number;
    averagePerTask: number;
  };
  monthlyTrend: Array<{
    month: string;
    earnings: number;
    tasks: number;
    averageRating: number;
  }>;
  taskBreakdown: Array<{
    type: string;
    count: number;
    earnings: number;
    color: string;
  }>;
  recentTransactions: Array<{
    id: string;
    taskId: string;
    taskTitle: string;
    amount: number;
    status: 'paid' | 'pending' | 'processing';
    date: string;
    paymentMethod: string;
    queenBee: string;
  }>;
  paymentMethods: Array<{
    type: string;
    details: string;
    isDefault: boolean;
  }>;
}

export default function BeeEarningsPage() {
  const [currentTab, setCurrentTab] = useState(0);

  const [earningsData, setEarningsData] = useState<EarningsData>({
    summary: {
      totalEarnings: 23450,
      thisMonth: 5400,
      thisWeek: 1350,
      today: 450,
      pendingPayments: 650,
      averagePerTask: 498
    },
    monthlyTrend: [
      { month: 'Aug', earnings: 3200, tasks: 8, averageRating: 4.5 },
      { month: 'Sep', earnings: 4100, tasks: 10, averageRating: 4.6 },
      { month: 'Oct', earnings: 4800, tasks: 12, averageRating: 4.7 },
      { month: 'Nov', earnings: 6200, tasks: 15, averageRating: 4.8 },
      { month: 'Dec', earnings: 5900, tasks: 14, averageRating: 4.9 },
      { month: 'Jan', earnings: 5400, tasks: 12, averageRating: 4.9 }
    ],
    taskBreakdown: [
      { type: 'Document Collection', count: 25, earnings: 11250, color: '#8884d8' },
      { type: 'Site Visits', count: 15, earnings: 9750, color: '#82ca9d' },
      { type: 'Briefing Attendance', count: 7, earnings: 5600, color: '#ffc658' },
      { type: 'Other Tasks', count: 5, earnings: 2250, color: '#ff7300' }
    ],
    recentTransactions: [
      {
        id: 'txn-001',
        taskId: 'task-001',
        taskTitle: 'Municipal Document Collection',
        amount: 450,
        status: 'paid',
        date: '2024-01-15',
        paymentMethod: 'Bank Transfer',
        queenBee: 'Queen Bee Sarah'
      },
      {
        id: 'txn-002',
        taskId: 'task-002',
        taskTitle: 'Site Visit - Road Construction',
        amount: 650,
        status: 'pending',
        date: '2024-01-14',
        paymentMethod: 'Bank Transfer',
        queenBee: 'Queen Bee Mike'
      },
      {
        id: 'txn-003',
        taskId: 'task-003',
        taskTitle: 'Tender Briefing Attendance',
        amount: 800,
        status: 'processing',
        date: '2024-01-13',
        paymentMethod: 'Bank Transfer',
        queenBee: 'Queen Bee Lisa'
      },
      {
        id: 'txn-004',
        taskId: 'task-004',
        taskTitle: 'Document Delivery Service',
        amount: 300,
        status: 'paid',
        date: '2024-01-12',
        paymentMethod: 'Bank Transfer',
        queenBee: 'Queen Bee Sarah'
      },
      {
        id: 'txn-005',
        taskId: 'task-005',
        taskTitle: 'Municipal Office Visit',
        amount: 400,
        status: 'paid',
        date: '2024-01-11',
        paymentMethod: 'Bank Transfer',
        queenBee: 'Queen Bee Mike'
      }
    ],
    paymentMethods: [
      {
        type: 'Bank Transfer',
        details: 'FNB - Account ending in 1234',
        isDefault: true
      },
      {
        type: 'Mobile Money',
        details: 'MTN Mobile Money - ************',
        isDefault: false
      }
    ]
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'success';
      case 'pending': return 'warning';
      case 'processing': return 'info';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid': return <Payment color="success" />;
      case 'pending': return <Schedule color="warning" />;
      case 'processing': return <TrendingUp color="info" />;
      default: return <Receipt />;
    }
  };

  const calculateGrowth = () => {
    const currentMonth = earningsData.summary.thisMonth;
    const previousMonth = earningsData.monthlyTrend[earningsData.monthlyTrend.length - 2]?.earnings || 0;
    return previousMonth > 0 ? ((currentMonth - previousMonth) / previousMonth * 100).toFixed(1) : 0;
  };

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" fontWeight="bold" gutterBottom>
          💰 My Earnings
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Track your income, payments, and financial performance
        </Typography>
      </Box>

      {/* Earnings Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={6} sm={4} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <AttachMoney sx={{ fontSize: 32, color: 'success.main', mb: 1 }} />
              <Typography variant="h5" fontWeight="bold">
                R{earningsData.summary.totalEarnings.toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Earnings
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={6} sm={4} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <TrendingUp sx={{ fontSize: 32, color: 'primary.main', mb: 1 }} />
              <Typography variant="h5" fontWeight="bold">
                R{earningsData.summary.thisMonth.toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                This Month
              </Typography>
              <Typography variant="caption" color="success.main">
                +{calculateGrowth()}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={6} sm={4} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Schedule sx={{ fontSize: 32, color: 'warning.main', mb: 1 }} />
              <Typography variant="h5" fontWeight="bold">
                R{earningsData.summary.thisWeek.toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                This Week
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={6} sm={4} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Receipt sx={{ fontSize: 32, color: 'info.main', mb: 1 }} />
              <Typography variant="h5" fontWeight="bold">
                R{earningsData.summary.today}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Today
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={6} sm={4} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <AccountBalance sx={{ fontSize: 32, color: 'warning.main', mb: 1 }} />
              <Typography variant="h5" fontWeight="bold">
                R{earningsData.summary.pendingPayments}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Pending
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={6} sm={4} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Star sx={{ fontSize: 32, color: 'gold', mb: 1 }} />
              <Typography variant="h5" fontWeight="bold">
                R{earningsData.summary.averagePerTask}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Avg per Task
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Earnings Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={currentTab} onChange={(e, newValue) => setCurrentTab(newValue)}>
          <Tab label="Earnings Overview" />
          <Tab label="Transaction History" />
          <Tab label="Payment Methods" />
        </Tabs>
      </Box>

      {/* Tab Content */}
      {currentTab === 0 && (
        <Grid container spacing={3}>
          {/* Monthly Earnings Trend */}
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  📈 Monthly Earnings Trend
                </Typography>
                <Box sx={{ height: 300 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={earningsData.monthlyTrend}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip formatter={(value, name) => [`R${value}`, name]} />
                      <Line 
                        type="monotone" 
                        dataKey="earnings" 
                        stroke="#8884d8" 
                        strokeWidth={3}
                        name="Earnings"
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Task Type Breakdown */}
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  📊 Earnings by Task Type
                </Typography>
                <Box sx={{ height: 300 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={earningsData.taskBreakdown}
                        cx="50%"
                        cy="50%"
                        innerRadius={40}
                        outerRadius={80}
                        dataKey="earnings"
                        label={({ type, earnings }) => `${type}: R${earnings}`}
                      >
                        {earningsData.taskBreakdown.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [`R${value}`, 'Earnings']} />
                    </PieChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Task Performance */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  🎯 Task Performance Summary
                </Typography>
                <Grid container spacing={2}>
                  {earningsData.taskBreakdown.map((task, index) => (
                    <Grid item xs={12} sm={6} md={3} key={index}>
                      <Paper sx={{ p: 2 }}>
                        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                          {task.type}
                        </Typography>
                        <Typography variant="h5" color="success.main" gutterBottom>
                          R{task.earnings.toLocaleString()}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {task.count} tasks completed
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Avg: R{Math.round(task.earnings / task.count)} per task
                        </Typography>
                      </Paper>
                    </Grid>
                  ))}
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {currentTab === 1 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6">
                    💳 Transaction History
                  </Typography>
                  <Button startIcon={<Download />} variant="outlined">
                    Export Statement
                  </Button>
                </Box>
                
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Date</TableCell>
                        <TableCell>Task</TableCell>
                        <TableCell>Queen Bee</TableCell>
                        <TableCell>Amount</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Payment Method</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {earningsData.recentTransactions.map((transaction) => (
                        <TableRow key={transaction.id}>
                          <TableCell>
                            {new Date(transaction.date).toLocaleDateString()}
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" fontWeight="bold">
                              {transaction.taskTitle}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              ID: {transaction.taskId}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Avatar sx={{ width: 24, height: 24, bgcolor: 'warning.main' }}>
                                👑
                              </Avatar>
                              <Typography variant="body2">
                                {transaction.queenBee}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body1" fontWeight="bold" color="success.main">
                              R{transaction.amount}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              icon={getStatusIcon(transaction.status)}
                              label={transaction.status.toUpperCase()}
                              color={getStatusColor(transaction.status) as any}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {transaction.paymentMethod}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Button
                              size="small"
                              startIcon={<Visibility />}
                              variant="outlined"
                            >
                              View
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {currentTab === 2 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  💳 Payment Methods
                </Typography>
                <Stack spacing={2}>
                  {earningsData.paymentMethods.map((method, index) => (
                    <Paper 
                      key={index}
                      sx={{ 
                        p: 2, 
                        border: method.isDefault ? '2px solid' : '1px solid',
                        borderColor: method.isDefault ? 'primary.main' : 'divider'
                      }}
                    >
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Box>
                          <Typography variant="subtitle1" fontWeight="bold">
                            {method.type}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {method.details}
                          </Typography>
                        </Box>
                        {method.isDefault && (
                          <Chip label="Default" color="primary" size="small" />
                        )}
                      </Box>
                    </Paper>
                  ))}
                </Stack>
                
                <Button variant="outlined" fullWidth sx={{ mt: 2 }}>
                  Add Payment Method
                </Button>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  📊 Payment Statistics
                </Typography>
                
                <Stack spacing={3}>
                  <Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Average Payment Time
                    </Typography>
                    <Typography variant="h5" fontWeight="bold">
                      2.3 days
                    </Typography>
                  </Box>
                  
                  <Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Payment Success Rate
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="h5" fontWeight="bold">
                        98.5%
                      </Typography>
                      <LinearProgress 
                        variant="determinate" 
                        value={98.5} 
                        sx={{ flex: 1, height: 8, borderRadius: 4 }}
                      />
                    </Box>
                  </Box>
                  
                  <Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Total Transactions
                    </Typography>
                    <Typography variant="h5" fontWeight="bold">
                      47
                    </Typography>
                  </Box>
                </Stack>

                <Alert severity="info" sx={{ mt: 3 }}>
                  <Typography variant="body2">
                    💡 Payments are processed within 24-48 hours after task completion and approval.
                  </Typography>
                </Alert>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
    </Container>
  );
}
