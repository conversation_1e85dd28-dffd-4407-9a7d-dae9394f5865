import './globals.css';
import AppProviders from '../components/providers/AppProviders';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'BYDER BY BIDBEEZ - South Africa\'s Premier Tendering Intelligence Platform',
  description: 'Revolutionizing how businesses discover, analyze, and win government tenders with AI-powered insights, WhatsApp automation, and psychological bidding intelligence.',
  keywords: ['BYDER', 'BidBeez', 'South Africa', 'Tendering', 'Government Tenders', 'AI', 'WhatsApp', 'Bidding'],
  authors: [{ name: 'BYDER BY BIDBEEZ Team' }],
  viewport: 'width=device-width, initial-scale=1',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body>
        <AppProviders>
          {children}
        </AppProviders>
      </body>
    </html>
  );
}
