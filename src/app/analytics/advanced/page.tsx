'use client';

import React, { useState } from 'react';
import { 
  Container, 
  Box, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  Tabs, 
  Tab, 
  Alert,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip
} from '@mui/material';
import { 
  Bar<PERSON>hart3, 
  TrendingUp, 
  PieChart, 
  Activity, 
  Download,
  Calendar,
  Target,
  DollarSign
} from 'lucide-react';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`analytics-tabpanel-${index}`}
      aria-labelledby={`analytics-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

export default function AdvancedAnalyticsPage() {
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Mock analytics data
  const performanceMetrics = {
    totalBids: 156,
    winRate: 23.7,
    avgBidValue: 'R1.2M',
    totalValue: 'R187.2M',
    roi: 15.8,
    avgResponseTime: '2.3 days'
  };

  const bidHistory = [
    { id: 1, tender: 'Municipal IT Services', value: 'R2.4M', status: 'Won', date: '2024-01-15', winProb: 87 },
    { id: 2, tender: 'Road Maintenance Contract', value: 'R890K', status: 'Lost', date: '2024-01-12', winProb: 65 },
    { id: 3, tender: 'Security Services', value: 'R1.2M', status: 'Pending', date: '2024-01-10', winProb: 74 },
    { id: 4, tender: 'Consulting Services', value: 'R650K', status: 'Won', date: '2024-01-08', winProb: 92 },
    { id: 5, tender: 'Equipment Supply', value: 'R3.1M', status: 'Lost', date: '2024-01-05', winProb: 45 }
  ];

  const psychologicalInsights = [
    { metric: 'Stress Level Impact', value: '-12%', description: 'High stress reduces win rate by 12%' },
    { metric: 'Confidence Correlation', value: '+18%', description: 'Higher confidence increases bid quality by 18%' },
    { metric: 'Optimal Timing', value: '10:30 AM', description: 'Peak performance window for bid submissions' },
    { metric: 'Archetype Advantage', value: 'Hunter', description: 'Your archetype excels in competitive scenarios' }
  ];

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
          📊 Advanced Bid Analytics
        </Typography>
        <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
          Comprehensive performance insights, psychological analytics, and predictive intelligence
        </Typography>
        
        <Alert severity="info" sx={{ mb: 3 }}>
          <strong>Analytics Engine:</strong> Processing 156 bids with psychological correlation analysis and predictive modeling.
        </Alert>
      </Box>

      {/* Performance Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Target size={32} color="#2196f3" />
              <Typography variant="h4" fontWeight="bold">{performanceMetrics.totalBids}</Typography>
              <Typography variant="body2" color="text.secondary">Total Bids</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <TrendingUp size={32} color="#4caf50" />
              <Typography variant="h4" fontWeight="bold">{performanceMetrics.winRate}%</Typography>
              <Typography variant="body2" color="text.secondary">Win Rate</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <DollarSign size={32} color="#ff9800" />
              <Typography variant="h4" fontWeight="bold">{performanceMetrics.avgBidValue}</Typography>
              <Typography variant="body2" color="text.secondary">Avg Bid Value</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <BarChart3 size={32} color="#9c27b0" />
              <Typography variant="h4" fontWeight="bold">{performanceMetrics.totalValue}</Typography>
              <Typography variant="body2" color="text.secondary">Total Value</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <PieChart size={32} color="#f44336" />
              <Typography variant="h4" fontWeight="bold">{performanceMetrics.roi}%</Typography>
              <Typography variant="body2" color="text.secondary">ROI</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Activity size={32} color="#607d8b" />
              <Typography variant="h4" fontWeight="bold">{performanceMetrics.avgResponseTime}</Typography>
              <Typography variant="body2" color="text.secondary">Avg Response</Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Export Controls */}
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 3 }}>
        <Button variant="outlined" startIcon={<Download />} sx={{ mr: 2 }}>
          Export PDF Report
        </Button>
        <Button variant="outlined" startIcon={<Calendar />}>
          Schedule Report
        </Button>
      </Box>

      {/* Tabs for different analytics views */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="analytics tabs">
          <Tab label="Performance Trends" />
          <Tab label="Bid History" />
          <Tab label="Psychological Insights" />
          <Tab label="Competitive Analysis" />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>Win Rate Trend</Typography>
                <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: '#f5f5f5' }}>
                  <Typography variant="body1" color="text.secondary">
                    📈 Interactive chart showing win rate trends over time
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>Key Insights</Typography>
                <Alert severity="success" sx={{ mb: 2 }}>
                  <strong>Improving Trend:</strong> Win rate up 8% this quarter
                </Alert>
                <Alert severity="info" sx={{ mb: 2 }}>
                  <strong>Peak Performance:</strong> Tuesdays show highest success
                </Alert>
                <Alert severity="warning">
                  <strong>Opportunity:</strong> Municipal tenders underperforming
                </Alert>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>Recent Bid History</Typography>
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Tender</TableCell>
                    <TableCell>Value</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Date</TableCell>
                    <TableCell>Win Probability</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {bidHistory.map((bid) => (
                    <TableRow key={bid.id}>
                      <TableCell>{bid.tender}</TableCell>
                      <TableCell>{bid.value}</TableCell>
                      <TableCell>
                        <Chip 
                          label={bid.status} 
                          color={bid.status === 'Won' ? 'success' : bid.status === 'Lost' ? 'error' : 'warning'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>{bid.date}</TableCell>
                      <TableCell>{bid.winProb}%</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <Grid container spacing={3}>
          {psychologicalInsights.map((insight, index) => (
            <Grid item xs={12} md={6} key={index}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>{insight.metric}</Typography>
                  <Typography 
                    variant="h4" 
                    color={insight.value.startsWith('+') ? 'success.main' : insight.value.startsWith('-') ? 'error.main' : 'primary.main'}
                    gutterBottom
                  >
                    {insight.value}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {insight.description}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
        
        <Alert severity="info" sx={{ mt: 3 }}>
          <strong>Psychological Analytics:</strong> These insights are based on your behavioral patterns, 
          stress levels, and psychological archetype analysis to optimize your bidding performance.
        </Alert>
      </TabPanel>

      <TabPanel value={tabValue} index={3}>
        <Alert severity="info" sx={{ mb: 3 }}>
          <strong>Competitive Intelligence:</strong> Analysis of competitor behavior and market positioning.
        </Alert>
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>Market Position</Typography>
                <Box sx={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: '#f5f5f5' }}>
                  <Typography variant="body1" color="text.secondary">
                    📊 Competitive positioning chart
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>Competitor Analysis</Typography>
                <Typography variant="body1" color="text.secondary">
                  Advanced competitor intelligence features are being processed. 
                  Analysis includes pricing strategies, win rates, and market share data.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>
    </Container>
  );
}
