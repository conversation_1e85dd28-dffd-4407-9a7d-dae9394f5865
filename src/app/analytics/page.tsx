'use client';

import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Chip,
  LinearProgress,
  Alert,
  Tabs,
  Tab,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  Analytics,
  Timeline,
  Assessment,
  PieChart,
  BarChart,
  ShowChart,
  Download,
  Refresh,
  FilterList
} from '@mui/icons-material';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`analytics-tabpanel-${index}`}
      aria-labelledby={`analytics-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

// Mock data for demo
const mockPerformance = {
  success_rate: 72.5,
  total_bids: 45,
  successful_bids: 32,
  total_value: 2450000,
  won_value: 1876000,
  roi: 68.4,
  profit: 520000,
  market_position: 3,
  revenue: 1876000,
  profit_margin: 27.7,
  cost_per_bid: 12500,
  revenue_per_bid: 41689,
  jobs_created: 127,
  economic_value: 3200000
};

const mockCompetitive = [
  { competitor_name: 'ABC Construction', heads_up_wins: 8, heads_up_losses: 12, win_rate: 40.0, threat_level: 'high' },
  { competitor_name: 'BuildCorp SA', heads_up_wins: 15, heads_up_losses: 7, win_rate: 68.2, threat_level: 'critical' },
  { competitor_name: 'Metro Contractors', heads_up_wins: 6, heads_up_losses: 4, win_rate: 60.0, threat_level: 'medium' }
];

const mockCategories = [
  { category: 'Construction', success_rate: 75.0, total_value: 1200000, profitability: 32.1 },
  { category: 'Infrastructure', success_rate: 68.5, total_value: 980000, profitability: 28.7 },
  { category: 'Maintenance', success_rate: 82.3, total_value: 270000, profitability: 41.2 }
];

export default function AdvancedAnalytics() {
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'quarter' | 'year'>('quarter');
  const [activeTab, setActiveTab] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleExport = async (format: 'pdf' | 'excel' | 'csv') => {
    alert(`Export to ${format.toUpperCase()} functionality will be implemented`);
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  return (
    <Box sx={{ p: 3, minHeight: '100vh', bgcolor: 'grey.50' }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main' }}>
            📊 Advanced Analytics
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Comprehensive bid performance insights and competitive intelligence
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Time Range</InputLabel>
            <Select
              value={timeRange}
              label="Time Range"
              onChange={(e) => setTimeRange(e.target.value as any)}
            >
              <MenuItem value="week">Last Week</MenuItem>
              <MenuItem value="month">Last Month</MenuItem>
              <MenuItem value="quarter">Last Quarter</MenuItem>
              <MenuItem value="year">Last Year</MenuItem>
            </Select>
          </FormControl>

          <Button
            variant="outlined"
            startIcon={<Download />}
            onClick={() => handleExport('pdf')}
          >
            Export PDF
          </Button>

          <Button
            variant="outlined"
            startIcon={<Download />}
            onClick={() => handleExport('excel')}
          >
            Export Excel
          </Button>
        </Box>
      </Box>

      {/* Key Metrics Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={3}>
          <Card elevation={2}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <TrendingUp color="success" />
                <Typography variant="h6">Success Rate</Typography>
              </Box>
              <Typography variant="h4" sx={{ mt: 1, fontWeight: 600 }}>
                {formatPercentage(mockPerformance.success_rate)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {mockPerformance.successful_bids} of {mockPerformance.total_bids} bids won
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card elevation={2}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Assessment color="primary" />
                <Typography variant="h6">Total Value</Typography>
              </Box>
              <Typography variant="h4" sx={{ mt: 1, fontWeight: 600 }}>
                {formatCurrency(mockPerformance.total_value)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Won: {formatCurrency(mockPerformance.won_value)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card elevation={2}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <ShowChart color="info" />
                <Typography variant="h6">ROI</Typography>
              </Box>
              <Typography variant="h4" sx={{ mt: 1, fontWeight: 600 }}>
                {formatPercentage(mockPerformance.roi)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Profit: {formatCurrency(mockPerformance.profit)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card elevation={2}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Timeline color="warning" />
                <Typography variant="h6">Market Position</Typography>
              </Box>
              <Typography variant="h4" sx={{ mt: 1, fontWeight: 600 }}>
                #{mockPerformance.market_position}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Competitive ranking
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Paper sx={{ mb: 3 }} elevation={2}>
        <Tabs value={activeTab} onChange={handleTabChange} variant="fullWidth">
          <Tab label="Performance Trends" icon={<TrendingUp />} />
          <Tab label="Competitive Analysis" icon={<BarChart />} />
          <Tab label="Category Performance" icon={<PieChart />} />
          <Tab label="Financial Analysis" icon={<Assessment />} />
        </Tabs>

        {/* Performance Trends Tab */}
        <TabPanel value={activeTab} index={0}>
          <Typography variant="h6" sx={{ mb: 2 }}>📈 Performance Trends</Typography>
          <Alert severity="info">
            Performance trend charts will be displayed here. Integration with charting library needed.
          </Alert>
        </TabPanel>

        {/* Competitive Analysis Tab */}
        <TabPanel value={activeTab} index={1}>
          <Typography variant="h6" sx={{ mb: 2 }}>🏆 Competitive Analysis</Typography>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Competitor</TableCell>
                  <TableCell align="right">Head-to-Head Wins</TableCell>
                  <TableCell align="right">Head-to-Head Losses</TableCell>
                  <TableCell align="right">Win Rate</TableCell>
                  <TableCell align="right">Threat Level</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {mockCompetitive.map((competitor) => (
                  <TableRow key={competitor.competitor_name}>
                    <TableCell>{competitor.competitor_name}</TableCell>
                    <TableCell align="right">{competitor.heads_up_wins}</TableCell>
                    <TableCell align="right">{competitor.heads_up_losses}</TableCell>
                    <TableCell align="right">{formatPercentage(competitor.win_rate)}</TableCell>
                    <TableCell align="right">
                      <Chip 
                        label={competitor.threat_level}
                        color={
                          competitor.threat_level === 'critical' ? 'error' :
                          competitor.threat_level === 'high' ? 'warning' :
                          competitor.threat_level === 'medium' ? 'info' : 'success'
                        }
                        size="small"
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        {/* Category Performance Tab */}
        <TabPanel value={activeTab} index={2}>
          <Typography variant="h6" sx={{ mb: 2 }}>📊 Category Performance</Typography>
          <Grid container spacing={2}>
            {mockCategories.map((category) => (
              <Grid item xs={12} md={6} key={category.category}>
                <Card elevation={1}>
                  <CardContent>
                    <Typography variant="h6">{category.category}</Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                      <Typography variant="body2">Success Rate:</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        {formatPercentage(category.success_rate)}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2">Total Value:</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        {formatCurrency(category.total_value)}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2">Profitability:</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        {formatPercentage(category.profitability)}
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </TabPanel>

        {/* Financial Analysis Tab */}
        <TabPanel value={activeTab} index={3}>
          <Typography variant="h6" sx={{ mb: 2 }}>💰 Financial Analysis</Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card elevation={1}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2 }}>Revenue Metrics</Typography>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography>Total Revenue:</Typography>
                    <Typography sx={{ fontWeight: 600 }}>
                      {formatCurrency(mockPerformance.revenue)}
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography>Profit Margin:</Typography>
                    <Typography sx={{ fontWeight: 600 }}>
                      {formatPercentage(mockPerformance.profit_margin)}
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography>Cost per Bid:</Typography>
                    <Typography sx={{ fontWeight: 600 }}>
                      {formatCurrency(mockPerformance.cost_per_bid)}
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography>Revenue per Bid:</Typography>
                    <Typography sx={{ fontWeight: 600 }}>
                      {formatCurrency(mockPerformance.revenue_per_bid)}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card elevation={1}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2 }}>Economic Impact</Typography>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography>Jobs Created:</Typography>
                    <Typography sx={{ fontWeight: 600 }}>
                      {mockPerformance.jobs_created}
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography>Economic Value:</Typography>
                    <Typography sx={{ fontWeight: 600 }}>
                      {formatCurrency(mockPerformance.economic_value)}
                    </Typography>
                  </Box>
                  <Alert severity="success" sx={{ mt: 2 }}>
                    Your bidding activity has created {mockPerformance.jobs_created} jobs and contributed {formatCurrency(mockPerformance.economic_value)} to the economy!
                  </Alert>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>
      </Paper>
    </Box>
  );
}