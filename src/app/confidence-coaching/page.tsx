'use client';

import React, { useState } from 'react';
import { 
  Con<PERSON><PERSON>, 
  Box, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  <PERSON>ert,
  Button,
  Chip,
  LinearProgress,
  Avatar,
  Paper,
  Stack,
  Tabs,
  Tab
} from '@mui/material';
import { 
  EmojiEvents, 
  TrendingUp, 
  Psychology, 
  School,
  PlayArrow,
  CheckCircle,
  Star,
  Timeline,
  Lightbulb,
  Refresh
} from '@mui/icons-material';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`coaching-tabpanel-${index}`}
      aria-labelledby={`coaching-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

export default function ConfidenceCoachingPage() {
  const [tabValue, setTabValue] = useState(0);
  const [currentConfidence, setCurrentConfidence] = useState(78);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Mock confidence coaching data
  const coachingMetrics = {
    currentLevel: 78,
    weeklyGrowth: 12,
    completedSessions: 24,
    activePrograms: 3,
    achievements: 8,
    nextMilestone: 85
  };

  const coachingPrograms = [
    {
      id: 1,
      title: 'Bidding Confidence Mastery',
      description: 'Build unshakeable confidence in high-stakes bidding situations',
      progress: 67,
      sessions: 8,
      completedSessions: 5,
      difficulty: 'Intermediate',
      estimatedTime: '3 weeks'
    },
    {
      id: 2,
      title: 'Presentation Power',
      description: 'Master confident presentation and client interaction skills',
      progress: 45,
      sessions: 6,
      completedSessions: 3,
      difficulty: 'Advanced',
      estimatedTime: '2 weeks'
    },
    {
      id: 3,
      title: 'Decision Making Under Pressure',
      description: 'Develop confidence in making critical decisions quickly',
      progress: 89,
      sessions: 5,
      completedSessions: 4,
      difficulty: 'Expert',
      estimatedTime: '1 week'
    }
  ];

  const confidenceBuilders = [
    { technique: 'Visualization Exercises', impact: 85, frequency: 'Daily', duration: '10 min' },
    { technique: 'Success Story Review', impact: 78, frequency: 'Weekly', duration: '15 min' },
    { technique: 'Positive Affirmations', impact: 72, frequency: 'Daily', duration: '5 min' },
    { technique: 'Power Posing', impact: 68, frequency: 'Before meetings', duration: '2 min' },
    { technique: 'Achievement Journaling', impact: 65, frequency: 'Weekly', duration: '20 min' }
  ];

  const recentAchievements = [
    { id: 1, title: 'Confidence Streak', description: '7 days above 75% confidence', date: '2 days ago', points: 50 },
    { id: 2, title: 'Presentation Master', description: 'Completed advanced presentation module', date: '1 week ago', points: 100 },
    { id: 3, title: 'Quick Decision Maker', description: 'Made 5 confident decisions under pressure', date: '1 week ago', points: 75 },
    { id: 4, title: 'Growth Mindset', description: '15% confidence improvement this month', date: '2 weeks ago', points: 125 }
  ];

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'success';
      case 'Intermediate': return 'info';
      case 'Advanced': return 'warning';
      case 'Expert': return 'error';
      default: return 'default';
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold" color="primary.main">
          🏆 Confidence Coaching Hub
        </Typography>
        <Typography variant="h6" color="text.primary" sx={{ mb: 2 }}>
          Personalized confidence building and performance optimization for bidding excellence
        </Typography>
        
        <Alert severity="success" sx={{ mb: 3 }}>
          <strong>Coaching Active:</strong> Current confidence level at {currentConfidence}% with 12% weekly growth and 24 completed sessions.
        </Alert>
      </Box>

      {/* Confidence Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ width: 80, height: 80, mx: 'auto', mb: 2, bgcolor: 'success.main' }}>
                <EmojiEvents sx={{ fontSize: 40 }} />
              </Avatar>
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {coachingMetrics.currentLevel}%
              </Typography>
              <Typography variant="body1" color="text.secondary" gutterBottom>
                Current Confidence Level
              </Typography>
              <LinearProgress 
                variant="determinate" 
                value={coachingMetrics.currentLevel} 
                sx={{ mt: 2, height: 8, borderRadius: 4 }}
                color="success"
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={8}>
          <Grid container spacing={2}>
            <Grid item xs={6} md={3}>
              <Paper sx={{ p: 2, textAlign: 'center' }}>
                <TrendingUp sx={{ fontSize: 32, color: 'success.main', mb: 1 }} />
                <Typography variant="h5" color="text.primary">+{coachingMetrics.weeklyGrowth}%</Typography>
                <Typography variant="body2" color="text.secondary">Weekly Growth</Typography>
              </Paper>
            </Grid>
            <Grid item xs={6} md={3}>
              <Paper sx={{ p: 2, textAlign: 'center' }}>
                <School sx={{ fontSize: 32, color: 'info.main', mb: 1 }} />
                <Typography variant="h5" color="text.primary">{coachingMetrics.completedSessions}</Typography>
                <Typography variant="body2" color="text.secondary">Sessions Done</Typography>
              </Paper>
            </Grid>
            <Grid item xs={6} md={3}>
              <Paper sx={{ p: 2, textAlign: 'center' }}>
                <PlayArrow sx={{ fontSize: 32, color: 'warning.main', mb: 1 }} />
                <Typography variant="h5" color="text.primary">{coachingMetrics.activePrograms}</Typography>
                <Typography variant="body2" color="text.secondary">Active Programs</Typography>
              </Paper>
            </Grid>
            <Grid item xs={6} md={3}>
              <Paper sx={{ p: 2, textAlign: 'center' }}>
                <Star sx={{ fontSize: 32, color: 'secondary.main', mb: 1 }} />
                <Typography variant="h5" color="text.primary">{coachingMetrics.achievements}</Typography>
                <Typography variant="body2" color="text.secondary">Achievements</Typography>
              </Paper>
            </Grid>
          </Grid>
        </Grid>
      </Grid>

      {/* Coaching Content Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="coaching tabs">
          <Tab label="Active Programs" />
          <Tab label="Confidence Builders" />
          <Tab label="Achievements" />
          <Tab label="Progress Tracking" />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        <Grid container spacing={3}>
          {coachingPrograms.map((program) => (
            <Grid item xs={12} md={4} key={program.id}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h6" color="text.primary">
                      {program.title}
                    </Typography>
                    <Chip 
                      label={program.difficulty} 
                      color={getDifficultyColor(program.difficulty) as any}
                      size="small"
                    />
                  </Box>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {program.description}
                  </Typography>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.primary" gutterBottom>
                      Progress: {program.progress}%
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={program.progress} 
                      sx={{ height: 6, borderRadius: 3 }}
                    />
                  </Box>
                  <Grid container spacing={1} sx={{ mb: 2 }}>
                    <Grid item xs={6}>
                      <Typography variant="caption" color="text.secondary">Sessions</Typography>
                      <Typography variant="body2" color="text.primary">
                        {program.completedSessions}/{program.sessions}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="caption" color="text.secondary">Est. Time</Typography>
                      <Typography variant="body2" color="text.primary">
                        {program.estimatedTime}
                      </Typography>
                    </Grid>
                  </Grid>
                  <Button variant="contained" fullWidth startIcon={<PlayArrow />}>
                    Continue Program
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Grid container spacing={3}>
          {confidenceBuilders.map((builder, index) => (
            <Grid item xs={12} md={6} key={index}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h6" color="text.primary">
                      {builder.technique}
                    </Typography>
                    <Typography variant="h6" color="success.main" fontWeight="bold">
                      {builder.impact}%
                    </Typography>
                  </Box>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="caption" color="text.secondary">Frequency</Typography>
                      <Typography variant="body2" color="text.primary">
                        {builder.frequency}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="caption" color="text.secondary">Duration</Typography>
                      <Typography variant="body2" color="text.primary">
                        {builder.duration}
                      </Typography>
                    </Grid>
                  </Grid>
                  <LinearProgress 
                    variant="determinate" 
                    value={builder.impact} 
                    sx={{ mt: 2, height: 6, borderRadius: 3 }}
                    color="success"
                  />
                  <Button variant="outlined" fullWidth sx={{ mt: 2 }} startIcon={<Lightbulb />}>
                    Start Exercise
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <Grid container spacing={3}>
          {recentAchievements.map((achievement) => (
            <Grid item xs={12} md={6} key={achievement.id}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                    <Avatar sx={{ bgcolor: 'warning.main' }}>
                      <EmojiEvents />
                    </Avatar>
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="h6" color="text.primary">
                        {achievement.title}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {achievement.description}
                      </Typography>
                    </Box>
                    <Chip 
                      label={`+${achievement.points} pts`} 
                      color="warning" 
                      size="small"
                    />
                  </Box>
                  <Typography variant="caption" color="text.secondary">
                    Earned {achievement.date}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </TabPanel>

      <TabPanel value={tabValue} index={3}>
        <Alert severity="info" sx={{ mb: 3 }}>
          <strong>Progress Tracking:</strong> Your confidence has improved by 23% over the last month with consistent coaching engagement.
        </Alert>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom color="text.primary">
                  Confidence Trend
                </Typography>
                <Box sx={{ height: 250, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: 'rgba(255,255,255,0.05)' }}>
                  <Typography variant="body1" color="text.secondary">
                    📈 Confidence progression over time
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom color="text.primary">
                  Coaching Impact
                </Typography>
                <Box sx={{ height: 250, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: 'rgba(255,255,255,0.05)' }}>
                  <Typography variant="body1" color="text.secondary">
                    📊 Coaching session effectiveness
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>
    </Container>
  );
}
