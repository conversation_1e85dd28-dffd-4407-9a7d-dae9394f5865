'use client';

import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Box, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  Alert,
  Button,
  Chip,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Switch,
  FormControlLabel,
  Tabs,
  Tab
} from '@mui/material';
import { 
  SmartToy, 
  Psychology, 
  TrendingUp, 
  Speed,
  Memory,
  CloudQueue,
  Settings,
  Refresh,
  PlayArrow,
  Stop,
  Visibility
} from '@mui/icons-material';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`ai-tabpanel-${index}`}
      aria-labelledby={`ai-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

export default function AIEngineDashboard() {
  const [tabValue, setTabValue] = useState(0);
  const [aiEnginesActive, setAiEnginesActive] = useState(true);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Mock AI Engine data
  const aiMetrics = {
    totalModels: 15,
    activeModels: 12,
    processingSpeed: '2.3ms',
    accuracy: 94.7,
    uptime: 99.8,
    requestsToday: 15420
  };

  const aiEngines = [
    {
      id: 1,
      name: 'Psychological Profiling Engine',
      status: 'Active',
      accuracy: 96.2,
      speed: '1.8ms',
      load: 67,
      requests: 3420,
      description: 'Advanced behavioral analysis and archetype detection'
    },
    {
      id: 2,
      name: 'Market Intelligence Engine',
      status: 'Active',
      accuracy: 93.8,
      speed: '2.1ms',
      load: 54,
      requests: 2890,
      description: 'Competitive analysis and market trend prediction'
    },
    {
      id: 3,
      name: 'Bid Optimization Engine',
      status: 'Active',
      accuracy: 91.5,
      speed: '3.2ms',
      load: 78,
      requests: 4120,
      description: 'Automated bid pricing and strategy optimization'
    },
    {
      id: 4,
      name: 'Compliance Automation Engine',
      status: 'Active',
      accuracy: 98.1,
      speed: '1.5ms',
      load: 34,
      requests: 1890,
      description: 'Legal compliance checking and documentation'
    },
    {
      id: 5,
      name: 'Risk Assessment Engine',
      status: 'Maintenance',
      accuracy: 89.7,
      speed: '4.1ms',
      load: 12,
      requests: 567,
      description: 'Financial and operational risk analysis'
    }
  ];

  const recentPredictions = [
    { id: 1, type: 'Win Probability', tender: 'MUN/2024/IT/001', prediction: '87%', confidence: 'High', timestamp: '5 min ago' },
    { id: 2, type: 'Market Trend', sector: 'IT Services', prediction: 'Upward', confidence: 'Medium', timestamp: '12 min ago' },
    { id: 3, type: 'Risk Level', tender: 'PROV/2024/ROAD/045', prediction: 'Low', confidence: 'High', timestamp: '18 min ago' },
    { id: 4, type: 'Optimal Bid', tender: 'NAT/2024/SEC/012', prediction: 'R1.2M', confidence: 'High', timestamp: '25 min ago' }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'success';
      case 'Maintenance': return 'warning';
      case 'Error': return 'error';
      default: return 'default';
    }
  };

  const getLoadColor = (load: number) => {
    if (load >= 80) return 'error';
    if (load >= 60) return 'warning';
    return 'success';
  };

  const getConfidenceColor = (confidence: string) => {
    switch (confidence) {
      case 'High': return 'success';
      case 'Medium': return 'warning';
      case 'Low': return 'error';
      default: return 'default';
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
          🤖 AI Engine Dashboard
        </Typography>
        <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
          Central command center for all AI-powered intelligence systems and machine learning models
        </Typography>
        
        <Alert severity="success" sx={{ mb: 3 }}>
          <strong>AI Systems Online:</strong> 12 of 15 AI engines active with 94.7% average accuracy and 99.8% uptime.
        </Alert>
      </Box>

      {/* AI System Controls */}
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item>
          <FormControlLabel
            control={
              <Switch
                checked={aiEnginesActive}
                onChange={(e) => setAiEnginesActive(e.target.checked)}
                color="primary"
              />
            }
            label="AI Engines Active"
          />
        </Grid>
        <Grid item>
          <Button variant="contained" startIcon={<Refresh />} color="primary">
            Refresh Models
          </Button>
        </Grid>
        <Grid item>
          <Button variant="outlined" startIcon={<Settings />}>
            AI Settings
          </Button>
        </Grid>
        <Grid item>
          <Button variant="outlined" startIcon={<Visibility />}>
            View Logs
          </Button>
        </Grid>
      </Grid>

      {/* AI Metrics Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <SmartToy sx={{ fontSize: 32, color: 'primary.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold">{aiMetrics.totalModels}</Typography>
              <Typography variant="body2" color="text.secondary">AI Models</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <PlayArrow sx={{ fontSize: 32, color: 'success.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold">{aiMetrics.activeModels}</Typography>
              <Typography variant="body2" color="text.secondary">Active</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Speed sx={{ fontSize: 32, color: 'info.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold">{aiMetrics.processingSpeed}</Typography>
              <Typography variant="body2" color="text.secondary">Avg Speed</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <TrendingUp sx={{ fontSize: 32, color: 'warning.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold">{aiMetrics.accuracy}%</Typography>
              <Typography variant="body2" color="text.secondary">Accuracy</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <CloudQueue sx={{ fontSize: 32, color: 'success.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold">{aiMetrics.uptime}%</Typography>
              <Typography variant="body2" color="text.secondary">Uptime</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Memory sx={{ fontSize: 32, color: 'secondary.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold">{aiMetrics.requestsToday.toLocaleString()}</Typography>
              <Typography variant="body2" color="text.secondary">Requests Today</Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Detailed AI Engine Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="ai engine tabs">
          <Tab label="Active Engines" />
          <Tab label="Recent Predictions" />
          <Tab label="Performance Metrics" />
          <Tab label="Model Training" />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>AI Engine Status</Typography>
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Engine Name</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Accuracy</TableCell>
                    <TableCell>Speed</TableCell>
                    <TableCell>Load</TableCell>
                    <TableCell>Requests</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {aiEngines.map((engine) => (
                    <TableRow key={engine.id}>
                      <TableCell>
                        <Box>
                          <Typography variant="subtitle2" fontWeight="medium">
                            {engine.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {engine.description}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={engine.status} 
                          color={getStatusColor(engine.status) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="success.main" fontWeight="medium">
                          {engine.accuracy}%
                        </Typography>
                      </TableCell>
                      <TableCell>{engine.speed}</TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <LinearProgress 
                            variant="determinate" 
                            value={engine.load} 
                            sx={{ width: 60, height: 6 }}
                            color={getLoadColor(engine.load) as any}
                          />
                          <Typography variant="body2">{engine.load}%</Typography>
                        </Box>
                      </TableCell>
                      <TableCell>{engine.requests.toLocaleString()}</TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Button size="small" startIcon={<Settings />}>
                            Config
                          </Button>
                          <Button size="small" startIcon={<Visibility />}>
                            Monitor
                          </Button>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>Recent AI Predictions</Typography>
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Prediction Type</TableCell>
                    <TableCell>Target</TableCell>
                    <TableCell>Prediction</TableCell>
                    <TableCell>Confidence</TableCell>
                    <TableCell>Timestamp</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {recentPredictions.map((prediction) => (
                    <TableRow key={prediction.id}>
                      <TableCell>
                        <Typography variant="subtitle2" fontWeight="medium">
                          {prediction.type}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        {prediction.tender || prediction.sector}
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontWeight="medium" color="primary.main">
                          {prediction.prediction}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={prediction.confidence} 
                          color={getConfidenceColor(prediction.confidence) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>{prediction.timestamp}</TableCell>
                      <TableCell>
                        <Button size="small" startIcon={<Visibility />}>
                          Details
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <Alert severity="info" sx={{ mb: 3 }}>
          <strong>Performance Analytics:</strong> Real-time monitoring of AI engine performance and optimization metrics.
        </Alert>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>Processing Speed Trends</Typography>
                <Box sx={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: '#f5f5f5' }}>
                  <Typography variant="body1" color="text.secondary">
                    📊 AI processing speed analytics chart
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>Accuracy Metrics</Typography>
                <Box sx={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: '#f5f5f5' }}>
                  <Typography variant="body1" color="text.secondary">
                    📈 AI accuracy performance chart
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      <TabPanel value={tabValue} index={3}>
        <Alert severity="warning" sx={{ mb: 3 }}>
          <strong>Model Training:</strong> 3 models currently in training phase. Estimated completion in 2.5 hours.
        </Alert>
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom color="primary.main">
                  Enhanced Psychological Model
                </Typography>
                <LinearProgress variant="determinate" value={78} sx={{ mb: 2 }} />
                <Typography variant="body2" sx={{ mb: 2 }}>
                  Training Progress: 78% complete
                </Typography>
                <Button variant="outlined" size="small">
                  View Training
                </Button>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom color="warning.main">
                  Market Prediction v2.0
                </Typography>
                <LinearProgress variant="determinate" value={45} sx={{ mb: 2 }} />
                <Typography variant="body2" sx={{ mb: 2 }}>
                  Training Progress: 45% complete
                </Typography>
                <Button variant="outlined" size="small">
                  Monitor Progress
                </Button>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom color="success.main">
                  Risk Assessment Plus
                </Typography>
                <LinearProgress variant="determinate" value={92} sx={{ mb: 2 }} />
                <Typography variant="body2" sx={{ mb: 2 }}>
                  Training Progress: 92% complete
                </Typography>
                <Button variant="outlined" size="small">
                  Deploy Soon
                </Button>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>
    </Container>
  );
}
