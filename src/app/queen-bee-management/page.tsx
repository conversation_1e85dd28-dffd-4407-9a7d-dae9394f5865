'use client';

import React, { useState } from 'react';
import { 
  Container, 
  Box, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  Alert,
  Button,
  Chip,
  Avatar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  LinearProgress,
  Stack
} from '@mui/material';
import {
  Star as Crown,
  Assignment,
  Group,
  TrendingUp,
  Visibility,
  Settings,
  Refresh,
  PlayArrow,
  Pause,
  CheckCircle,
  Timer,
  Speed
} from '@mui/icons-material';

export default function QueenBeeManagementPage() {
  const [systemActive, setSystemActive] = useState(true);

  // Mock Queen Bee system data
  const systemMetrics = {
    totalBees: 156,
    activeBees: 134,
    tasksAssigned: 2847,
    completionRate: 94.7,
    efficiency: 87.3,
    avgResponseTime: '2.3s'
  };

  const beeWorkers = [
    {
      id: 1,
      name: 'Tender Scout Alpha',
      type: 'Discovery',
      status: 'Active',
      tasksCompleted: 847,
      efficiency: 96.8,
      currentTask: 'Scanning municipal tenders',
      lastActive: '2 min ago'
    },
    {
      id: 2,
      name: 'Compliance Checker Beta',
      type: 'Compliance',
      status: 'Active',
      tasksCompleted: 1247,
      efficiency: 98.2,
      currentTask: 'Validating B-BBEE certificates',
      lastActive: '1 min ago'
    },
    {
      id: 3,
      name: 'Bid Analyzer Gamma',
      type: 'Analysis',
      status: 'Active',
      tasksCompleted: 456,
      efficiency: 92.4,
      currentTask: 'Calculating win probabilities',
      lastActive: '30 sec ago'
    },
    {
      id: 4,
      name: 'Document Generator Delta',
      type: 'Generation',
      status: 'Paused',
      tasksCompleted: 234,
      efficiency: 89.7,
      currentTask: 'Template maintenance',
      lastActive: '15 min ago'
    },
    {
      id: 5,
      name: 'WhatsApp Messenger Epsilon',
      type: 'Communication',
      status: 'Active',
      tasksCompleted: 678,
      efficiency: 94.1,
      currentTask: 'Processing bid submissions',
      lastActive: '45 sec ago'
    }
  ];

  const taskQueues = [
    { queue: 'Tender Discovery', pending: 23, processing: 5, completed: 847, priority: 'High' },
    { queue: 'Compliance Checks', pending: 12, processing: 3, completed: 1247, priority: 'Critical' },
    { queue: 'Bid Analysis', pending: 8, processing: 2, completed: 456, priority: 'Medium' },
    { queue: 'Document Generation', pending: 15, processing: 0, completed: 234, priority: 'Low' },
    { queue: 'Communication Tasks', pending: 6, processing: 4, completed: 678, priority: 'High' }
  ];

  const recentAssignments = [
    { id: 1, task: 'Scan new municipal tenders', assignedTo: 'Tender Scout Alpha', status: 'Completed', duration: '1.2s' },
    { id: 2, task: 'Validate tax clearance', assignedTo: 'Compliance Checker Beta', status: 'In Progress', duration: '3.1s' },
    { id: 3, task: 'Calculate bid probability', assignedTo: 'Bid Analyzer Gamma', status: 'Completed', duration: '0.8s' },
    { id: 4, task: 'Send WhatsApp notification', assignedTo: 'WhatsApp Messenger Epsilon', status: 'Completed', duration: '0.5s' }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'success';
      case 'Paused': return 'warning';
      case 'Error': return 'error';
      case 'Completed': return 'success';
      case 'In Progress': return 'info';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Critical': return 'error';
      case 'High': return 'warning';
      case 'Medium': return 'info';
      case 'Low': return 'success';
      default: return 'default';
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold" color="primary.main">
          👑 Queen Bee Management System
        </Typography>
        <Typography variant="h6" color="text.primary" sx={{ mb: 2 }}>
          Intelligent task orchestration and bee worker assignment for optimal platform performance
        </Typography>
        
        <Alert severity="success" sx={{ mb: 3 }}>
          <strong>Hive Active:</strong> 134 of 156 bee workers operational with 94.7% task completion rate and 2.3s average response time.
        </Alert>
      </Box>

      {/* System Controls */}
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item>
          <Button 
            variant="contained" 
            startIcon={systemActive ? <Pause /> : <PlayArrow />} 
            color={systemActive ? "warning" : "success"}
            onClick={() => setSystemActive(!systemActive)}
          >
            {systemActive ? 'Pause Hive' : 'Activate Hive'}
          </Button>
        </Grid>
        <Grid item>
          <Button variant="outlined" startIcon={<Refresh />}>
            Refresh Status
          </Button>
        </Grid>
        <Grid item>
          <Button variant="outlined" startIcon={<Assignment />}>
            Task Queue
          </Button>
        </Grid>
        <Grid item>
          <Button variant="outlined" startIcon={<Settings />}>
            Hive Settings
          </Button>
        </Grid>
      </Grid>

      {/* System Metrics Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Crown sx={{ fontSize: 32, color: 'warning.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {systemMetrics.totalBees}
              </Typography>
              <Typography variant="body2" color="text.secondary">Total Bees</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Group sx={{ fontSize: 32, color: 'success.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {systemMetrics.activeBees}
              </Typography>
              <Typography variant="body2" color="text.secondary">Active Bees</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Assignment sx={{ fontSize: 32, color: 'primary.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {systemMetrics.tasksAssigned}
              </Typography>
              <Typography variant="body2" color="text.secondary">Tasks Assigned</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <CheckCircle sx={{ fontSize: 32, color: 'success.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {systemMetrics.completionRate}%
              </Typography>
              <Typography variant="body2" color="text.secondary">Completion Rate</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <TrendingUp sx={{ fontSize: 32, color: 'info.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {systemMetrics.efficiency}%
              </Typography>
              <Typography variant="body2" color="text.secondary">Efficiency</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Speed sx={{ fontSize: 32, color: 'secondary.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {systemMetrics.avgResponseTime}
              </Typography>
              <Typography variant="body2" color="text.secondary">Avg Response</Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Bee Workers Status */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom color="text.primary">
                Bee Worker Status
              </Typography>
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell><Typography color="text.primary" fontWeight="bold">Bee Worker</Typography></TableCell>
                      <TableCell><Typography color="text.primary" fontWeight="bold">Type</Typography></TableCell>
                      <TableCell><Typography color="text.primary" fontWeight="bold">Status</Typography></TableCell>
                      <TableCell><Typography color="text.primary" fontWeight="bold">Efficiency</Typography></TableCell>
                      <TableCell><Typography color="text.primary" fontWeight="bold">Tasks</Typography></TableCell>
                      <TableCell><Typography color="text.primary" fontWeight="bold">Current Task</Typography></TableCell>
                      <TableCell><Typography color="text.primary" fontWeight="bold">Actions</Typography></TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {beeWorkers.map((bee) => (
                      <TableRow key={bee.id}>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            <Avatar sx={{ bgcolor: 'warning.main' }}>
                              🐝
                            </Avatar>
                            <Box>
                              <Typography variant="subtitle2" fontWeight="medium" color="text.primary">
                                {bee.name}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {bee.lastActive}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip label={bee.type} variant="outlined" size="small" />
                        </TableCell>
                        <TableCell>
                          <Chip 
                            label={bee.status} 
                            color={getStatusColor(bee.status) as any}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <LinearProgress 
                              variant="determinate" 
                              value={bee.efficiency} 
                              sx={{ width: 60, height: 6 }}
                              color="success"
                            />
                            <Typography variant="body2" color="text.primary">
                              {bee.efficiency}%
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" color="text.primary">
                            {bee.tasksCompleted}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" color="text.secondary">
                            {bee.currentTask}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Button size="small" startIcon={<Visibility />}>
                            Monitor
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom color="text.primary">
                Task Queues
              </Typography>
              <Stack spacing={2}>
                {taskQueues.map((queue, index) => (
                  <Box key={index} sx={{ p: 2, border: '1px solid #333', borderRadius: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="subtitle2" color="text.primary">
                        {queue.queue}
                      </Typography>
                      <Chip 
                        label={queue.priority} 
                        color={getPriorityColor(queue.priority) as any}
                        size="small"
                      />
                    </Box>
                    <Grid container spacing={1} sx={{ mb: 1 }}>
                      <Grid item xs={4}>
                        <Typography variant="caption" color="text.secondary">Pending</Typography>
                        <Typography variant="body2" color="warning.main">{queue.pending}</Typography>
                      </Grid>
                      <Grid item xs={4}>
                        <Typography variant="caption" color="text.secondary">Processing</Typography>
                        <Typography variant="body2" color="info.main">{queue.processing}</Typography>
                      </Grid>
                      <Grid item xs={4}>
                        <Typography variant="caption" color="text.secondary">Completed</Typography>
                        <Typography variant="body2" color="success.main">{queue.completed}</Typography>
                      </Grid>
                    </Grid>
                  </Box>
                ))}
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Recent Task Assignments */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom color="text.primary">
            Recent Task Assignments
          </Typography>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell><Typography color="text.primary" fontWeight="bold">Task</Typography></TableCell>
                  <TableCell><Typography color="text.primary" fontWeight="bold">Assigned To</Typography></TableCell>
                  <TableCell><Typography color="text.primary" fontWeight="bold">Status</Typography></TableCell>
                  <TableCell><Typography color="text.primary" fontWeight="bold">Duration</Typography></TableCell>
                  <TableCell><Typography color="text.primary" fontWeight="bold">Actions</Typography></TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {recentAssignments.map((assignment) => (
                  <TableRow key={assignment.id}>
                    <TableCell>
                      <Typography variant="body2" color="text.primary">
                        {assignment.task}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="text.secondary">
                        {assignment.assignedTo}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={assignment.status} 
                        color={getStatusColor(assignment.status) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="text.primary">
                        {assignment.duration}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Button size="small" startIcon={<Visibility />}>
                        Details
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Performance Analytics */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom color="text.primary">
                Hive Performance Trends
              </Typography>
              <Box sx={{ height: 250, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: 'rgba(255,255,255,0.05)' }}>
                <Typography variant="body1" color="text.secondary">
                  📈 Hive performance analytics over time
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom color="text.primary">
                Task Distribution
              </Typography>
              <Box sx={{ height: 250, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: 'rgba(255,255,255,0.05)' }}>
                <Typography variant="body1" color="text.secondary">
                  📊 Task distribution across bee workers
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
}
