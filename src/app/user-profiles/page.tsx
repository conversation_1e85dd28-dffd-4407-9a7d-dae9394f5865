'use client';

import React from 'react';
import { Container, Typo<PERSON>, Card, CardContent, Grid, Button, Box, Avatar, Chip, Table, TableBody, TableCell, TableHead, TableRow } from '@mui/material';
import { Person, Edit, Delete, Add } from '@mui/icons-material';

const users = [
  { name: "<PERSON>", role: "Admin", status: "Active", lastLogin: "2024-01-11" },
  { name: "<PERSON>", role: "Manager", status: "Active", lastLogin: "2024-01-10" },
  { name: "<PERSON>", role: "User", status: "Inactive", lastLogin: "2024-01-05" }
];

export default function UserProfilesPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
            User Profiles
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Manage user accounts and profiles
          </Typography>
        </Box>
        <Button variant="contained" startIcon={<Add />}>
          Add User
        </Button>
      </Box>
      
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" fontWeight="bold" color="primary">
                25
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Users
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" fontWeight="bold" color="success.main">
                22
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Active Users
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>User Management</Typography>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Role</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Last Login</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {users.map((user, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Avatar><Person /></Avatar>
                      {user.name}
                    </Box>
                  </TableCell>
                  <TableCell>{user.role}</TableCell>
                  <TableCell>
                    <Chip 
                      label={user.status}
                      color={user.status === 'Active' ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>{user.lastLogin}</TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Button size="small" startIcon={<Edit />}>Edit</Button>
                      <Button size="small" color="error" startIcon={<Delete />}>Delete</Button>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </Container>
  );
}
