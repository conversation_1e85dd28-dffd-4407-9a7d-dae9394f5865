'use client';

import React, { useState } from 'react';
import {
  Box,
  Container,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Tabs,
  Tab,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Stack,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  LinearProgress,
  IconButton,
  Tooltip,
  Alert
} from '@mui/material';
import {
  MonetizationOn,
  TrendingUp,
  TrendingDown,
  Assessment,
  PieChart,
  BarChart,
  Timeline,
  Download,
  Share,
  Refresh,
  AccountBalance,
  Receipt,
  Savings,
  CreditCard,
  Psychology,
  Target
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, ResponsiveContainer, AreaChart, Area, PieChart as RechartsPieChart, Cell, BarChart as RechartsBarChart, Bar } from 'recharts';

interface FinancialData {
  period: string;
  revenue: number;
  expenses: number;
  profit: number;
  bidsWon: number;
  avgBidValue: number;
  cashFlow: number;
}

const mockFinancialData: FinancialData[] = [
  { period: 'Jan', revenue: 2400000, expenses: 1800000, profit: 600000, bidsWon: 4, avgBidValue: 600000, cashFlow: 500000 },
  { period: 'Feb', revenue: 3200000, expenses: 2100000, profit: 1100000, bidsWon: 6, avgBidValue: 533333, cashFlow: 800000 },
  { period: 'Mar', revenue: 4100000, expenses: 2600000, profit: 1500000, bidsWon: 8, avgBidValue: 512500, cashFlow: 1200000 },
  { period: 'Apr', revenue: 4800000, expenses: 2900000, profit: 1900000, bidsWon: 9, avgBidValue: 533333, cashFlow: 1600000 },
  { period: 'May', revenue: 6200000, expenses: 3400000, profit: 2800000, bidsWon: 12, avgBidValue: 516667, cashFlow: 2200000 },
  { period: 'Jun', revenue: 7500000, expenses: 3800000, profit: 3700000, bidsWon: 15, avgBidValue: 500000, cashFlow: 3100000 }
];

const expenseCategories = [
  { name: 'Operations', value: 35, amount: 1330000, color: '#8884d8' },
  { name: 'Marketing', value: 20, amount: 760000, color: '#82ca9d' },
  { name: 'Personnel', value: 25, amount: 950000, color: '#ffc658' },
  { name: 'Technology', value: 12, amount: 456000, color: '#ff7300' },
  { name: 'Other', value: 8, amount: 304000, color: '#00ff00' }
];

export default function FinancialDashboardPage() {
  const [activeTab, setActiveTab] = useState(0);
  const [timeRange, setTimeRange] = useState('6months');

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const calculateGrowth = (current: number, previous: number) => {
    return ((current - previous) / previous * 100).toFixed(1);
  };

  const currentMonth = mockFinancialData[mockFinancialData.length - 1];
  const previousMonth = mockFinancialData[mockFinancialData.length - 2];
  const totalRevenue = mockFinancialData.reduce((sum, item) => sum + item.revenue, 0);
  const totalProfit = mockFinancialData.reduce((sum, item) => sum + item.profit, 0);
  const profitMargin = ((totalProfit / totalRevenue) * 100).toFixed(1);

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
            💰 Financial Dashboard
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Revenue analytics, profit tracking, and financial insights for your bidding business
          </Typography>
        </Box>
        
        <Stack direction="row" spacing={2}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Time Range</InputLabel>
            <Select
              value={timeRange}
              label="Time Range"
              onChange={(e) => setTimeRange(e.target.value)}
            >
              <MenuItem value="1month">1 Month</MenuItem>
              <MenuItem value="3months">3 Months</MenuItem>
              <MenuItem value="6months">6 Months</MenuItem>
              <MenuItem value="1year">1 Year</MenuItem>
              <MenuItem value="all">All Time</MenuItem>
            </Select>
          </FormControl>
          
          <Button variant="outlined" startIcon={<Download />}>
            Export Report
          </Button>
          
          <Button variant="contained" startIcon={<Refresh />}>
            Refresh Data
          </Button>
        </Stack>
      </Box>

      {/* Key Financial Metrics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" color="success.main" gutterBottom>
                    {formatCurrency(totalRevenue)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Revenue
                  </Typography>
                </Box>
                <MonetizationOn color="success" sx={{ fontSize: 40 }} />
              </Box>
              <Typography variant="caption" color="success.main">
                +{calculateGrowth(currentMonth.revenue, previousMonth.revenue)}% from last month
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" color="primary.main" gutterBottom>
                    {formatCurrency(totalProfit)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Profit
                  </Typography>
                </Box>
                <TrendingUp color="primary" sx={{ fontSize: 40 }} />
              </Box>
              <Typography variant="caption" color="primary.main">
                {profitMargin}% profit margin
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" color="info.main" gutterBottom>
                    {formatCurrency(currentMonth.avgBidValue)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Avg Bid Value
                  </Typography>
                </Box>
                <Target color="info" sx={{ fontSize: 40 }} />
              </Box>
              <Typography variant="caption" color="info.main">
                {currentMonth.bidsWon} bids won this month
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" color="warning.main" gutterBottom>
                    {formatCurrency(currentMonth.cashFlow)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Cash Flow
                  </Typography>
                </Box>
                <AccountBalance color="warning" sx={{ fontSize: 40 }} />
              </Box>
              <Typography variant="caption" color="warning.main">
                +{calculateGrowth(currentMonth.cashFlow, previousMonth.cashFlow)}% growth
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Psychological Financial Insights */}
      <Alert severity="info" sx={{ mb: 4 }}>
        <Typography variant="body2">
          <strong>🧠 Financial Psychology Insight:</strong> Your revenue growth correlates with reduced stress levels. 
          Consider maintaining current psychological optimization strategies for sustained financial performance.
        </Typography>
      </Alert>

      {/* Financial Analytics Tabs */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={activeTab} onChange={handleTabChange}>
            <Tab label="Revenue Trends" icon={<TrendingUp />} />
            <Tab label="Expense Analysis" icon={<PieChart />} />
            <Tab label="Profit & Loss" icon={<Assessment />} />
            <Tab label="Cash Flow" icon={<AccountBalance />} />
          </Tabs>
        </Box>

        <CardContent>
          {/* Revenue Trends Tab */}
          {activeTab === 0 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Revenue & Profit Trends
              </Typography>
              
              <Box sx={{ height: 400, mt: 2 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={mockFinancialData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="period" />
                    <YAxis />
                    <Area 
                      type="monotone" 
                      dataKey="revenue" 
                      stackId="1"
                      stroke="#8884d8" 
                      fill="#8884d8" 
                      fillOpacity={0.6}
                      name="Revenue"
                    />
                    <Area 
                      type="monotone" 
                      dataKey="profit" 
                      stackId="2"
                      stroke="#82ca9d" 
                      fill="#82ca9d" 
                      fillOpacity={0.6}
                      name="Profit"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </Box>
              
              <Grid container spacing={3} sx={{ mt: 3 }}>
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="subtitle1" gutterBottom>
                      Revenue Growth Analysis
                    </Typography>
                    <Stack spacing={2}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body2">6-Month Growth:</Typography>
                        <Typography variant="body2" fontWeight="bold" color="success.main">
                          +{calculateGrowth(currentMonth.revenue, mockFinancialData[0].revenue)}%
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body2">Monthly Average:</Typography>
                        <Typography variant="body2" fontWeight="bold">
                          {formatCurrency(totalRevenue / mockFinancialData.length)}
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body2">Best Month:</Typography>
                        <Typography variant="body2" fontWeight="bold" color="primary.main">
                          {formatCurrency(Math.max(...mockFinancialData.map(d => d.revenue)))}
                        </Typography>
                      </Box>
                    </Stack>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="subtitle1" gutterBottom>
                      Bidding Performance Impact
                    </Typography>
                    <Stack spacing={2}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body2">Total Bids Won:</Typography>
                        <Typography variant="body2" fontWeight="bold">
                          {mockFinancialData.reduce((sum, item) => sum + item.bidsWon, 0)}
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body2">Revenue per Bid:</Typography>
                        <Typography variant="body2" fontWeight="bold" color="success.main">
                          {formatCurrency(totalRevenue / mockFinancialData.reduce((sum, item) => sum + item.bidsWon, 0))}
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body2">Win Rate Impact:</Typography>
                        <Typography variant="body2" fontWeight="bold" color="primary.main">
                          +23% revenue correlation
                        </Typography>
                      </Box>
                    </Stack>
                  </Paper>
                </Grid>
              </Grid>
            </Box>
          )}

          {/* Expense Analysis Tab */}
          {activeTab === 1 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Expense Breakdown & Analysis
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Box sx={{ height: 300 }}>
                    <ResponsiveContainer width="100%" height="100%">
                      <RechartsPieChart>
                        <RechartsPieChart data={expenseCategories} cx="50%" cy="50%" outerRadius={80}>
                          {expenseCategories.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </RechartsPieChart>
                      </RechartsPieChart>
                    </ResponsiveContainer>
                  </Box>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <TableContainer component={Paper}>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Category</TableCell>
                          <TableCell align="right">Amount</TableCell>
                          <TableCell align="right">Percentage</TableCell>
                          <TableCell align="right">Trend</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {expenseCategories.map((category) => (
                          <TableRow key={category.name}>
                            <TableCell>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Box 
                                  sx={{ 
                                    width: 12, 
                                    height: 12, 
                                    backgroundColor: category.color,
                                    borderRadius: '50%'
                                  }} 
                                />
                                {category.name}
                              </Box>
                            </TableCell>
                            <TableCell align="right">
                              {formatCurrency(category.amount)}
                            </TableCell>
                            <TableCell align="right">{category.value}%</TableCell>
                            <TableCell align="right">
                              <Chip 
                                label={Math.random() > 0.5 ? '+5%' : '-2%'}
                                color={Math.random() > 0.5 ? 'success' : 'error'}
                                size="small"
                              />
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Grid>
              </Grid>
            </Box>
          )}

          {/* Profit & Loss Tab */}
          {activeTab === 2 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Profit & Loss Statement
              </Typography>
              
              <Box sx={{ height: 400, mt: 2 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsBarChart data={mockFinancialData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="period" />
                    <YAxis />
                    <Bar dataKey="revenue" fill="#8884d8" name="Revenue" />
                    <Bar dataKey="expenses" fill="#82ca9d" name="Expenses" />
                    <Bar dataKey="profit" fill="#ffc658" name="Profit" />
                  </RechartsBarChart>
                </ResponsiveContainer>
              </Box>
              
              <Grid container spacing={3} sx={{ mt: 3 }}>
                <Grid item xs={12}>
                  <TableContainer component={Paper}>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Period</TableCell>
                          <TableCell align="right">Revenue</TableCell>
                          <TableCell align="right">Expenses</TableCell>
                          <TableCell align="right">Profit</TableCell>
                          <TableCell align="right">Margin</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {mockFinancialData.map((row) => (
                          <TableRow key={row.period}>
                            <TableCell>{row.period}</TableCell>
                            <TableCell align="right" sx={{ color: 'success.main', fontWeight: 'bold' }}>
                              {formatCurrency(row.revenue)}
                            </TableCell>
                            <TableCell align="right" sx={{ color: 'error.main' }}>
                              {formatCurrency(row.expenses)}
                            </TableCell>
                            <TableCell align="right" sx={{ color: 'primary.main', fontWeight: 'bold' }}>
                              {formatCurrency(row.profit)}
                            </TableCell>
                            <TableCell align="right">
                              <Chip 
                                label={`${((row.profit / row.revenue) * 100).toFixed(1)}%`}
                                color="primary"
                                size="small"
                              />
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Grid>
              </Grid>
            </Box>
          )}

          {/* Cash Flow Tab */}
          {activeTab === 3 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Cash Flow Analysis
              </Typography>
              
              <Box sx={{ height: 400, mt: 2 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={mockFinancialData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="period" />
                    <YAxis />
                    <Line 
                      type="monotone" 
                      dataKey="cashFlow" 
                      stroke="#ff7300" 
                      strokeWidth={3}
                      name="Cash Flow"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </Box>
              
              <Grid container spacing={3} sx={{ mt: 3 }}>
                <Grid item xs={12} md={4}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color="success.main" gutterBottom>
                      {formatCurrency(currentMonth.cashFlow)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Current Cash Flow
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={4}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color="primary.main" gutterBottom>
                      {formatCurrency(mockFinancialData.reduce((sum, item) => sum + item.cashFlow, 0) / mockFinancialData.length)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Average Monthly
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={4}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color="warning.main" gutterBottom>
                      +{calculateGrowth(currentMonth.cashFlow, mockFinancialData[0].cashFlow)}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      6-Month Growth
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
            </Box>
          )}
        </CardContent>
      </Card>
    </Container>
  );
}
