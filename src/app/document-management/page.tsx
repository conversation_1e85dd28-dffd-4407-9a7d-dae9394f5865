'use client';

import React, { useState } from 'react';
import {
  <PERSON>,
  Container,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Grid,
  TextField,
  Chip,
  Stack,
  Alert,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  LinearProgress,
  Menu,
  MenuItem
} from '@mui/material';
import {
  CloudUpload,
  Description,
  PictureAsPdf,
  Image,
  VideoFile,
  AudioFile,
  Archive,
  Download,
  Share,
  Delete,
  Edit,
  Visibility,
  MoreVert,
  FolderOpen,
  CreateNewFolder,
  Search,
  FilterList,
  Sort,
  Security,
  Verified,
  Schedule
} from '@mui/icons-material';

interface Document {
  id: string;
  name: string;
  type: 'pdf' | 'image' | 'video' | 'audio' | 'archive' | 'other';
  size: number;
  uploadDate: string;
  category: 'tender_docs' | 'compliance' | 'technical' | 'commercial' | 'supporting';
  status: 'uploaded' | 'processing' | 'verified' | 'rejected';
  tags: string[];
  tenderId?: string;
  tenderTitle?: string;
  securityLevel: 'public' | 'internal' | 'confidential' | 'restricted';
  version: number;
  lastModified: string;
  uploadedBy: string;
}

const mockDocuments: Document[] = [
  {
    id: 'doc-001',
    name: 'Municipal Infrastructure Tender Specification.pdf',
    type: 'pdf',
    size: 2456789,
    uploadDate: '2024-01-15T10:30:00Z',
    category: 'tender_docs',
    status: 'verified',
    tags: ['infrastructure', 'municipal', 'specifications'],
    tenderId: 'tender-001',
    tenderTitle: 'Municipal Infrastructure Development',
    securityLevel: 'internal',
    version: 1,
    lastModified: '2024-01-15T10:30:00Z',
    uploadedBy: 'John Smith'
  },
  {
    id: 'doc-002',
    name: 'B-BBEE Certificate Level 4.pdf',
    type: 'pdf',
    size: 1234567,
    uploadDate: '2024-01-14T14:20:00Z',
    category: 'compliance',
    status: 'verified',
    tags: ['bbbee', 'compliance', 'certificate'],
    securityLevel: 'confidential',
    version: 2,
    lastModified: '2024-01-14T14:20:00Z',
    uploadedBy: 'Sarah Johnson'
  },
  {
    id: 'doc-003',
    name: 'Technical Proposal Draft v3.docx',
    type: 'other',
    size: 987654,
    uploadDate: '2024-01-16T09:15:00Z',
    category: 'technical',
    status: 'processing',
    tags: ['proposal', 'technical', 'draft'],
    tenderId: 'tender-001',
    tenderTitle: 'Municipal Infrastructure Development',
    securityLevel: 'internal',
    version: 3,
    lastModified: '2024-01-16T09:15:00Z',
    uploadedBy: 'Mike Wilson'
  }
];

export default function DocumentManagementPage() {
  const [activeTab, setActiveTab] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedDoc, setSelectedDoc] = useState<Document | null>(null);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'pdf': return <PictureAsPdf color="error" />;
      case 'image': return <Image color="primary" />;
      case 'video': return <VideoFile color="secondary" />;
      case 'audio': return <AudioFile color="warning" />;
      case 'archive': return <Archive color="info" />;
      default: return <Description color="action" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'verified': return 'success';
      case 'processing': return 'warning';
      case 'rejected': return 'error';
      default: return 'default';
    }
  };

  const getSecurityColor = (level: string) => {
    switch (level) {
      case 'restricted': return 'error';
      case 'confidential': return 'warning';
      case 'internal': return 'info';
      default: return 'default';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, doc: Document) => {
    setAnchorEl(event.currentTarget);
    setSelectedDoc(doc);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedDoc(null);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
            📋 Document Management
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Secure document storage, version control, and AI-powered organization
          </Typography>
        </Box>
        
        <Stack direction="row" spacing={2}>
          <Button
            variant="outlined"
            startIcon={<CreateNewFolder />}
          >
            New Folder
          </Button>
          <Button
            variant="contained"
            startIcon={<CloudUpload />}
            onClick={() => setUploadDialogOpen(true)}
          >
            Upload Documents
          </Button>
        </Stack>
      </Box>

      {/* Search and Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Search documents"
                variant="outlined"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                select
                label="Category"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                SelectProps={{ native: true }}
              >
                <option value="all">All Categories</option>
                <option value="tender_docs">Tender Documents</option>
                <option value="compliance">Compliance</option>
                <option value="technical">Technical</option>
                <option value="commercial">Commercial</option>
                <option value="supporting">Supporting</option>
              </TextField>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                variant="outlined"
                fullWidth
                startIcon={<FilterList />}
                sx={{ height: 56 }}
              >
                More Filters
              </Button>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                variant="outlined"
                fullWidth
                startIcon={<Sort />}
                sx={{ height: 56 }}
              >
                Sort By
              </Button>
            </Grid>
            <Grid item xs={12} md={1}>
              <Tooltip title="Security Overview">
                <IconButton color="primary" sx={{ height: 56, width: 56 }}>
                  <Security />
                </IconButton>
              </Tooltip>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Document Tabs */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={activeTab} onChange={handleTabChange}>
            <Tab label="All Documents" />
            <Tab label="Recent" />
            <Tab label="Shared" />
            <Tab label="Favorites" />
            <Tab label="Trash" />
          </Tabs>
        </Box>

        <CardContent>
          {/* Storage Usage */}
          <Alert severity="info" sx={{ mb: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
              <Typography variant="body2">
                <strong>Storage Usage:</strong> 2.4 GB of 10 GB used
              </Typography>
              <LinearProgress 
                variant="determinate" 
                value={24} 
                sx={{ width: 200, ml: 2 }}
              />
            </Box>
          </Alert>

          {/* Document List */}
          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Document</TableCell>
                  <TableCell>Category</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Security</TableCell>
                  <TableCell>Size</TableCell>
                  <TableCell>Modified</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {mockDocuments.map((doc) => (
                  <TableRow key={doc.id} hover>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        {getFileIcon(doc.type)}
                        <Box>
                          <Typography variant="subtitle2" gutterBottom>
                            {doc.name}
                          </Typography>
                          {doc.tenderTitle && (
                            <Typography variant="caption" color="text.secondary">
                              {doc.tenderTitle}
                            </Typography>
                          )}
                          <Box sx={{ mt: 0.5 }}>
                            {doc.tags.map((tag) => (
                              <Chip 
                                key={tag} 
                                label={tag} 
                                size="small" 
                                variant="outlined" 
                                sx={{ mr: 0.5, mb: 0.5 }}
                              />
                            ))}
                          </Box>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={doc.category.replace('_', ' ')} 
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={doc.status}
                        color={getStatusColor(doc.status)}
                        size="small"
                        icon={doc.status === 'verified' ? <Verified /> : undefined}
                      />
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={doc.securityLevel}
                        color={getSecurityColor(doc.securityLevel)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {formatFileSize(doc.size)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Schedule fontSize="small" color="action" />
                        <Typography variant="body2">
                          {formatDate(doc.lastModified)}
                        </Typography>
                      </Box>
                      <Typography variant="caption" color="text.secondary">
                        by {doc.uploadedBy}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Stack direction="row" spacing={1}>
                        <Tooltip title="View">
                          <IconButton size="small">
                            <Visibility />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Download">
                          <IconButton size="small">
                            <Download />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Share">
                          <IconButton size="small">
                            <Share />
                          </IconButton>
                        </Tooltip>
                        <IconButton 
                          size="small"
                          onClick={(e) => handleMenuClick(e, doc)}
                        >
                          <MoreVert />
                        </IconButton>
                      </Stack>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Upload Dialog */}
      <Dialog 
        open={uploadDialogOpen} 
        onClose={() => setUploadDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Upload Documents</DialogTitle>
        <DialogContent>
          <Box sx={{ py: 2 }}>
            <Paper 
              sx={{ 
                p: 4, 
                textAlign: 'center', 
                border: '2px dashed',
                borderColor: 'primary.main',
                bgcolor: 'primary.50'
              }}
            >
              <CloudUpload sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                Drag and drop files here
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                or click to browse files
              </Typography>
              <Button variant="contained" sx={{ mt: 2 }}>
                Choose Files
              </Button>
            </Paper>
            
            <Grid container spacing={2} sx={{ mt: 3 }}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  select
                  label="Category"
                  defaultValue="tender_docs"
                  SelectProps={{ native: true }}
                >
                  <option value="tender_docs">Tender Documents</option>
                  <option value="compliance">Compliance</option>
                  <option value="technical">Technical</option>
                  <option value="commercial">Commercial</option>
                  <option value="supporting">Supporting</option>
                </TextField>
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  select
                  label="Security Level"
                  defaultValue="internal"
                  SelectProps={{ native: true }}
                >
                  <option value="public">Public</option>
                  <option value="internal">Internal</option>
                  <option value="confidential">Confidential</option>
                  <option value="restricted">Restricted</option>
                </TextField>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Tags (comma separated)"
                  placeholder="e.g., infrastructure, municipal, specifications"
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUploadDialogOpen(false)}>
            Cancel
          </Button>
          <Button variant="contained">
            Upload
          </Button>
        </DialogActions>
      </Dialog>

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleMenuClose}>
          <Edit sx={{ mr: 1 }} /> Rename
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <Share sx={{ mr: 1 }} /> Share
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <Download sx={{ mr: 1 }} /> Download
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <FolderOpen sx={{ mr: 1 }} /> Move to Folder
        </MenuItem>
        <MenuItem onClick={handleMenuClose} sx={{ color: 'error.main' }}>
          <Delete sx={{ mr: 1 }} /> Delete
        </MenuItem>
      </Menu>
    </Container>
  );
}
