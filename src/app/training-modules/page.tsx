'use client';

import React from 'react';
import { Container, Typo<PERSON>, Card, CardContent, Grid, Button, Box, Avatar, Chip, LinearProgress } from '@mui/material';
import { School, PlayArrow, CheckCircle, Star } from '@mui/icons-material';

const modules = [
  { name: "Tender Basics", progress: 100, duration: "2 hours", difficulty: "Beginner" },
  { name: "Advanced Bidding", progress: 75, duration: "4 hours", difficulty: "Advanced" },
  { name: "Legal Compliance", progress: 0, duration: "3 hours", difficulty: "Intermediate" }
];

export default function TrainingModulesPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
        Training Modules
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        Comprehensive training programs for tender management
      </Typography>
      
      <Grid container spacing={3} sx={{ mt: 2 }}>
        {modules.map((module, index) => (
          <Grid item xs={12} md={4} key={index}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <Avatar sx={{ bgcolor: 'primary.main' }}>
                    <School />
                  </Avatar>
                  <Box>
                    <Typography variant="h6">{module.name}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {module.duration}
                    </Typography>
                  </Box>
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Chip 
                    label={module.difficulty}
                    color={module.difficulty === 'Beginner' ? 'success' : module.difficulty === 'Intermediate' ? 'warning' : 'error'}
                    size="small"
                  />
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" gutterBottom>
                    Progress: {module.progress}%
                  </Typography>
                  <LinearProgress variant="determinate" value={module.progress} />
                </Box>
                <Button 
                  variant={module.progress === 0 ? 'contained' : 'outlined'} 
                  startIcon={module.progress === 100 ? <CheckCircle /> : <PlayArrow />}
                  fullWidth
                >
                  {module.progress === 100 ? 'Completed' : module.progress > 0 ? 'Continue' : 'Start'}
                </Button>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Container>
  );
}
