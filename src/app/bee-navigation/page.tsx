'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Chip,
  LinearProgress,
  Stack,
  IconButton,
  Alert,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField
} from '@mui/material';
import {
  Navigation,
  LocationOn,
  MyLocation,
  DirectionsCar,
  DirectionsWalk,
  Timer,
  CheckCircle,
  Camera,
  Upload,
  Phone,
  Warning,
  Info,
  Speed,
  Traffic,
  LocalGasStation,
  Restaurant
} from '@mui/icons-material';

interface TaskLocation {
  id: string;
  title: string;
  address: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  type: 'document_collection' | 'site_visit' | 'briefing_attendance';
  deadline: string;
  estimatedDuration: string;
  specialInstructions?: string;
  contactPerson?: {
    name: string;
    phone: string;
  };
  checkInRequired: boolean;
  documentsRequired: string[];
}

interface NavigationInfo {
  distance: string;
  duration: string;
  traffic: 'light' | 'moderate' | 'heavy';
  route: 'fastest' | 'shortest' | 'avoid_tolls';
  estimatedArrival: string;
}

export default function BeeNavigationPage() {
  const [currentLocation, setCurrentLocation] = useState({
    latitude: -26.1076,
    longitude: 28.0567,
    address: 'Sandton, Johannesburg'
  });

  const [selectedTask, setSelectedTask] = useState<TaskLocation>({
    id: 'task-001',
    title: 'Municipal Tender Document Collection',
    address: 'City of Johannesburg Offices, 158 Loveday Street, Braamfontein, Johannesburg, 2001',
    coordinates: {
      latitude: -26.1929,
      longitude: 28.0344
    },
    type: 'document_collection',
    deadline: '2024-01-15T16:00:00',
    estimatedDuration: '2 hours',
    specialInstructions: 'Enter through main entrance. Ask for Municipal Procurement Department. Bring ID and authorization letter.',
    contactPerson: {
      name: 'Ms. Nomsa Dlamini',
      phone: '+27 11 407 7911'
    },
    checkInRequired: true,
    documentsRequired: ['ID Document', 'BidBeez Authorization Letter', 'Task Assignment Form']
  });

  const [navigationInfo, setNavigationInfo] = useState<NavigationInfo>({
    distance: '12.5 km',
    duration: '28 minutes',
    traffic: 'moderate',
    route: 'fastest',
    estimatedArrival: '14:45'
  });

  const [isNavigating, setIsNavigating] = useState(false);
  const [hasCheckedIn, setHasCheckedIn] = useState(false);
  const [checkInDialogOpen, setCheckInDialogOpen] = useState(false);
  const [checkOutDialogOpen, setCheckOutDialogOpen] = useState(false);
  const [taskNotes, setTaskNotes] = useState('');

  const getTrafficColor = (traffic: string) => {
    switch (traffic) {
      case 'light': return 'success';
      case 'moderate': return 'warning';
      case 'heavy': return 'error';
      default: return 'default';
    }
  };

  const handleStartNavigation = () => {
    setIsNavigating(true);
    // In a real app, this would open the device's navigation app
    const googleMapsUrl = `https://www.google.com/maps/dir/${currentLocation.latitude},${currentLocation.longitude}/${selectedTask.coordinates.latitude},${selectedTask.coordinates.longitude}`;
    window.open(googleMapsUrl, '_blank');
  };

  const handleCheckIn = () => {
    setHasCheckedIn(true);
    setCheckInDialogOpen(false);
    // In a real app, this would record the check-in time and location
  };

  const handleCheckOut = () => {
    setCheckOutDialogOpen(false);
    // In a real app, this would record the check-out time and update task status
    alert('Task completed and checked out successfully!');
  };

  const nearbyServices = [
    { name: 'Engen Fuel Station', type: 'fuel', distance: '2.1 km', icon: <LocalGasStation /> },
    { name: 'McDonald\'s Braamfontein', type: 'food', distance: '1.8 km', icon: <Restaurant /> },
    { name: 'Parking Garage', type: 'parking', distance: '0.5 km', icon: <DirectionsCar /> }
  ];

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" fontWeight="bold" gutterBottom>
          🧭 Navigation & GPS
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Navigate to your task location and manage check-in/out
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Current Task Card */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="h6" fontWeight="bold" gutterBottom>
                    📋 {selectedTask.title}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <LocationOn color="primary" />
                    <Typography variant="body1">
                      {selectedTask.address}
                    </Typography>
                  </Box>
                  
                  <Grid container spacing={2} sx={{ mb: 2 }}>
                    <Grid item xs={12} sm={6} md={3}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Timer sx={{ fontSize: 16, color: 'text.secondary' }} />
                        <Typography variant="body2" color="text.secondary">
                          Due: {new Date(selectedTask.deadline).toLocaleString()}
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Speed sx={{ fontSize: 16, color: 'text.secondary' }} />
                        <Typography variant="body2" color="text.secondary">
                          Duration: {selectedTask.estimatedDuration}
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <Chip 
                        label={selectedTask.type.replace('_', ' ').toUpperCase()}
                        color="primary"
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <Chip 
                        label={hasCheckedIn ? 'CHECKED IN' : 'NOT CHECKED IN'}
                        color={hasCheckedIn ? 'success' : 'warning'}
                        size="small"
                      />
                    </Grid>
                  </Grid>

                  {selectedTask.specialInstructions && (
                    <Alert severity="info" sx={{ mb: 2 }}>
                      <Typography variant="body2">
                        <strong>Special Instructions:</strong> {selectedTask.specialInstructions}
                      </Typography>
                    </Alert>
                  )}
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Navigation Info */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                🗺️ Route Information
              </Typography>
              
              <Grid container spacing={3} sx={{ mb: 3 }}>
                <Grid item xs={6} sm={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color="primary.main">
                      {navigationInfo.distance}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Distance
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color="warning.main">
                      {navigationInfo.duration}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Travel Time
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Chip 
                      label={navigationInfo.traffic.toUpperCase()}
                      color={getTrafficColor(navigationInfo.traffic) as any}
                      sx={{ mb: 1 }}
                    />
                    <Typography variant="body2" color="text.secondary">
                      Traffic
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color="success.main">
                      {navigationInfo.estimatedArrival}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      ETA
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>

              {/* Navigation Buttons */}
              <Stack direction="row" spacing={2} sx={{ mb: 3 }}>
                <Button
                  variant="contained"
                  size="large"
                  startIcon={<Navigation />}
                  onClick={handleStartNavigation}
                  sx={{ flex: 1 }}
                >
                  Start Navigation
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<DirectionsWalk />}
                  sx={{ flex: 1 }}
                >
                  Walking Directions
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<Phone />}
                  href={`tel:${selectedTask.contactPerson?.phone}`}
                >
                  Call Contact
                </Button>
              </Stack>

              {/* Current Location */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" gutterBottom>
                  📍 Current Location
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <MyLocation color="success" />
                  <Typography variant="body2">
                    {currentLocation.address}
                  </Typography>
                  <Button size="small" startIcon={<MyLocation />}>
                    Update Location
                  </Button>
                </Box>
              </Box>

              {/* Nearby Services */}
              <Box>
                <Typography variant="subtitle1" gutterBottom>
                  🏪 Nearby Services
                </Typography>
                <List dense>
                  {nearbyServices.map((service, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        {service.icon}
                      </ListItemIcon>
                      <ListItemText
                        primary={service.name}
                        secondary={`${service.distance} away`}
                      />
                    </ListItem>
                  ))}
                </List>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Task Management */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                ✅ Task Management
              </Typography>
              
              {/* Check-in/Check-out */}
              <Stack spacing={2} sx={{ mb: 3 }}>
                {!hasCheckedIn ? (
                  <Button
                    variant="contained"
                    color="success"
                    startIcon={<CheckCircle />}
                    onClick={() => setCheckInDialogOpen(true)}
                    fullWidth
                  >
                    Check In at Location
                  </Button>
                ) : (
                  <Button
                    variant="contained"
                    color="warning"
                    startIcon={<CheckCircle />}
                    onClick={() => setCheckOutDialogOpen(true)}
                    fullWidth
                  >
                    Check Out & Complete
                  </Button>
                )}
                
                <Button
                  variant="outlined"
                  startIcon={<Camera />}
                  fullWidth
                >
                  Take Photos
                </Button>
                
                <Button
                  variant="outlined"
                  startIcon={<Upload />}
                  fullWidth
                >
                  Upload Documents
                </Button>
              </Stack>

              {/* Required Documents */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" gutterBottom>
                  📄 Required Documents
                </Typography>
                <List dense>
                  {selectedTask.documentsRequired.map((doc, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        <CheckCircle color="success" />
                      </ListItemIcon>
                      <ListItemText primary={doc} />
                    </ListItem>
                  ))}
                </List>
              </Box>

              {/* Contact Information */}
              {selectedTask.contactPerson && (
                <Box>
                  <Typography variant="subtitle1" gutterBottom>
                    📞 Contact Person
                  </Typography>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="body1" fontWeight="bold">
                      {selectedTask.contactPerson.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {selectedTask.contactPerson.phone}
                    </Typography>
                    <Button
                      size="small"
                      startIcon={<Phone />}
                      href={`tel:${selectedTask.contactPerson.phone}`}
                      sx={{ mt: 1 }}
                    >
                      Call Now
                    </Button>
                  </Paper>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Check-in Dialog */}
      <Dialog open={checkInDialogOpen} onClose={() => setCheckInDialogOpen(false)}>
        <DialogTitle>📍 Check In at Location</DialogTitle>
        <DialogContent>
          <Typography variant="body1" gutterBottom>
            Confirm that you have arrived at the task location:
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            {selectedTask.address}
          </Typography>
          <Alert severity="info">
            Your location will be recorded for verification purposes.
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCheckInDialogOpen(false)}>
            Cancel
          </Button>
          <Button variant="contained" onClick={handleCheckIn}>
            Confirm Check-In
          </Button>
        </DialogActions>
      </Dialog>

      {/* Check-out Dialog */}
      <Dialog open={checkOutDialogOpen} onClose={() => setCheckOutDialogOpen(false)}>
        <DialogTitle>✅ Complete Task & Check Out</DialogTitle>
        <DialogContent>
          <Typography variant="body1" gutterBottom>
            Add any final notes about the task completion:
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={4}
            placeholder="Task completion notes, any issues encountered, additional observations..."
            value={taskNotes}
            onChange={(e) => setTaskNotes(e.target.value)}
            sx={{ mt: 2 }}
          />
          <Alert severity="success" sx={{ mt: 2 }}>
            Task will be marked as completed and Queen Bee will be notified.
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCheckOutDialogOpen(false)}>
            Cancel
          </Button>
          <Button variant="contained" color="success" onClick={handleCheckOut}>
            Complete Task
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
}
