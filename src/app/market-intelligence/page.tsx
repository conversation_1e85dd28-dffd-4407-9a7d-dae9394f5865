'use client';

import React, { useState } from 'react';
import { 
  Con<PERSON>er, 
  <PERSON>, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  Alert,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Tabs,
  Tab,
  LinearProgress
} from '@mui/material';
import { 
  TrendingUp, 
  Assessment, 
  Visibility, 
  CompareArrows,
  Timeline,
  PieChart,
  BarChart,
  ShowChart,
  Refresh,
  Download,
  Settings
} from '@mui/icons-material';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`intelligence-tabpanel-${index}`}
      aria-labelledby={`intelligence-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

export default function MarketIntelligencePage() {
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Mock market intelligence data
  const marketMetrics = {
    totalTenders: 2847,
    marketValue: 'R2.4B',
    competitorCount: 156,
    winRate: 23.7,
    marketShare: 8.9,
    trendDirection: 'Upward'
  };

  const competitorAnalysis = [
    {
      id: 1,
      company: 'TechSolutions SA',
      marketShare: 15.2,
      winRate: 34.5,
      avgBidValue: 'R1.2M',
      strength: 'IT Services',
      threat: 'High',
      trend: 'Growing'
    },
    {
      id: 2,
      company: 'BuildCorp Ltd',
      marketShare: 12.8,
      winRate: 28.9,
      avgBidValue: 'R890K',
      strength: 'Construction',
      threat: 'Medium',
      trend: 'Stable'
    },
    {
      id: 3,
      company: 'SecureGuard Pro',
      marketShare: 10.4,
      winRate: 31.2,
      avgBidValue: 'R650K',
      strength: 'Security',
      threat: 'Medium',
      trend: 'Declining'
    },
    {
      id: 4,
      company: 'ConsultPro Africa',
      marketShare: 9.7,
      winRate: 42.1,
      avgBidValue: 'R1.8M',
      strength: 'Consulting',
      threat: 'High',
      trend: 'Growing'
    }
  ];

  const marketTrends = [
    { sector: 'IT Services', growth: '+23%', value: 'R890M', opportunities: 45, trend: 'up' },
    { sector: 'Construction', growth: '+12%', value: 'R1.2B', opportunities: 67, trend: 'up' },
    { sector: 'Security', growth: '-5%', value: 'R340M', opportunities: 23, trend: 'down' },
    { sector: 'Consulting', growth: '+18%', value: 'R560M', opportunities: 34, trend: 'up' },
    { sector: 'Healthcare', growth: '+31%', value: 'R720M', opportunities: 56, trend: 'up' }
  ];

  const getThreatColor = (threat: string) => {
    switch (threat) {
      case 'High': return 'error';
      case 'Medium': return 'warning';
      case 'Low': return 'success';
      default: return 'default';
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'Growing': return 'success';
      case 'Stable': return 'info';
      case 'Declining': return 'error';
      default: return 'default';
    }
  };

  const getSectorTrendIcon = (trend: string) => {
    return trend === 'up' ? <TrendingUp color="success" /> : <ShowChart color="error" />;
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
          📊 Market Intelligence Center
        </Typography>
        <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
          Competitive analysis, market trends, and strategic intelligence for optimal bidding decisions
        </Typography>
        
        <Alert severity="info" sx={{ mb: 3 }}>
          <strong>Intelligence Active:</strong> Monitoring 2,847 tenders across R2.4B market value with real-time competitor analysis.
        </Alert>
      </Box>

      {/* Quick Actions */}
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item>
          <Button variant="contained" startIcon={<Refresh />} color="primary">
            Refresh Data
          </Button>
        </Grid>
        <Grid item>
          <Button variant="outlined" startIcon={<Download />}>
            Export Report
          </Button>
        </Grid>
        <Grid item>
          <Button variant="outlined" startIcon={<Settings />}>
            Intelligence Settings
          </Button>
        </Grid>
      </Grid>

      {/* Market Metrics Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Assessment sx={{ fontSize: 32, color: 'primary.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold">{marketMetrics.totalTenders}</Typography>
              <Typography variant="body2" color="text.secondary">Total Tenders</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <PieChart sx={{ fontSize: 32, color: 'success.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold">{marketMetrics.marketValue}</Typography>
              <Typography variant="body2" color="text.secondary">Market Value</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <CompareArrows sx={{ fontSize: 32, color: 'warning.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold">{marketMetrics.competitorCount}</Typography>
              <Typography variant="body2" color="text.secondary">Competitors</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <TrendingUp sx={{ fontSize: 32, color: 'info.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold">{marketMetrics.winRate}%</Typography>
              <Typography variant="body2" color="text.secondary">Win Rate</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <BarChart sx={{ fontSize: 32, color: 'secondary.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold">{marketMetrics.marketShare}%</Typography>
              <Typography variant="body2" color="text.secondary">Market Share</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Timeline sx={{ fontSize: 32, color: 'success.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold">{marketMetrics.trendDirection}</Typography>
              <Typography variant="body2" color="text.secondary">Market Trend</Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Intelligence Analysis Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="intelligence tabs">
          <Tab label="Competitor Analysis" />
          <Tab label="Market Trends" />
          <Tab label="Opportunity Mapping" />
          <Tab label="Strategic Insights" />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>Competitive Landscape Analysis</Typography>
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Company</TableCell>
                    <TableCell>Market Share</TableCell>
                    <TableCell>Win Rate</TableCell>
                    <TableCell>Avg Bid Value</TableCell>
                    <TableCell>Core Strength</TableCell>
                    <TableCell>Threat Level</TableCell>
                    <TableCell>Trend</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {competitorAnalysis.map((competitor) => (
                    <TableRow key={competitor.id}>
                      <TableCell>
                        <Typography variant="subtitle2" fontWeight="medium">
                          {competitor.company}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <LinearProgress 
                            variant="determinate" 
                            value={competitor.marketShare} 
                            sx={{ width: 60, height: 6 }}
                          />
                          <Typography variant="body2">{competitor.marketShare}%</Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="success.main" fontWeight="medium">
                          {competitor.winRate}%
                        </Typography>
                      </TableCell>
                      <TableCell>{competitor.avgBidValue}</TableCell>
                      <TableCell>{competitor.strength}</TableCell>
                      <TableCell>
                        <Chip 
                          label={competitor.threat} 
                          color={getThreatColor(competitor.threat) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={competitor.trend} 
                          color={getTrendColor(competitor.trend) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Button size="small" startIcon={<Visibility />}>
                          Analyze
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Alert severity="success" sx={{ mb: 3 }}>
          <strong>Market Growth:</strong> Overall market showing 18% growth with IT Services and Healthcare leading expansion.
        </Alert>
        <Grid container spacing={3}>
          {marketTrends.map((sector, index) => (
            <Grid item xs={12} md={6} lg={4} key={index}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h6">{sector.sector}</Typography>
                    {getSectorTrendIcon(sector.trend)}
                  </Box>
                  <Typography variant="h4" color="primary.main" fontWeight="bold" gutterBottom>
                    {sector.growth}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Market Value: {sector.value}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Opportunities: {sector.opportunities}
                  </Typography>
                  <Button variant="outlined" size="small" sx={{ mt: 2 }}>
                    View Details
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <Alert severity="info" sx={{ mb: 3 }}>
          <strong>Opportunity Mapping:</strong> AI-identified 67 high-value opportunities with 78% win probability.
        </Alert>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>Geographic Opportunities</Typography>
                <Box sx={{ height: 250, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: '#f5f5f5' }}>
                  <Typography variant="body1" color="text.secondary">
                    🗺️ Interactive opportunity map
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>Sector Opportunities</Typography>
                <Box sx={{ height: 250, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: '#f5f5f5' }}>
                  <Typography variant="body1" color="text.secondary">
                    📊 Sector opportunity breakdown
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      <TabPanel value={tabValue} index={3}>
        <Alert severity="warning" sx={{ mb: 3 }}>
          <strong>Strategic Recommendation:</strong> Focus on IT Services and Healthcare sectors for maximum growth potential.
        </Alert>
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom color="success.main">
                  Market Entry Strategy
                </Typography>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  Recommended approach for entering high-growth sectors with minimal competition.
                </Typography>
                <Button variant="outlined" size="small">
                  View Strategy
                </Button>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom color="warning.main">
                  Competitive Positioning
                </Typography>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  Strategic positioning recommendations to outperform key competitors.
                </Typography>
                <Button variant="outlined" size="small">
                  Learn More
                </Button>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom color="info.main">
                  Pricing Optimization
                </Typography>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  AI-powered pricing strategies to maximize win rate and profitability.
                </Typography>
                <Button variant="outlined" size="small">
                  Optimize Pricing
                </Button>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>
    </Container>
  );
}
