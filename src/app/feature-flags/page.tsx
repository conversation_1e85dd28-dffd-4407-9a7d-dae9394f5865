'use client';

import React, { useState } from 'react';
import { 
  Con<PERSON>er, 
  Box, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  Alert,
  Button,
  Chip,
  Switch,
  FormControlLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  LinearProgress,
  Stack
} from '@mui/material';
import { 
  Flag, 
  ToggleOn, 
  ToggleOff, 
  Settings,
  Visibility,
  Timeline,
  Group,
  Security,
  Refresh,
  Download
} from '@mui/icons-material';

export default function FeatureFlagsPage() {
  const [masterToggle, setMasterToggle] = useState(true);

  // Mock feature flags data
  const flagMetrics = {
    totalFlags: 47,
    activeFlags: 34,
    rolloutPercentage: 78.4,
    affectedUsers: 15420,
    flagsUpdatedToday: 8,
    successRate: 96.7
  };

  const featureFlags = [
    {
      id: 1,
      name: 'psychological_profiling_v2',
      displayName: 'Advanced Psychological Profiling',
      description: 'Enhanced psychological analysis with new archetype detection algorithms',
      enabled: true,
      rollout: 85,
      environment: 'Production',
      userSegment: 'All Users',
      lastUpdated: '2 hours ago',
      category: 'Psychological'
    },
    {
      id: 2,
      name: 'ai_engine_optimization',
      displayName: 'AI Engine Performance Optimization',
      description: 'Improved AI processing speed and accuracy enhancements',
      enabled: true,
      rollout: 100,
      environment: 'Production',
      userSegment: 'Premium Users',
      lastUpdated: '1 day ago',
      category: 'AI'
    },
    {
      id: 3,
      name: 'whatsapp_automation_v3',
      displayName: 'WhatsApp Automation v3.0',
      description: 'Next-generation WhatsApp integration with enhanced auto-bidding',
      enabled: false,
      rollout: 0,
      environment: 'Staging',
      userSegment: 'Beta Users',
      lastUpdated: '3 hours ago',
      category: 'Automation'
    },
    {
      id: 4,
      name: 'compliance_automation_plus',
      displayName: 'Enhanced Compliance Automation',
      description: 'Advanced SA legal compliance with automated document generation',
      enabled: true,
      rollout: 67,
      environment: 'Production',
      userSegment: 'Enterprise Users',
      lastUpdated: '5 hours ago',
      category: 'Compliance'
    },
    {
      id: 5,
      name: 'queen_bee_orchestration',
      displayName: 'Queen Bee Task Orchestration',
      description: 'Intelligent task assignment and bee worker management system',
      enabled: true,
      rollout: 92,
      environment: 'Production',
      userSegment: 'All Users',
      lastUpdated: '30 min ago',
      category: 'Management'
    }
  ];

  const flagCategories = [
    { category: 'Psychological', count: 12, active: 9, color: 'secondary' },
    { category: 'AI', count: 8, active: 7, color: 'primary' },
    { category: 'Automation', count: 10, active: 6, color: 'success' },
    { category: 'Compliance', count: 7, active: 5, color: 'warning' },
    { category: 'Management', count: 6, active: 4, color: 'info' },
    { category: 'Analytics', count: 4, active: 3, color: 'error' }
  ];

  const recentChanges = [
    { id: 1, flag: 'queen_bee_orchestration', action: 'Rollout increased to 92%', user: 'System Admin', time: '30 min ago' },
    { id: 2, flag: 'psychological_profiling_v2', action: 'Enabled for all users', user: 'Product Manager', time: '2 hours ago' },
    { id: 3, flag: 'whatsapp_automation_v3', action: 'Disabled for staging testing', user: 'DevOps Engineer', time: '3 hours ago' },
    { id: 4, flag: 'compliance_automation_plus', action: 'Rollout reduced to 67%', user: 'QA Lead', time: '5 hours ago' }
  ];

  const getEnvironmentColor = (env: string) => {
    switch (env) {
      case 'Production': return 'success';
      case 'Staging': return 'warning';
      case 'Development': return 'info';
      default: return 'default';
    }
  };

  const getCategoryColor = (category: string) => {
    const cat = flagCategories.find(c => c.category === category);
    return cat ? cat.color : 'default';
  };

  const toggleFlag = (flagId: number) => {
    // Mock toggle functionality
    console.log(`Toggling flag ${flagId}`);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold" color="primary.main">
          🎛️ Feature Flags Manager
        </Typography>
        <Typography variant="h6" color="text.primary" sx={{ mb: 2 }}>
          Dynamic feature control and progressive rollout management for all platform capabilities
        </Typography>
        
        <Alert severity="success" sx={{ mb: 3 }}>
          <strong>Flags Active:</strong> 34 of 47 feature flags enabled with 78.4% average rollout and 96.7% success rate.
        </Alert>
      </Box>

      {/* Master Controls */}
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item>
          <FormControlLabel
            control={
              <Switch
                checked={masterToggle}
                onChange={(e) => setMasterToggle(e.target.checked)}
                color="primary"
              />
            }
            label="Master Feature Flag Control"
          />
        </Grid>
        <Grid item>
          <Button variant="contained" startIcon={<Refresh />} color="primary">
            Refresh Flags
          </Button>
        </Grid>
        <Grid item>
          <Button variant="outlined" startIcon={<Download />}>
            Export Config
          </Button>
        </Grid>
        <Grid item>
          <Button variant="outlined" startIcon={<Settings />}>
            Global Settings
          </Button>
        </Grid>
      </Grid>

      {/* Flag Metrics Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Flag sx={{ fontSize: 32, color: 'primary.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {flagMetrics.totalFlags}
              </Typography>
              <Typography variant="body2" color="text.secondary">Total Flags</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <ToggleOn sx={{ fontSize: 32, color: 'success.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {flagMetrics.activeFlags}
              </Typography>
              <Typography variant="body2" color="text.secondary">Active Flags</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Timeline sx={{ fontSize: 32, color: 'info.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {flagMetrics.rolloutPercentage}%
              </Typography>
              <Typography variant="body2" color="text.secondary">Avg Rollout</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Group sx={{ fontSize: 32, color: 'warning.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {flagMetrics.affectedUsers.toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">Affected Users</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Security sx={{ fontSize: 32, color: 'secondary.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {flagMetrics.flagsUpdatedToday}
              </Typography>
              <Typography variant="body2" color="text.secondary">Updated Today</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <ToggleOn sx={{ fontSize: 32, color: 'success.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {flagMetrics.successRate}%
              </Typography>
              <Typography variant="body2" color="text.secondary">Success Rate</Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Feature Categories */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom color="text.primary">
                Feature Flag Management
              </Typography>
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell><Typography color="text.primary" fontWeight="bold">Feature</Typography></TableCell>
                      <TableCell><Typography color="text.primary" fontWeight="bold">Status</Typography></TableCell>
                      <TableCell><Typography color="text.primary" fontWeight="bold">Rollout</Typography></TableCell>
                      <TableCell><Typography color="text.primary" fontWeight="bold">Environment</Typography></TableCell>
                      <TableCell><Typography color="text.primary" fontWeight="bold">Segment</Typography></TableCell>
                      <TableCell><Typography color="text.primary" fontWeight="bold">Actions</Typography></TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {featureFlags.map((flag) => (
                      <TableRow key={flag.id}>
                        <TableCell>
                          <Box>
                            <Typography variant="subtitle2" fontWeight="medium" color="text.primary">
                              {flag.displayName}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {flag.name}
                            </Typography>
                            <br />
                            <Chip 
                              label={flag.category} 
                              color={getCategoryColor(flag.category) as any}
                              size="small"
                            />
                          </Box>
                        </TableCell>
                        <TableCell>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={flag.enabled}
                                onChange={() => toggleFlag(flag.id)}
                                color="primary"
                                size="small"
                              />
                            }
                            label={flag.enabled ? 'On' : 'Off'}
                          />
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <LinearProgress 
                              variant="determinate" 
                              value={flag.rollout} 
                              sx={{ width: 60, height: 6 }}
                            />
                            <Typography variant="body2" color="text.primary">
                              {flag.rollout}%
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip 
                            label={flag.environment} 
                            color={getEnvironmentColor(flag.environment) as any}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" color="text.secondary">
                            {flag.userSegment}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Button size="small" startIcon={<Visibility />}>
                            Details
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom color="text.primary">
                Flag Categories
              </Typography>
              <Stack spacing={2}>
                {flagCategories.map((category, index) => (
                  <Box key={index} sx={{ p: 2, border: '1px solid #333', borderRadius: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="subtitle2" color="text.primary">
                        {category.category}
                      </Typography>
                      <Chip 
                        label={`${category.active}/${category.count}`} 
                        color={category.color as any}
                        size="small"
                      />
                    </Box>
                    <LinearProgress 
                      variant="determinate" 
                      value={(category.active / category.count) * 100} 
                      sx={{ height: 6, borderRadius: 3 }}
                      color={category.color as any}
                    />
                  </Box>
                ))}
              </Stack>
            </CardContent>
          </Card>

          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom color="text.primary">
                Recent Changes
              </Typography>
              <Stack spacing={2}>
                {recentChanges.map((change) => (
                  <Paper key={change.id} sx={{ p: 2 }}>
                    <Typography variant="subtitle2" color="text.primary" gutterBottom>
                      {change.flag}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      {change.action}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      By {change.user} • {change.time}
                    </Typography>
                  </Paper>
                ))}
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
}
