'use client';

import React, { useState } from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  Typo<PERSON>, 
  <PERSON>rid, 
  Card, 
  CardContent, 
  Alert,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Tabs,
  Tab,
  LinearProgress
} from '@mui/material';
import { 
  Psychology, 
  TrendingUp, 
  Assessment, 
  Timeline,
  Visibility,
  Download,
  Refresh,
  <PERSON>tings,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart
} from '@mui/icons-material';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`behavioral-tabpanel-${index}`}
      aria-labelledby={`behavioral-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

export default function BehavioralAnalyticsPage() {
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Mock behavioral analytics data
  const analyticsMetrics = {
    totalBehaviors: 15420,
    patternsIdentified: 89,
    accuracyRate: 96.8,
    predictionSuccess: 87.3,
    improvementRate: 34.2
  };

  const behavioralPatterns = [
    {
      id: 1,
      pattern: 'Decision Hesitation',
      frequency: 78.4,
      impact: 'High',
      trend: 'Increasing',
      description: 'Tendency to delay bid submissions near deadlines',
      recommendation: 'Implement confidence coaching protocols'
    },
    {
      id: 2,
      pattern: 'Risk Aversion Spike',
      frequency: 65.2,
      impact: 'Medium',
      trend: 'Stable',
      description: 'Increased conservative bidding during market uncertainty',
      recommendation: 'Provide market stability insights'
    },
    {
      id: 3,
      pattern: 'Competitive Response',
      frequency: 89.7,
      impact: 'High',
      trend: 'Increasing',
      description: 'Aggressive bidding when specific competitors are present',
      recommendation: 'Optimize competitive intelligence timing'
    },
    {
      id: 4,
      pattern: 'Success Momentum',
      frequency: 92.1,
      impact: 'Very High',
      trend: 'Stable',
      description: 'Increased confidence and bid quality after wins',
      recommendation: 'Leverage momentum for strategic opportunities'
    }
  ];

  const behavioralTriggers = [
    { trigger: 'Deadline Pressure', impact: 'High', frequency: 89, response: 'Stress Increase' },
    { trigger: 'Competitor Presence', impact: 'Medium', frequency: 76, response: 'Aggressive Bidding' },
    { trigger: 'Large Contract Value', impact: 'High', frequency: 67, response: 'Analysis Paralysis' },
    { trigger: 'Previous Win', impact: 'Positive', frequency: 94, response: 'Confidence Boost' },
    { trigger: 'Market Volatility', impact: 'Medium', frequency: 58, response: 'Conservative Approach' }
  ];

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'Very High': return 'error';
      case 'High': return 'warning';
      case 'Medium': return 'info';
      case 'Positive': return 'success';
      default: return 'default';
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'Increasing': return 'warning';
      case 'Stable': return 'success';
      case 'Decreasing': return 'info';
      default: return 'default';
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold" color="primary.main">
          📊 Behavioral Analytics Dashboard
        </Typography>
        <Typography variant="h6" color="text.primary" sx={{ mb: 2 }}>
          Advanced behavioral pattern analysis and predictive modeling for bidding optimization
        </Typography>
        
        <Alert severity="success" sx={{ mb: 3 }}>
          <strong>Analytics Active:</strong> 96.8% pattern recognition accuracy with 15,420 behaviors analyzed and 89 patterns identified.
        </Alert>
      </Box>

      {/* Quick Actions */}
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item>
          <Button variant="contained" startIcon={<Refresh />} color="primary">
            Refresh Analytics
          </Button>
        </Grid>
        <Grid item>
          <Button variant="outlined" startIcon={<Download />}>
            Export Report
          </Button>
        </Grid>
        <Grid item>
          <Button variant="outlined" startIcon={<Settings />}>
            Analytics Settings
          </Button>
        </Grid>
      </Grid>

      {/* Analytics Metrics Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Psychology sx={{ fontSize: 32, color: 'primary.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {analyticsMetrics.totalBehaviors.toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">Behaviors Analyzed</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Assessment sx={{ fontSize: 32, color: 'success.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {analyticsMetrics.patternsIdentified}
              </Typography>
              <Typography variant="body2" color="text.secondary">Patterns Identified</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <TrendingUp sx={{ fontSize: 32, color: 'info.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {analyticsMetrics.accuracyRate}%
              </Typography>
              <Typography variant="body2" color="text.secondary">Accuracy Rate</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Timeline sx={{ fontSize: 32, color: 'warning.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {analyticsMetrics.predictionSuccess}%
              </Typography>
              <Typography variant="body2" color="text.secondary">Prediction Success</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <ShowChart sx={{ fontSize: 32, color: 'secondary.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {analyticsMetrics.improvementRate}%
              </Typography>
              <Typography variant="body2" color="text.secondary">Improvement Rate</Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Behavioral Analysis Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="behavioral analytics tabs">
          <Tab label="Behavioral Patterns" />
          <Tab label="Trigger Analysis" />
          <Tab label="Predictive Models" />
          <Tab label="Optimization Insights" />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom color="text.primary">
              Identified Behavioral Patterns
            </Typography>
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell><Typography color="text.primary" fontWeight="bold">Pattern</Typography></TableCell>
                    <TableCell><Typography color="text.primary" fontWeight="bold">Frequency</Typography></TableCell>
                    <TableCell><Typography color="text.primary" fontWeight="bold">Impact</Typography></TableCell>
                    <TableCell><Typography color="text.primary" fontWeight="bold">Trend</Typography></TableCell>
                    <TableCell><Typography color="text.primary" fontWeight="bold">Description</Typography></TableCell>
                    <TableCell><Typography color="text.primary" fontWeight="bold">Actions</Typography></TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {behavioralPatterns.map((pattern) => (
                    <TableRow key={pattern.id}>
                      <TableCell>
                        <Typography variant="subtitle2" fontWeight="medium" color="text.primary">
                          {pattern.pattern}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <LinearProgress 
                            variant="determinate" 
                            value={pattern.frequency} 
                            sx={{ width: 60, height: 6 }}
                          />
                          <Typography variant="body2" color="text.primary">
                            {pattern.frequency}%
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={pattern.impact} 
                          color={getImpactColor(pattern.impact) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={pattern.trend} 
                          color={getTrendColor(pattern.trend) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="text.secondary">
                          {pattern.description}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Button size="small" startIcon={<Visibility />}>
                          Analyze
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Alert severity="info" sx={{ mb: 3 }}>
          <strong>Trigger Analysis:</strong> Identifying environmental and situational factors that influence bidding behavior.
        </Alert>
        <Grid container spacing={3}>
          {behavioralTriggers.map((trigger, index) => (
            <Grid item xs={12} md={6} lg={4} key={index}>
              <Card>
                <CardContent>
                  <Typography variant="h6" color="text.primary" gutterBottom>
                    {trigger.trigger}
                  </Typography>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Frequency: {trigger.frequency}%
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={trigger.frequency} 
                      sx={{ height: 6, borderRadius: 3 }}
                    />
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Chip 
                      label={trigger.impact} 
                      color={getImpactColor(trigger.impact) as any}
                      size="small"
                    />
                    <Typography variant="body2" color="text.secondary">
                      Response: {trigger.response}
                    </Typography>
                  </Box>
                  <Button variant="outlined" size="small" fullWidth>
                    View Details
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <Alert severity="warning" sx={{ mb: 3 }}>
          <strong>Predictive Models:</strong> AI models predicting behavioral responses with 87.3% accuracy.
        </Alert>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom color="text.primary">
                  Behavioral Prediction Model
                </Typography>
                <Box sx={{ height: 250, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: 'rgba(255,255,255,0.05)' }}>
                  <Typography variant="body1" color="text.secondary">
                    📈 Behavioral prediction accuracy chart
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom color="text.primary">
                  Pattern Evolution Timeline
                </Typography>
                <Box sx={{ height: 250, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: 'rgba(255,255,255,0.05)' }}>
                  <Typography variant="body1" color="text.secondary">
                    📊 Pattern evolution over time
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      <TabPanel value={tabValue} index={3}>
        <Alert severity="success" sx={{ mb: 3 }}>
          <strong>Optimization Insights:</strong> Personalized recommendations based on behavioral analysis.
        </Alert>
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom color="success.main">
                  Timing Optimization
                </Typography>
                <Typography variant="body2" sx={{ mb: 2 }} color="text.secondary">
                  Optimal bid submission timing based on your behavioral patterns.
                </Typography>
                <Button variant="outlined" size="small">
                  View Recommendations
                </Button>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom color="warning.main">
                  Stress Management
                </Typography>
                <Typography variant="body2" sx={{ mb: 2 }} color="text.secondary">
                  Personalized stress reduction techniques for high-pressure situations.
                </Typography>
                <Button variant="outlined" size="small">
                  Learn Techniques
                </Button>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom color="info.main">
                  Decision Enhancement
                </Typography>
                <Typography variant="body2" sx={{ mb: 2 }} color="text.secondary">
                  Strategies to improve decision-making speed and accuracy.
                </Typography>
                <Button variant="outlined" size="small">
                  Start Training
                </Button>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>
    </Container>
  );
}
