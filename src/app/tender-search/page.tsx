'use client';

import React from 'react';
import { Container, Typography, Card, CardContent, Grid, Button, Box, TextField, MenuItem, Chip } from '@mui/material';
import { Search, FilterList, Assignment, LocationOn } from '@mui/icons-material';

export default function TenderSearchPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
        Tender Search
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        Advanced search and filtering for tender opportunities
      </Typography>
      
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <TextField fullWidth label="Search Keywords" variant="outlined" />
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField fullWidth select label="Category" variant="outlined" defaultValue="">
                <MenuItem value="">All Categories</MenuItem>
                <MenuItem value="construction">Construction</MenuItem>
                <MenuItem value="it">IT Services</MenuItem>
                <MenuItem value="consulting">Consulting</MenuItem>
              </TextField>
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField fullWidth select label="Location" variant="outlined" defaultValue="">
                <MenuItem value="">All Locations</MenuItem>
                <MenuItem value="gauteng">Gauteng</MenuItem>
                <MenuItem value="western-cape">Western Cape</MenuItem>
                <MenuItem value="kwazulu-natal">KwaZulu-Natal</MenuItem>
              </TextField>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button variant="contained" fullWidth startIcon={<Search />} sx={{ height: 56 }}>
                Search
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Search Results</Typography>
              <Box sx={{ mt: 2 }}>
                {[1, 2, 3].map((item) => (
                  <Card key={item} variant="outlined" sx={{ mb: 2 }}>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Municipal Infrastructure Development Project {item}
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                        <Chip label="Construction" color="primary" size="small" />
                        <Chip label="R2.4M" color="success" size="small" />
                        <Chip label="Gauteng" color="info" size="small" />
                      </Box>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Deadline: 2024-01-15 | Published: 2024-01-01
                      </Typography>
                      <Button variant="outlined" size="small">View Details</Button>
                    </CardContent>
                  </Card>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Search Filters</Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Button variant="outlined" startIcon={<FilterList />}>
                  Value Range
                </Button>
                <Button variant="outlined" startIcon={<Assignment />}>
                  Tender Type
                </Button>
                <Button variant="outlined" startIcon={<LocationOn />}>
                  Geographic Area
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
}
