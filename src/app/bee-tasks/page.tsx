'use client';

import React, { useState } from 'react';
import {
  <PERSON>,
  Container,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Grid,
  Chip,
  LinearProgress,
  Stack,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Tab,
  Tabs,
  Avatar,
  Divider,
  Alert
} from '@mui/material';
import {
  Assignment,
  LocationOn,
  Timer,
  AttachMoney,
  Navigation,
  CheckCircle,
  Cancel,
  PlayArrow,
  Pause,
  Camera,
  Upload,
  Phone,
  Message,
  Info,
  Edit,
  Description,
  VerifiedUser,
  Engineering
} from '@mui/icons-material';

interface BeeTask {
  id: string;
  title: string;
  description: string;
  type: 'document_collection' | 'site_visit' | 'briefing_attendance' | 'delivery' | 'inspection' | 'form_completion' | 'tender_preparation' | 'document_processing' | 'compliance_checking' | 'technical_evaluation';
  status: 'pending' | 'accepted' | 'in_progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  assignedBy: {
    name: string;
    role: string;
    avatar?: string;
  };
  location: {
    address: string;
    coordinates: { lat: number; lng: number };
    distance: string;
  };
  deadline: string;
  estimatedDuration: string;
  payment: number;
  requirements: string[];
  progress: number;
  createdAt: string;
}

export default function BeeTasksPage() {
  const [currentTab, setCurrentTab] = useState(0);
  const [selectedTask, setSelectedTask] = useState<BeeTask | null>(null);
  const [taskDialogOpen, setTaskDialogOpen] = useState(false);
  const [progressUpdate, setProgressUpdate] = useState('');

  const [tasks, setTasks] = useState<BeeTask[]>([
    {
      id: 'task-001',
      title: 'Municipal Tender Document Collection',
      description: 'Collect tender documents from City of Johannesburg municipal offices for infrastructure project bid.',
      type: 'document_collection',
      status: 'in_progress',
      priority: 'high',
      assignedBy: {
        name: 'Queen Bee Sarah',
        role: 'Queen Bee - Johannesburg',
        avatar: '/avatars/queen-bee.jpg'
      },
      location: {
        address: 'City of Johannesburg Offices, Braamfontein',
        coordinates: { lat: -26.1929, lng: 28.0344 },
        distance: '12.5 km'
      },
      deadline: '2024-01-15T16:00:00',
      estimatedDuration: '2 hours',
      payment: 450,
      requirements: [
        'Valid ID document',
        'BidBeez authorization letter',
        'Transport to location',
        'Camera for document verification'
      ],
      progress: 65,
      createdAt: '2024-01-15T08:00:00'
    },
    {
      id: 'task-002',
      title: 'Site Visit - Road Construction Project',
      description: 'Conduct site visit and assessment for road construction tender in Soweto area.',
      type: 'site_visit',
      status: 'pending',
      priority: 'medium',
      assignedBy: {
        name: 'Queen Bee Mike',
        role: 'Queen Bee - Soweto',
        avatar: '/avatars/queen-bee-2.jpg'
      },
      location: {
        address: 'Vilakazi Street, Orlando West, Soweto',
        coordinates: { lat: -26.2394, lng: 27.8947 },
        distance: '18.2 km'
      },
      deadline: '2024-01-16T14:00:00',
      estimatedDuration: '3 hours',
      payment: 650,
      requirements: [
        'Safety equipment (hard hat, vest)',
        'Measuring tools',
        'Camera for site documentation',
        'GPS device'
      ],
      progress: 0,
      createdAt: '2024-01-15T10:30:00'
    },
    {
      id: 'task-003',
      title: 'Tender Briefing Attendance',
      description: 'Attend mandatory tender briefing session for provincial IT infrastructure project.',
      type: 'briefing_attendance',
      status: 'accepted',
      priority: 'urgent',
      assignedBy: {
        name: 'Queen Bee Lisa',
        role: 'Queen Bee - Pretoria',
        avatar: '/avatars/queen-bee-3.jpg'
      },
      location: {
        address: 'Gauteng Provincial Government, Johannesburg Road, Pretoria',
        coordinates: { lat: -25.7479, lng: 28.2293 },
        distance: '45.8 km'
      },
      deadline: '2024-01-17T09:00:00',
      estimatedDuration: '4 hours',
      payment: 800,
      requirements: [
        'Formal business attire',
        'Notebook and pen',
        'BidBeez credentials',
        'Punctual arrival'
      ],
      progress: 0,
      createdAt: '2024-01-15T12:15:00'
    },
    {
      id: 'task-004',
      title: 'Tender Form Completion - Road Maintenance',
      description: 'Complete comprehensive tender application forms for provincial road maintenance contract, including technical specifications and pricing schedules.',
      type: 'form_completion',
      status: 'pending',
      priority: 'high',
      assignedBy: {
        name: 'Queen Bee Sarah',
        role: 'Queen Bee - Johannesburg',
        avatar: '/avatars/queen-bee.jpg'
      },
      location: {
        address: 'Remote Work - BidBeez Office, Sandton',
        coordinates: { lat: -26.1076, lng: 28.0567 },
        distance: '0 km (Remote)'
      },
      deadline: '2024-01-18T17:00:00',
      estimatedDuration: '6 hours',
      payment: 1200,
      requirements: [
        'Access to tender documents',
        'Technical specification knowledge',
        'Pricing calculation skills',
        'Quality assurance review'
      ],
      progress: 0,
      createdAt: '2024-01-15T14:30:00'
    },
    {
      id: 'task-005',
      title: 'Bid Document Preparation - IT Services',
      description: 'Prepare complete bid submission package including technical proposal, financial proposal, and compliance documentation for government IT services tender.',
      type: 'tender_preparation',
      status: 'pending',
      priority: 'urgent',
      assignedBy: {
        name: 'Queen Bee Mike',
        role: 'Queen Bee - Pretoria',
        avatar: '/avatars/queen-bee-2.jpg'
      },
      location: {
        address: 'Remote Work - BidBeez Office, Pretoria',
        coordinates: { lat: -25.7479, lng: 28.2293 },
        distance: '0 km (Remote)'
      },
      deadline: '2024-01-16T12:00:00',
      estimatedDuration: '8 hours',
      payment: 1500,
      requirements: [
        'Technical writing expertise',
        'Financial modeling skills',
        'Compliance knowledge',
        'Document formatting proficiency'
      ],
      progress: 0,
      createdAt: '2024-01-15T15:45:00'
    },
    {
      id: 'task-006',
      title: 'Compliance Documentation Review',
      description: 'Review and complete all compliance requirements for construction tender including B-BBEE certificates, tax clearance, and technical qualifications.',
      type: 'compliance_checking',
      status: 'accepted',
      priority: 'medium',
      assignedBy: {
        name: 'Queen Bee Lisa',
        role: 'Queen Bee - Cape Town',
        avatar: '/avatars/queen-bee-3.jpg'
      },
      location: {
        address: 'Remote Work - BidBeez Office, Cape Town',
        coordinates: { lat: -33.9249, lng: 18.4241 },
        distance: '0 km (Remote)'
      },
      deadline: '2024-01-19T16:00:00',
      estimatedDuration: '4 hours',
      payment: 800,
      requirements: [
        'Compliance expertise',
        'Document verification skills',
        'Regulatory knowledge',
        'Attention to detail'
      ],
      progress: 25,
      createdAt: '2024-01-15T16:00:00'
    }
  ]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'warning';
      case 'accepted': return 'info';
      case 'in_progress': return 'primary';
      case 'completed': return 'success';
      case 'cancelled': return 'error';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  const getTaskIcon = (type: string) => {
    switch (type) {
      case 'document_collection': return '📄';
      case 'site_visit': return '🏗️';
      case 'briefing_attendance': return '👥';
      case 'delivery': return '🚚';
      case 'inspection': return '🔍';
      case 'form_completion': return '📝';
      case 'tender_preparation': return '📊';
      case 'document_processing': return '📋';
      case 'compliance_checking': return '✅';
      case 'technical_evaluation': return '🔧';
      default: return '📋';
    }
  };

  const handleTaskAction = (taskId: string, action: string) => {
    setTasks(prev => prev.map(task => {
      if (task.id === taskId) {
        switch (action) {
          case 'accept':
            return { ...task, status: 'accepted' as const };
          case 'start':
            return { ...task, status: 'in_progress' as const };
          case 'complete':
            return { ...task, status: 'completed' as const, progress: 100 };
          case 'cancel':
            return { ...task, status: 'cancelled' as const };
          default:
            return task;
        }
      }
      return task;
    }));
  };

  const openTaskDialog = (task: BeeTask) => {
    setSelectedTask(task);
    setTaskDialogOpen(true);
  };

  const filteredTasks = tasks.filter(task => {
    switch (currentTab) {
      case 0: return task.status === 'pending';
      case 1: return task.status === 'accepted' || task.status === 'in_progress';
      case 2: return task.status === 'completed';
      default: return true;
    }
  });

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" fontWeight="bold" gutterBottom>
          🐝 My Tasks
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage your assigned tasks and track progress
        </Typography>
      </Box>

      {/* Task Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={currentTab} onChange={(e, newValue) => setCurrentTab(newValue)}>
          <Tab label={`Pending (${tasks.filter(t => t.status === 'pending').length})`} />
          <Tab label={`Active (${tasks.filter(t => t.status === 'accepted' || t.status === 'in_progress').length})`} />
          <Tab label={`Completed (${tasks.filter(t => t.status === 'completed').length})`} />
        </Tabs>
      </Box>

      {/* Tasks List */}
      <Grid container spacing={3}>
        {filteredTasks.map((task) => (
          <Grid item xs={12} key={task.id}>
            <Card 
              sx={{ 
                border: task.priority === 'urgent' ? '2px solid #f44336' : 'none',
                boxShadow: task.priority === 'urgent' ? '0 0 10px rgba(244, 67, 54, 0.3)' : undefined
              }}
            >
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Box sx={{ flex: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Typography variant="h6" fontWeight="bold">
                        {getTaskIcon(task.type)} {task.title}
                      </Typography>
                      <Chip 
                        label={task.priority.toUpperCase()}
                        color={getPriorityColor(task.priority) as any}
                        size="small"
                      />
                      <Chip 
                        label={task.status.replace('_', ' ').toUpperCase()}
                        color={getStatusColor(task.status) as any}
                        size="small"
                      />
                    </Box>
                    
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {task.description}
                    </Typography>

                    <Grid container spacing={2} sx={{ mb: 2 }}>
                      <Grid item xs={12} sm={6} md={3}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <LocationOn sx={{ fontSize: 16, color: 'text.secondary' }} />
                          <Typography variant="body2" color="text.secondary">
                            {task.location.distance}
                          </Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Timer sx={{ fontSize: 16, color: 'text.secondary' }} />
                          <Typography variant="body2" color="text.secondary">
                            {task.estimatedDuration}
                          </Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <AttachMoney sx={{ fontSize: 16, color: 'success.main' }} />
                          <Typography variant="body2" color="success.main" fontWeight="bold">
                            R{task.payment}
                          </Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <Typography variant="body2" color="text.secondary">
                          Due: {new Date(task.deadline).toLocaleDateString()}
                        </Typography>
                      </Grid>
                    </Grid>

                    {task.status === 'in_progress' && (
                      <Box sx={{ mb: 2 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="body2">Progress</Typography>
                          <Typography variant="body2">{task.progress}%</Typography>
                        </Box>
                        <LinearProgress 
                          variant="determinate" 
                          value={task.progress}
                          sx={{ height: 6, borderRadius: 3 }}
                        />
                      </Box>
                    )}
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, ml: 2 }}>
                    <Avatar 
                      src={task.assignedBy.avatar}
                      sx={{ width: 32, height: 32 }}
                    >
                      👑
                    </Avatar>
                    <Box>
                      <Typography variant="caption" display="block">
                        {task.assignedBy.name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {task.assignedBy.role}
                      </Typography>
                    </Box>
                  </Box>
                </Box>

                <Divider sx={{ my: 2 }} />

                <Stack direction="row" spacing={1} flexWrap="wrap">
                  <Button
                    size="small"
                    startIcon={<Info />}
                    onClick={() => openTaskDialog(task)}
                  >
                    Details
                  </Button>

                  {task.status === 'pending' && (
                    <Button
                      size="small"
                      variant="contained"
                      color="success"
                      startIcon={<CheckCircle />}
                      onClick={() => handleTaskAction(task.id, 'accept')}
                    >
                      Accept
                    </Button>
                  )}

                  {task.status === 'accepted' && (
                    <Button
                      size="small"
                      variant="contained"
                      startIcon={<PlayArrow />}
                      onClick={() => handleTaskAction(task.id, 'start')}
                    >
                      Start
                    </Button>
                  )}

                  {task.status === 'in_progress' && (
                    <>
                      <Button
                        size="small"
                        variant="outlined"
                        startIcon={<Navigation />}
                        href="/bee-navigation"
                      >
                        Navigate
                      </Button>
                      <Button
                        size="small"
                        variant="outlined"
                        startIcon={<Camera />}
                      >
                        Update
                      </Button>
                      <Button
                        size="small"
                        variant="contained"
                        color="success"
                        startIcon={<CheckCircle />}
                        onClick={() => handleTaskAction(task.id, 'complete')}
                      >
                        Complete
                      </Button>
                    </>
                  )}

                  {(task.status === 'pending' || task.status === 'accepted') && (
                    <Button
                      size="small"
                      color="error"
                      startIcon={<Cancel />}
                      onClick={() => handleTaskAction(task.id, 'cancel')}
                    >
                      Decline
                    </Button>
                  )}

                  <Button
                    size="small"
                    startIcon={<Message />}
                    href="/bee-communication"
                  >
                    Contact Queen Bee
                  </Button>
                </Stack>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {filteredTasks.length === 0 && (
        <Alert severity="info" sx={{ mt: 3 }}>
          No tasks in this category. Check other tabs for available tasks.
        </Alert>
      )}

      {/* Task Details Dialog */}
      <Dialog 
        open={taskDialogOpen} 
        onClose={() => setTaskDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        {selectedTask && (
          <>
            <DialogTitle>
              {getTaskIcon(selectedTask.type)} {selectedTask.title}
            </DialogTitle>
            <DialogContent>
              <Typography variant="body1" sx={{ mb: 2 }}>
                {selectedTask.description}
              </Typography>
              
              <Typography variant="h6" gutterBottom>
                📍 Location
              </Typography>
              <Typography variant="body2" sx={{ mb: 2 }}>
                {selectedTask.location.address}
              </Typography>

              <Typography variant="h6" gutterBottom>
                ✅ Requirements
              </Typography>
              <Stack spacing={1} sx={{ mb: 2 }}>
                {selectedTask.requirements.map((req, index) => (
                  <Typography key={index} variant="body2">
                    • {req}
                  </Typography>
                ))}
              </Stack>

              <Typography variant="h6" gutterBottom>
                👑 Assigned By
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar src={selectedTask.assignedBy.avatar}>👑</Avatar>
                <Box>
                  <Typography variant="body1">{selectedTask.assignedBy.name}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {selectedTask.assignedBy.role}
                  </Typography>
                </Box>
              </Box>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setTaskDialogOpen(false)}>
                Close
              </Button>
              <Button 
                variant="contained"
                startIcon={<Navigation />}
                href="/bee-navigation"
              >
                Navigate to Location
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Container>
  );
}
