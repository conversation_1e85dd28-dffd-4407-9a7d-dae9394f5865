'use client';

import React from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  Container,
  Paper,
  Stack,
  Divider
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Analytics as AnalyticsIcon,
  WhatsApp as WhatsAppIcon,
  Security as SecurityIcon,
  Assignment as AssignmentIcon,
  Search as SearchIcon,
  Business as BusinessIcon,
  Home as HomeIcon,
  Login as LoginIcon,
  PersonAdd as RegisterIcon,
  Code as CodeIcon,
  Map as MapIcon
} from '@mui/icons-material';

interface PageInfo {
  title: string;
  description: string;
  url: string;
  icon: React.ReactElement;
  category: string;
  status: 'Live' | 'Demo' | 'Auth';
  features: string[];
}

const pages: PageInfo[] = [
  // Core Pages
  {
    title: 'Home / Landing Page',
    description: 'Main landing page with company overview and call-to-actions',
    url: '/',
    icon: <HomeIcon />,
    category: 'Core',
    status: 'Live',
    features: ['Company Overview', 'Feature Highlights', 'Call-to-Actions']
  },
  {
    title: 'Main Dashboard',
    description: 'Complex interactive dashboard with feature toggles and metrics',
    url: '/dashboard',
    icon: <DashboardIcon />,
    category: 'Core',
    status: 'Live',
    features: ['Feature Toggles', 'Real-time Metrics', 'Progress Tracking', 'Quick Actions']
  },
  {
    title: 'Simple Demo Page',
    description: 'Basic demonstration page showing platform features',
    url: '/simple',
    icon: <CodeIcon />,
    category: 'Core',
    status: 'Demo',
    features: ['Basic HTML', 'Platform Overview', 'Navigation Links']
  },

  // Business Intelligence
  {
    title: 'Advanced Analytics',
    description: 'Comprehensive bid performance insights and competitive intelligence',
    url: '/analytics',
    icon: <AnalyticsIcon />,
    category: 'Business Intelligence',
    status: 'Live',
    features: ['Performance Trends', 'Competitive Analysis', 'ROI Tracking', 'Export Tools']
  },
  {
    title: 'Tender Discovery',
    description: 'AI-powered tender matching and discovery platform',
    url: '/tenders',
    icon: <SearchIcon />,
    category: 'Business Intelligence',
    status: 'Live',
    features: ['Smart Search', 'Match Scoring', 'Bookmarking', 'Filtering']
  },

  // Operations
  {
    title: 'Bid Management',
    description: 'Complete bid lifecycle management and tracking',
    url: '/bids',
    icon: <AssignmentIcon />,
    category: 'Operations',
    status: 'Live',
    features: ['Bid Tracking', 'Progress Monitoring', 'Win Predictions', 'Status Management']
  },
  {
    title: 'WhatsApp Auto-Bidding',
    description: 'Automated bidding intelligence through WhatsApp integration',
    url: '/whatsapp',
    icon: <WhatsAppIcon />,
    category: 'Operations',
    status: 'Live',
    features: ['Auto-Bid Control', 'Message Processing', 'Activity Tracking', 'Status Monitoring']
  },

  // Compliance & Legal
  {
    title: 'SA Compliance Tools',
    description: 'Legal compliance management with anxiety-reducing UX design',
    url: '/compliance',
    icon: <SecurityIcon />,
    category: 'Compliance',
    status: 'Live',
    features: ['B-BBEE Tracking', 'Document Management', 'Compliance Intelligence', 'Legal Support']
  },

  // Business Management
  {
    title: 'Supplier Dashboard',
    description: 'Business performance tracking and supplier management',
    url: '/supplier',
    icon: <BusinessIcon />,
    category: 'Business Management',
    status: 'Live',
    features: ['Revenue Analytics', 'Contract Tracking', 'Performance Metrics', 'Capability Management']
  },

  // Authentication
  {
    title: 'Login Page',
    description: 'User authentication and login portal',
    url: '/auth/login',
    icon: <LoginIcon />,
    category: 'Authentication',
    status: 'Auth',
    features: ['User Authentication', 'Secure Login', 'Password Recovery']
  },
  {
    title: 'Registration Page',
    description: 'New user registration and onboarding',
    url: '/auth/register',
    icon: <RegisterIcon />,
    category: 'Authentication',
    status: 'Auth',
    features: ['User Registration', 'Account Creation', 'Profile Setup']
  }
];

const getCategoryColor = (category: string) => {
  switch (category) {
    case 'Core': return 'primary';
    case 'Business Intelligence': return 'info';
    case 'Operations': return 'success';
    case 'Compliance': return 'warning';
    case 'Business Management': return 'secondary';
    case 'Authentication': return 'error';
    default: return 'default';
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'Live': return 'success';
    case 'Demo': return 'info';
    case 'Auth': return 'warning';
    default: return 'default';
  }
};

export default function SitemapPage() {
  const categories = [...new Set(pages.map(page => page.category))];

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ textAlign: 'center', mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold" color="primary">
          🗺️ BidBeez Application Sitemap
        </Typography>
        <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
          Complete overview of all pages and features in the BidBeez platform
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Click any card below to navigate to that page and explore the full functionality
        </Typography>
      </Box>

      {/* Quick Navigation */}
      <Paper elevation={2} sx={{ p: 3, mb: 4, bgcolor: 'grey.50' }}>
        <Typography variant="h6" gutterBottom>
          🚀 Quick Navigation
        </Typography>
        <Stack direction="row" spacing={2} flexWrap="wrap" gap={2}>
          {pages.slice(0, 6).map((page) => (
            <Button
              key={page.url}
              variant="outlined"
              startIcon={page.icon}
              href={page.url}
              size="small"
            >
              {page.title.split(' ')[0]}
            </Button>
          ))}
        </Stack>
      </Paper>

      {/* Pages by Category */}
      {categories.map((category) => (
        <Box key={category} sx={{ mb: 4 }}>
          <Typography variant="h5" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
            {category}
          </Typography>
          
          <Grid container spacing={3}>
            {pages
              .filter(page => page.category === category)
              .map((page) => (
                <Grid item xs={12} md={6} lg={4} key={page.url}>
                  <Card 
                    elevation={2} 
                    sx={{ 
                      height: '100%', 
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      '&:hover': { 
                        elevation: 6,
                        transform: 'translateY(-4px)'
                      }
                    }}
                    onClick={() => window.open(page.url, '_blank')}
                  >
                    <CardContent sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                      {/* Header */}
                      <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                        <Box sx={{ 
                          p: 1, 
                          borderRadius: 2, 
                          bgcolor: `${getCategoryColor(page.category)}.light`,
                          color: `${getCategoryColor(page.category)}.contrastText`,
                          mr: 2,
                          minWidth: 48,
                          textAlign: 'center'
                        }}>
                          {page.icon}
                        </Box>
                        <Box sx={{ flex: 1 }}>
                          <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
                            {page.title}
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
                            <Chip 
                              label={page.status}
                              size="small"
                              color={getStatusColor(page.status) as any}
                            />
                            <Chip 
                              label={page.category}
                              size="small"
                              variant="outlined"
                              color={getCategoryColor(page.category) as any}
                            />
                          </Box>
                        </Box>
                      </Box>

                      {/* Description */}
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2, flex: 1 }}>
                        {page.description}
                      </Typography>

                      {/* Features */}
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="caption" color="text.secondary" sx={{ fontWeight: 600 }}>
                          Key Features:
                        </Typography>
                        <Box sx={{ mt: 0.5 }}>
                          {page.features.map((feature, index) => (
                            <Chip 
                              key={index}
                              label={feature}
                              size="small"
                              sx={{ mr: 0.5, mb: 0.5 }}
                              variant="outlined"
                            />
                          ))}
                        </Box>
                      </Box>

                      {/* Action Button */}
                      <Button
                        variant="contained"
                        fullWidth
                        startIcon={page.icon}
                        href={page.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        color={getCategoryColor(page.category) as any}
                        onClick={(e) => e.stopPropagation()}
                      >
                        Visit {page.title}
                      </Button>

                      {/* URL Display */}
                      <Typography variant="caption" color="text.secondary" sx={{ mt: 1, textAlign: 'center' }}>
                        localhost:3000{page.url}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
          </Grid>
        </Box>
      ))}

      {/* Statistics */}
      <Paper elevation={2} sx={{ p: 3, mt: 4, textAlign: 'center' }}>
        <Typography variant="h6" gutterBottom>
          📊 Platform Statistics
        </Typography>
        <Grid container spacing={4}>
          <Grid item xs={6} md={3}>
            <Typography variant="h4" color="primary" sx={{ fontWeight: 600 }}>
              {pages.length}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Total Pages
            </Typography>
          </Grid>
          <Grid item xs={6} md={3}>
            <Typography variant="h4" color="success.main" sx={{ fontWeight: 600 }}>
              {pages.filter(p => p.status === 'Live').length}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Live Features
            </Typography>
          </Grid>
          <Grid item xs={6} md={3}>
            <Typography variant="h4" color="info.main" sx={{ fontWeight: 600 }}>
              {categories.length}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Feature Categories
            </Typography>
          </Grid>
          <Grid item xs={6} md={3}>
            <Typography variant="h4" color="warning.main" sx={{ fontWeight: 600 }}>
              {pages.reduce((total, page) => total + page.features.length, 0)}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Total Features
            </Typography>
          </Grid>
        </Grid>
      </Paper>

      {/* Back to Dashboard */}
      <Box sx={{ textAlign: 'center', mt: 4 }}>
        <Button
          variant="outlined"
          size="large"
          startIcon={<DashboardIcon />}
          href="/dashboard"
          sx={{ mr: 2 }}
        >
          Return to Dashboard
        </Button>
        <Button
          variant="contained"
          size="large"
          startIcon={<HomeIcon />}
          href="/"
        >
          Go to Home
        </Button>
      </Box>
    </Container>
  );
}