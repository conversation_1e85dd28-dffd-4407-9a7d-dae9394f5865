'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  <PERSON>ton,
  Stack,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Alert,
  LinearProgress,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Attach<PERSON>oney,
  TrendingUp,
  AccountBalance,
  Receipt,
  Schedule,
  Star,
  Payment,
  Download,
  Visibility,
  Security,
  Warning
} from '@mui/icons-material';
// Using Material-UI components instead of recharts for better compatibility

// Database-aligned interfaces
interface BeeWallet {
  id: number;
  bee_id: number;
  available_balance: string;
  pending_balance: string;
  total_balance: string;
  daily_withdrawal_limit: string;
  monthly_withdrawal_limit: string;
  auto_withdrawal_enabled: boolean;
  auto_withdrawal_threshold: string;
  is_frozen: boolean;
  frozen_reason: string | null;
  created_at: string;
  updated_at: string;
}

interface PaymentTransaction {
  id: number;
  bee_id: number;
  task_id: number;
  amount: string;
  transaction_type: 'payment' | 'withdrawal' | 'bonus' | 'penalty';
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  payment_method: string;
  reference_number: string;
  processed_at: string | null;
  created_at: string;
}

interface TaskEarning {
  id: number;
  task_id: number;
  bee_id: number;
  base_amount: string;
  bonus_amount: string;
  penalty_amount: string;
  final_amount: string;
  payment_status: 'pending' | 'paid' | 'disputed';
  task_title: string;
  task_type: string;
  completed_at: string;
  paid_at: string | null;
}

export default function BeeEarningsIntegratedPage() {
  const [currentTab, setCurrentTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [wallet, setWallet] = useState<BeeWallet | null>(null);
  const [transactions, setTransactions] = useState<PaymentTransaction[]>([]);
  const [taskEarnings, setTaskEarnings] = useState<TaskEarning[]>([]);

  useEffect(() => {
    // Simulate API calls to Supabase
    setTimeout(() => {
      setWallet({
        id: 1,
        bee_id: 1,
        available_balance: '2450.00',
        pending_balance: '650.00',
        total_balance: '23450.00',
        daily_withdrawal_limit: '5000.00',
        monthly_withdrawal_limit: '50000.00',
        auto_withdrawal_enabled: true,
        auto_withdrawal_threshold: '1000.00',
        is_frozen: false,
        frozen_reason: null,
        created_at: '2023-08-15T00:00:00Z',
        updated_at: new Date().toISOString()
      });

      setTransactions([
        {
          id: 1,
          bee_id: 1,
          task_id: 101,
          amount: '450.00',
          transaction_type: 'payment',
          status: 'completed',
          payment_method: 'bank_transfer',
          reference_number: 'TXN-2024-001',
          processed_at: '2024-01-15T16:30:00Z',
          created_at: '2024-01-15T14:00:00Z'
        },
        {
          id: 2,
          bee_id: 1,
          task_id: 102,
          amount: '650.00',
          transaction_type: 'payment',
          status: 'pending',
          payment_method: 'bank_transfer',
          reference_number: 'TXN-2024-002',
          processed_at: null,
          created_at: '2024-01-14T12:00:00Z'
        },
        {
          id: 3,
          bee_id: 1,
          task_id: 0,
          amount: '1200.00',
          transaction_type: 'withdrawal',
          status: 'completed',
          payment_method: 'bank_transfer',
          reference_number: 'WTH-2024-001',
          processed_at: '2024-01-13T10:15:00Z',
          created_at: '2024-01-13T09:00:00Z'
        }
      ]);

      setTaskEarnings([
        {
          id: 1,
          task_id: 101,
          bee_id: 1,
          base_amount: '400.00',
          bonus_amount: '50.00',
          penalty_amount: '0.00',
          final_amount: '450.00',
          payment_status: 'paid',
          task_title: 'Municipal Document Collection',
          task_type: 'document_collection',
          completed_at: '2024-01-15T14:00:00Z',
          paid_at: '2024-01-15T16:30:00Z'
        },
        {
          id: 2,
          task_id: 102,
          bee_id: 1,
          base_amount: '600.00',
          bonus_amount: '50.00',
          penalty_amount: '0.00',
          final_amount: '650.00',
          payment_status: 'pending',
          task_title: 'Site Visit - Road Construction',
          task_type: 'site_visit',
          completed_at: '2024-01-14T12:00:00Z',
          paid_at: null
        },
        {
          id: 3,
          task_id: 103,
          bee_id: 1,
          base_amount: '750.00',
          bonus_amount: '100.00',
          penalty_amount: '50.00',
          final_amount: '800.00',
          payment_status: 'paid',
          task_title: 'Tender Form Completion - IT Services',
          task_type: 'form_completion',
          completed_at: '2024-01-13T18:00:00Z',
          paid_at: '2024-01-14T10:00:00Z'
        }
      ]);

      setLoading(false);
    }, 1000);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
      case 'paid': return 'success';
      case 'pending': return 'warning';
      case 'failed':
      case 'cancelled':
      case 'disputed': return 'error';
      default: return 'default';
    }
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'payment': return <Payment color="success" />;
      case 'withdrawal': return <AccountBalance color="info" />;
      case 'bonus': return <Star color="warning" />;
      case 'penalty': return <Warning color="error" />;
      default: return <Receipt />;
    }
  };

  const handleAutoWithdrawalToggle = async () => {
    if (!wallet) return;
    
    // In real app, this would update Supabase
    setWallet({
      ...wallet,
      auto_withdrawal_enabled: !wallet.auto_withdrawal_enabled,
      updated_at: new Date().toISOString()
    });
  };

  const handleWithdrawal = async () => {
    // In real app, this would create a withdrawal transaction
    alert('Withdrawal request submitted! Processing time: 1-2 business days.');
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 3 }}>
        <LinearProgress />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Loading earnings data...
        </Typography>
      </Container>
    );
  }

  if (!wallet) {
    return (
      <Container maxWidth="lg" sx={{ py: 3 }}>
        <Alert severity="error">
          Failed to load wallet information. Please try again.
        </Alert>
      </Container>
    );
  }

  const monthlyEarnings = taskEarnings
    .filter(earning => earning.payment_status === 'paid')
    .reduce((sum, earning) => sum + parseFloat(earning.final_amount), 0);

  const pendingEarnings = taskEarnings
    .filter(earning => earning.payment_status === 'pending')
    .reduce((sum, earning) => sum + parseFloat(earning.final_amount), 0);

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" fontWeight="bold" gutterBottom>
          💰 Earnings Tracker (Integrated)
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Real-time financial tracking connected to database
        </Typography>
      </Box>

      {/* Wallet Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <AttachMoney sx={{ fontSize: 32, color: 'success.main', mb: 1 }} />
              <Typography variant="h5" fontWeight="bold">
                R{parseFloat(wallet.available_balance).toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Available Balance
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Schedule sx={{ fontSize: 32, color: 'warning.main', mb: 1 }} />
              <Typography variant="h5" fontWeight="bold">
                R{parseFloat(wallet.pending_balance).toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Pending Balance
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <TrendingUp sx={{ fontSize: 32, color: 'primary.main', mb: 1 }} />
              <Typography variant="h5" fontWeight="bold">
                R{parseFloat(wallet.total_balance).toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Earnings
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <AccountBalance sx={{ fontSize: 32, color: 'info.main', mb: 1 }} />
              <Typography variant="h5" fontWeight="bold">
                R{parseFloat(wallet.daily_withdrawal_limit).toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Daily Limit
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Wallet Status */}
      {wallet.is_frozen && (
        <Alert severity="error" sx={{ mb: 3 }}>
          <Typography variant="body1" fontWeight="bold">
            ⚠️ Wallet Frozen
          </Typography>
          <Typography variant="body2">
            Reason: {wallet.frozen_reason}
          </Typography>
        </Alert>
      )}

      {/* Auto-Withdrawal Settings */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            ⚙️ Wallet Settings
          </Typography>
          
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Switch 
                    checked={wallet.auto_withdrawal_enabled}
                    onChange={handleAutoWithdrawalToggle}
                    disabled={wallet.is_frozen}
                  />
                }
                label={`Auto-withdrawal (Threshold: R${parseFloat(wallet.auto_withdrawal_threshold).toLocaleString()})`}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Button
                variant="contained"
                startIcon={<AccountBalance />}
                onClick={handleWithdrawal}
                disabled={wallet.is_frozen || parseFloat(wallet.available_balance) < 100}
                fullWidth
              >
                Request Withdrawal
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Earnings Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={currentTab} onChange={(e, newValue) => setCurrentTab(newValue)}>
          <Tab label="Task Earnings" />
          <Tab label="Transaction History" />
          <Tab label="Analytics" />
        </Tabs>
      </Box>

      {/* Tab Content */}
      {currentTab === 0 && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              📋 Task Earnings Breakdown
            </Typography>
            
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Task</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Base Amount</TableCell>
                    <TableCell>Bonus</TableCell>
                    <TableCell>Penalty</TableCell>
                    <TableCell>Final Amount</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Completed</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {taskEarnings.map((earning) => (
                    <TableRow key={earning.id}>
                      <TableCell>
                        <Typography variant="body2" fontWeight="bold">
                          {earning.task_title}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Task #{earning.task_id}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={earning.task_type.replace('_', ' ').toUpperCase()}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>R{parseFloat(earning.base_amount).toLocaleString()}</TableCell>
                      <TableCell>
                        <Typography color="success.main">
                          +R{parseFloat(earning.bonus_amount).toLocaleString()}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography color="error.main">
                          -R{parseFloat(earning.penalty_amount).toLocaleString()}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body1" fontWeight="bold">
                          R{parseFloat(earning.final_amount).toLocaleString()}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={earning.payment_status.toUpperCase()}
                          color={getStatusColor(earning.payment_status) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {new Date(earning.completed_at).toLocaleDateString()}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      )}

      {currentTab === 1 && (
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                💳 Transaction History
              </Typography>
              <Button startIcon={<Download />} variant="outlined">
                Export Statement
              </Button>
            </Box>
            
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Date</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Reference</TableCell>
                    <TableCell>Amount</TableCell>
                    <TableCell>Method</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Processed</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {transactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell>
                        {new Date(transaction.created_at).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {getTransactionIcon(transaction.transaction_type)}
                          <Typography variant="body2">
                            {transaction.transaction_type.replace('_', ' ').toUpperCase()}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontFamily="monospace">
                          {transaction.reference_number}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography 
                          variant="body1" 
                          fontWeight="bold"
                          color={transaction.transaction_type === 'withdrawal' ? 'error.main' : 'success.main'}
                        >
                          {transaction.transaction_type === 'withdrawal' ? '-' : '+'}R{parseFloat(transaction.amount).toLocaleString()}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        {transaction.payment_method.replace('_', ' ').toUpperCase()}
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={transaction.status.toUpperCase()}
                          color={getStatusColor(transaction.status) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {transaction.processed_at 
                          ? new Date(transaction.processed_at).toLocaleDateString()
                          : 'Pending'
                        }
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      )}

      {currentTab === 2 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  📊 Earnings Summary
                </Typography>
                
                <Stack spacing={2}>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      This Month Earnings
                    </Typography>
                    <Typography variant="h4" color="success.main">
                      R{monthlyEarnings.toLocaleString()}
                    </Typography>
                  </Box>
                  
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Pending Payments
                    </Typography>
                    <Typography variant="h4" color="warning.main">
                      R{pendingEarnings.toLocaleString()}
                    </Typography>
                  </Box>
                  
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Average per Task
                    </Typography>
                    <Typography variant="h4">
                      R{taskEarnings.length > 0 
                        ? (taskEarnings.reduce((sum, e) => sum + parseFloat(e.final_amount), 0) / taskEarnings.length).toFixed(0)
                        : '0'
                      }
                    </Typography>
                  </Box>
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  💡 Financial Insights
                </Typography>
                
                <Alert severity="success" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    🎉 Your earnings are 15% above average for your experience level!
                  </Typography>
                </Alert>

                <Alert severity="info" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    💰 Auto-withdrawal is enabled. Funds will be transferred when balance reaches R{parseFloat(wallet.auto_withdrawal_threshold).toLocaleString()}.
                  </Typography>
                </Alert>

                <Alert severity="warning">
                  <Typography variant="body2">
                    ⏰ You have R{pendingEarnings.toLocaleString()} in pending payments. Expected processing: 1-2 business days.
                  </Typography>
                </Alert>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
    </Container>
  );
}
