'use client';

import React from 'react';
import { Container, Typography, Card, CardContent, Grid, Button, Box, Avatar, Chip } from '@mui/material';
import { AttachMoney, TrendingUp, Analytics, Speed } from '@mui/icons-material';

export default function PricingOptimizationPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
        Pricing Optimization
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        AI-powered pricing strategies for maximum profitability
      </Typography>
      
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="primary">
                    R2.4M
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Optimal Price
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <AttachMoney />
                </Avatar>
              </Box>
              <Chip label="+15% profit margin" color="success" size="small" sx={{ mt: 1 }} />
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="success.main">
                    89%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Win Probability
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <TrendingUp />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Pricing Recommendations</Typography>
              <Box sx={{ mt: 2 }}>
                <Typography variant="body2" gutterBottom>
                  • Competitive pricing analysis suggests 8% reduction for higher win rate
                </Typography>
                <Typography variant="body2" gutterBottom>
                  • Market conditions favor premium pricing in Q1
                </Typography>
                <Typography variant="body2" gutterBottom>
                  • Consider value-based pricing for specialized services
                </Typography>
              </Box>
              <Button variant="contained" sx={{ mt: 2 }}>
                Apply Optimization
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
}
