'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Container,
  Card,
  CardContent,
  Typo<PERSON>,
  Grid,
  Chip,
  Button,
  Stack,
  Avatar,
  TextField,
  Switch,
  FormControlLabel,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Alert,
  LinearProgress,
  Paper,
  Divider,
  IconButton
} from '@mui/material';
import {
  Edit,
  Save,
  Cancel,
  Star,
  Verified,
  Security,
  TrendingUp,
  LocationOn,
  Phone,
  Email,
  Work,
  Schedule,
  Settings,
  Warning,
  CheckCircle
} from '@mui/icons-material';

// Database-aligned interface
interface BeeProfileDB {
  id: number;
  user_id: string;
  full_name: string;
  phone: string;
  transport_mode: 'car' | 'motorcycle' | 'bicycle' | 'walking';
  max_range_km: number;
  rating: number;
  is_enterprise_bee: boolean;
  is_active: boolean;
  is_verified: boolean;
  availability_status: 'online' | 'offline' | 'busy';
  performance_score: string;
  total_earnings: string;
  last_heartbeat: string | null;
  risk_score: number;
  skills: string; // JSON string
  preferred_categories: string; // JSON string
  created_at: string;
  professional_title: string | null;
  bio: string | null;
  experience_years: number;
  hourly_rate: string | null;
  minimum_task_value: string;
  verification_level: 'unverified' | 'basic' | 'standard' | 'premium' | 'elite';
  verification_score: number;
  completed_tasks: number;
  cancelled_tasks: number;
}

interface BeeVerification {
  id: number;
  bee_id: number;
  verification_type: string;
  verification_method: string;
  status: 'pending' | 'verified' | 'failed' | 'expired';
  confidence_level: number;
  verification_score: number;
  ai_analysis_result: string | null;
  fraud_indicators: string | null;
  verification_provider: string | null;
  document_type: string | null;
  document_number: string | null;
  document_expiry_date: string | null;
  verified_at: string | null;
  expires_at: string | null;
}

interface BeeWallet {
  id: number;
  bee_id: number;
  available_balance: string;
  pending_balance: string;
  total_balance: string;
  daily_withdrawal_limit: string;
  monthly_withdrawal_limit: string;
  auto_withdrawal_enabled: boolean;
  auto_withdrawal_threshold: string;
  is_frozen: boolean;
  frozen_reason: string | null;
}

export default function BeeProfileIntegratedPage() {
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [profile, setProfile] = useState<BeeProfileDB | null>(null);
  const [verifications, setVerifications] = useState<BeeVerification[]>([]);
  const [wallet, setWallet] = useState<BeeWallet | null>(null);

  // Sample data - in real app, this would come from Supabase
  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setProfile({
        id: 1,
        user_id: 'a8cc3c05-a177-486b-b2c7-ae454dcaeaf9',
        full_name: 'Sarah Mthembu',
        phone: '+27821234567',
        transport_mode: 'car',
        max_range_km: 75,
        rating: 4.8,
        is_enterprise_bee: true,
        is_active: true,
        is_verified: true,
        availability_status: 'online',
        performance_score: '96.5',
        total_earnings: '23450.00',
        last_heartbeat: new Date().toISOString(),
        risk_score: 2,
        skills: '["Document Processing", "Site Visits", "Compliance Checking", "Form Completion"]',
        preferred_categories: '["Government Tenders", "Municipal Contracts", "Construction Projects"]',
        created_at: '2023-08-15T00:00:00Z',
        professional_title: 'Senior Tender Specialist',
        bio: 'Experienced tender professional with 5+ years in government procurement processes. Specialized in municipal tenders and compliance documentation.',
        experience_years: 5,
        hourly_rate: '450.00',
        minimum_task_value: '200.00',
        verification_level: 'premium',
        verification_score: 96,
        completed_tasks: 47,
        cancelled_tasks: 1
      });

      setVerifications([
        {
          id: 1,
          bee_id: 1,
          verification_type: 'identity',
          verification_method: 'biometric_id_check',
          status: 'verified',
          confidence_level: 98,
          verification_score: 95,
          ai_analysis_result: '{"face_match": 0.98, "document_authenticity": 0.96, "liveness_check": 0.99}',
          fraud_indicators: null,
          verification_provider: 'home_affairs_db',
          document_type: 'south_african_id',
          document_number: '9001015678901',
          document_expiry_date: null,
          verified_at: '2023-08-15T10:30:00Z',
          expires_at: null
        },
        {
          id: 2,
          bee_id: 1,
          verification_type: 'criminal_background',
          verification_method: 'saps_database_check',
          status: 'verified',
          confidence_level: 100,
          verification_score: 100,
          ai_analysis_result: '{"criminal_record": false, "pending_cases": false, "risk_assessment": "low"}',
          fraud_indicators: null,
          verification_provider: 'saps_database',
          document_type: 'police_clearance',
          document_number: 'PC-2023-081501',
          document_expiry_date: '2024-08-15',
          verified_at: '2023-08-16T14:20:00Z',
          expires_at: '2024-08-15T23:59:59Z'
        }
      ]);

      setWallet({
        id: 1,
        bee_id: 1,
        available_balance: '2450.00',
        pending_balance: '650.00',
        total_balance: '23450.00',
        daily_withdrawal_limit: '5000.00',
        monthly_withdrawal_limit: '50000.00',
        auto_withdrawal_enabled: true,
        auto_withdrawal_threshold: '1000.00',
        is_frozen: false,
        frozen_reason: null
      });

      setLoading(false);
    }, 1000);
  }, []);

  const getVerificationLevelColor = (level: string) => {
    switch (level) {
      case 'elite': return 'gold';
      case 'premium': return 'primary';
      case 'standard': return 'info';
      case 'basic': return 'warning';
      default: return 'error';
    }
  };

  const getAvailabilityColor = (status: string) => {
    switch (status) {
      case 'online': return 'success';
      case 'busy': return 'warning';
      case 'offline': return 'error';
      default: return 'default';
    }
  };

  const parseSkills = (skillsJson: string): string[] => {
    try {
      return JSON.parse(skillsJson);
    } catch {
      return [];
    }
  };

  const parseCategories = (categoriesJson: string): string[] => {
    try {
      return JSON.parse(categoriesJson);
    } catch {
      return [];
    }
  };

  const handleSave = async () => {
    // In real app, this would update Supabase
    setIsEditing(false);
    // API call to update profile
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 3 }}>
        <LinearProgress />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Loading bee worker profile...
        </Typography>
      </Container>
    );
  }

  if (!profile) {
    return (
      <Container maxWidth="lg" sx={{ py: 3 }}>
        <Alert severity="error">
          Failed to load bee worker profile. Please try again.
        </Alert>
      </Container>
    );
  }

  const successRate = profile.completed_tasks > 0 
    ? ((profile.completed_tasks / (profile.completed_tasks + profile.cancelled_tasks)) * 100).toFixed(1)
    : '0';

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" fontWeight="bold">
          🐝 Bee Worker Profile (Integrated)
        </Typography>
        <Button
          variant={isEditing ? "outlined" : "contained"}
          startIcon={isEditing ? <Cancel /> : <Edit />}
          onClick={() => setIsEditing(!isEditing)}
        >
          {isEditing ? 'Cancel' : 'Edit Profile'}
        </Button>
      </Box>

      <Grid container spacing={3}>
        {/* Profile Overview */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar 
                sx={{ width: 100, height: 100, mx: 'auto', mb: 2, bgcolor: 'warning.main' }}
              >
                🐝
              </Avatar>
              
              <Typography variant="h5" fontWeight="bold" gutterBottom>
                {profile.full_name}
              </Typography>
              
              <Typography variant="body1" color="text.secondary" gutterBottom>
                {profile.professional_title || 'Bee Worker'}
              </Typography>

              <Stack direction="row" spacing={1} justifyContent="center" sx={{ mb: 2 }}>
                <Chip 
                  label={`${profile.verification_level.toUpperCase()} VERIFIED`}
                  color={getVerificationLevelColor(profile.verification_level) as any}
                  icon={<Verified />}
                  size="small"
                />
                {profile.is_enterprise_bee && (
                  <Chip 
                    label="ENTERPRISE BEE"
                    color="secondary"
                    size="small"
                  />
                )}
              </Stack>

              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1, mb: 2 }}>
                <Star sx={{ color: 'gold' }} />
                <Typography variant="h6">{profile.rating}</Typography>
                <Typography variant="body2" color="text.secondary">
                  ({profile.completed_tasks} tasks)
                </Typography>
              </Box>

              <Chip 
                label={profile.availability_status.toUpperCase()}
                color={getAvailabilityColor(profile.availability_status) as any}
                sx={{ mb: 2 }}
              />

              <Typography variant="body2" color="text.secondary">
                Member since {new Date(profile.created_at).toLocaleDateString()}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Performance Metrics */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                📊 Performance Metrics
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={6} sm={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color="success.main">
                      {profile.performance_score}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Performance Score
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={6} sm={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color="primary.main">
                      {profile.verification_score}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Verification Score
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={6} sm={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color="info.main">
                      {successRate}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Success Rate
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={6} sm={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color={profile.risk_score <= 5 ? 'success.main' : 'warning.main'}>
                      {profile.risk_score}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Risk Score
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>

              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle1" gutterBottom>
                  📈 Financial Overview
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={4}>
                    <Typography variant="body2" color="text.secondary">
                      Total Earnings
                    </Typography>
                    <Typography variant="h6" color="success.main">
                      R{parseFloat(profile.total_earnings).toLocaleString()}
                    </Typography>
                  </Grid>
                  <Grid item xs={4}>
                    <Typography variant="body2" color="text.secondary">
                      Hourly Rate
                    </Typography>
                    <Typography variant="h6">
                      R{profile.hourly_rate ? parseFloat(profile.hourly_rate).toLocaleString() : 'Not Set'}
                    </Typography>
                  </Grid>
                  <Grid item xs={4}>
                    <Typography variant="body2" color="text.secondary">
                      Min Task Value
                    </Typography>
                    <Typography variant="h6">
                      R{parseFloat(profile.minimum_task_value).toLocaleString()}
                    </Typography>
                  </Grid>
                </Grid>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Skills & Specialties */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                🎯 Skills & Specialties
              </Typography>
              
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Core Skills
                </Typography>
                <Stack direction="row" spacing={1} flexWrap="wrap" gap={1}>
                  {parseSkills(profile.skills).map((skill, index) => (
                    <Chip 
                      key={index}
                      label={skill}
                      color="primary"
                      variant="outlined"
                      size="small"
                    />
                  ))}
                </Stack>
              </Box>

              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Preferred Categories
                </Typography>
                <Stack direction="row" spacing={1} flexWrap="wrap" gap={1}>
                  {parseCategories(profile.preferred_categories).map((category, index) => (
                    <Chip 
                      key={index}
                      label={category}
                      color="secondary"
                      variant="outlined"
                      size="small"
                    />
                  ))}
                </Stack>
              </Box>

              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Transport & Range
                </Typography>
                <Typography variant="body2">
                  🚗 {profile.transport_mode.charAt(0).toUpperCase() + profile.transport_mode.slice(1)} - 
                  {profile.max_range_km}km range
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Verification Status */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                🔍 Verification Status
              </Typography>
              
              {verifications.map((verification) => (
                <Box key={verification.id} sx={{ mb: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="subtitle2">
                      {verification.verification_type.replace('_', ' ').toUpperCase()}
                    </Typography>
                    <Chip 
                      label={verification.status.toUpperCase()}
                      color={verification.status === 'verified' ? 'success' : 'warning'}
                      size="small"
                    />
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    Score: {verification.verification_score}% | 
                    Confidence: {verification.confidence_level}%
                  </Typography>
                  {verification.expires_at && (
                    <Typography variant="caption" color="text.secondary">
                      Expires: {new Date(verification.expires_at).toLocaleDateString()}
                    </Typography>
                  )}
                  <Divider sx={{ mt: 1 }} />
                </Box>
              ))}

              <Alert severity="success" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  🛡️ All verifications current and valid
                </Typography>
              </Alert>
            </CardContent>
          </Card>
        </Grid>

        {/* Wallet Information */}
        {wallet && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  💰 Wallet & Financial Settings
                </Typography>
                
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={4}>
                    <Paper sx={{ p: 2 }}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Available Balance
                      </Typography>
                      <Typography variant="h5" color="success.main">
                        R{parseFloat(wallet.available_balance).toLocaleString()}
                      </Typography>
                    </Paper>
                  </Grid>
                  
                  <Grid item xs={12} sm={4}>
                    <Paper sx={{ p: 2 }}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Pending Balance
                      </Typography>
                      <Typography variant="h5" color="warning.main">
                        R{parseFloat(wallet.pending_balance).toLocaleString()}
                      </Typography>
                    </Paper>
                  </Grid>
                  
                  <Grid item xs={12} sm={4}>
                    <Paper sx={{ p: 2 }}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Daily Limit
                      </Typography>
                      <Typography variant="h5">
                        R{parseFloat(wallet.daily_withdrawal_limit).toLocaleString()}
                      </Typography>
                    </Paper>
                  </Grid>
                </Grid>

                <Box sx={{ mt: 3 }}>
                  <FormControlLabel
                    control={
                      <Switch 
                        checked={wallet.auto_withdrawal_enabled}
                        disabled={!isEditing}
                      />
                    }
                    label={`Auto-withdrawal enabled (Threshold: R${parseFloat(wallet.auto_withdrawal_threshold).toLocaleString()})`}
                  />
                </Box>

                {wallet.is_frozen && (
                  <Alert severity="error" sx={{ mt: 2 }}>
                    ⚠️ Wallet is frozen: {wallet.frozen_reason}
                  </Alert>
                )}
              </CardContent>
            </Card>
          </Grid>
        )}
      </Grid>

      {isEditing && (
        <Box sx={{ mt: 3, textAlign: 'center' }}>
          <Button
            variant="contained"
            startIcon={<Save />}
            onClick={handleSave}
            sx={{ mr: 2 }}
          >
            Save Changes
          </Button>
          <Button
            variant="outlined"
            onClick={() => setIsEditing(false)}
          >
            Cancel
          </Button>
        </Box>
      )}
    </Container>
  );
}
