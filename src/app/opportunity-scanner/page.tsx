'use client';

import React from 'react';
import { Container, Typography, Card, CardContent, Grid, <PERSON>ton, Box, Chip } from '@mui/material';
import { Search, Visibility, Star, TrendingUp } from '@mui/icons-material';

const opportunities = [
  { title: "Municipal IT Upgrade", value: "R2.4M", match: 94, deadline: "2024-01-15" },
  { title: "Construction Project", value: "R5.2M", match: 87, deadline: "2024-01-20" },
  { title: "Consulting Services", value: "R890K", match: 91, deadline: "2024-01-18" }
];

export default function OpportunityScannerPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
        Opportunity Scanner
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        AI-powered opportunity detection and matching
      </Typography>
      
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" fontWeight="bold" color="primary">
                25
              </Typography>
              <Typography variant="body2" color="text.secondary">
                New Opportunities
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" fontWeight="bold" color="success.main">
                89%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Avg Match Score
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>High-Match Opportunities</Typography>
          <Grid container spacing={3}>
            {opportunities.map((opp, index) => (
              <Grid item xs={12} md={4} key={index}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>{opp.title}</Typography>
                    <Typography variant="h5" color="primary.main" gutterBottom>
                      {opp.value}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                      <Chip 
                        label={`${opp.match}% match`}
                        color={opp.match > 90 ? 'success' : 'primary'}
                        size="small"
                      />
                      <Chip label={opp.deadline} color="info" size="small" />
                    </Box>
                    <Button variant="contained" size="small" startIcon={<Visibility />} fullWidth>
                      View Details
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>
    </Container>
  );
}
