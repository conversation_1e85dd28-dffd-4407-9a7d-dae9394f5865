'use client';

import React, { useState } from 'react';
import { 
  Con<PERSON><PERSON>, 
  <PERSON>, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  Alert,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TextField,
  InputAdornment
} from '@mui/material';
import { 
  FileText, 
  Plus, 
  Search, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  Edit,
  Eye,
  Send
} from 'lucide-react';

export default function RFQManagementPage() {
  const [searchTerm, setSearchTerm] = useState('');

  // Mock RFQ data
  const stats = {
    totalRFQs: 47,
    activeRFQs: 12,
    completedRFQs: 28,
    avgResponseTime: '2.3 days',
    responseRate: 78.5
  };

  const rfqs = [
    {
      id: 1,
      rfqNumber: 'RFQ-2024-001',
      title: 'IT Infrastructure Upgrade',
      description: 'Complete network infrastructure overhaul for municipal offices',
      category: 'IT Services',
      estimatedValue: 'R2.4M',
      status: 'Active',
      responses: 8,
      deadline: '2024-02-15',
      createdDate: '2024-01-15',
      priority: 'high'
    },
    {
      id: 2,
      rfqNumber: 'RFQ-2024-002',
      title: 'Security Services Contract',
      description: 'Comprehensive security services for government buildings',
      category: 'Security',
      estimatedValue: 'R1.2M',
      status: 'Under Review',
      responses: 12,
      deadline: '2024-02-10',
      createdDate: '2024-01-12',
      priority: 'medium'
    },
    {
      id: 3,
      rfqNumber: 'RFQ-2024-003',
      title: 'Consulting Services',
      description: 'Strategic planning and implementation consulting',
      category: 'Consulting',
      estimatedValue: 'R890K',
      status: 'Completed',
      responses: 15,
      deadline: '2024-01-30',
      createdDate: '2024-01-08',
      priority: 'low'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'success';
      case 'Under Review': return 'warning';
      case 'Completed': return 'info';
      case 'Cancelled': return 'error';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'info';
      default: return 'default';
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
          📋 RFQ Management
        </Typography>
        <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
          Create and manage Request for Quotations with 90-second challenge system
        </Typography>
        
        <Alert severity="info" sx={{ mb: 3 }}>
          <strong>RFQ System Active:</strong> 47 RFQs managed with 78.5% response rate and 2.3-day average response time.
        </Alert>
      </Box>

      {/* Quick Actions */}
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item>
          <Button variant="contained" startIcon={<Plus />} color="primary">
            Create New RFQ
          </Button>
        </Grid>
        <Grid item>
          <Button variant="outlined" startIcon={<FileText />}>
            RFQ Templates
          </Button>
        </Grid>
        <Grid item>
          <Button variant="outlined" startIcon={<Send />}>
            Bulk Send
          </Button>
        </Grid>
      </Grid>

      {/* Search Bar */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <TextField
            fullWidth
            placeholder="Search RFQs by title, category, or RFQ number..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search size={20} />
                </InputAdornment>
              ),
            }}
            sx={{ mb: 2 }}
          />
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            <Chip label="Active RFQs" clickable />
            <Chip label="IT Services" clickable />
            <Chip label="Security" clickable />
            <Chip label="Consulting" clickable />
            <Chip label="High Priority" clickable />
          </Box>
        </CardContent>
      </Card>

      {/* Stats Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <FileText size={32} color="#2196f3" />
              <Typography variant="h4" fontWeight="bold">{stats.totalRFQs}</Typography>
              <Typography variant="body2" color="text.secondary">Total RFQs</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Clock size={32} color="#4caf50" />
              <Typography variant="h4" fontWeight="bold">{stats.activeRFQs}</Typography>
              <Typography variant="body2" color="text.secondary">Active RFQs</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <CheckCircle size={32} color="#ff9800" />
              <Typography variant="h4" fontWeight="bold">{stats.completedRFQs}</Typography>
              <Typography variant="body2" color="text.secondary">Completed</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <AlertTriangle size={32} color="#9c27b0" />
              <Typography variant="h4" fontWeight="bold">{stats.avgResponseTime}</Typography>
              <Typography variant="body2" color="text.secondary">Avg Response</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Send size={32} color="#f44336" />
              <Typography variant="h4" fontWeight="bold">{stats.responseRate}%</Typography>
              <Typography variant="body2" color="text.secondary">Response Rate</Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* RFQ Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>RFQ Management Dashboard</Typography>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>RFQ Number</TableCell>
                  <TableCell>Title</TableCell>
                  <TableCell>Category</TableCell>
                  <TableCell>Estimated Value</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Responses</TableCell>
                  <TableCell>Deadline</TableCell>
                  <TableCell>Priority</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {rfqs.map((rfq) => (
                  <TableRow key={rfq.id}>
                    <TableCell>
                      <Typography variant="subtitle2" fontWeight="medium">
                        {rfq.rfqNumber}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="subtitle2">{rfq.title}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {rfq.description}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>{rfq.category}</TableCell>
                    <TableCell>
                      <Typography variant="subtitle2" color="success.main" fontWeight="bold">
                        {rfq.estimatedValue}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={rfq.status} 
                        color={getStatusColor(rfq.status) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="subtitle2">
                        {rfq.responses} responses
                      </Typography>
                    </TableCell>
                    <TableCell>{rfq.deadline}</TableCell>
                    <TableCell>
                      <Chip 
                        label={rfq.priority.toUpperCase()} 
                        color={getPriorityColor(rfq.priority) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Button size="small" startIcon={<Eye />} variant="outlined">
                          View
                        </Button>
                        <Button size="small" startIcon={<Edit />} variant="outlined">
                          Edit
                        </Button>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* 90-Second Challenge Feature */}
      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>⚡ 90-Second RFQ Challenge</Typography>
          <Alert severity="success" sx={{ mb: 2 }}>
            <strong>Speed Challenge:</strong> Create professional RFQs in under 90 seconds with our AI-powered wizard.
          </Alert>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Clock size={32} color="#4caf50" />
                <Typography variant="h6">Average Time</Typography>
                <Typography variant="h4" color="success.main">67s</Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <CheckCircle size={32} color="#2196f3" />
                <Typography variant="h6">Success Rate</Typography>
                <Typography variant="h4" color="primary.main">94%</Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Send size={32} color="#ff9800" />
                <Typography variant="h6">RFQs Created</Typography>
                <Typography variant="h4" color="warning.main">156</Typography>
              </Box>
            </Grid>
          </Grid>
          <Box sx={{ mt: 3, textAlign: 'center' }}>
            <Button variant="contained" size="large" startIcon={<Plus />}>
              Start 90-Second Challenge
            </Button>
          </Box>
        </CardContent>
      </Card>
    </Container>
  );
}
