'use client';

import React, { useState } from 'react';
import {
  Box,
  Container,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  Button,
  Stack,
  Alert,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Tab,
  Tabs,
  LinearProgress,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField
} from '@mui/material';
import {
  Security,
  LocalHospital,
  DirectionsCar,
  Phone,
  Assignment,
  CheckCircle,
  Warning,
  Info,
  AttachMoney,
  Description,
  ContactSupport,
  Emergency,
  Shield,
  HealthAndSafety,
  Gavel
} from '@mui/icons-material';

interface InsuranceCoverage {
  id: string;
  type: string;
  name: string;
  description: string;
  coverage: string;
  premium: number;
  deductible: number;
  status: 'active' | 'pending' | 'expired';
  validFrom: string;
  validTo: string;
  claimsThisYear: number;
  maxClaims: number;
}

interface SafetyRecord {
  totalTasks: number;
  safetyIncidents: number;
  safetyRating: number;
  lastIncident: string | null;
  safetyTraining: string[];
  certifications: string[];
}

export default function BeeInsurancePage() {
  const [currentTab, setCurrentTab] = useState(0);
  const [claimDialogOpen, setClaimDialogOpen] = useState(false);
  const [emergencyDialogOpen, setEmergencyDialogOpen] = useState(false);
  const [claimDescription, setClaimDescription] = useState('');

  const [insuranceCoverages, setInsuranceCoverages] = useState<InsuranceCoverage[]>([
    {
      id: 'coverage-001',
      type: 'liability',
      name: 'Professional Liability Insurance',
      description: 'Covers professional errors, omissions, and negligence during task execution',
      coverage: 'R2,000,000',
      premium: 150,
      deductible: 500,
      status: 'active',
      validFrom: '2024-01-01',
      validTo: '2024-12-31',
      claimsThisYear: 0,
      maxClaims: 3
    },
    {
      id: 'coverage-002',
      type: 'accident',
      name: 'Personal Accident Insurance',
      description: 'Covers medical expenses and compensation for work-related injuries',
      coverage: 'R500,000',
      premium: 85,
      deductible: 200,
      status: 'active',
      validFrom: '2024-01-01',
      validTo: '2024-12-31',
      claimsThisYear: 0,
      maxClaims: 5
    },
    {
      id: 'coverage-003',
      type: 'equipment',
      name: 'Equipment Protection',
      description: 'Covers damage or theft of work equipment (phone, camera, tools)',
      coverage: 'R50,000',
      premium: 45,
      deductible: 100,
      status: 'active',
      validFrom: '2024-01-01',
      validTo: '2024-12-31',
      claimsThisYear: 1,
      maxClaims: 2
    },
    {
      id: 'coverage-004',
      type: 'travel',
      name: 'Travel Insurance',
      description: 'Covers travel-related incidents during task execution',
      coverage: 'R100,000',
      premium: 35,
      deductible: 150,
      status: 'active',
      validFrom: '2024-01-01',
      validTo: '2024-12-31',
      claimsThisYear: 0,
      maxClaims: 3
    }
  ]);

  const [safetyRecord, setSafetyRecord] = useState<SafetyRecord>({
    totalTasks: 47,
    safetyIncidents: 0,
    safetyRating: 5.0,
    lastIncident: null,
    safetyTraining: [
      'Workplace Safety Fundamentals',
      'Site Safety Protocols',
      'Emergency Response Training',
      'First Aid Certification'
    ],
    certifications: [
      'First Aid Level 1',
      'Occupational Health & Safety',
      'Defensive Driving Certificate'
    ]
  });

  const emergencyContacts = [
    {
      name: 'BidBeez Emergency Hotline',
      number: '+27 86 243 2339',
      type: 'primary',
      available: '24/7'
    },
    {
      name: 'Medical Emergency',
      number: '10177',
      type: 'medical',
      available: '24/7'
    },
    {
      name: 'Police Emergency',
      number: '10111',
      type: 'police',
      available: '24/7'
    },
    {
      name: 'Insurance Claims Hotline',
      number: '+27 11 555 0123',
      type: 'insurance',
      available: 'Mon-Fri 8AM-6PM'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'pending': return 'warning';
      case 'expired': return 'error';
      default: return 'default';
    }
  };

  const getCoverageIcon = (type: string) => {
    switch (type) {
      case 'liability': return <Gavel />;
      case 'accident': return <LocalHospital />;
      case 'equipment': return <Security />;
      case 'travel': return <DirectionsCar />;
      default: return <Shield />;
    }
  };

  const totalPremium = insuranceCoverages.reduce((sum, coverage) => sum + coverage.premium, 0);
  const totalCoverage = insuranceCoverages.reduce((sum, coverage) => {
    const amount = parseInt(coverage.coverage.replace(/[R,]/g, ''));
    return sum + amount;
  }, 0);

  const handleFileClaim = () => {
    setClaimDialogOpen(false);
    setClaimDescription('');
    alert('Claim submitted successfully! You will receive a reference number via SMS.');
  };

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" fontWeight="bold" gutterBottom>
          🛡️ Insurance & Protection
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Your comprehensive protection coverage while working as a bee
        </Typography>
      </Box>

      {/* Coverage Summary */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Shield sx={{ fontSize: 32, color: 'success.main', mb: 1 }} />
              <Typography variant="h5" fontWeight="bold">
                R{totalCoverage.toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Coverage
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <AttachMoney sx={{ fontSize: 32, color: 'primary.main', mb: 1 }} />
              <Typography variant="h5" fontWeight="bold">
                R{totalPremium}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Monthly Premium
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <HealthAndSafety sx={{ fontSize: 32, color: 'warning.main', mb: 1 }} />
              <Typography variant="h5" fontWeight="bold">
                {safetyRecord.safetyRating}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Safety Rating
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <CheckCircle sx={{ fontSize: 32, color: 'success.main', mb: 1 }} />
              <Typography variant="h5" fontWeight="bold">
                {safetyRecord.totalTasks - safetyRecord.safetyIncidents}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Safe Tasks
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Emergency Alert */}
      <Alert severity="error" sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="body2">
            <strong>🚨 Emergency?</strong> Need immediate assistance during a task?
          </Typography>
          <Button
            variant="contained"
            color="error"
            startIcon={<Emergency />}
            onClick={() => setEmergencyDialogOpen(true)}
          >
            Emergency Help
          </Button>
        </Box>
      </Alert>

      {/* Insurance Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={currentTab} onChange={(e, newValue) => setCurrentTab(newValue)}>
          <Tab label="Coverage Details" />
          <Tab label="Safety Record" />
          <Tab label="Claims & Support" />
        </Tabs>
      </Box>

      {/* Tab Content */}
      {currentTab === 0 && (
        <Grid container spacing={3}>
          {insuranceCoverages.map((coverage) => (
            <Grid item xs={12} md={6} key={coverage.id}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                    <Avatar sx={{ bgcolor: 'primary.main' }}>
                      {getCoverageIcon(coverage.type)}
                    </Avatar>
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="h6" fontWeight="bold">
                        {coverage.name}
                      </Typography>
                      <Chip 
                        label={coverage.status.toUpperCase()}
                        color={getStatusColor(coverage.status) as any}
                        size="small"
                      />
                    </Box>
                  </Box>

                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {coverage.description}
                  </Typography>

                  <Grid container spacing={2} sx={{ mb: 2 }}>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">
                        Coverage Amount
                      </Typography>
                      <Typography variant="h6" color="success.main">
                        {coverage.coverage}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">
                        Monthly Premium
                      </Typography>
                      <Typography variant="h6">
                        R{coverage.premium}
                      </Typography>
                    </Grid>
                  </Grid>

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Claims Used This Year
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={(coverage.claimsThisYear / coverage.maxClaims) * 100}
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                    <Typography variant="caption" color="text.secondary">
                      {coverage.claimsThisYear} of {coverage.maxClaims} claims used
                    </Typography>
                  </Box>

                  <Typography variant="body2" color="text.secondary">
                    Valid: {new Date(coverage.validFrom).toLocaleDateString()} - {new Date(coverage.validTo).toLocaleDateString()}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {currentTab === 1 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  🏆 Safety Performance
                </Typography>
                
                <Stack spacing={3}>
                  <Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Safety Rating
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="h4" color="success.main">
                        {safetyRecord.safetyRating}/5.0
                      </Typography>
                      <LinearProgress 
                        variant="determinate" 
                        value={(safetyRecord.safetyRating / 5) * 100}
                        sx={{ flex: 1, height: 8, borderRadius: 4 }}
                      />
                    </Box>
                  </Box>

                  <Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Safety Record
                    </Typography>
                    <Typography variant="h5">
                      {safetyRecord.safetyIncidents} incidents in {safetyRecord.totalTasks} tasks
                    </Typography>
                    <Typography variant="body2" color="success.main">
                      {((1 - safetyRecord.safetyIncidents / safetyRecord.totalTasks) * 100).toFixed(1)}% safety rate
                    </Typography>
                  </Box>

                  {safetyRecord.lastIncident ? (
                    <Alert severity="warning">
                      Last incident: {new Date(safetyRecord.lastIncident).toLocaleDateString()}
                    </Alert>
                  ) : (
                    <Alert severity="success">
                      🎉 No safety incidents recorded! Excellent work!
                    </Alert>
                  )}
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  📚 Safety Training & Certifications
                </Typography>
                
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    Completed Training
                  </Typography>
                  <List dense>
                    {safetyRecord.safetyTraining.map((training, index) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          <CheckCircle color="success" />
                        </ListItemIcon>
                        <ListItemText primary={training} />
                      </ListItem>
                    ))}
                  </List>
                </Box>

                <Box>
                  <Typography variant="subtitle1" gutterBottom>
                    Active Certifications
                  </Typography>
                  <Stack spacing={1}>
                    {safetyRecord.certifications.map((cert, index) => (
                      <Chip 
                        key={index}
                        label={cert}
                        color="success"
                        icon={<CheckCircle />}
                      />
                    ))}
                  </Stack>
                </Box>

                <Button variant="outlined" fullWidth sx={{ mt: 2 }}>
                  View Training Schedule
                </Button>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {currentTab === 2 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  📋 File Insurance Claim
                </Typography>
                
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  If you've experienced an incident during a task, file a claim here.
                </Typography>

                <Stack spacing={2}>
                  <Button
                    variant="contained"
                    startIcon={<Description />}
                    onClick={() => setClaimDialogOpen(true)}
                    fullWidth
                  >
                    File New Claim
                  </Button>
                  
                  <Button
                    variant="outlined"
                    startIcon={<Assignment />}
                    fullWidth
                  >
                    Track Existing Claims
                  </Button>
                  
                  <Button
                    variant="outlined"
                    startIcon={<ContactSupport />}
                    fullWidth
                  >
                    Contact Insurance Support
                  </Button>
                </Stack>

                <Alert severity="info" sx={{ mt: 3 }}>
                  <Typography variant="body2">
                    💡 Claims are typically processed within 5-7 business days. You'll receive updates via SMS and email.
                  </Typography>
                </Alert>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  📞 Emergency Contacts
                </Typography>
                
                <List>
                  {emergencyContacts.map((contact, index) => (
                    <React.Fragment key={index}>
                      <ListItem>
                        <ListItemIcon>
                          <Phone color="primary" />
                        </ListItemIcon>
                        <ListItemText
                          primary={contact.name}
                          secondary={
                            <Box>
                              <Typography variant="body2" fontWeight="bold">
                                {contact.number}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {contact.available}
                              </Typography>
                            </Box>
                          }
                        />
                        <Button
                          size="small"
                          variant="outlined"
                          href={`tel:${contact.number}`}
                        >
                          Call
                        </Button>
                      </ListItem>
                      {index < emergencyContacts.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* File Claim Dialog */}
      <Dialog open={claimDialogOpen} onClose={() => setClaimDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>📋 File Insurance Claim</DialogTitle>
        <DialogContent>
          <Typography variant="body1" gutterBottom>
            Please provide details about the incident:
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={6}
            placeholder="Describe what happened, when it occurred, and any damages or injuries..."
            value={claimDescription}
            onChange={(e) => setClaimDescription(e.target.value)}
            sx={{ mt: 2 }}
          />
          <Alert severity="info" sx={{ mt: 2 }}>
            <Typography variant="body2">
              You may be contacted for additional information or documentation.
            </Typography>
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setClaimDialogOpen(false)}>
            Cancel
          </Button>
          <Button 
            variant="contained" 
            onClick={handleFileClaim}
            disabled={!claimDescription.trim()}
          >
            Submit Claim
          </Button>
        </DialogActions>
      </Dialog>

      {/* Emergency Dialog */}
      <Dialog open={emergencyDialogOpen} onClose={() => setEmergencyDialogOpen(false)}>
        <DialogTitle>🚨 Emergency Assistance</DialogTitle>
        <DialogContent>
          <Typography variant="body1" gutterBottom>
            Choose the type of emergency assistance you need:
          </Typography>
          <Stack spacing={2} sx={{ mt: 2 }}>
            <Button
              variant="contained"
              color="error"
              startIcon={<LocalHospital />}
              href="tel:10177"
              fullWidth
            >
              Medical Emergency (10177)
            </Button>
            <Button
              variant="contained"
              color="error"
              startIcon={<Phone />}
              href="tel:10111"
              fullWidth
            >
              Police Emergency (10111)
            </Button>
            <Button
              variant="contained"
              startIcon={<ContactSupport />}
              href="tel:+27862432339"
              fullWidth
            >
              BidBeez Emergency Hotline
            </Button>
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEmergencyDialogOpen(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
}
