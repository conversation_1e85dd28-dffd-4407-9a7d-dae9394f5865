'use client';

import React from 'react';
import { Container, Typo<PERSON>, Card, CardContent, Grid, Button, Box, TextField, Stepper, Step, StepLabel } from '@mui/material';
import { Send, AttachFile, Preview, CheckCircle } from '@mui/icons-material';

const steps = ['Bid Details', 'Documents', 'Review', 'Submit'];

export default function BidSubmissionPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
        Bid Submission
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        Submit your tender bid with confidence
      </Typography>
      
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Stepper activeStep={0} alternativeLabel>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </CardContent>
      </Card>

      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Bid Information</Typography>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField fullWidth label="Tender Reference" variant="outlined" />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField fullWidth label="Bid Amount" variant="outlined" type="number" />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField fullWidth label="Completion Period" variant="outlined" />
                </Grid>
                <Grid item xs={12}>
                  <TextField fullWidth multiline rows={4} label="Bid Description" variant="outlined" />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Quick Actions</Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Button variant="contained" startIcon={<AttachFile />}>
                  Upload Documents
                </Button>
                <Button variant="outlined" startIcon={<Preview />}>
                  Preview Bid
                </Button>
                <Button variant="outlined" startIcon={<CheckCircle />}>
                  Validate Submission
                </Button>
                <Button variant="contained" color="success" startIcon={<Send />}>
                  Submit Bid
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
}
