'use client';

import React from 'react';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Avatar,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  LinearProgress,
  Rating,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper
} from '@mui/material';
import {
  Business,
  Star,
  TrendingUp,
  Assignment,
  CheckCircle,
  Schedule,
  AccountBalance,
  Verified,
  LocalShipping,
  Group,
  AttachMoney,
  Timeline
} from '@mui/icons-material';

const recentOrders = [
  { id: 1, tender: "Municipal IT Equipment", amount: "R450,000", status: "completed", date: "2024-01-10" },
  { id: 2, tender: "Office Supplies Contract", amount: "R125,000", status: "in_progress", date: "2024-01-08" },
  { id: 3, tender: "Construction Materials", amount: "R890,000", status: "pending", date: "2024-01-05" }
];

export default function SupplierDashboardPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
          Supplier Dashboard
        </Typography>
        <Typography variant="h6" color="text.secondary">
          Manage your supplier profile and track business performance
        </Typography>
      </Box>

      {/* Supplier Profile Card */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Grid container spacing={3} alignItems="center">
            <Grid item>
              <Avatar sx={{ width: 80, height: 80, bgcolor: 'primary.main' }}>
                <Business sx={{ fontSize: 40 }} />
              </Avatar>
            </Grid>
            <Grid item xs>
              <Typography variant="h5" fontWeight="bold" gutterBottom>
                TechSolutions SA (Pty) Ltd
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                <Chip icon={<Verified />} label="Verified Supplier" color="success" />
                <Chip icon={<AccountBalance />} label="B-BBEE Level 2" color="primary" />
                <Chip icon={<Star />} label="Premium Partner" color="warning" />
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Rating value={4.8} precision={0.1} readOnly />
                <Typography variant="body2" color="text.secondary">
                  4.8/5 (127 reviews)
                </Typography>
              </Box>
            </Grid>
            <Grid item>
              <Button variant="contained" startIcon={<Business />}>
                Edit Profile
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="primary">
                    R2.4M
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Revenue
                  </Typography>
                </Box>
                <AttachMoney sx={{ fontSize: 40, color: 'primary.main' }} />
              </Box>
              <Box sx={{ mt: 2 }}>
                <Chip label="+15% this month" color="success" size="small" />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="success.main">
                    34
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Completed Orders
                  </Typography>
                </Box>
                <CheckCircle sx={{ fontSize: 40, color: 'success.main' }} />
              </Box>
              <Box sx={{ mt: 2 }}>
                <Chip label="98% completion rate" color="success" size="small" />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="warning.main">
                    8
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active Contracts
                  </Typography>
                </Box>
                <Assignment sx={{ fontSize: 40, color: 'warning.main' }} />
              </Box>
              <Box sx={{ mt: 2 }}>
                <Chip label="R890K value" color="warning" size="small" />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="info.main">
                    4.8
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Avg Rating
                  </Typography>
                </Box>
                <Star sx={{ fontSize: 40, color: 'info.main' }} />
              </Box>
              <Box sx={{ mt: 2 }}>
                <Chip label="Top 5% suppliers" color="info" size="small" />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Recent Orders */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Orders
              </Typography>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Tender</TableCell>
                      <TableCell>Amount</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Date</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {recentOrders.map((order) => (
                      <TableRow key={order.id}>
                        <TableCell>{order.tender}</TableCell>
                        <TableCell fontWeight="bold">{order.amount}</TableCell>
                        <TableCell>
                          <Chip
                            label={order.status.replace('_', ' ')}
                            color={
                              order.status === 'completed' ? 'success' :
                              order.status === 'in_progress' ? 'warning' : 'default'
                            }
                            size="small"
                          />
                        </TableCell>
                        <TableCell>{order.date}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Performance Metrics */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Performance Metrics
              </Typography>
              <List>
                <ListItem>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: 'success.main' }}>
                      <Timeline />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary="Delivery Performance"
                    secondary={
                      <Box>
                        <LinearProgress variant="determinate" value={96} color="success" />
                        <Typography variant="caption">96% on-time delivery</Typography>
                      </Box>
                    }
                  />
                </ListItem>
                <Divider variant="inset" component="li" />
                <ListItem>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: 'primary.main' }}>
                      <Group />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary="Customer Satisfaction"
                    secondary={
                      <Box>
                        <LinearProgress variant="determinate" value={94} color="primary" />
                        <Typography variant="caption">4.8/5 average rating</Typography>
                      </Box>
                    }
                  />
                </ListItem>
                <Divider variant="inset" component="li" />
                <ListItem>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: 'warning.main' }}>
                      <LocalShipping />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary="Quality Score"
                    secondary={
                      <Box>
                        <LinearProgress variant="determinate" value={92} color="warning" />
                        <Typography variant="caption">92% quality compliance</Typography>
                      </Box>
                    }
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Quick Actions */}
      <Box sx={{ mt: 4, textAlign: 'center' }}>
        <Typography variant="h6" gutterBottom>
          Quick Actions
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
          <Button variant="contained" startIcon={<Assignment />}>
            View Available Tenders
          </Button>
          <Button variant="outlined" startIcon={<Business />}>
            Update Company Profile
          </Button>
          <Button variant="outlined" startIcon={<Verified />}>
            Manage Certifications
          </Button>
          <Button variant="outlined" startIcon={<TrendingUp />}>
            View Analytics
          </Button>
        </Box>
      </Box>
    </Container>
  );
}
