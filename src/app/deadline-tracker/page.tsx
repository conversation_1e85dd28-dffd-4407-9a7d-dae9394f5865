'use client';

import React from 'react';
import { Container, Typography, Card, CardContent, Grid, Box, Chip, Table, TableBody, TableCell, TableHead, TableRow } from '@mui/material';
import { Schedule, Warning, CheckCircle } from '@mui/icons-material';

const deadlines = [
  { task: "Municipal IT Tender", deadline: "2024-01-15", status: "Urgent", daysLeft: 3 },
  { task: "Construction Bid Review", deadline: "2024-01-20", status: "Normal", daysLeft: 8 },
  { task: "Compliance Report", deadline: "2024-01-25", status: "Normal", daysLeft: 13 }
];

export default function DeadlineTrackerPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
        Deadline Tracker
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        Monitor and manage all important deadlines
      </Typography>
      
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="error.main">
                    3
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Urgent Deadlines
                  </Typography>
                </Box>
                <Warning sx={{ fontSize: 40, color: 'error.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="primary">
                    12
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Active
                  </Typography>
                </Box>
                <Schedule sx={{ fontSize: 40, color: 'primary.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Upcoming Deadlines</Typography>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Task</TableCell>
                <TableCell>Deadline</TableCell>
                <TableCell>Days Left</TableCell>
                <TableCell>Priority</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {deadlines.map((item, index) => (
                <TableRow key={index}>
                  <TableCell>{item.task}</TableCell>
                  <TableCell>{item.deadline}</TableCell>
                  <TableCell>{item.daysLeft} days</TableCell>
                  <TableCell>
                    <Chip 
                      label={item.status}
                      color={item.status === 'Urgent' ? 'error' : 'primary'}
                      size="small"
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </Container>
  );
}
