'use client';

import React, { useState } from 'react';
import {
  Box,
  Container,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Tabs,
  Tab,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Stack,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  LinearProgress,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Analytics,
  TrendingUp,
  Assessment,
  PieChart,
  BarChart,
  Timeline,
  Download,
  Share,
  Refresh,
  FilterList,
  DateRange,
  Psychology,
  Speed,
  Target,
  MonetizationOn
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, ResponsiveContainer, AreaChart, Area, <PERSON><PERSON>hart as RechartsPieChart, Cell, BarChart as RechartsBarChart, Bar } from 'recharts';

interface AnalyticsData {
  period: string;
  bids: number;
  wins: number;
  revenue: number;
  winRate: number;
  avgBidValue: number;
  psychScore: number;
}

const mockAnalyticsData: AnalyticsData[] = [
  { period: 'Jan', bids: 12, wins: 4, revenue: 2400000, winRate: 33.3, avgBidValue: 600000, psychScore: 72 },
  { period: 'Feb', bids: 15, wins: 6, revenue: 3200000, winRate: 40.0, avgBidValue: 533333, psychScore: 78 },
  { period: 'Mar', bids: 18, wins: 8, revenue: 4100000, winRate: 44.4, avgBidValue: 512500, psychScore: 82 },
  { period: 'Apr', bids: 22, wins: 9, revenue: 4800000, winRate: 40.9, avgBidValue: 533333, psychScore: 85 },
  { period: 'May', bids: 25, wins: 12, revenue: 6200000, winRate: 48.0, avgBidValue: 516667, psychScore: 88 },
  { period: 'Jun', bids: 28, wins: 15, revenue: 7500000, winRate: 53.6, avgBidValue: 500000, psychScore: 91 }
];

const categoryData = [
  { name: 'Construction', value: 35, color: '#8884d8' },
  { name: 'IT Services', value: 25, color: '#82ca9d' },
  { name: 'Consulting', value: 20, color: '#ffc658' },
  { name: 'Healthcare', value: 12, color: '#ff7300' },
  { name: 'Other', value: 8, color: '#00ff00' }
];

export default function AdvancedAnalyticsPage() {
  const [activeTab, setActiveTab] = useState(0);
  const [timeRange, setTimeRange] = useState('6months');
  const [selectedMetric, setSelectedMetric] = useState('revenue');

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
            📊 Advanced Analytics
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Deep insights into your bidding performance and psychological optimization
          </Typography>
        </Box>
        
        <Stack direction="row" spacing={2}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Time Range</InputLabel>
            <Select
              value={timeRange}
              label="Time Range"
              onChange={(e) => setTimeRange(e.target.value)}
            >
              <MenuItem value="1month">1 Month</MenuItem>
              <MenuItem value="3months">3 Months</MenuItem>
              <MenuItem value="6months">6 Months</MenuItem>
              <MenuItem value="1year">1 Year</MenuItem>
              <MenuItem value="all">All Time</MenuItem>
            </Select>
          </FormControl>
          
          <Button variant="outlined" startIcon={<Download />}>
            Export Report
          </Button>
          
          <Button variant="contained" startIcon={<Refresh />}>
            Refresh Data
          </Button>
        </Stack>
      </Box>

      {/* Key Metrics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" color="primary" gutterBottom>
                    {mockAnalyticsData[mockAnalyticsData.length - 1].winRate.toFixed(1)}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Win Rate
                  </Typography>
                </Box>
                <Target color="primary" sx={{ fontSize: 40 }} />
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={mockAnalyticsData[mockAnalyticsData.length - 1].winRate} 
                sx={{ mt: 1 }}
                color="primary"
              />
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" color="success.main" gutterBottom>
                    {formatCurrency(mockAnalyticsData.reduce((sum, item) => sum + item.revenue, 0))}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Revenue
                  </Typography>
                </Box>
                <MonetizationOn color="success" sx={{ fontSize: 40 }} />
              </Box>
              <Typography variant="caption" color="success.main">
                +28% from last period
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" color="info.main" gutterBottom>
                    {mockAnalyticsData.reduce((sum, item) => sum + item.bids, 0)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Bids
                  </Typography>
                </Box>
                <Assessment color="info" sx={{ fontSize: 40 }} />
              </Box>
              <Typography variant="caption" color="info.main">
                {mockAnalyticsData[mockAnalyticsData.length - 1].bids} this month
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" color="warning.main" gutterBottom>
                    {mockAnalyticsData[mockAnalyticsData.length - 1].psychScore}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Psych Score
                  </Typography>
                </Box>
                <Psychology color="warning" sx={{ fontSize: 40 }} />
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={mockAnalyticsData[mockAnalyticsData.length - 1].psychScore} 
                sx={{ mt: 1 }}
                color="warning"
              />
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Analytics Tabs */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={activeTab} onChange={handleTabChange}>
            <Tab label="Performance Trends" icon={<TrendingUp />} />
            <Tab label="Category Analysis" icon={<PieChart />} />
            <Tab label="Psychological Insights" icon={<Psychology />} />
            <Tab label="Competitive Analysis" icon={<Speed />} />
          </Tabs>
        </Box>

        <CardContent>
          {/* Performance Trends Tab */}
          {activeTab === 0 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Performance Trends Over Time
              </Typography>
              
              <Box sx={{ height: 400, mt: 2 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={mockAnalyticsData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="period" />
                    <YAxis />
                    <Area 
                      type="monotone" 
                      dataKey="revenue" 
                      stroke="#8884d8" 
                      fill="#8884d8" 
                      fillOpacity={0.3}
                    />
                    <Area 
                      type="monotone" 
                      dataKey="winRate" 
                      stroke="#82ca9d" 
                      fill="#82ca9d" 
                      fillOpacity={0.3}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </Box>
              
              <Grid container spacing={3} sx={{ mt: 3 }}>
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="subtitle1" gutterBottom>
                      Key Performance Indicators
                    </Typography>
                    <Stack spacing={2}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body2">Average Win Rate:</Typography>
                        <Typography variant="body2" fontWeight="bold">42.7%</Typography>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body2">Revenue Growth:</Typography>
                        <Typography variant="body2" fontWeight="bold" color="success.main">+212%</Typography>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body2">Bid Volume Growth:</Typography>
                        <Typography variant="body2" fontWeight="bold" color="info.main">+133%</Typography>
                      </Box>
                    </Stack>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="subtitle1" gutterBottom>
                      Psychological Optimization Impact
                    </Typography>
                    <Stack spacing={2}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body2">Stress Reduction:</Typography>
                        <Typography variant="body2" fontWeight="bold" color="success.main">-34%</Typography>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body2">Confidence Increase:</Typography>
                        <Typography variant="body2" fontWeight="bold" color="primary.main">+26%</Typography>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="body2">Decision Speed:</Typography>
                        <Typography variant="body2" fontWeight="bold" color="warning.main">+45%</Typography>
                      </Box>
                    </Stack>
                  </Paper>
                </Grid>
              </Grid>
            </Box>
          )}

          {/* Category Analysis Tab */}
          {activeTab === 1 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Bid Distribution by Category
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Box sx={{ height: 300 }}>
                    <ResponsiveContainer width="100%" height="100%">
                      <RechartsPieChart>
                        <RechartsPieChart data={categoryData} cx="50%" cy="50%" outerRadius={80}>
                          {categoryData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </RechartsPieChart>
                      </RechartsPieChart>
                    </ResponsiveContainer>
                  </Box>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <TableContainer component={Paper}>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Category</TableCell>
                          <TableCell align="right">Percentage</TableCell>
                          <TableCell align="right">Win Rate</TableCell>
                          <TableCell align="right">Avg Value</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {categoryData.map((row) => (
                          <TableRow key={row.name}>
                            <TableCell>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Box 
                                  sx={{ 
                                    width: 12, 
                                    height: 12, 
                                    backgroundColor: row.color,
                                    borderRadius: '50%'
                                  }} 
                                />
                                {row.name}
                              </Box>
                            </TableCell>
                            <TableCell align="right">{row.value}%</TableCell>
                            <TableCell align="right">
                              {(Math.random() * 30 + 30).toFixed(1)}%
                            </TableCell>
                            <TableCell align="right">
                              {formatCurrency(Math.random() * 2000000 + 500000)}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Grid>
              </Grid>
            </Box>
          )}

          {/* Psychological Insights Tab */}
          {activeTab === 2 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Psychological Performance Analysis
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={8}>
                  <Box sx={{ height: 300 }}>
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={mockAnalyticsData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="period" />
                        <YAxis />
                        <Line 
                          type="monotone" 
                          dataKey="psychScore" 
                          stroke="#ff7300" 
                          strokeWidth={3}
                          name="Psychological Score"
                        />
                        <Line 
                          type="monotone" 
                          dataKey="winRate" 
                          stroke="#8884d8" 
                          strokeWidth={2}
                          name="Win Rate"
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </Box>
                </Grid>
                
                <Grid item xs={12} md={4}>
                  <Stack spacing={2}>
                    <Paper sx={{ p: 2 }}>
                      <Typography variant="subtitle2" gutterBottom>
                        🧠 Cognitive Load Optimization
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Your cognitive load has decreased by 23% through adaptive interface optimization.
                      </Typography>
                      <LinearProgress variant="determinate" value={77} sx={{ mt: 1 }} />
                    </Paper>
                    
                    <Paper sx={{ p: 2 }}>
                      <Typography variant="subtitle2" gutterBottom>
                        😌 Stress Management
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Stress levels reduced by 34% through psychological triggers and timing optimization.
                      </Typography>
                      <LinearProgress variant="determinate" value={66} color="success" sx={{ mt: 1 }} />
                    </Paper>
                    
                    <Paper sx={{ p: 2 }}>
                      <Typography variant="subtitle2" gutterBottom>
                        🎯 Decision Confidence
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Decision confidence increased by 26% through behavioral coaching.
                      </Typography>
                      <LinearProgress variant="determinate" value={84} color="warning" sx={{ mt: 1 }} />
                    </Paper>
                  </Stack>
                </Grid>
              </Grid>
            </Box>
          )}

          {/* Competitive Analysis Tab */}
          {activeTab === 3 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Competitive Intelligence & Market Position
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Paper sx={{ p: 3 }}>
                    <Typography variant="subtitle1" gutterBottom>
                      Market Position Analysis
                    </Typography>
                    <Typography variant="body2" color="text.secondary" paragraph>
                      Based on AI analysis of 2,847 similar bidders in your market segment:
                    </Typography>
                    
                    <Grid container spacing={3}>
                      <Grid item xs={12} md={4}>
                        <Box sx={{ textAlign: 'center' }}>
                          <Typography variant="h3" color="success.main">
                            Top 15%
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Market Position
                          </Typography>
                        </Box>
                      </Grid>
                      
                      <Grid item xs={12} md={4}>
                        <Box sx={{ textAlign: 'center' }}>
                          <Typography variant="h3" color="primary.main">
                            92.3%
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Competitive Score
                          </Typography>
                        </Box>
                      </Grid>
                      
                      <Grid item xs={12} md={4}>
                        <Box sx={{ textAlign: 'center' }}>
                          <Typography variant="h3" color="warning.main">
                            +47%
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Above Average
                          </Typography>
                        </Box>
                      </Grid>
                    </Grid>
                  </Paper>
                </Grid>
              </Grid>
            </Box>
          )}
        </CardContent>
      </Card>
    </Container>
  );
}
