'use client';

import React, { useState } from 'react';
import {
  <PERSON>,
  Container,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Grid,
  TextField,
  Chip,
  Stack,
  Divider,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Search,
  FilterList,
  Download,
  Visibility,
  Star,
  StarBorder,
  LocationOn,
  AttachMoney,
  Schedule,
  Business
} from '@mui/icons-material';

interface GovernmentRFQ {
  id: string;
  title: string;
  department: string;
  reference: string;
  value: number;
  publishDate: string;
  closingDate: string;
  location: string;
  category: string;
  status: 'open' | 'closing_soon' | 'closed';
  isBookmarked: boolean;
}

const mockRFQs: GovernmentRFQ[] = [
  {
    id: 'rfq-001',
    title: 'Supply and Installation of IT Equipment',
    department: 'Department of Home Affairs',
    reference: 'DHA/IT/2024/001',
    value: 2500000,
    publishDate: '2024-01-15',
    closingDate: '2024-02-15',
    location: 'Pretoria, Gauteng',
    category: 'IT & Technology',
    status: 'open',
    isBookmarked: true
  },
  {
    id: 'rfq-002',
    title: 'Construction of Government Office Building',
    department: 'Department of Public Works',
    reference: 'DPW/CONST/2024/007',
    value: 15000000,
    publishDate: '2024-01-20',
    closingDate: '2024-02-28',
    location: 'Cape Town, Western Cape',
    category: 'Construction',
    status: 'open',
    isBookmarked: false
  },
  {
    id: 'rfq-003',
    title: 'Medical Equipment Procurement',
    department: 'Department of Health',
    reference: 'DOH/MED/2024/012',
    value: 8500000,
    publishDate: '2024-01-25',
    closingDate: '2024-02-10',
    location: 'Johannesburg, Gauteng',
    category: 'Healthcare',
    status: 'closing_soon',
    isBookmarked: true
  }
];

export default function GovernmentRFQsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [rfqs, setRfqs] = useState<GovernmentRFQ[]>(mockRFQs);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'success';
      case 'closing_soon': return 'warning';
      case 'closed': return 'error';
      default: return 'default';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'open': return 'Open';
      case 'closing_soon': return 'Closing Soon';
      case 'closed': return 'Closed';
      default: return status;
    }
  };

  const toggleBookmark = (id: string) => {
    setRfqs(prev => prev.map(rfq => 
      rfq.id === id ? { ...rfq, isBookmarked: !rfq.isBookmarked } : rfq
    ));
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
        Government RFQs
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        Official government requests for quotations and tenders
      </Typography>

      {/* Search and Filters */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Search RFQs"
                variant="outlined"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                select
                label="Category"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                SelectProps={{ native: true }}
              >
                <option value="all">All Categories</option>
                <option value="construction">Construction</option>
                <option value="it">IT & Technology</option>
                <option value="healthcare">Healthcare</option>
                <option value="consulting">Consulting</option>
              </TextField>
            </Grid>
            <Grid item xs={12} md={3}>
              <Button
                variant="outlined"
                fullWidth
                startIcon={<FilterList />}
                sx={{ height: 56 }}
              >
                Advanced Filters
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Statistics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" color="primary" gutterBottom>
                {rfqs.filter(rfq => rfq.status === 'open').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Open RFQs
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" color="warning.main" gutterBottom>
                {rfqs.filter(rfq => rfq.status === 'closing_soon').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Closing Soon
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" color="success.main" gutterBottom>
                {formatCurrency(rfqs.reduce((sum, rfq) => sum + rfq.value, 0))}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Value
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" color="info.main" gutterBottom>
                {rfqs.filter(rfq => rfq.isBookmarked).length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Bookmarked
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* RFQ Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Available RFQs
          </Typography>
          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Title</TableCell>
                  <TableCell>Department</TableCell>
                  <TableCell>Value</TableCell>
                  <TableCell>Closing Date</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {rfqs.map((rfq) => (
                  <TableRow key={rfq.id} hover>
                    <TableCell>
                      <Box>
                        <Typography variant="subtitle2" gutterBottom>
                          {rfq.title}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {rfq.reference}
                        </Typography>
                        <br />
                        <Chip 
                          label={rfq.category} 
                          size="small" 
                          variant="outlined" 
                          sx={{ mt: 0.5 }}
                        />
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Business fontSize="small" color="action" />
                        <Typography variant="body2">{rfq.department}</Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                        <LocationOn fontSize="small" color="action" />
                        <Typography variant="caption" color="text.secondary">
                          {rfq.location}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="subtitle2" color="success.main">
                        {formatCurrency(rfq.value)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Schedule fontSize="small" color="action" />
                        <Typography variant="body2">{rfq.closingDate}</Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={getStatusLabel(rfq.status)}
                        color={getStatusColor(rfq.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Stack direction="row" spacing={1}>
                        <Tooltip title={rfq.isBookmarked ? "Remove Bookmark" : "Add Bookmark"}>
                          <IconButton 
                            size="small"
                            onClick={() => toggleBookmark(rfq.id)}
                          >
                            {rfq.isBookmarked ? <Star color="primary" /> : <StarBorder />}
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="View Details">
                          <IconButton size="small">
                            <Visibility />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Download Documents">
                          <IconButton size="small">
                            <Download />
                          </IconButton>
                        </Tooltip>
                      </Stack>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      <Alert severity="info" sx={{ mt: 3 }}>
        <Typography variant="body2">
          <strong>Note:</strong> All RFQs are sourced from official government portals. 
          Make sure to verify requirements and deadlines before submitting proposals.
        </Typography>
      </Alert>
    </Container>
  );
}
