'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  TextField,
  <PERSON>ert,
  <PERSON><PERSON>,
  <PERSON>,
  Step<PERSON>abel,
  Divider,
  Chip,
  LinearProgress,
  Stack,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  Send,
  AttachFile,
  CheckCircle,
  Warning,
  Info,
  TrendingUp,
  Schedule,
  LocationOn,
  Business,
  AttachMoney
} from '@mui/icons-material';
import { useParams, useRouter } from 'next/navigation';

interface GovernmentRFQ {
  id: string;
  rfq_id: string;
  title: string;
  description: string;
  organization: string;
  estimated_value: number;
  closing_date: string;
  category: string;
  province: string;
  technical_requirements: any;
  commercial_requirements: any;
  evaluation_criteria: any;
}

interface RFQResponse {
  total_quote_amount: number;
  currency: string;
  delivery_timeframe: string;
  payment_terms: string;
  warranty_terms: string;
  technical_proposal: any;
  compliance_documents: string[];
}

const GovernmentRFQResponsePage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const rfqId = params.id as string;
  
  // State management
  const [rfq, setRfq] = useState<GovernmentRFQ | null>(null);
  const [response, setResponse] = useState<RFQResponse>({
    total_quote_amount: 0,
    currency: 'ZAR',
    delivery_timeframe: '',
    payment_terms: '',
    warranty_terms: '',
    technical_proposal: {},
    compliance_documents: []
  });
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [portfolioImpact, setPortfolioImpact] = useState<any>(null);

  const steps = [
    'RFQ Details',
    'Quote Information',
    'Technical Proposal',
    'Compliance & Submit'
  ];

  // Load RFQ details
  useEffect(() => {
    loadRFQDetails();
    calculatePortfolioImpact();
  }, [rfqId]);

  const loadRFQDetails = async () => {
    try {
      setLoading(true);
      
      const response = await fetch(`/api/government-rfqs/${rfqId}`);
      
      if (response.ok) {
        const data = await response.json();
        setRfq(data);
      } else {
        console.error('Failed to load RFQ details');
      }
    } catch (error) {
      console.error('Error loading RFQ:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculatePortfolioImpact = async () => {
    try {
      const response = await fetch('/api/portfolio/balance');
      
      if (response.ok) {
        const data = await response.json();
        
        // Calculate impact of this RFQ response
        const currentRfqRatio = data.balance.current_rfq_ratio;
        const totalActivities = data.balance.total_activities;
        const newRfqRatio = ((currentRfqRatio * totalActivities) + 1) / (totalActivities + 1) * 100;
        
        setPortfolioImpact({
          current_ratio: currentRfqRatio,
          new_ratio: newRfqRatio,
          improvement: newRfqRatio - currentRfqRatio,
          moves_toward_target: newRfqRatio <= 60 // Target is 60% RFQ
        });
      }
    } catch (error) {
      console.error('Error calculating portfolio impact:', error);
    }
  };

  const handleNext = () => {
    setActiveStep(prev => prev + 1);
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  const handleSubmitResponse = async () => {
    try {
      setSubmitting(true);
      
      const submitResponse = await fetch(`/api/government-rfqs/${rfqId}/respond`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(response)
      });
      
      if (submitResponse.ok) {
        const result = await submitResponse.json();
        
        // Show success and redirect
        router.push('/opportunities', {
          state: {
            success: true,
            message: `Government RFQ response submitted successfully! Response ID: ${result.response_id}`,
            portfolioUpdate: portfolioImpact
          }
        });
      } else {
        console.error('Failed to submit response');
      }
    } catch (error) {
      console.error('Error submitting response:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const formatCurrency = (value: number) => {
    if (value >= 1000000) return `R${(value / 1000000).toFixed(1)}M`;
    if (value >= 1000) return `R${(value / 1000).toFixed(0)}k`;
    return `R${value}`;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) return 'CLOSED';
    if (diffDays === 0) return 'TODAY';
    if (diffDays === 1) return 'TOMORROW';
    return `${diffDays} DAYS LEFT`;
  };

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <LinearProgress />
        <Typography variant="body2" sx={{ mt: 2, textAlign: 'center' }}>
          Loading Government RFQ details...
        </Typography>
      </Box>
    );
  }

  if (!rfq) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h6" color="error">
          Government RFQ not found
        </Typography>
        <Button onClick={() => router.push('/opportunities')} sx={{ mt: 2 }}>
          Back to Opportunities
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3, maxWidth: 1200, mx: 'auto' }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold' }}>
          🏛️ Government RFQ Response
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Submit your quote for government RFQ: {rfq.rfq_id}
        </Typography>
      </Box>

      {/* Portfolio Impact Alert */}
      {portfolioImpact && (
        <Alert 
          severity="success" 
          sx={{ mb: 3 }}
          icon={<TrendingUp />}
        >
          <Typography variant="body2">
            <strong>Portfolio Impact:</strong> This RFQ response will improve your RFQ ratio from {portfolioImpact.current_ratio.toFixed(1)}% to {portfolioImpact.new_ratio.toFixed(1)}%
            {portfolioImpact.moves_toward_target && (
              <span style={{ color: '#4CAF50', marginLeft: 8 }}>
                ✅ Moves toward optimal 60% RFQ target
              </span>
            )}
          </Typography>
        </Alert>
      )}

      {/* Success Rate Alert */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          🚀 <strong>Government RFQs have 88% success rate</strong> - Higher than regular tenders (75%)
        </Typography>
      </Alert>

      {/* Stepper */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Stepper activeStep={activeStep} alternativeLabel>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </CardContent>
      </Card>

      {/* Step Content */}
      <Grid container spacing={3}>
        {/* Main Content */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              {activeStep === 0 && (
                <Box>
                  <Typography variant="h6" gutterBottom>
                    RFQ Details Review
                  </Typography>
                  
                  <Grid container spacing={2} sx={{ mb: 3 }}>
                    <Grid item xs={12}>
                      <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 2 }}>
                        {rfq.title}
                      </Typography>
                    </Grid>
                    
                    <Grid item xs={6}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Business fontSize="small" color="action" />
                        <Typography variant="body2">
                          {rfq.organization}
                        </Typography>
                      </Box>
                    </Grid>
                    
                    <Grid item xs={6}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <AttachMoney fontSize="small" color="action" />
                        <Typography variant="body2">
                          {formatCurrency(rfq.estimated_value)}
                        </Typography>
                      </Box>
                    </Grid>
                    
                    <Grid item xs={6}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <LocationOn fontSize="small" color="action" />
                        <Typography variant="body2">
                          {rfq.province}
                        </Typography>
                      </Box>
                    </Grid>
                    
                    <Grid item xs={6}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Schedule fontSize="small" color="action" />
                        <Typography variant="body2">
                          Closes: {formatDate(rfq.closing_date)}
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                  
                  <Divider sx={{ my: 2 }} />
                  
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    {rfq.description}
                  </Typography>
                  
                  <Chip label={rfq.category} color="primary" sx={{ mr: 1 }} />
                  <Chip label="Government RFQ" color="success" />
                </Box>
              )}

              {activeStep === 1 && (
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Quote Information
                  </Typography>
                  
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Total Quote Amount"
                        type="number"
                        value={response.total_quote_amount}
                        onChange={(e) => setResponse(prev => ({
                          ...prev,
                          total_quote_amount: parseFloat(e.target.value) || 0
                        }))}
                        InputProps={{
                          startAdornment: <Typography sx={{ mr: 1 }}>R</Typography>
                        }}
                      />
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                      <FormControl fullWidth>
                        <InputLabel>Currency</InputLabel>
                        <Select
                          value={response.currency}
                          onChange={(e) => setResponse(prev => ({
                            ...prev,
                            currency: e.target.value
                          }))}
                        >
                          <MenuItem value="ZAR">ZAR (South African Rand)</MenuItem>
                          <MenuItem value="USD">USD (US Dollar)</MenuItem>
                          <MenuItem value="EUR">EUR (Euro)</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Delivery Timeframe"
                        value={response.delivery_timeframe}
                        onChange={(e) => setResponse(prev => ({
                          ...prev,
                          delivery_timeframe: e.target.value
                        }))}
                        placeholder="e.g., 2-3 weeks"
                      />
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Payment Terms"
                        value={response.payment_terms}
                        onChange={(e) => setResponse(prev => ({
                          ...prev,
                          payment_terms: e.target.value
                        }))}
                        placeholder="e.g., 30 days net"
                      />
                    </Grid>
                    
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Warranty Terms"
                        multiline
                        rows={3}
                        value={response.warranty_terms}
                        onChange={(e) => setResponse(prev => ({
                          ...prev,
                          warranty_terms: e.target.value
                        }))}
                        placeholder="Describe warranty terms and conditions"
                      />
                    </Grid>
                  </Grid>
                </Box>
              )}

              {activeStep === 2 && (
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Technical Proposal
                  </Typography>
                  
                  <Alert severity="info" sx={{ mb: 3 }}>
                    <Typography variant="body2">
                      Provide detailed technical specifications and methodology for your proposed solution.
                    </Typography>
                  </Alert>
                  
                  <TextField
                    fullWidth
                    label="Technical Approach"
                    multiline
                    rows={6}
                    placeholder="Describe your technical approach, methodology, and implementation plan..."
                    sx={{ mb: 3 }}
                  />
                  
                  <TextField
                    fullWidth
                    label="Key Features & Benefits"
                    multiline
                    rows={4}
                    placeholder="Highlight key features and benefits of your solution..."
                    sx={{ mb: 3 }}
                  />
                  
                  <TextField
                    fullWidth
                    label="Team & Resources"
                    multiline
                    rows={3}
                    placeholder="Describe your team composition and resources..."
                  />
                </Box>
              )}

              {activeStep === 3 && (
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Compliance & Final Review
                  </Typography>
                  
                  <Alert severity="warning" sx={{ mb: 3 }}>
                    <Typography variant="body2">
                      Ensure all required compliance documents are attached before submission.
                    </Typography>
                  </Alert>
                  
                  <Stack spacing={2} sx={{ mb: 3 }}>
                    <Button
                      variant="outlined"
                      startIcon={<AttachFile />}
                      fullWidth
                    >
                      Upload B-BBEE Certificate
                    </Button>
                    
                    <Button
                      variant="outlined"
                      startIcon={<AttachFile />}
                      fullWidth
                    >
                      Upload Tax Clearance Certificate
                    </Button>
                    
                    <Button
                      variant="outlined"
                      startIcon={<AttachFile />}
                      fullWidth
                    >
                      Upload Company Registration
                    </Button>
                  </Stack>
                  
                  <Divider sx={{ my: 3 }} />
                  
                  <Typography variant="h6" gutterBottom>
                    Response Summary
                  </Typography>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">
                        Quote Amount:
                      </Typography>
                      <Typography variant="h6">
                        {formatCurrency(response.total_quote_amount)}
                      </Typography>
                    </Grid>
                    
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">
                        Delivery:
                      </Typography>
                      <Typography variant="h6">
                        {response.delivery_timeframe || 'Not specified'}
                      </Typography>
                    </Grid>
                  </Grid>
                </Box>
              )}

              {/* Navigation Buttons */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
                <Button
                  onClick={handleBack}
                  disabled={activeStep === 0}
                >
                  Back
                </Button>
                
                {activeStep === steps.length - 1 ? (
                  <Button
                    variant="contained"
                    onClick={handleSubmitResponse}
                    disabled={submitting}
                    startIcon={submitting ? <LinearProgress /> : <Send />}
                    sx={{ 
                      backgroundColor: '#4CAF50',
                      fontWeight: 'bold',
                      px: 4
                    }}
                  >
                    {submitting ? 'Submitting...' : 'Submit Response'}
                  </Button>
                ) : (
                  <Button
                    variant="contained"
                    onClick={handleNext}
                  >
                    Next
                  </Button>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Sidebar */}
        <Grid item xs={12} md={4}>
          <Stack spacing={3}>
            {/* Success Rate Card */}
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <CheckCircle color="success" />
                  Success Metrics
                </Typography>
                
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Government RFQ Success Rate
                  </Typography>
                  <Typography variant="h4" color="success.main" sx={{ fontWeight: 'bold' }}>
                    88%
                  </Typography>
                </Box>
                
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Average Response Time
                  </Typography>
                  <Typography variant="h6">
                    2-3 days
                  </Typography>
                </Box>
                
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    Competition Level
                  </Typography>
                  <Chip label="Low" color="success" size="small" />
                </Box>
              </CardContent>
            </Card>

            {/* Tips Card */}
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Info color="primary" />
                  Success Tips
                </Typography>
                
                <Stack spacing={1}>
                  <Typography variant="body2">
                    • Respond quickly - government RFQs favor fast responses
                  </Typography>
                  <Typography variant="body2">
                    • Be competitive with pricing
                  </Typography>
                  <Typography variant="body2">
                    • Ensure all compliance documents are included
                  </Typography>
                  <Typography variant="body2">
                    • Provide clear delivery terms
                  </Typography>
                  <Typography variant="body2">
                    • Highlight relevant experience
                  </Typography>
                </Stack>
              </CardContent>
            </Card>
          </Stack>
        </Grid>
      </Grid>
    </Box>
  );
};

export default GovernmentRFQResponsePage;
