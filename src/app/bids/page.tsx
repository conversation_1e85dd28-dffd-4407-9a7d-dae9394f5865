'use client';

import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  Alert,
  Tabs,
  Tab,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress,
  Stack
} from '@mui/material';
import {
  Assignment as AssignmentIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  MoreVert as MoreIcon,
  TrendingUp as TrendingUpIcon,
  Schedule as ScheduleIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Cancel as CancelIcon,
  AttachMoney as MoneyIcon,
  BusinessCenter as BusinessIcon,
  AutoAwesome as AIIcon
} from '@mui/icons-material';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`bids-tabpanel-${index}`}
      aria-labelledby={`bids-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

// Mock bid data
const mockBids = [
  {
    id: 'bid-001',
    tenderTitle: 'Municipal Infrastructure Development',
    tenderReference: 'MUN-2024-001',
    bidAmount: 15600000,
    status: 'submitted',
    submissionDate: '2024-01-15',
    dueDate: '2024-02-20',
    progress: 100,
    priority: 'high',
    estimatedWinChance: 75
  },
  {
    id: 'bid-002',
    tenderTitle: 'Highway Construction Project',
    tenderReference: 'DOT-2024-007',
    bidAmount: 22400000,
    status: 'in_progress',
    submissionDate: null,
    dueDate: '2024-03-15',
    progress: 65,
    priority: 'medium',
    estimatedWinChance: 68
  },
  {
    id: 'bid-003',
    tenderTitle: 'School Building Renovation',
    tenderReference: 'EDU-2024-012',
    bidAmount: 8900000,
    status: 'won',
    submissionDate: '2024-01-05',
    dueDate: '2024-01-30',
    progress: 100,
    priority: 'high',
    estimatedWinChance: 100
  },
  {
    id: 'bid-004',
    tenderTitle: 'Water Treatment Plant',
    tenderReference: 'WAT-2024-003',
    bidAmount: 45200000,
    status: 'lost',
    submissionDate: '2024-01-10',
    dueDate: '2024-02-05',
    progress: 100,
    priority: 'high',
    estimatedWinChance: 0
  }
];

export default function BidsManagement() {
  const [activeTab, setActiveTab] = useState(0);
  const [selectedBid, setSelectedBid] = useState<string | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, bidId: string) => {
    setAnchorEl(event.currentTarget);
    setSelectedBid(bidId);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedBid(null);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'won':
        return <CheckCircleIcon color="success" />;
      case 'lost':
        return <CancelIcon color="error" />;
      case 'submitted':
        return <ScheduleIcon color="info" />;
      case 'in_progress':
        return <WarningIcon color="warning" />;
      default:
        return <AssignmentIcon color="action" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'won':
        return 'success';
      case 'lost':
        return 'error';
      case 'submitted':
        return 'info';
      case 'in_progress':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'error';
      case 'medium':
        return 'warning';
      case 'low':
        return 'info';
      default:
        return 'default';
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const filterBidsByStatus = (status: string) => {
    if (status === 'all') return mockBids;
    return mockBids.filter(bid => bid.status === status);
  };

  const getTotalValue = (bids: typeof mockBids) => {
    return bids.reduce((total, bid) => total + bid.bidAmount, 0);
  };

  const getTabBids = () => {
    switch (activeTab) {
      case 0: return mockBids; // All
      case 1: return filterBidsByStatus('in_progress'); // Active
      case 2: return filterBidsByStatus('submitted'); // Submitted
      case 3: return filterBidsByStatus('won'); // Won
      case 4: return filterBidsByStatus('lost'); // Lost
      default: return mockBids;
    }
  };

  const currentBids = getTabBids();

  return (
    <Box sx={{ p: 3, minHeight: '100vh', bgcolor: 'grey.50' }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main' }}>
            📋 Bid Management
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Track and manage your tender bids with AI-powered insights
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<AIIcon />}
            onClick={() => alert('AI Bid Assistant')}
          >
            AI Assistant
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => alert('Create New Bid')}
          >
            New Bid
          </Button>
        </Box>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={2}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <AssignmentIcon color="primary" />
                <Typography variant="h6">Total Bids</Typography>
              </Box>
              <Typography variant="h3" sx={{ fontWeight: 600, mb: 1 }}>
                {mockBids.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Active bidding projects
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={2}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <MoneyIcon color="success" />
                <Typography variant="h6">Total Value</Typography>
              </Box>
              <Typography variant="h3" sx={{ fontWeight: 600, mb: 1 }}>
                {formatCurrency(getTotalValue(mockBids))}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Cumulative bid amount
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={2}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <CheckCircleIcon color="success" />
                <Typography variant="h6">Won Bids</Typography>
              </Box>
              <Typography variant="h3" sx={{ fontWeight: 600, mb: 1 }}>
                {filterBidsByStatus('won').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Success rate: {Math.round((filterBidsByStatus('won').length / mockBids.length) * 100)}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={2}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <ScheduleIcon color="warning" />
                <Typography variant="h6">In Progress</Typography>
              </Box>
              <Typography variant="h3" sx={{ fontWeight: 600, mb: 1 }}>
                {filterBidsByStatus('in_progress').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Requiring attention
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs and Content */}
      <Paper elevation={2}>
        <Tabs value={activeTab} onChange={handleTabChange} variant="fullWidth">
          <Tab label={`All (${mockBids.length})`} />
          <Tab label={`Active (${filterBidsByStatus('in_progress').length})`} />
          <Tab label={`Submitted (${filterBidsByStatus('submitted').length})`} />
          <Tab label={`Won (${filterBidsByStatus('won').length})`} />
          <Tab label={`Lost (${filterBidsByStatus('lost').length})`} />
        </Tabs>

        <TabPanel value={activeTab} index={activeTab}>
          {currentBids.length > 0 ? (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Tender Details</TableCell>
                    <TableCell>Bid Amount</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Progress</TableCell>
                    <TableCell>Due Date</TableCell>
                    <TableCell>Win Chance</TableCell>
                    <TableCell>Priority</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {currentBids.map((bid) => (
                    <TableRow key={bid.id} hover>
                      <TableCell>
                        <Box>
                          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                            {bid.tenderTitle}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {bid.tenderReference}
                          </Typography>
                        </Box>
                      </TableCell>

                      <TableCell>
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          {formatCurrency(bid.bidAmount)}
                        </Typography>
                      </TableCell>

                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {getStatusIcon(bid.status)}
                          <Chip 
                            label={bid.status.replace('_', ' ').toUpperCase()}
                            size="small"
                            color={getStatusColor(bid.status) as any}
                          />
                        </Box>
                      </TableCell>

                      <TableCell>
                        <Box sx={{ width: '100%' }}>
                          <LinearProgress 
                            variant="determinate" 
                            value={bid.progress}
                            sx={{ mb: 0.5 }}
                            color={bid.progress === 100 ? 'success' : 'primary'}
                          />
                          <Typography variant="caption">
                            {bid.progress}% complete
                          </Typography>
                        </Box>
                      </TableCell>

                      <TableCell>
                        <Typography variant="body2">
                          {new Date(bid.dueDate).toLocaleDateString()}
                        </Typography>
                        {bid.submissionDate && (
                          <Typography variant="caption" color="text.secondary" display="block">
                            Submitted: {new Date(bid.submissionDate).toLocaleDateString()}
                          </Typography>
                        )}
                      </TableCell>

                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 600 }}>
                            {bid.estimatedWinChance}%
                          </Typography>
                          <TrendingUpIcon 
                            fontSize="small"
                            color={bid.estimatedWinChance > 70 ? 'success' : bid.estimatedWinChance > 40 ? 'warning' : 'error'}
                          />
                        </Box>
                      </TableCell>

                      <TableCell>
                        <Chip 
                          label={bid.priority.toUpperCase()}
                          size="small"
                          color={getPriorityColor(bid.priority) as any}
                          variant="outlined"
                        />
                      </TableCell>

                      <TableCell>
                        <Stack direction="row" spacing={1}>
                          <IconButton size="small" onClick={() => alert(`View bid ${bid.id}`)}>
                            <ViewIcon />
                          </IconButton>
                          <IconButton size="small" onClick={() => alert(`Edit bid ${bid.id}`)}>
                            <EditIcon />
                          </IconButton>
                          <IconButton 
                            size="small" 
                            onClick={(e) => handleMenuClick(e, bid.id)}
                          >
                            <MoreIcon />
                          </IconButton>
                        </Stack>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <BusinessIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary" sx={{ mb: 1 }}>
                No bids found
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                {activeTab === 0 
                  ? "You haven't created any bids yet." 
                  : `No bids found with ${['all', 'in_progress', 'submitted', 'won', 'lost'][activeTab]} status.`
                }
              </Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => alert('Create New Bid')}
              >
                Create Your First Bid
              </Button>
            </Box>
          )}
        </TabPanel>
      </Paper>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => { alert('View Details'); handleMenuClose(); }}>
          <ViewIcon sx={{ mr: 1 }} /> View Details
        </MenuItem>
        <MenuItem onClick={() => { alert('Edit Bid'); handleMenuClose(); }}>
          <EditIcon sx={{ mr: 1 }} /> Edit Bid
        </MenuItem>
        <MenuItem onClick={() => { alert('Duplicate Bid'); handleMenuClose(); }}>
          <AssignmentIcon sx={{ mr: 1 }} /> Duplicate
        </MenuItem>
        <MenuItem onClick={() => { setDialogOpen(true); handleMenuClose(); }}>
          <DeleteIcon sx={{ mr: 1 }} /> Delete
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this bid? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={() => { alert('Bid deleted'); setDialogOpen(false); }} 
            color="error"
            variant="contained"
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}