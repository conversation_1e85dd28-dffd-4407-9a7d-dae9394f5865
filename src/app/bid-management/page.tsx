'use client';

import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  LinearProgress
} from '@mui/material';
import {
  Assignment,
  Edit,
  Delete,
  Visibility,
  Add,
  Download,
  Upload,
  TrendingUp,
  Schedule,
  CheckCircle,
  Warning
} from '@mui/icons-material';

const mockBids = [
  {
    id: 1,
    tenderTitle: "Municipal Infrastructure Development",
    bidAmount: "R2,450,000",
    status: "submitted",
    deadline: "2024-01-15",
    probability: 85,
    lastUpdated: "2024-01-10"
  },
  {
    id: 2,
    tenderTitle: "IT Equipment Procurement",
    bidAmount: "R890,000",
    status: "draft",
    deadline: "2024-01-20",
    probability: 72,
    lastUpdated: "2024-01-11"
  },
  {
    id: 3,
    tenderTitle: "Construction Services Contract",
    bidAmount: "R5,200,000",
    status: "under_review",
    deadline: "2024-01-12",
    probability: 91,
    lastUpdated: "2024-01-09"
  }
];

export default function BidManagementPage() {
  const [open, setOpen] = useState(false);
  const [selectedBid, setSelectedBid] = useState(null);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'submitted': return 'success';
      case 'draft': return 'warning';
      case 'under_review': return 'info';
      case 'rejected': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'submitted': return <CheckCircle />;
      case 'draft': return <Edit />;
      case 'under_review': return <Schedule />;
      case 'rejected': return <Warning />;
      default: return <Assignment />;
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
            Bid Management
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Manage your tender bids and track submission status
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => setOpen(true)}
          size="large"
        >
          Create New Bid
        </Button>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="primary">
                    12
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active Bids
                  </Typography>
                </Box>
                <Assignment sx={{ fontSize: 40, color: 'primary.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="success.main">
                    8
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Submitted
                  </Typography>
                </Box>
                <CheckCircle sx={{ fontSize: 40, color: 'success.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="warning.main">
                    3
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    In Draft
                  </Typography>
                </Box>
                <Edit sx={{ fontSize: 40, color: 'warning.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="info.main">
                    87%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Avg Win Rate
                  </Typography>
                </Box>
                <TrendingUp sx={{ fontSize: 40, color: 'info.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Bids Table */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6">
              Current Bids
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button startIcon={<Download />} variant="outlined" size="small">
                Export
              </Button>
              <Button startIcon={<Upload />} variant="outlined" size="small">
                Import
              </Button>
            </Box>
          </Box>
          
          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Tender Title</TableCell>
                  <TableCell>Bid Amount</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Deadline</TableCell>
                  <TableCell>Win Probability</TableCell>
                  <TableCell>Last Updated</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {mockBids.map((bid) => (
                  <TableRow key={bid.id} hover>
                    <TableCell>
                      <Typography variant="subtitle2" fontWeight="medium">
                        {bid.tenderTitle}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {bid.bidAmount}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        icon={getStatusIcon(bid.status)}
                        label={bid.status.replace('_', ' ').toUpperCase()}
                        color={getStatusColor(bid.status) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>{bid.deadline}</TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <LinearProgress
                          variant="determinate"
                          value={bid.probability}
                          sx={{ width: 60, height: 6 }}
                          color={bid.probability > 80 ? 'success' : bid.probability > 60 ? 'warning' : 'error'}
                        />
                        <Typography variant="body2">{bid.probability}%</Typography>
                      </Box>
                    </TableCell>
                    <TableCell>{bid.lastUpdated}</TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 0.5 }}>
                        <IconButton size="small" color="primary">
                          <Visibility />
                        </IconButton>
                        <IconButton size="small" color="secondary">
                          <Edit />
                        </IconButton>
                        <IconButton size="small" color="error">
                          <Delete />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Create Bid Dialog */}
      <Dialog open={open} onClose={() => setOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Create New Bid</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Tender Title"
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Bid Amount"
                variant="outlined"
                type="number"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Status"
                variant="outlined"
                select
                defaultValue="draft"
              >
                <MenuItem value="draft">Draft</MenuItem>
                <MenuItem value="submitted">Submitted</MenuItem>
                <MenuItem value="under_review">Under Review</MenuItem>
              </TextField>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Deadline"
                variant="outlined"
                type="date"
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpen(false)}>Cancel</Button>
          <Button variant="contained" onClick={() => setOpen(false)}>
            Create Bid
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
}
