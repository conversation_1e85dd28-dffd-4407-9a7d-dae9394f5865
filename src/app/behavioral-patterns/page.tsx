'use client';

import React from 'react';
import { Container, Typography, Card, CardContent, Grid, Box, Avatar, Chip } from '@mui/material';
import { Psychology, Timeline, TrendingUp, Assessment } from '@mui/icons-material';

export default function BehavioralPatternsPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
        Behavioral Patterns
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        Analyze and understand your behavioral patterns for better decision making
      </Typography>
      
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Decision Making Patterns</Typography>
              <Box sx={{ mt: 2 }}>
                <Box sx={{ p: 2, bgcolor: 'primary.50', borderRadius: 1, mb: 2 }}>
                  <Typography variant="subtitle2" color="primary.main" gutterBottom>
                    Analytical Approach
                  </Typography>
                  <Typography variant="body2">
                    You tend to analyze data thoroughly before making decisions (87% of cases)
                  </Typography>
                </Box>
                <Box sx={{ p: 2, bgcolor: 'success.50', borderRadius: 1, mb: 2 }}>
                  <Typography variant="subtitle2" color="success.main" gutterBottom>
                    Risk Assessment
                  </Typography>
                  <Typography variant="body2">
                    Strong pattern of evaluating risks before committing (94% accuracy)
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Communication Patterns</Typography>
              <Box sx={{ mt: 2 }}>
                <Box sx={{ p: 2, bgcolor: 'info.50', borderRadius: 1, mb: 2 }}>
                  <Typography variant="subtitle2" color="info.main" gutterBottom>
                    Direct Communication
                  </Typography>
                  <Typography variant="body2">
                    Preference for clear, concise communication (91% of interactions)
                  </Typography>
                </Box>
                <Box sx={{ p: 2, bgcolor: 'warning.50', borderRadius: 1 }}>
                  <Typography variant="subtitle2" color="warning.main" gutterBottom>
                    Follow-up Consistency
                  </Typography>
                  <Typography variant="body2">
                    Regular follow-up pattern with clients and partners (85% consistency)
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Pattern Analysis Summary</Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Avatar sx={{ bgcolor: 'primary.main', mx: 'auto', mb: 1 }}>
                  <Psychology />
                </Avatar>
                <Typography variant="h6">Analytical</Typography>
                <Chip label="Dominant" color="primary" size="small" />
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Avatar sx={{ bgcolor: 'success.main', mx: 'auto', mb: 1 }}>
                  <Assessment />
                </Avatar>
                <Typography variant="h6">Methodical</Typography>
                <Chip label="Strong" color="success" size="small" />
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Avatar sx={{ bgcolor: 'info.main', mx: 'auto', mb: 1 }}>
                  <Timeline />
                </Avatar>
                <Typography variant="h6">Consistent</Typography>
                <Chip label="High" color="info" size="small" />
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Avatar sx={{ bgcolor: 'warning.main', mx: 'auto', mb: 1 }}>
                  <TrendingUp />
                </Avatar>
                <Typography variant="h6">Adaptive</Typography>
                <Chip label="Moderate" color="warning" size="small" />
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Container>
  );
}
