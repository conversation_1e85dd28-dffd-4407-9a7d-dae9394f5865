'use client';

import React from 'react';
import { Container, Typo<PERSON>, Card, CardContent, Grid, Button, Box, Switch, FormControlLabel } from '@mui/material';
import { Security, Lock, Shield, Key } from '@mui/icons-material';

export default function AccessControlPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
        Access Control
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        Configure security settings and access controls
      </Typography>
      
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Authentication Settings</Typography>
              <Box sx={{ mt: 2 }}>
                <FormControlLabel control={<Switch defaultChecked />} label="Two-Factor Authentication" />
                <FormControlLabel control={<Switch defaultChecked />} label="Password Complexity" />
                <FormControlLabel control={<Switch />} label="Single Sign-On (SSO)" />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Session Management</Typography>
              <Box sx={{ mt: 2 }}>
                <FormControlLabel control={<Switch defaultChecked />} label="Auto Logout" />
                <FormControlLabel control={<Switch defaultChecked />} label="Session Monitoring" />
                <FormControlLabel control={<Switch />} label="Concurrent Sessions" />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Security Overview</Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Security sx={{ fontSize: 48, color: 'primary.main', mb: 1 }} />
                <Typography variant="h6">Security Score</Typography>
                <Typography variant="h4" color="primary.main">94%</Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Shield sx={{ fontSize: 48, color: 'success.main', mb: 1 }} />
                <Typography variant="h6">Active Sessions</Typography>
                <Typography variant="h4" color="success.main">23</Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Container>
  );
}
