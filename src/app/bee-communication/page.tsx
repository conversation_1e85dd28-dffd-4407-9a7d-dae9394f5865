'use client';

import React, { useState } from 'react';
import {
  <PERSON>,
  Container,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Grid,
  TextField,
  Button,
  Avatar,
  Stack,
  Chip,
  Badge,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  Paper,
  Tab,
  Tabs,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  Send,
  Phone,
  VideoCall,
  Attachment,
  Notifications,
  Message,
  Assignment,
  Warning,
  Info,
  CheckCircle,
  Schedule,
  LocationOn,
  Camera
} from '@mui/icons-material';

interface Message {
  id: string;
  senderId: string;
  senderName: string;
  senderRole: 'queen_bee' | 'bee_worker' | 'system';
  content: string;
  timestamp: string;
  type: 'text' | 'task_update' | 'system_notification' | 'urgent';
  taskId?: string;
  attachments?: Array<{
    name: string;
    type: string;
    url: string;
  }>;
  isRead: boolean;
}

interface Conversation {
  id: string;
  queenBee: {
    id: string;
    name: string;
    avatar?: string;
    territory: string;
    isOnline: boolean;
  };
  lastMessage: Message;
  unreadCount: number;
  messages: Message[];
}

export default function BeeCommunicationPage() {
  const [currentTab, setCurrentTab] = useState(0);
  const [selectedConversation, setSelectedConversation] = useState<string | null>('conv-001');
  const [newMessage, setNewMessage] = useState('');
  const [emergencyDialogOpen, setEmergencyDialogOpen] = useState(false);

  const [conversations, setConversations] = useState<Conversation[]>([
    {
      id: 'conv-001',
      queenBee: {
        id: 'qb-001',
        name: 'Queen Bee Sarah',
        avatar: '/avatars/queen-bee.jpg',
        territory: 'Johannesburg Central',
        isOnline: true
      },
      lastMessage: {
        id: 'msg-001',
        senderId: 'qb-001',
        senderName: 'Queen Bee Sarah',
        senderRole: 'queen_bee',
        content: 'Great work on the municipal document collection! The client was very impressed.',
        timestamp: '2024-01-15T14:30:00',
        type: 'text',
        isRead: false
      },
      unreadCount: 2,
      messages: [
        {
          id: 'msg-001',
          senderId: 'qb-001',
          senderName: 'Queen Bee Sarah',
          senderRole: 'queen_bee',
          content: 'Great work on the municipal document collection! The client was very impressed.',
          timestamp: '2024-01-15T14:30:00',
          type: 'text',
          isRead: false
        },
        {
          id: 'msg-002',
          senderId: 'qb-001',
          senderName: 'Queen Bee Sarah',
          senderRole: 'queen_bee',
          content: 'I have a new urgent task for you. Can you handle a site visit tomorrow morning?',
          timestamp: '2024-01-15T14:35:00',
          type: 'urgent',
          taskId: 'task-002',
          isRead: false
        }
      ]
    },
    {
      id: 'conv-002',
      queenBee: {
        id: 'qb-002',
        name: 'Queen Bee Mike',
        avatar: '/avatars/queen-bee-2.jpg',
        territory: 'Soweto',
        isOnline: false
      },
      lastMessage: {
        id: 'msg-003',
        senderId: 'bee-001',
        senderName: 'Sarah Mthembu',
        senderRole: 'bee_worker',
        content: 'Task completed successfully. Photos uploaded.',
        timestamp: '2024-01-14T16:45:00',
        type: 'task_update',
        isRead: true
      },
      unreadCount: 0,
      messages: []
    }
  ]);

  const [systemNotifications, setSystemNotifications] = useState<Message[]>([
    {
      id: 'sys-001',
      senderId: 'system',
      senderName: 'BidBeez System',
      senderRole: 'system',
      content: 'New task assigned: Municipal Tender Document Collection',
      timestamp: '2024-01-15T08:00:00',
      type: 'system_notification',
      taskId: 'task-001',
      isRead: false
    },
    {
      id: 'sys-002',
      senderId: 'system',
      senderName: 'BidBeez System',
      senderRole: 'system',
      content: 'Payment processed: R450 for completed task',
      timestamp: '2024-01-14T18:00:00',
      type: 'system_notification',
      isRead: true
    }
  ]);

  const selectedConv = conversations.find(c => c.id === selectedConversation);

  const handleSendMessage = () => {
    if (!newMessage.trim() || !selectedConversation) return;

    const message: Message = {
      id: `msg-${Date.now()}`,
      senderId: 'bee-001',
      senderName: 'Sarah Mthembu',
      senderRole: 'bee_worker',
      content: newMessage,
      timestamp: new Date().toISOString(),
      type: 'text',
      isRead: true
    };

    setConversations(prev => prev.map(conv => {
      if (conv.id === selectedConversation) {
        return {
          ...conv,
          messages: [...conv.messages, message],
          lastMessage: message
        };
      }
      return conv;
    }));

    setNewMessage('');
  };

  const getMessageTypeIcon = (type: string) => {
    switch (type) {
      case 'urgent': return <Warning color="error" />;
      case 'task_update': return <Assignment color="primary" />;
      case 'system_notification': return <Info color="info" />;
      default: return <Message />;
    }
  };

  const getMessageTypeColor = (type: string) => {
    switch (type) {
      case 'urgent': return 'error';
      case 'task_update': return 'primary';
      case 'system_notification': return 'info';
      default: return 'default';
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" fontWeight="bold" gutterBottom>
          💬 Communication Center
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Stay connected with Queen Bees and receive important updates
        </Typography>
      </Box>

      {/* Emergency Contact Button */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="body2">
            Need immediate assistance? Use the emergency contact feature.
          </Typography>
          <Button
            variant="contained"
            color="error"
            startIcon={<Phone />}
            onClick={() => setEmergencyDialogOpen(true)}
          >
            Emergency Contact
          </Button>
        </Box>
      </Alert>

      {/* Communication Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={currentTab} onChange={(e, newValue) => setCurrentTab(newValue)}>
          <Tab 
            label={
              <Badge badgeContent={conversations.reduce((sum, conv) => sum + conv.unreadCount, 0)} color="error">
                Queen Bee Messages
              </Badge>
            } 
          />
          <Tab 
            label={
              <Badge badgeContent={systemNotifications.filter(n => !n.isRead).length} color="error">
                System Notifications
              </Badge>
            } 
          />
        </Tabs>
      </Box>

      {currentTab === 0 && (
        <Grid container spacing={3} sx={{ height: '600px' }}>
          {/* Conversations List */}
          <Grid item xs={12} md={4}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  👑 Queen Bees
                </Typography>
                <List sx={{ maxHeight: '500px', overflow: 'auto' }}>
                  {conversations.map((conversation) => (
                    <React.Fragment key={conversation.id}>
                      <ListItem
                        button
                        selected={selectedConversation === conversation.id}
                        onClick={() => setSelectedConversation(conversation.id)}
                        sx={{
                          borderRadius: 2,
                          mb: 1,
                          bgcolor: selectedConversation === conversation.id ? 'action.selected' : 'transparent'
                        }}
                      >
                        <ListItemAvatar>
                          <Badge
                            color={conversation.queenBee.isOnline ? 'success' : 'default'}
                            variant="dot"
                            anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                          >
                            <Avatar src={conversation.queenBee.avatar}>
                              👑
                            </Avatar>
                          </Badge>
                        </ListItemAvatar>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                              <Typography variant="subtitle1" fontWeight="bold">
                                {conversation.queenBee.name}
                              </Typography>
                              {conversation.unreadCount > 0 && (
                                <Badge badgeContent={conversation.unreadCount} color="error" />
                              )}
                            </Box>
                          }
                          secondary={
                            <Box>
                              <Typography variant="body2" color="text.secondary" noWrap>
                                {conversation.lastMessage.content}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {conversation.queenBee.territory} • {new Date(conversation.lastMessage.timestamp).toLocaleTimeString()}
                              </Typography>
                            </Box>
                          }
                        />
                      </ListItem>
                      <Divider />
                    </React.Fragment>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* Chat Area */}
          <Grid item xs={12} md={8}>
            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              {selectedConv ? (
                <>
                  {/* Chat Header */}
                  <CardContent sx={{ borderBottom: 1, borderColor: 'divider', py: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Badge
                          color={selectedConv.queenBee.isOnline ? 'success' : 'default'}
                          variant="dot"
                        >
                          <Avatar src={selectedConv.queenBee.avatar}>👑</Avatar>
                        </Badge>
                        <Box>
                          <Typography variant="h6">{selectedConv.queenBee.name}</Typography>
                          <Typography variant="body2" color="text.secondary">
                            {selectedConv.queenBee.territory} • {selectedConv.queenBee.isOnline ? 'Online' : 'Offline'}
                          </Typography>
                        </Box>
                      </Box>
                      <Stack direction="row" spacing={1}>
                        <IconButton color="primary">
                          <Phone />
                        </IconButton>
                        <IconButton color="primary">
                          <VideoCall />
                        </IconButton>
                      </Stack>
                    </Box>
                  </CardContent>

                  {/* Messages */}
                  <Box sx={{ flex: 1, overflow: 'auto', p: 2 }}>
                    <Stack spacing={2}>
                      {selectedConv.messages.map((message) => (
                        <Box
                          key={message.id}
                          sx={{
                            display: 'flex',
                            justifyContent: message.senderRole === 'bee_worker' ? 'flex-end' : 'flex-start'
                          }}
                        >
                          <Paper
                            sx={{
                              p: 2,
                              maxWidth: '70%',
                              bgcolor: message.senderRole === 'bee_worker' ? 'primary.main' : 'grey.100',
                              color: message.senderRole === 'bee_worker' ? 'white' : 'text.primary',
                              borderRadius: 2,
                              border: message.type === 'urgent' ? '2px solid #f44336' : 'none'
                            }}
                          >
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                              {getMessageTypeIcon(message.type)}
                              <Typography variant="caption" fontWeight="bold">
                                {message.senderName}
                              </Typography>
                              {message.type !== 'text' && (
                                <Chip 
                                  label={message.type.replace('_', ' ').toUpperCase()}
                                  size="small"
                                  color={getMessageTypeColor(message.type) as any}
                                />
                              )}
                            </Box>
                            <Typography variant="body1">{message.content}</Typography>
                            <Typography variant="caption" sx={{ opacity: 0.7, display: 'block', mt: 1 }}>
                              {new Date(message.timestamp).toLocaleString()}
                            </Typography>
                          </Paper>
                        </Box>
                      ))}
                    </Stack>
                  </Box>

                  {/* Message Input */}
                  <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <TextField
                        fullWidth
                        placeholder="Type your message..."
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                        multiline
                        maxRows={3}
                      />
                      <IconButton color="primary">
                        <Attachment />
                      </IconButton>
                      <IconButton color="primary">
                        <Camera />
                      </IconButton>
                      <Button
                        variant="contained"
                        endIcon={<Send />}
                        onClick={handleSendMessage}
                        disabled={!newMessage.trim()}
                      >
                        Send
                      </Button>
                    </Box>
                  </Box>
                </>
              ) : (
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
                  <Typography variant="h6" color="text.secondary">
                    Select a Queen Bee to start messaging
                  </Typography>
                </Box>
              )}
            </Card>
          </Grid>
        </Grid>
      )}

      {currentTab === 1 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  🔔 System Notifications
                </Typography>
                <List>
                  {systemNotifications.map((notification, index) => (
                    <React.Fragment key={notification.id}>
                      <ListItem>
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: 'info.main' }}>
                            {getMessageTypeIcon(notification.type)}
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="subtitle1">
                                {notification.content}
                              </Typography>
                              {!notification.isRead && (
                                <Chip label="NEW" size="small" color="error" />
                              )}
                            </Box>
                          }
                          secondary={new Date(notification.timestamp).toLocaleString()}
                        />
                        {notification.taskId && (
                          <Button
                            variant="outlined"
                            size="small"
                            startIcon={<Assignment />}
                            href="/bee-tasks"
                          >
                            View Task
                          </Button>
                        )}
                      </ListItem>
                      {index < systemNotifications.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Emergency Contact Dialog */}
      <Dialog open={emergencyDialogOpen} onClose={() => setEmergencyDialogOpen(false)}>
        <DialogTitle>🚨 Emergency Contact</DialogTitle>
        <DialogContent>
          <Typography variant="body1" gutterBottom>
            This will immediately notify all Queen Bees and the BidBeez support team.
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Use only for genuine emergencies such as:
          </Typography>
          <Typography variant="body2" component="ul" sx={{ mt: 1 }}>
            <li>Safety concerns at task location</li>
            <li>Urgent assistance needed</li>
            <li>Equipment failure preventing task completion</li>
            <li>Medical emergency</li>
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEmergencyDialogOpen(false)}>
            Cancel
          </Button>
          <Button variant="contained" color="error" startIcon={<Phone />}>
            Send Emergency Alert
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
}
