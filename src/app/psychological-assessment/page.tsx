'use client';

import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Box, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  Alert,
  Button,
  Chip,
  LinearProgress,
  Avatar,
  Tabs,
  Tab,
  Paper,
  Stack,
  Divider
} from '@mui/material';
import { 
  Psychology, 
  TrendingUp, 
  Assessment, 
  Visibility,
  AutoAwesome,
  Timer,
  CheckCircle,
  Warning,
  Refresh,
  Settings
} from '@mui/icons-material';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`assessment-tabpanel-${index}`}
      aria-labelledby={`assessment-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

export default function PsychologicalAssessmentPage() {
  const [tabValue, setTabValue] = useState(0);
  const [assessmentProgress, setAssessmentProgress] = useState(67);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const startAnalysis = () => {
    setIsAnalyzing(true);
    setTimeout(() => setIsAnalyzing(false), 3000);
  };

  // Mock psychological data
  const psychologicalProfile = {
    archetype: 'Strategic Analyzer',
    confidence: 78,
    stressLevel: 34,
    cognitiveLoad: 56,
    engagement: 89,
    riskTolerance: 72,
    decisionSpeed: 'Moderate',
    communicationStyle: 'Data-Driven'
  };

  const assessmentMetrics = {
    totalAssessments: 156,
    completedToday: 12,
    averageScore: 78.5,
    improvementRate: 23.4,
    accuracyRate: 94.7
  };

  const recentAssessments = [
    { id: 1, type: 'Stress Analysis', score: 78, status: 'Complete', timestamp: '2 hours ago' },
    { id: 2, type: 'Confidence Mapping', score: 85, status: 'Complete', timestamp: '4 hours ago' },
    { id: 3, type: 'Cognitive Load', score: 72, status: 'In Progress', timestamp: '6 hours ago' },
    { id: 4, type: 'Risk Assessment', score: 91, status: 'Complete', timestamp: '1 day ago' }
  ];

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'success';
    if (score >= 60) return 'warning';
    return 'error';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Complete': return 'success';
      case 'In Progress': return 'warning';
      case 'Failed': return 'error';
      default: return 'default';
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
          🧠 Psychological Assessment Center
        </Typography>
        <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
          Advanced psychological profiling and behavioral analysis for optimal bidding performance
        </Typography>
        
        <Alert severity="info" sx={{ mb: 3 }}>
          <strong>AI Analysis Active:</strong> Real-time psychological profiling with 94.7% accuracy rate and continuous behavioral optimization.
        </Alert>
      </Box>

      {/* Quick Actions */}
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item>
          <Button 
            variant="contained" 
            startIcon={<Psychology />} 
            color="primary"
            onClick={startAnalysis}
            disabled={isAnalyzing}
          >
            {isAnalyzing ? 'Analyzing...' : 'Start Assessment'}
          </Button>
        </Grid>
        <Grid item>
          <Button variant="outlined" startIcon={<Assessment />}>
            View Reports
          </Button>
        </Grid>
        <Grid item>
          <Button variant="outlined" startIcon={<Settings />}>
            Configure Tests
          </Button>
        </Grid>
      </Grid>

      {/* Current Psychological Profile */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Current Psychological Profile</Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Avatar sx={{ width: 80, height: 80, mx: 'auto', mb: 2, bgcolor: 'primary.main' }}>
                  <Psychology sx={{ fontSize: 40 }} />
                </Avatar>
                <Typography variant="h6">{psychologicalProfile.archetype}</Typography>
                <Typography variant="body2" color="text.secondary">Primary Archetype</Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={9}>
              <Grid container spacing={2}>
                <Grid item xs={6} md={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color="success.main">{psychologicalProfile.confidence}%</Typography>
                    <Typography variant="body2">Confidence</Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6} md={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color="warning.main">{psychologicalProfile.stressLevel}%</Typography>
                    <Typography variant="body2">Stress Level</Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6} md={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color="info.main">{psychologicalProfile.cognitiveLoad}%</Typography>
                    <Typography variant="body2">Cognitive Load</Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6} md={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color="primary.main">{psychologicalProfile.engagement}%</Typography>
                    <Typography variant="body2">Engagement</Typography>
                  </Paper>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Assessment Progress */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Assessment Progress</Typography>
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" color="text.secondary">
              Overall Assessment Completion: {assessmentProgress}%
            </Typography>
            <LinearProgress 
              variant="determinate" 
              value={assessmentProgress} 
              sx={{ mt: 1, height: 8, borderRadius: 4 }}
            />
          </Box>
          <Grid container spacing={3}>
            <Grid item xs={6} md={2.4}>
              <Box sx={{ textAlign: 'center' }}>
                <Assessment sx={{ fontSize: 32, color: 'primary.main', mb: 1 }} />
                <Typography variant="h5">{assessmentMetrics.totalAssessments}</Typography>
                <Typography variant="body2" color="text.secondary">Total Tests</Typography>
              </Box>
            </Grid>
            <Grid item xs={6} md={2.4}>
              <Box sx={{ textAlign: 'center' }}>
                <Timer sx={{ fontSize: 32, color: 'success.main', mb: 1 }} />
                <Typography variant="h5">{assessmentMetrics.completedToday}</Typography>
                <Typography variant="body2" color="text.secondary">Today</Typography>
              </Box>
            </Grid>
            <Grid item xs={6} md={2.4}>
              <Box sx={{ textAlign: 'center' }}>
                <TrendingUp sx={{ fontSize: 32, color: 'warning.main', mb: 1 }} />
                <Typography variant="h5">{assessmentMetrics.averageScore}</Typography>
                <Typography variant="body2" color="text.secondary">Avg Score</Typography>
              </Box>
            </Grid>
            <Grid item xs={6} md={2.4}>
              <Box sx={{ textAlign: 'center' }}>
                <AutoAwesome sx={{ fontSize: 32, color: 'info.main', mb: 1 }} />
                <Typography variant="h5">{assessmentMetrics.improvementRate}%</Typography>
                <Typography variant="body2" color="text.secondary">Improvement</Typography>
              </Box>
            </Grid>
            <Grid item xs={6} md={2.4}>
              <Box sx={{ textAlign: 'center' }}>
                <CheckCircle sx={{ fontSize: 32, color: 'success.main', mb: 1 }} />
                <Typography variant="h5">{assessmentMetrics.accuracyRate}%</Typography>
                <Typography variant="body2" color="text.secondary">Accuracy</Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Detailed Analysis Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="assessment tabs">
          <Tab label="Recent Assessments" />
          <Tab label="Behavioral Patterns" />
          <Tab label="Optimization Recommendations" />
          <Tab label="Historical Trends" />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>Recent Assessment Results</Typography>
            <Stack spacing={2}>
              {recentAssessments.map((assessment) => (
                <Paper key={assessment.id} sx={{ p: 3 }}>
                  <Grid container alignItems="center" spacing={2}>
                    <Grid item xs={12} md={4}>
                      <Typography variant="subtitle1" fontWeight="medium">
                        {assessment.type}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {assessment.timestamp}
                      </Typography>
                    </Grid>
                    <Grid item xs={6} md={2}>
                      <Typography variant="h6" color={`${getScoreColor(assessment.score)}.main`}>
                        {assessment.score}%
                      </Typography>
                    </Grid>
                    <Grid item xs={6} md={2}>
                      <Chip 
                        label={assessment.status} 
                        color={getStatusColor(assessment.status) as any}
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                        <Button size="small" startIcon={<Visibility />}>
                          View Details
                        </Button>
                        <Button size="small" startIcon={<Refresh />}>
                          Retest
                        </Button>
                      </Box>
                    </Grid>
                  </Grid>
                </Paper>
              ))}
            </Stack>
          </CardContent>
        </Card>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Alert severity="success" sx={{ mb: 3 }}>
          <strong>Behavioral Analysis:</strong> Identified 12 key behavioral patterns with 89% consistency across assessments.
        </Alert>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>Decision Making Patterns</Typography>
                <Box sx={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: '#f5f5f5' }}>
                  <Typography variant="body1" color="text.secondary">
                    📊 Decision pattern analysis chart
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>Stress Response Patterns</Typography>
                <Box sx={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: '#f5f5f5' }}>
                  <Typography variant="body1" color="text.secondary">
                    📈 Stress response timeline
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <Alert severity="info" sx={{ mb: 3 }}>
          <strong>AI Recommendations:</strong> Based on your psychological profile, here are personalized optimization strategies.
        </Alert>
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom color="success.main">
                  Confidence Boosting
                </Typography>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  Recommended techniques to increase bidding confidence by 15-20%.
                </Typography>
                <Button variant="outlined" size="small">
                  View Techniques
                </Button>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom color="warning.main">
                  Stress Reduction
                </Typography>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  Personalized stress management strategies for high-pressure bidding.
                </Typography>
                <Button variant="outlined" size="small">
                  Learn More
                </Button>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom color="info.main">
                  Cognitive Enhancement
                </Typography>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  Techniques to optimize cognitive load and decision-making speed.
                </Typography>
                <Button variant="outlined" size="small">
                  Start Training
                </Button>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      <TabPanel value={tabValue} index={3}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>Historical Performance Trends</Typography>
            <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: '#f5f5f5' }}>
              <Typography variant="body1" color="text.secondary">
                📈 Historical psychological performance trends chart
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </TabPanel>
    </Container>
  );
}
