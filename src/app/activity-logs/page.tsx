'use client';

import React from 'react';
import { Container, Typography, Card, CardContent, Grid, Button, Box, Chip, Table, TableBody, TableCell, TableHead, TableRow } from '@mui/material';
import { History, Download, FilterList, Search } from '@mui/icons-material';

const activities = [
  { user: "<PERSON>", action: "Bid Submitted", target: "Municipal IT Tender", time: "2024-01-11 14:30", status: "Success" },
  { user: "<PERSON> Johnson", action: "User Login", target: "Dashboard", time: "2024-01-11 09:15", status: "Success" },
  { user: "<PERSON>", action: "Report Generated", target: "Monthly Analytics", time: "2024-01-10 16:45", status: "Success" }
];

export default function ActivityLogsPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
            Activity Logs
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Monitor and audit all system activities
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button variant="outlined" startIcon={<FilterList />}>Filter</Button>
          <Button variant="outlined" startIcon={<Download />}>Export</Button>
        </Box>
      </Box>
      
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" fontWeight="bold" color="primary">
                1,247
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Activities
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" fontWeight="bold" color="success.main">
                98.7%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Success Rate
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Recent Activities</Typography>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>User</TableCell>
                <TableCell>Action</TableCell>
                <TableCell>Target</TableCell>
                <TableCell>Time</TableCell>
                <TableCell>Status</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {activities.map((activity, index) => (
                <TableRow key={index}>
                  <TableCell>{activity.user}</TableCell>
                  <TableCell>{activity.action}</TableCell>
                  <TableCell>{activity.target}</TableCell>
                  <TableCell>{activity.time}</TableCell>
                  <TableCell>
                    <Chip 
                      label={activity.status}
                      color="success"
                      size="small"
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </Container>
  );
}
