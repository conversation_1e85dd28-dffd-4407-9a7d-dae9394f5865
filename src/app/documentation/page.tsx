'use client';

import React from 'react';
import { Container, Typography, Card, CardContent, Grid, Button, Box } from '@mui/material';
import { Book, PlayArrow, Download, Search } from '@mui/icons-material';

const docSections = [
  { title: "Getting Started", description: "Quick start guide and basic setup", articles: 12 },
  { title: "User Guide", description: "Comprehensive user documentation", articles: 45 },
  { title: "API Reference", description: "Complete API documentation", articles: 28 },
  { title: "Troubleshooting", description: "Common issues and solutions", articles: 18 }
];

export default function DocumentationPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
        Documentation
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        Comprehensive documentation and guides for BidBeez platform
      </Typography>
      
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button variant="contained" startIcon={<Search />}>
              Search Documentation
            </Button>
            <Button variant="outlined" startIcon={<Download />}>
              Download PDF
            </Button>
          </Box>
        </CardContent>
      </Card>

      <Grid container spacing={3}>
        {docSections.map((section, index) => (
          <Grid item xs={12} md={6} key={index}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>{section.title}</Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {section.description}
                </Typography>
                <Typography variant="caption" color="text.secondary" gutterBottom>
                  {section.articles} articles
                </Typography>
                <Box sx={{ mt: 2 }}>
                  <Button variant="outlined" size="small" startIcon={<Book />}>
                    Browse Articles
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Container>
  );
}
