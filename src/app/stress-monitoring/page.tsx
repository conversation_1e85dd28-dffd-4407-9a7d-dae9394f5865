'use client';

import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Box, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  Alert,
  Button,
  Chip,
  LinearProgress,
  CircularProgress,
  Paper,
  Stack
} from '@mui/material';
import { 
  MonitorHeart, 
  TrendingUp, 
  Warning, 
  CheckCircle,
  Refresh,
  Settings,
  Timeline,
  Psychology,
  Speed,
  Healing
} from '@mui/icons-material';

export default function StressMonitoringPage() {
  const [currentStressLevel, setCurrentStressLevel] = useState(34);
  const [isMonitoring, setIsMonitoring] = useState(true);

  // Mock stress monitoring data
  const stressMetrics = {
    currentLevel: 34,
    averageToday: 28,
    peakToday: 67,
    lowToday: 12,
    weeklyAverage: 31,
    stressEvents: 8
  };

  const stressFactors = [
    { factor: 'Deadline Pressure', impact: 45, trend: 'increasing', color: '#f44336' },
    { factor: 'Competition Intensity', impact: 32, trend: 'stable', color: '#ff9800' },
    { factor: 'Workload Volume', impact: 28, trend: 'decreasing', color: '#4caf50' },
    { factor: 'Technical Complexity', impact: 23, trend: 'stable', color: '#2196f3' },
    { factor: 'Client Expectations', impact: 19, trend: 'increasing', color: '#9c27b0' }
  ];

  const recentEvents = [
    { id: 1, time: '14:23', event: 'Stress spike detected', level: 67, trigger: 'Bid deadline approaching' },
    { id: 2, time: '12:45', event: 'Stress normalized', level: 28, trigger: 'Break taken' },
    { id: 3, time: '11:30', event: 'Elevated stress', level: 52, trigger: 'Competitor analysis' },
    { id: 4, time: '09:15', event: 'Low stress period', level: 15, trigger: 'Morning routine' }
  ];

  const getStressColor = (level: number) => {
    if (level >= 60) return 'error';
    if (level >= 40) return 'warning';
    if (level >= 20) return 'info';
    return 'success';
  };

  const getStressLabel = (level: number) => {
    if (level >= 60) return 'High Stress';
    if (level >= 40) return 'Moderate Stress';
    if (level >= 20) return 'Low Stress';
    return 'Minimal Stress';
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold" color="primary.main">
          💓 Stress Monitoring System
        </Typography>
        <Typography variant="h6" color="text.primary" sx={{ mb: 2 }}>
          Real-time psychological stress tracking and wellness optimization for peak bidding performance
        </Typography>
        
        <Alert severity={currentStressLevel >= 60 ? 'error' : currentStressLevel >= 40 ? 'warning' : 'success'} sx={{ mb: 3 }}>
          <strong>Current Status:</strong> {getStressLabel(currentStressLevel)} ({currentStressLevel}%) - 
          {currentStressLevel >= 60 ? ' Immediate intervention recommended' : 
           currentStressLevel >= 40 ? ' Monitor closely and consider stress reduction' : 
           ' Optimal stress levels for performance'}
        </Alert>
      </Box>

      {/* Real-time Stress Monitor */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h6" gutterBottom color="text.primary">
                Current Stress Level
              </Typography>
              <Box sx={{ position: 'relative', display: 'inline-flex', mb: 2 }}>
                <CircularProgress
                  variant="determinate"
                  value={currentStressLevel}
                  size={120}
                  thickness={6}
                  color={getStressColor(currentStressLevel) as any}
                />
                <Box
                  sx={{
                    top: 0,
                    left: 0,
                    bottom: 0,
                    right: 0,
                    position: 'absolute',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    flexDirection: 'column'
                  }}
                >
                  <Typography variant="h4" component="div" color="text.primary" fontWeight="bold">
                    {currentStressLevel}%
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {getStressLabel(currentStressLevel)}
                  </Typography>
                </Box>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2 }}>
                <Button 
                  variant={isMonitoring ? "outlined" : "contained"} 
                  onClick={() => setIsMonitoring(!isMonitoring)}
                  color={isMonitoring ? "error" : "success"}
                >
                  {isMonitoring ? 'Stop Monitoring' : 'Start Monitoring'}
                </Button>
                <Button variant="outlined" startIcon={<Settings />}>
                  Configure
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom color="text.primary">
                Stress Metrics Today
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h5" color="info.main">{stressMetrics.averageToday}%</Typography>
                    <Typography variant="body2" color="text.secondary">Average</Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h5" color="error.main">{stressMetrics.peakToday}%</Typography>
                    <Typography variant="body2" color="text.secondary">Peak</Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h5" color="success.main">{stressMetrics.lowToday}%</Typography>
                    <Typography variant="body2" color="text.secondary">Lowest</Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h5" color="warning.main">{stressMetrics.stressEvents}</Typography>
                    <Typography variant="body2" color="text.secondary">Events</Typography>
                  </Paper>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Stress Factors Analysis */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom color="text.primary">
                Stress Factor Analysis
              </Typography>
              <Stack spacing={3}>
                {stressFactors.map((factor, index) => (
                  <Box key={index}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="subtitle1" color="text.primary">
                        {factor.factor}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body2" color="text.primary">
                          {factor.impact}%
                        </Typography>
                        <Chip 
                          label={factor.trend} 
                          size="small" 
                          color={factor.trend === 'increasing' ? 'error' : factor.trend === 'decreasing' ? 'success' : 'info'}
                        />
                      </Box>
                    </Box>
                    <LinearProgress 
                      variant="determinate" 
                      value={factor.impact} 
                      sx={{ 
                        height: 8, 
                        borderRadius: 4,
                        backgroundColor: 'rgba(255,255,255,0.1)',
                        '& .MuiLinearProgress-bar': {
                          backgroundColor: factor.color
                        }
                      }}
                    />
                  </Box>
                ))}
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom color="text.primary">
                Recent Stress Events
              </Typography>
              <Stack spacing={2}>
                {recentEvents.map((event) => (
                  <Paper key={event.id} sx={{ p: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="caption" color="text.secondary">
                        {event.time}
                      </Typography>
                      <Chip 
                        label={`${event.level}%`} 
                        size="small" 
                        color={getStressColor(event.level) as any}
                      />
                    </Box>
                    <Typography variant="subtitle2" color="text.primary" gutterBottom>
                      {event.event}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {event.trigger}
                    </Typography>
                  </Paper>
                ))}
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Stress Management Recommendations */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom color="text.primary">
            Personalized Stress Management
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Alert severity="info">
                <strong>Breathing Exercise:</strong> Try the 4-7-8 breathing technique for immediate stress relief.
              </Alert>
            </Grid>
            <Grid item xs={12} md={4}>
              <Alert severity="success">
                <strong>Break Reminder:</strong> Take a 5-minute break every hour to maintain optimal stress levels.
              </Alert>
            </Grid>
            <Grid item xs={12} md={4}>
              <Alert severity="warning">
                <strong>Workload Alert:</strong> Consider delegating tasks when stress exceeds 50% for extended periods.
              </Alert>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Container>
  );
}
