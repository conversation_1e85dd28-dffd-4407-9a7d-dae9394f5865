'use client';

import React, { useState } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Grid, 
  Card, 
  CardContent, 
  Chip, 
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Button,
  Badge
} from '@mui/material';
import { 
  ExpandMore as ExpandMoreIcon,
  Psychology as PsychologyIcon,
  SmartToy as AIIcon,
  AutoAwesome as AutomationIcon,
  Star as QueenBeeIcon,
  School as SkillIcon,
  Build as ToolIcon,
  Gavel as ComplianceIcon,
  Dashboard as DashboardIcon,
  Analytics as AnalyticsIcon,
  WhatsApp as WhatsAppIcon,
  Security as SecurityIcon,
  Assignment as AssignmentIcon,
  Search as SearchIcon,
  Business as BusinessIcon,
  Home as HomeIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
  Calculate as CalculateIcon,
  Timeline as TimelineIcon
} from '@mui/icons-material';

export default function ComprehensiveSitemapPage() {
  const [expandedService, setExpandedService] = useState<string | false>('core');

  const handleServiceChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedService(isExpanded ? panel : false);
  };

  // Comprehensive service structure with all 92 pages
  const services = [
    {
      id: 'core',
      name: 'Core Platform Services',
      icon: <DashboardIcon />,
      description: 'Essential platform functionality and user interfaces',
      pageCount: 22,
      color: 'primary',
      pages: [
        { name: 'Home/Landing Page', url: '/', status: 'Live', description: 'Main platform entry point' },
        { name: 'Main Dashboard', url: '/dashboard', status: 'Live', description: 'Central user dashboard' },
        { name: 'Analytics Dashboard', url: '/analytics', status: 'Live', description: 'Performance insights' },
        { name: 'Advanced Analytics', url: '/analytics/advanced', status: 'Live', description: 'Comprehensive analytics' },
        { name: 'Tender Discovery', url: '/tenders', status: 'Live', description: 'AI-powered tender search' },
        { name: 'Bid Management', url: '/bids', status: 'Live', description: 'Complete bid lifecycle' },
        { name: 'Supplier Dashboard', url: '/supplier', status: 'Live', description: 'Business performance tracking' },
        { name: 'Login Page', url: '/auth/login', status: 'Live', description: 'User authentication' },
        { name: 'Registration Page', url: '/auth/register', status: 'Live', description: 'New user registration' },
        { name: 'All Pages Directory', url: '/all-pages', status: 'Live', description: 'Complete page listing' },
        { name: 'Original Sitemap', url: '/sitemap', status: 'Live', description: 'Basic page overview' },
        { name: 'Simple Demo Page', url: '/simple', status: 'Demo', description: 'Basic demonstration' }
      ]
    },
    {
      id: 'psychological',
      name: 'Psychological Profiling Suite',
      icon: <PsychologyIcon />,
      description: 'Advanced behavioral analysis and psychological optimization',
      pageCount: 15,
      color: 'secondary',
      pages: [
        { name: 'Sales Rep Psychological Center', url: '/sales-rep-center', status: 'Live', description: 'Behavioral optimization hub' },
        { name: 'Sales Rep Onboarding', url: '/sales-rep-onboarding', status: 'Live', description: 'Archetype detection onboarding' },
        { name: 'Psychological Assessment Center', url: '/psychological-assessment', status: 'Live', description: 'Comprehensive psychological testing' },
        { name: 'Archetype Detection Dashboard', url: '/archetype-detection', status: 'Live', description: 'AI-powered archetype analysis' },
        { name: 'Behavioral Analytics', url: '/behavioral-analytics', status: 'Live', description: 'Pattern recognition and analysis' },
        { name: 'Stress Monitoring System', url: '/stress-monitoring', status: 'Planned', description: 'Real-time stress tracking' },
        { name: 'Confidence Coaching Hub', url: '/confidence-coaching', status: 'Planned', description: 'Performance optimization' },
        { name: 'Cognitive Load Manager', url: '/cognitive-load', status: 'Planned', description: 'Mental capacity optimization' },
        { name: 'Engagement Optimizer', url: '/engagement-optimizer', status: 'Planned', description: 'User engagement enhancement' },
        { name: 'Personality Insights', url: '/personality-insights', status: 'Planned', description: 'Deep personality analysis' },
        { name: 'Psychological Reports', url: '/psychological-reports', status: 'Planned', description: 'Comprehensive reporting' },
        { name: 'Behavioral Patterns', url: '/behavioral-patterns', status: 'Planned', description: 'Pattern visualization' },
        { name: 'Mental State Tracker', url: '/mental-state-tracker', status: 'Planned', description: 'Real-time mental state' },
        { name: 'Psychological Settings', url: '/psychological-settings', status: 'Planned', description: 'Profiling configuration' },
        { name: 'Archetype Profiles', url: '/archetype-profiles', status: 'Planned', description: 'Detailed archetype library' }
      ]
    },
    {
      id: 'ai',
      name: 'AI-Powered Intelligence',
      icon: <AIIcon />,
      description: 'Machine learning and artificial intelligence systems',
      pageCount: 15,
      color: 'info',
      pages: [
        { name: 'AI Insights Dashboard', url: '/ai-insights', status: 'Live', description: 'Central AI intelligence hub' },
        { name: 'AI Engine Dashboard', url: '/ai-engine', status: 'Live', description: 'AI system management' },
        { name: 'Market Intelligence Center', url: '/market-intelligence', status: 'Live', description: 'Competitive analysis hub' },
        { name: 'Win Probability Calculator', url: '/win-probability', status: 'Live', description: 'AI-powered win predictions' },
        { name: 'Competitive Analysis', url: '/competitive-analysis', status: 'Planned', description: 'Competitor intelligence' },
        { name: 'Pricing Optimization', url: '/pricing-optimization', status: 'Planned', description: 'AI pricing strategies' },
        { name: 'Risk Assessment Hub', url: '/risk-assessment', status: 'Planned', description: 'Financial risk analysis' },
        { name: 'Predictive Analytics', url: '/predictive-analytics', status: 'Planned', description: 'Future trend prediction' },
        { name: 'AI Recommendations', url: '/ai-recommendations', status: 'Planned', description: 'Personalized suggestions' },
        { name: 'Market Trends', url: '/market-trends', status: 'Planned', description: 'Market analysis' },
        { name: 'AI Settings & Config', url: '/ai-settings', status: 'Planned', description: 'AI system configuration' },
        { name: 'Machine Learning Models', url: '/ml-models', status: 'Planned', description: 'Model management' },
        { name: 'AI Performance Metrics', url: '/ai-performance', status: 'Planned', description: 'AI system analytics' },
        { name: 'Intelligent Matching', url: '/intelligent-matching', status: 'Planned', description: 'AI-powered matching' },
        { name: 'AI Training Center', url: '/ai-training', status: 'Planned', description: 'Model training interface' }
      ]
    },
    {
      id: 'automation',
      name: 'Automation & Integration',
      icon: <AutomationIcon />,
      description: 'Automated workflows and system integrations',
      pageCount: 10,
      color: 'success',
      pages: [
        { name: 'WhatsApp Auto-Bidding', url: '/whatsapp', status: 'Live', description: 'Automated WhatsApp bidding' },
        { name: 'WhatsApp Dashboard', url: '/whatsapp/dashboard', status: 'Live', description: 'WhatsApp automation control' },
        { name: 'Automation Hub', url: '/automation-hub', status: 'Live', description: 'Central automation control' },
        { name: 'Auto-Bid Configuration', url: '/auto-bid-config', status: 'Planned', description: 'Bidding automation setup' },
        { name: 'Message Templates', url: '/message-templates', status: 'Planned', description: 'Communication templates' },
        { name: 'Automation Analytics', url: '/automation-analytics', status: 'Planned', description: 'Automation performance' },
        { name: 'Integration Settings', url: '/integration-settings', status: 'Planned', description: 'System integrations' },
        { name: 'API Management', url: '/api-management', status: 'Planned', description: 'API configuration' },
        { name: 'Webhook Configuration', url: '/webhook-config', status: 'Planned', description: 'Webhook management' },
        { name: 'Automation Rules', url: '/automation-rules', status: 'Planned', description: 'Rule configuration' }
      ]
    },
    {
      id: 'compliance',
      name: 'Compliance & Legal',
      icon: <ComplianceIcon />,
      description: 'SA legal compliance and regulatory management',
      pageCount: 10,
      color: 'warning',
      pages: [
        { name: 'SA Compliance Tools', url: '/compliance', status: 'Live', description: 'Legal compliance management' },
        { name: 'Bid Protests', url: '/compliance/protests', status: 'Live', description: 'Bid protest management' },
        { name: 'Compliance Dashboard', url: '/compliance-dashboard', status: 'Live', description: 'Comprehensive compliance hub' },
        { name: 'Legal Framework Manager', url: '/legal-framework', status: 'Planned', description: 'Legal framework management' },
        { name: 'Protest Wizard', url: '/protest-wizard', status: 'Planned', description: 'Guided protest creation' },
        { name: 'Evidence Manager', url: '/evidence-manager', status: 'Planned', description: 'Legal evidence management' },
        { name: 'Deadline Tracker', url: '/deadline-tracker', status: 'Planned', description: 'Compliance deadline tracking' },
        { name: 'Irregularity Detector', url: '/irregularity-detector', status: 'Planned', description: 'Automated irregularity detection' },
        { name: 'Compliance Reports', url: '/compliance-reports', status: 'Planned', description: 'Compliance reporting' },
        { name: 'Legal Templates', url: '/legal-templates', status: 'Planned', description: 'Legal document templates' }
      ]
    },
    {
      id: 'ecosystem',
      name: 'Ecosystem & Marketplace',
      icon: <SkillIcon />,
      description: 'SkillSync and ToolSync marketplace integration',
      pageCount: 10,
      color: 'info',
      pages: [
        { name: 'SkillSync Marketplace', url: '/skillsync', status: 'Live', description: 'Skill provider discovery' },
        { name: 'ToolSync Management', url: '/toolsync', status: 'Live', description: 'Software license optimization' },
        { name: 'Ecosystem Overview', url: '/ecosystem-overview', status: 'Planned', description: 'Marketplace management' },
        { name: 'Skill Provider Profiles', url: '/skill-providers', status: 'Planned', description: 'Provider management' },
        { name: 'Tool License Exchange', url: '/license-exchange', status: 'Planned', description: 'License trading' },
        { name: 'Marketplace Analytics', url: '/marketplace-analytics', status: 'Planned', description: 'Ecosystem analytics' },
        { name: 'Provider Verification', url: '/provider-verification', status: 'Planned', description: 'Provider validation' },
        { name: 'Skill Matching Engine', url: '/skill-matching', status: 'Planned', description: 'AI skill matching' },
        { name: 'License Optimization', url: '/license-optimization', status: 'Planned', description: 'License cost optimization' },
        { name: 'Partnership Management', url: '/partnership-management', status: 'Planned', description: 'Partnership coordination' }
      ]
    },
    {
      id: 'management',
      name: 'System Management',
      icon: <QueenBeeIcon />,
      description: 'Platform administration and system control',
      pageCount: 10,
      color: 'error',
      pages: [
        { name: 'Gamification Hub', url: '/gamification', status: 'Live', description: 'Achievement and rewards system' },
        { name: 'RFQ Management', url: '/rfq', status: 'Live', description: 'Request for quotations' },
        { name: 'Queen Bee Management', url: '/queen-bee-management', status: 'Live', description: 'Task orchestration system' },
        { name: 'Performance Metrics', url: '/performance-metrics', status: 'Planned', description: 'System performance analytics' },
        { name: 'Feature Flags Manager', url: '/feature-flags', status: 'Planned', description: 'Dynamic feature control' },
        { name: 'System Administration', url: '/system-admin', status: 'Planned', description: 'Platform administration' },
        { name: 'Courier Dispatch', url: '/courier-dispatch', status: 'Planned', description: 'Delivery management' },
        { name: 'Delivery Tracking', url: '/delivery-tracking', status: 'Planned', description: 'Package tracking' },
        { name: 'Production Optimizer', url: '/production-optimizer', status: 'Planned', description: 'Performance optimization' },
        { name: 'Platform Analytics', url: '/platform-analytics', status: 'Planned', description: 'Platform-wide analytics' }
      ]
    }
  ];

  const totalPages = services.reduce((sum, service) => sum + service.pageCount, 0);
  const livePages = services.reduce((sum, service) => 
    sum + service.pages.filter(page => page.status === 'Live').length, 0
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Live': return 'success';
      case 'Demo': return 'info';
      case 'Planned': return 'warning';
      default: return 'default';
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold" color="primary.main">
          🗺️ Comprehensive Platform Sitemap
        </Typography>
        <Typography variant="h6" color="text.primary" sx={{ mb: 2 }}>
          Complete overview of all BidBeez services, features, and 92 sophisticated pages
        </Typography>
        
        <Alert severity="success" sx={{ mb: 3 }}>
          <strong>Platform Status:</strong> {livePages} of {totalPages} pages live with 7 major service categories and full AI-powered functionality.
        </Alert>
      </Box>

      {/* Platform Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h3" fontWeight="bold" color="primary.main">
                {totalPages}
              </Typography>
              <Typography variant="body1" color="text.primary">Total Pages</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h3" fontWeight="bold" color="success.main">
                {livePages}
              </Typography>
              <Typography variant="body1" color="text.primary">Live Pages</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h3" fontWeight="bold" color="info.main">
                {services.length}
              </Typography>
              <Typography variant="body1" color="text.primary">Service Categories</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h3" fontWeight="bold" color="warning.main">
                {Math.round((livePages / totalPages) * 100)}%
              </Typography>
              <Typography variant="body1" color="text.primary">Completion Rate</Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Service Categories */}
      <Typography variant="h4" gutterBottom color="text.primary" fontWeight="bold" sx={{ mb: 3 }}>
        Service Categories & Pages
      </Typography>

      {services.map((service) => (
        <Accordion 
          key={service.id}
          expanded={expandedService === service.id}
          onChange={handleServiceChange(service.id)}
          sx={{ mb: 2 }}
        >
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
              <Box sx={{ color: `${service.color}.main` }}>
                {service.icon}
              </Box>
              <Box sx={{ flexGrow: 1 }}>
                <Typography variant="h6" color="text.primary" fontWeight="bold">
                  {service.name}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {service.description}
                </Typography>
              </Box>
              <Badge badgeContent={service.pageCount} color={service.color as any}>
                <Chip 
                  label={`${service.pages.filter(p => p.status === 'Live').length} Live`}
                  color={service.color as any}
                  size="small"
                />
              </Badge>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <List>
              {service.pages.map((page, index) => (
                <React.Fragment key={index}>
                  <ListItem>
                    <ListItemIcon>
                      <Chip 
                        label={page.status} 
                        color={getStatusColor(page.status) as any}
                        size="small"
                      />
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Typography variant="subtitle1" color="text.primary" fontWeight="medium">
                          {page.name}
                        </Typography>
                      }
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            {page.description}
                          </Typography>
                          <Typography variant="caption" color="primary.main">
                            {page.url}
                          </Typography>
                        </Box>
                      }
                    />
                    <Button 
                      size="small" 
                      variant="outlined" 
                      href={page.url}
                      disabled={page.status === 'Planned'}
                    >
                      {page.status === 'Live' ? 'Visit' : page.status}
                    </Button>
                  </ListItem>
                  {index < service.pages.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </AccordionDetails>
        </Accordion>
      ))}

      {/* Quick Navigation */}
      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom color="text.primary">
            Quick Navigation
          </Typography>
          <Grid container spacing={2}>
            <Grid item>
              <Button variant="contained" href="/all-pages">
                All Pages Directory
              </Button>
            </Grid>
            <Grid item>
              <Button variant="outlined" href="/dashboard">
                Main Dashboard
              </Button>
            </Grid>
            <Grid item>
              <Button variant="outlined" href="/psychological-assessment">
                Psychological Center
              </Button>
            </Grid>
            <Grid item>
              <Button variant="outlined" href="/ai-engine">
                AI Engine
              </Button>
            </Grid>
            <Grid item>
              <Button variant="outlined" href="/automation-hub">
                Automation Hub
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Container>
  );
}
