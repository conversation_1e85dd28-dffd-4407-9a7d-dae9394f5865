'use client';

import React, { useState } from 'react';
import {
  <PERSON>,
  Container,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  Button,
  Stack,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Avatar,
  LinearProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  Assignment,
  LocationOn,
  Star,
  Visibility,
  Add,
  Phone,
  Message,
  Navigation,
  CheckCircle,
  Schedule,
  AttachMoney,
  Group,
  TrendingUp,
  Security
} from '@mui/icons-material';

// Client-facing bee management interfaces
interface ClientBeeWorker {
  id: string;
  beeId: string;
  fullName: string;
  avatar?: string;
  rating: number;
  specialties: string[];
  currentLocation: string;
  status: 'available' | 'busy' | 'offline';
  verificationLevel: string;
  completedTasks: number;
  successRate: number;
  currentTask?: {
    id: string;
    title: string;
    progress: number;
    estimatedCompletion: string;
  };
}

interface ClientTask {
  id: string;
  title: string;
  type: 'document_collection' | 'site_visit' | 'briefing_attendance' | 'form_completion' | 'courier_delivery' | 'courier_pickup' | 'compliance_checking' | 'technical_evaluation' | 'ad_hoc';
  status: 'pending' | 'assigned' | 'in_progress' | 'completed';
  assignedBee?: ClientBeeWorker;
  location: string;
  deadline: string;
  budget: number;
  progress: number;
  createdAt: string;

  // MANDATORY TENDER ASSOCIATION
  tenderId: string; // Must be valid BidBeez tender ID
  tenderTitle: string;
  tenderSource: 'bidbeez_core' | 'skillsync' | 'toolsync' | 'government_portal' | 'supplier_network';
  tenderReference: string;

  // COURIER SPECIFIC FIELDS
  courierDetails?: {
    pickupAddress?: string;
    deliveryAddress?: string;
    deliveryMode: 'bee_direct' | 'courier' | 'bee_air_bee' | 'bee_air_bee_extended' | 'courier_plus_bee';
    documentType: string;
    specialRequirements: string[];
    trackingNumber?: string;
  };
}

export default function ClientBeeManagementPage() {
  const [currentTab, setCurrentTab] = useState(0);
  const [taskDialogOpen, setTaskDialogOpen] = useState(false);
  const [selectedBee, setSelectedBee] = useState<ClientBeeWorker | null>(null);
  const [newTask, setNewTask] = useState({
    title: '',
    type: 'document_collection',
    location: '',
    deadline: '',
    budget: 0,
    description: '',
    tenderId: '',
    tenderTitle: '',
    tenderSource: 'bidbeez_core',
    tenderReference: '',
    courierDetails: {
      pickupAddress: '',
      deliveryAddress: '',
      deliveryMode: 'bee_direct',
      documentType: '',
      specialRequirements: []
    }
  });

  const [availableTenders] = useState([
    { id: 'BID-20240115-JHB001', title: 'Municipal Infrastructure Development', source: 'government_portal', reference: 'COJ-INF-2024-001' },
    { id: 'BID-20240116-DOH002', title: 'Medical Equipment Procurement', source: 'government_portal', reference: 'DOH-MED-2024-002' },
    { id: 'BID-20240113-SKILL001', title: 'Government IT Security Infrastructure', source: 'skillsync', reference: 'SITA-SEC-2024-001' },
    { id: 'BID-20240117-TOOL001', title: 'Construction Equipment Rental', source: 'toolsync', reference: 'PWD-EQUIP-2024-001' },
    { id: 'BID-20240118-SUP001', title: 'Office Supplies Procurement', source: 'supplier_network', reference: 'SAPS-OFF-2024-001' }
  ]);

  const [tenderValidation, setTenderValidation] = useState({
    isValid: false,
    message: '',
    tenderDetails: null
  });

  // Mock data for client view
  const [availableBees] = useState<ClientBeeWorker[]>([
    {
      id: 'bee-001',
      beeId: 'BEE-JHB-001',
      fullName: 'Sarah Mthembu',
      avatar: '/avatars/bee-1.jpg',
      rating: 4.8,
      specialties: ['Document Collection', 'Site Visits', 'Form Completion'],
      currentLocation: 'Sandton, Johannesburg',
      status: 'available',
      verificationLevel: 'Premium',
      completedTasks: 47,
      successRate: 96
    },
    {
      id: 'bee-002',
      beeId: 'BEE-CPT-002',
      fullName: 'Michael Johnson',
      avatar: '/avatars/bee-2.jpg',
      rating: 4.9,
      specialties: ['Tender Preparation', 'Compliance Checking', 'Technical Evaluation'],
      currentLocation: 'Cape Town CBD',
      status: 'busy',
      verificationLevel: 'Elite',
      completedTasks: 63,
      successRate: 98,
      currentTask: {
        id: 'task-101',
        title: 'Municipal Tender Form Completion',
        progress: 75,
        estimatedCompletion: '2024-01-16T14:00:00'
      }
    },
    {
      id: 'bee-003',
      beeId: 'BEE-DBN-003',
      fullName: 'Priya Patel',
      avatar: '/avatars/bee-3.jpg',
      rating: 4.7,
      specialties: ['Site Visits', 'Briefing Attendance', 'Document Processing'],
      currentLocation: 'Durban North',
      status: 'available',
      verificationLevel: 'Standard',
      completedTasks: 34,
      successRate: 94
    }
  ]);

  const [clientTasks] = useState<ClientTask[]>([
    {
      id: 'task-001',
      title: 'Municipal Document Collection',
      type: 'document_collection',
      status: 'in_progress',
      assignedBee: availableBees[0],
      location: 'City of Johannesburg Offices',
      deadline: '2024-01-15T16:00:00',
      budget: 450,
      progress: 65,
      createdAt: '2024-01-15T08:00:00',
      tenderId: 'BID-20240115-JHB001',
      tenderTitle: 'Municipal Infrastructure Development - Phase 3',
      tenderSource: 'government_portal',
      tenderReference: 'COJ-INF-2024-001'
    },
    {
      id: 'task-002',
      title: 'Urgent Courier Delivery - Medical Equipment Bid',
      type: 'courier_delivery',
      status: 'in_progress',
      assignedBee: availableBees[1],
      location: 'Department of Health, Bhisho',
      deadline: '2024-01-16T12:00:00',
      budget: 850,
      progress: 75,
      createdAt: '2024-01-15T15:00:00',
      tenderId: 'BID-20240116-DOH002',
      tenderTitle: 'Medical Equipment Procurement - Eastern Cape',
      tenderSource: 'government_portal',
      tenderReference: 'DOH-MED-2024-002',
      courierDetails: {
        pickupAddress: 'BidBeez Office, Sandton City, Johannesburg',
        deliveryAddress: 'Department of Health, Bhisho, Eastern Cape',
        deliveryMode: 'bee_direct',
        documentType: 'bid_submission',
        specialRequirements: ['signature_required', 'photo_evidence', 'time_sensitive'],
        trackingNumber: 'BEE-DEL-20240115-001'
      }
    },
    {
      id: 'task-003',
      title: 'Site Visit - Construction Project',
      type: 'site_visit',
      status: 'completed',
      assignedBee: availableBees[2],
      location: 'Midrand Construction Site',
      deadline: '2024-01-13T17:00:00',
      budget: 800,
      progress: 100,
      createdAt: '2024-01-13T07:00:00',
      tenderId: 'tender-003',
      tenderTitle: 'Construction Project Evaluation'
    },
    {
      id: 'task-004',
      title: 'Compliance Documentation Review',
      type: 'compliance_checking',
      status: 'pending',
      location: 'Remote Work',
      deadline: '2024-01-18T16:00:00',
      budget: 600,
      progress: 0,
      createdAt: '2024-01-15T16:30:00',
      tenderId: 'tender-004',
      tenderTitle: 'Healthcare Equipment Tender'
    }
  ]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'in_progress': return 'info';
      case 'assigned': return 'warning';
      case 'pending': return 'default';
      default: return 'default';
    }
  };

  const getBeeStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'success';
      case 'busy': return 'warning';
      case 'offline': return 'error';
      default: return 'default';
    }
  };

  const validateTender = (tenderId: string) => {
    const tender = availableTenders.find(t => t.id === tenderId);
    if (tender) {
      setTenderValidation({
        isValid: true,
        message: `✅ Valid tender found: ${tender.title}`,
        tenderDetails: tender
      });
      setNewTask(prev => ({
        ...prev,
        tenderTitle: tender.title,
        tenderSource: tender.source,
        tenderReference: tender.reference
      }));
    } else {
      setTenderValidation({
        isValid: false,
        message: '❌ Invalid tender ID. Please select from available tenders.',
        tenderDetails: null
      });
    }
  };

  const handleCreateTask = () => {
    if (!tenderValidation.isValid) {
      alert('Please select a valid tender before creating the task.');
      return;
    }

    // In real app, this would create a task and assign to bee
    setTaskDialogOpen(false);
    setNewTask({
      title: '',
      type: 'document_collection',
      location: '',
      deadline: '',
      budget: 0,
      description: '',
      tenderId: '',
      tenderTitle: '',
      tenderSource: 'bidbeez_core',
      tenderReference: '',
      courierDetails: {
        pickupAddress: '',
        deliveryAddress: '',
        deliveryMode: 'bee_direct',
        documentType: '',
        specialRequirements: []
      }
    });
    setTenderValidation({ isValid: false, message: '', tenderDetails: null });
    alert('Task created and assigned to bee worker!');
  };

  const handleAssignBee = (task: ClientTask, bee: ClientBeeWorker) => {
    // In real app, this would assign the bee to the task
    alert(`Assigned ${bee.fullName} to task: ${task.title}`);
  };

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" fontWeight="bold" gutterBottom>
          🐝 Bee Worker Management
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage and track your bee workers for tender-related tasks
        </Typography>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Group sx={{ fontSize: 32, color: 'primary.main', mb: 1 }} />
              <Typography variant="h5" fontWeight="bold">
                {availableBees.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Available Bees
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Assignment sx={{ fontSize: 32, color: 'info.main', mb: 1 }} />
              <Typography variant="h5" fontWeight="bold">
                {clientTasks.filter(t => t.status === 'in_progress').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Active Tasks
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <CheckCircle sx={{ fontSize: 32, color: 'success.main', mb: 1 }} />
              <Typography variant="h5" fontWeight="bold">
                {clientTasks.filter(t => t.status === 'completed').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Completed Tasks
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <AttachMoney sx={{ fontSize: 32, color: 'warning.main', mb: 1 }} />
              <Typography variant="h5" fontWeight="bold">
                R{clientTasks.reduce((sum, task) => sum + task.budget, 0).toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Budget
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Action Button */}
      <Box sx={{ mb: 3 }}>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => setTaskDialogOpen(true)}
          size="large"
        >
          Create New Bee Task
        </Button>
      </Box>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={currentTab} onChange={(e, newValue) => setCurrentTab(newValue)}>
          <Tab label="My Tasks" />
          <Tab label="Available Bees" />
          <Tab label="Task Analytics" />
        </Tabs>
      </Box>

      {/* Tab Content */}
      {currentTab === 0 && (
        <Grid container spacing={3}>
          {clientTasks.map((task) => (
            <Grid item xs={12} key={task.id}>
              <Card>
                <CardContent>
                  <Grid container spacing={3} alignItems="center">
                    <Grid item xs={12} md={8}>
                      <Typography variant="h6" fontWeight="bold" gutterBottom>
                        {task.title}
                      </Typography>
                      
                      <Stack direction="row" spacing={1} sx={{ mb: 2 }}>
                        <Chip 
                          label={task.status.toUpperCase()}
                          color={getStatusColor(task.status) as any}
                          size="small"
                        />
                        <Chip 
                          label={task.type.replace('_', ' ').toUpperCase()}
                          variant="outlined"
                          size="small"
                        />
                        {task.tenderId && (
                          <Chip 
                            label={`Tender: ${task.tenderTitle}`}
                            color="info"
                            variant="outlined"
                            size="small"
                          />
                        )}
                      </Stack>

                      <Grid container spacing={2} sx={{ mb: 2 }}>
                        <Grid item xs={12} sm={6}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <LocationOn sx={{ fontSize: 16, color: 'text.secondary' }} />
                            <Typography variant="body2" color="text.secondary">
                              {task.location}
                            </Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Schedule sx={{ fontSize: 16, color: 'text.secondary' }} />
                            <Typography variant="body2" color="text.secondary">
                              Due: {new Date(task.deadline).toLocaleDateString()}
                            </Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <AttachMoney sx={{ fontSize: 16, color: 'text.secondary' }} />
                            <Typography variant="body2" color="text.secondary">
                              Budget: R{task.budget.toLocaleString()}
                            </Typography>
                          </Box>
                        </Grid>
                        {task.assignedBee && (
                          <Grid item xs={12} sm={6}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Avatar sx={{ width: 20, height: 20 }}>🐝</Avatar>
                              <Typography variant="body2" color="text.secondary">
                                Assigned: {task.assignedBee.fullName}
                              </Typography>
                            </Box>
                          </Grid>
                        )}
                      </Grid>

                      {task.status === 'in_progress' && (
                        <Box sx={{ mb: 2 }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                            <Typography variant="body2" color="text.secondary">
                              Progress
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {task.progress}%
                            </Typography>
                          </Box>
                          <LinearProgress 
                            variant="determinate" 
                            value={task.progress}
                            sx={{ height: 8, borderRadius: 4 }}
                          />
                        </Box>
                      )}
                    </Grid>

                    <Grid item xs={12} md={4}>
                      <Stack spacing={2}>
                        {task.assignedBee ? (
                          <>
                            <Button
                              variant="outlined"
                              startIcon={<Visibility />}
                              fullWidth
                            >
                              Track Progress
                            </Button>
                            <Button
                              variant="outlined"
                              startIcon={<Message />}
                              fullWidth
                            >
                              Message Bee
                            </Button>
                            <Button
                              variant="outlined"
                              startIcon={<Phone />}
                              fullWidth
                            >
                              Call Bee
                            </Button>
                          </>
                        ) : (
                          <Button
                            variant="contained"
                            startIcon={<Assignment />}
                            onClick={() => setSelectedBee(availableBees[0])}
                            fullWidth
                          >
                            Assign Bee
                          </Button>
                        )}
                      </Stack>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {currentTab === 1 && (
        <Grid container spacing={3}>
          {availableBees.map((bee) => (
            <Grid item xs={12} md={6} key={bee.id}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                    <Avatar sx={{ width: 60, height: 60, bgcolor: 'warning.main' }}>
                      🐝
                    </Avatar>
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="h6" fontWeight="bold">
                        {bee.fullName}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {bee.beeId} • {bee.currentLocation}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                        <Star sx={{ color: 'gold', fontSize: 16 }} />
                        <Typography variant="body2">{bee.rating}</Typography>
                        <Chip 
                          label={bee.status.toUpperCase()}
                          color={getBeeStatusColor(bee.status) as any}
                          size="small"
                        />
                        <Chip 
                          label={bee.verificationLevel}
                          color="primary"
                          variant="outlined"
                          size="small"
                        />
                      </Box>
                    </Box>
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Specialties
                    </Typography>
                    <Stack direction="row" spacing={1} flexWrap="wrap" gap={1}>
                      {bee.specialties.map((specialty, index) => (
                        <Chip 
                          key={index}
                          label={specialty}
                          size="small"
                          variant="outlined"
                        />
                      ))}
                    </Stack>
                  </Box>

                  <Grid container spacing={2} sx={{ mb: 2 }}>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">
                        Completed Tasks
                      </Typography>
                      <Typography variant="h6">{bee.completedTasks}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">
                        Success Rate
                      </Typography>
                      <Typography variant="h6" color="success.main">
                        {bee.successRate}%
                      </Typography>
                    </Grid>
                  </Grid>

                  {bee.currentTask && (
                    <Alert severity="info" sx={{ mb: 2 }}>
                      <Typography variant="body2">
                        Currently working on: {bee.currentTask.title} ({bee.currentTask.progress}% complete)
                      </Typography>
                    </Alert>
                  )}

                  <Stack direction="row" spacing={1}>
                    <Button
                      variant={bee.status === 'available' ? 'contained' : 'outlined'}
                      startIcon={<Assignment />}
                      disabled={bee.status !== 'available'}
                      fullWidth
                    >
                      Assign Task
                    </Button>
                    <Button
                      variant="outlined"
                      startIcon={<Visibility />}
                      fullWidth
                    >
                      View Profile
                    </Button>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {currentTab === 2 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  📊 Task Performance
                </Typography>
                
                <Stack spacing={2}>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Average Completion Time
                    </Typography>
                    <Typography variant="h5">2.3 days</Typography>
                  </Box>
                  
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Success Rate
                    </Typography>
                    <Typography variant="h5" color="success.main">96%</Typography>
                  </Box>
                  
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Average Bee Rating
                    </Typography>
                    <Typography variant="h5">4.8/5.0</Typography>
                  </Box>
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  💰 Cost Analysis
                </Typography>
                
                <Stack spacing={2}>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Total Spent This Month
                    </Typography>
                    <Typography variant="h5">R{(1500 + 450 + 800).toLocaleString()}</Typography>
                  </Box>
                  
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Average Cost per Task
                    </Typography>
                    <Typography variant="h5">R583</Typography>
                  </Box>
                  
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Cost Savings vs Traditional
                    </Typography>
                    <Typography variant="h5" color="success.main">45%</Typography>
                  </Box>
                </Stack>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Create Task Dialog */}
      <Dialog open={taskDialogOpen} onClose={() => setTaskDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>🐝 Create New Bee Task</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Task Title"
                value={newTask.title}
                onChange={(e) => setNewTask({...newTask, title: e.target.value})}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Task Type</InputLabel>
                <Select
                  value={newTask.type}
                  onChange={(e) => setNewTask({...newTask, type: e.target.value})}
                >
                  <MenuItem value="document_collection">Document Collection</MenuItem>
                  <MenuItem value="site_visit">Site Visit</MenuItem>
                  <MenuItem value="form_completion">Form Completion</MenuItem>
                  <MenuItem value="briefing_attendance">Briefing Attendance</MenuItem>
                  <MenuItem value="compliance_checking">Compliance Checking</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Budget (R)"
                type="number"
                value={newTask.budget}
                onChange={(e) => setNewTask({...newTask, budget: parseInt(e.target.value)})}
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Location"
                value={newTask.location}
                onChange={(e) => setNewTask({...newTask, location: e.target.value})}
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Deadline"
                type="datetime-local"
                value={newTask.deadline}
                onChange={(e) => setNewTask({...newTask, deadline: e.target.value})}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="Task Description"
                value={newTask.description}
                onChange={(e) => setNewTask({...newTask, description: e.target.value})}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTaskDialogOpen(false)}>
            Cancel
          </Button>
          <Button 
            variant="contained" 
            onClick={handleCreateTask}
            disabled={!newTask.title || !newTask.location || !newTask.deadline}
          >
            Create & Assign Task
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
}
