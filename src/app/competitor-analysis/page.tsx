'use client';

import React from 'react';
import { Container, Typography, Card, CardContent, Grid, Box, Avatar, Chip, Table, TableBody, TableCell, TableHead, TableRow } from '@mui/material';
import { Business, TrendingUp, Assessment, Speed } from '@mui/icons-material';

const competitors = [
  { name: "TechCorp Solutions", winRate: 78, avgBid: "R1.2M", strength: "IT Services" },
  { name: "BuildMax Ltd", winRate: 65, avgBid: "R2.8M", strength: "Construction" },
  { name: "ConsultPro", winRate: 82, avgBid: "R450K", strength: "Consulting" }
];

export default function CompetitorAnalysisPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
        Competitor Analysis
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        Analyze competitor strategies and market positioning
      </Typography>
      
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="primary">
                    25
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Tracked Competitors
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <Business />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="success.main">
                    94.7%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Your Win Rate
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <TrendingUp />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Top Competitors</Typography>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Company</TableCell>
                <TableCell>Win Rate</TableCell>
                <TableCell>Avg Bid Value</TableCell>
                <TableCell>Strength Area</TableCell>
                <TableCell>Threat Level</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {competitors.map((competitor, index) => (
                <TableRow key={index}>
                  <TableCell>{competitor.name}</TableCell>
                  <TableCell>{competitor.winRate}%</TableCell>
                  <TableCell fontWeight="bold">{competitor.avgBid}</TableCell>
                  <TableCell>{competitor.strength}</TableCell>
                  <TableCell>
                    <Chip 
                      label={competitor.winRate > 80 ? 'High' : competitor.winRate > 60 ? 'Medium' : 'Low'}
                      color={competitor.winRate > 80 ? 'error' : competitor.winRate > 60 ? 'warning' : 'success'}
                      size="small"
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </Container>
  );
}
