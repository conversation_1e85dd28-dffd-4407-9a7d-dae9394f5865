'use client';

import React, { useState } from 'react';
import { 
  Con<PERSON><PERSON>, 
  <PERSON>, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  Alert,
  Button,
  Chip,
  Avatar,
  Paper,
  Stack,
  LinearProgress
} from '@mui/material';
import { 
  Hub, 
  School, 
  Build, 
  TrendingUp, 
  People,
  Handshake,
  Star,
  CheckCircle,
  Timeline,
  Assessment,
  Visibility,
  Settings
} from '@mui/icons-material';

export default function EcosystemOverviewPage() {
  // Mock ecosystem data
  const ecosystemMetrics = {
    totalPartners: 247,
    activeProviders: 189,
    skillsAvailable: 1547,
    toolsShared: 892,
    monthlyTransactions: 3420,
    ecosystemValue: 'R12.4M'
  };

  const ecosystemServices = [
    {
      id: 1,
      name: 'SkillSync Marketplace',
      description: 'AI-powered skill provider discovery and B-BBEE compliant talent matching',
      providers: 156,
      skills: 1247,
      successRate: 94.7,
      monthlyMatches: 847,
      icon: <School />,
      color: 'primary'
    },
    {
      id: 2,
      name: 'ToolSync Management',
      description: 'Software license optimization and collaborative license sharing',
      providers: 91,
      tools: 892,
      successRate: 89.3,
      monthlySavings: 'R45.6K',
      icon: <Build />,
      color: 'secondary'
    }
  ];

  const topProviders = [
    { id: 1, name: 'TechSkills SA', type: 'Skills', rating: 4.9, projects: 156, specialty: 'IT & Engineering' },
    { id: 2, name: 'LicenseHub Pro', type: 'Tools', rating: 4.8, projects: 234, specialty: 'Software Licensing' },
    { id: 3, name: 'ConsultExperts', type: 'Skills', rating: 4.7, projects: 89, specialty: 'Business Consulting' },
    { id: 4, name: 'DevTools Exchange', type: 'Tools', rating: 4.6, projects: 167, specialty: 'Development Tools' }
  ];

  const recentActivity = [
    { id: 1, action: 'New skill provider verified', provider: 'DataScience Pro', time: '5 min ago', type: 'Skills' },
    { id: 2, action: 'License sharing agreement', provider: 'Adobe Creative Suite', time: '12 min ago', type: 'Tools' },
    { id: 3, action: 'Successful skill match', provider: 'Project Manager Expert', time: '18 min ago', type: 'Skills' },
    { id: 4, action: 'Tool optimization completed', provider: 'Microsoft Office 365', time: '25 min ago', type: 'Tools' }
  ];

  const partnerships = [
    { name: 'SA Skills Development Council', type: 'Government', status: 'Active', projects: 45 },
    { name: 'Tech Industry Alliance', type: 'Industry', status: 'Active', projects: 78 },
    { name: 'B-BBEE Verification Agency', type: 'Compliance', status: 'Active', projects: 123 },
    { name: 'Software Vendors Consortium', type: 'Commercial', status: 'Active', projects: 67 }
  ];

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'Skills': return 'primary';
      case 'Tools': return 'secondary';
      case 'Government': return 'success';
      case 'Industry': return 'info';
      case 'Compliance': return 'warning';
      case 'Commercial': return 'error';
      default: return 'default';
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold" color="primary.main">
          🌐 Ecosystem Overview
        </Typography>
        <Typography variant="h6" color="text.primary" sx={{ mb: 2 }}>
          Comprehensive marketplace and ecosystem management for SkillSync and ToolSync integration
        </Typography>
        
        <Alert severity="success" sx={{ mb: 3 }}>
          <strong>Ecosystem Active:</strong> 247 partners with 189 active providers, R12.4M ecosystem value, and 3,420 monthly transactions.
        </Alert>
      </Box>

      {/* Ecosystem Metrics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Hub sx={{ fontSize: 32, color: 'primary.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {ecosystemMetrics.totalPartners}
              </Typography>
              <Typography variant="body2" color="text.secondary">Total Partners</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <People sx={{ fontSize: 32, color: 'success.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {ecosystemMetrics.activeProviders}
              </Typography>
              <Typography variant="body2" color="text.secondary">Active Providers</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <School sx={{ fontSize: 32, color: 'info.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {ecosystemMetrics.skillsAvailable}
              </Typography>
              <Typography variant="body2" color="text.secondary">Skills Available</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Build sx={{ fontSize: 32, color: 'warning.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {ecosystemMetrics.toolsShared}
              </Typography>
              <Typography variant="body2" color="text.secondary">Tools Shared</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <TrendingUp sx={{ fontSize: 32, color: 'secondary.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {ecosystemMetrics.monthlyTransactions}
              </Typography>
              <Typography variant="body2" color="text.secondary">Monthly Transactions</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Assessment sx={{ fontSize: 32, color: 'error.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {ecosystemMetrics.ecosystemValue}
              </Typography>
              <Typography variant="body2" color="text.secondary">Ecosystem Value</Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Ecosystem Services */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {ecosystemServices.map((service) => (
          <Grid item xs={12} md={6} key={service.id}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <Avatar sx={{ bgcolor: `${service.color}.main` }}>
                    {service.icon}
                  </Avatar>
                  <Box sx={{ flexGrow: 1 }}>
                    <Typography variant="h6" color="text.primary">
                      {service.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {service.description}
                    </Typography>
                  </Box>
                </Box>
                
                <Grid container spacing={2} sx={{ mb: 2 }}>
                  <Grid item xs={6}>
                    <Paper sx={{ p: 1, textAlign: 'center' }}>
                      <Typography variant="h6" color="text.primary">{service.providers}</Typography>
                      <Typography variant="caption" color="text.secondary">Providers</Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={6}>
                    <Paper sx={{ p: 1, textAlign: 'center' }}>
                      <Typography variant="h6" color="text.primary">
                        {service.skills || service.tools}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {service.skills ? 'Skills' : 'Tools'}
                      </Typography>
                    </Paper>
                  </Grid>
                </Grid>
                
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.primary" gutterBottom>
                    Success Rate: {service.successRate}%
                  </Typography>
                  <LinearProgress 
                    variant="determinate" 
                    value={service.successRate} 
                    sx={{ height: 6, borderRadius: 3 }}
                    color={service.color as any}
                  />
                </Box>
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="body2" color="text.secondary">
                    {service.monthlyMatches ? `${service.monthlyMatches} matches/month` : `${service.monthlySavings} saved/month`}
                  </Typography>
                  <Button size="small" variant="outlined" startIcon={<Visibility />}>
                    View Details
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Top Providers and Recent Activity */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom color="text.primary">
                Top Ecosystem Providers
              </Typography>
              <Stack spacing={2}>
                {topProviders.map((provider) => (
                  <Paper key={provider.id} sx={{ p: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="subtitle1" fontWeight="medium" color="text.primary">
                        {provider.name}
                      </Typography>
                      <Chip 
                        label={provider.type} 
                        color={getTypeColor(provider.type) as any}
                        size="small"
                      />
                    </Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      {provider.specialty}
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Star sx={{ fontSize: 16, color: 'warning.main' }} />
                        <Typography variant="body2" color="text.primary">
                          {provider.rating}
                        </Typography>
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        {provider.projects} projects
                      </Typography>
                    </Box>
                  </Paper>
                ))}
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom color="text.primary">
                Recent Ecosystem Activity
              </Typography>
              <Stack spacing={2}>
                {recentActivity.map((activity) => (
                  <Paper key={activity.id} sx={{ p: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="subtitle2" color="text.primary">
                        {activity.action}
                      </Typography>
                      <Chip 
                        label={activity.type} 
                        color={getTypeColor(activity.type) as any}
                        size="small"
                      />
                    </Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      {activity.provider}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {activity.time}
                    </Typography>
                  </Paper>
                ))}
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Strategic Partnerships */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom color="text.primary">
            Strategic Partnerships
          </Typography>
          <Grid container spacing={3}>
            {partnerships.map((partnership, index) => (
              <Grid item xs={12} md={3} key={index}>
                <Paper sx={{ p: 2, textAlign: 'center' }}>
                  <Handshake sx={{ fontSize: 32, color: 'success.main', mb: 1 }} />
                  <Typography variant="subtitle1" fontWeight="medium" color="text.primary" gutterBottom>
                    {partnership.name}
                  </Typography>
                  <Chip 
                    label={partnership.type} 
                    color={getTypeColor(partnership.type) as any}
                    size="small"
                    sx={{ mb: 1 }}
                  />
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Status: {partnership.status}
                  </Typography>
                  <Typography variant="body2" color="text.primary">
                    {partnership.projects} active projects
                  </Typography>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>
    </Container>
  );
}
