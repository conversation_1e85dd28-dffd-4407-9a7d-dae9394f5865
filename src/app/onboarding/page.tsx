'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Paper,
  Typography,
  Stepper,
  Step,
  StepLabel,
  Alert,
  CircularProgress
} from '@mui/material';
import { useRouter } from 'next/navigation';

import { OnboardingFlow } from '../../types/subscription';
import SubscriptionService from '../../services/SubscriptionService';
import TierSelectionStep from '../../components/onboarding/TierSelectionStep';
import PlanSelectionStep from '../../components/onboarding/PlanSelectionStep';

const steps = [
  'Welcome',
  'Organization Info',
  'Choose Tier',
  'Select Plan',
  'Payment Setup',
  'Complete'
];

export default function OnboardingPage() {
  const [onboardingFlow, setOnboardingFlow] = useState<OnboardingFlow | null>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const router = useRouter();
  const subscriptionService = SubscriptionService.getInstance();

  useEffect(() => {
    initializeOnboarding();
  }, []);

  const initializeOnboarding = async () => {
    try {
      setLoading(true);
      
      // Mock user ID - in real app, get from auth context
      const userId = 'user-123';
      
      // Check if user has existing onboarding flow
      let flow = await subscriptionService.getUserOnboardingFlow(userId);
      
      if (!flow) {
        // Create new onboarding flow
        flow = await subscriptionService.createOnboardingFlow(userId);
      }
      
      setOnboardingFlow(flow);
      setCurrentStep(flow.currentStep);
    } catch (error) {
      console.error('Error initializing onboarding:', error);
      setError('Failed to initialize onboarding. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleStepComplete = async (stepId: string, data: any) => {
    if (!onboardingFlow) return;

    try {
      setLoading(true);
      
      const updatedFlow = await subscriptionService.updateOnboardingStep(
        onboardingFlow.id,
        stepId,
        data
      );
      
      setOnboardingFlow(updatedFlow);
      setCurrentStep(updatedFlow.currentStep);
      
      // Check if onboarding is complete
      if (updatedFlow.currentStep >= updatedFlow.totalSteps - 1) {
        // Redirect to dashboard or main app
        router.push('/dashboard');
      }
    } catch (error) {
      console.error('Error updating onboarding step:', error);
      setError('Failed to save progress. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const renderCurrentStep = () => {
    if (!onboardingFlow) return null;

    const currentStepData = onboardingFlow.steps[currentStep];
    
    switch (currentStepData?.name) {
      case 'welcome':
        return (
          <WelcomeStep
            onNext={(data) => handleStepComplete('welcome', data)}
          />
        );
        
      case 'organization_type':
        return (
          <OrganizationTypeStep
            onNext={(data) => handleStepComplete('organization_type', data)}
            onBack={handleBack}
          />
        );
        
      case 'tier_selection':
        return (
          <TierSelectionStep
            onNext={(data) => handleStepComplete('tier_selection', data)}
            onBack={handleBack}
            organizationType={onboardingFlow.organizationType}
            teamSize={onboardingFlow.teamSize}
          />
        );
        
      case 'plan_selection':
        return (
          <PlanSelectionStep
            onNext={(data) => handleStepComplete('plan_selection', data)}
            onBack={handleBack}
            selectedTier={onboardingFlow.selectedTier || 'individual'}
            organizationType={onboardingFlow.organizationType}
            teamSize={onboardingFlow.teamSize}
          />
        );
        
      case 'payment':
        return (
          <PaymentStep
            onNext={(data) => handleStepComplete('payment', data)}
            onBack={handleBack}
            selectedPlan={onboardingFlow.selectedPlan}
          />
        );
        
      case 'completion':
        return (
          <CompletionStep
            onNext={(data) => handleStepComplete('completion', data)}
            onboardingFlow={onboardingFlow}
          />
        );
        
      default:
        return (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <Typography variant="h6" color="text.secondary">
              Step not found
            </Typography>
          </Box>
        );
    }
  };

  if (loading && !onboardingFlow) {
    return (
      <Container maxWidth="md" sx={{ py: 8 }}>
        <Box sx={{ textAlign: 'center' }}>
          <CircularProgress size={60} sx={{ mb: 2 }} />
          <Typography variant="h6" color="text.secondary">
            Initializing your onboarding experience...
          </Typography>
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="md" sx={{ py: 8 }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      </Container>
    );
  }

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'grey.50', py: 4 }}>
      <Container maxWidth="lg">
        {/* Header */}
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <Typography variant="h3" sx={{ fontWeight: 600, color: 'primary.main', mb: 2 }}>
            Welcome to BidBeez
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Let's set up your account for success
          </Typography>
        </Box>

        {/* Progress Stepper */}
        <Paper elevation={2} sx={{ p: 3, mb: 4 }}>
          <Stepper activeStep={currentStep} alternativeLabel>
            {steps.map((label, index) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </Paper>

        {/* Current Step Content */}
        <Paper elevation={2} sx={{ minHeight: 600 }}>
          {renderCurrentStep()}
        </Paper>

        {/* Footer */}
        <Box sx={{ textAlign: 'center', mt: 4 }}>
          <Typography variant="caption" color="text.secondary">
            Need help? Contact our support <NAME_EMAIL>
          </Typography>
        </Box>
      </Container>
    </Box>
  );
}

// Placeholder components - these would be implemented separately
function WelcomeStep({ onNext }: { onNext: (data: any) => void }) {
  return (
    <Box sx={{ p: 4, textAlign: 'center' }}>
      <Typography variant="h4" gutterBottom>
        Welcome to BidBeez!
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        The most advanced tender management platform designed to help you win more bids.
      </Typography>
      <button onClick={() => onNext({ welcomed: true })}>
        Get Started
      </button>
    </Box>
  );
}

function OrganizationTypeStep({ onNext, onBack }: { onNext: (data: any) => void; onBack: () => void }) {
  return (
    <Box sx={{ p: 4 }}>
      <Typography variant="h5" gutterBottom>
        Tell us about your organization
      </Typography>
      {/* Organization type selection form would go here */}
      <button onClick={() => onNext({ organizationType: 'small_business', teamSize: 5 })}>
        Continue
      </button>
    </Box>
  );
}

function PaymentStep({ onNext, onBack, selectedPlan }: { onNext: (data: any) => void; onBack: () => void; selectedPlan?: string }) {
  return (
    <Box sx={{ p: 4 }}>
      <Typography variant="h5" gutterBottom>
        Payment Setup
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Set up your payment method for plan: {selectedPlan}
      </Typography>
      {/* Payment form would go here */}
      <button onClick={() => onNext({ paymentSetup: true })}>
        Complete Payment Setup
      </button>
    </Box>
  );
}

function CompletionStep({ onNext, onboardingFlow }: { onNext: (data: any) => void; onboardingFlow: OnboardingFlow }) {
  return (
    <Box sx={{ p: 4, textAlign: 'center' }}>
      <Typography variant="h4" gutterBottom>
        You're all set!
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Welcome to BidBeez. Start discovering tender opportunities now.
      </Typography>
      <button onClick={() => onNext({ completed: true })}>
        Enter BidBeez
      </button>
    </Box>
  );
}
