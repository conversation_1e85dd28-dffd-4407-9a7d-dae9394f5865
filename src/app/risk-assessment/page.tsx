'use client';

import React from 'react';
import { Container, Typography, Card, CardContent, Grid, Chip, LinearProgress, Box, Avatar } from '@mui/material';
import { Warning, Security, Assessment, TrendingDown } from '@mui/icons-material';

export default function RiskAssessmentPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
        Risk Assessment
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        Comprehensive risk analysis for informed decision making
      </Typography>
      
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                <Avatar sx={{ bgcolor: 'error.main' }}>
                  <Warning />
                </Avatar>
                <Box>
                  <Typography variant="h6">Overall Risk Level</Typography>
                  <Typography variant="h4" color="warning.main">Medium</Typography>
                </Box>
              </Box>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" gutterBottom>Financial Risk: 34%</Typography>
                <LinearProgress variant="determinate" value={34} color="warning" />
              </Box>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" gutterBottom>Market Risk: 28%</Typography>
                <LinearProgress variant="determinate" value={28} color="success" />
              </Box>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" gutterBottom>Operational Risk: 45%</Typography>
                <LinearProgress variant="determinate" value={45} color="error" />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Risk Mitigation Strategies</Typography>
              <Box sx={{ mt: 2 }}>
                <Chip label="Diversify Portfolio" color="primary" sx={{ mr: 1, mb: 1 }} />
                <Chip label="Insurance Coverage" color="success" sx={{ mr: 1, mb: 1 }} />
                <Chip label="Contingency Planning" color="warning" sx={{ mr: 1, mb: 1 }} />
                <Chip label="Regular Monitoring" color="info" sx={{ mr: 1, mb: 1 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
}
