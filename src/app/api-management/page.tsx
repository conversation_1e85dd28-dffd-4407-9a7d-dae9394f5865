'use client';

import React from 'react';
import { Container, Typo<PERSON>, Card, CardContent, Grid, Button, Box, Chip, Table, TableBody, TableCell, TableHead, TableRow } from '@mui/material';
import { Api, Security, Speed, CheckCircle } from '@mui/icons-material';

const apiEndpoints = [
  { name: "Tender Search", endpoint: "/api/tenders", status: "Active", calls: 1250 },
  { name: "Bid Submission", endpoint: "/api/bids", status: "Active", calls: 890 },
  { name: "User Authentication", endpoint: "/api/auth", status: "Active", calls: 2340 }
];

export default function ApiManagementPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
        API Management
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        Monitor and manage API endpoints and integrations
      </Typography>
      
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="primary">
                    15
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active APIs
                  </Typography>
                </Box>
                <Api sx={{ fontSize: 40, color: 'primary.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="success.main">
                    99.9%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Uptime
                  </Typography>
                </Box>
                <CheckCircle sx={{ fontSize: 40, color: 'success.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      
      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>API Endpoints</Typography>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Endpoint</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Monthly Calls</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {apiEndpoints.map((api, index) => (
                <TableRow key={index}>
                  <TableCell>{api.name}</TableCell>
                  <TableCell><code>{api.endpoint}</code></TableCell>
                  <TableCell>
                    <Chip label={api.status} color="success" size="small" />
                  </TableCell>
                  <TableCell>{api.calls.toLocaleString()}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </Container>
  );
}
