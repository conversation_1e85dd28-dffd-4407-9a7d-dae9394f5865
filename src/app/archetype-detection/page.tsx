'use client';

import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Box, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  Alert,
  Button,
  Chip,
  LinearProgress,
  Avatar,
  Paper,
  Stack,
  Divider,
  CircularProgress
} from '@mui/material';
import { 
  Psychology, 
  Person, 
  TrendingUp, 
  Assessment,
  Visibility,
  Refresh,
  Download,
  Settings,
  CheckCircle,
  Timer
} from '@mui/icons-material';

export default function ArchetypeDetectionPage() {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [detectionProgress, setDetectionProgress] = useState(78);

  const startDetection = () => {
    setIsAnalyzing(true);
    setTimeout(() => setIsAnalyzing(false), 4000);
  };

  // Mock archetype data
  const currentArchetype = {
    primary: 'Strategic Analyzer',
    secondary: 'Methodical Planner',
    confidence: 89.7,
    traits: ['Data-Driven', 'Risk-Aware', 'Detail-Oriented', 'Strategic'],
    strengths: ['Thorough Analysis', 'Risk Management', 'Strategic Planning'],
    challenges: ['Decision Speed', 'Adaptability', 'Intuitive Decisions']
  };

  const archetypeProfiles = [
    {
      id: 1,
      name: 'Strategic Analyzer',
      description: 'Data-driven decision maker with strong analytical skills',
      percentage: 34.2,
      traits: ['Analytical', 'Methodical', 'Risk-Aware'],
      color: '#2196f3',
      bidStyle: 'Conservative, well-researched bids'
    },
    {
      id: 2,
      name: 'Aggressive Competitor',
      description: 'High-energy, competitive bidder focused on winning',
      percentage: 28.5,
      traits: ['Competitive', 'Fast-paced', 'Confident'],
      color: '#f44336',
      bidStyle: 'Bold, aggressive pricing strategies'
    },
    {
      id: 3,
      name: 'Relationship Builder',
      description: 'Focuses on long-term partnerships and trust',
      percentage: 23.1,
      traits: ['Collaborative', 'Trust-focused', 'Patient'],
      color: '#4caf50',
      bidStyle: 'Relationship-based, value-driven bids'
    },
    {
      id: 4,
      name: 'Innovation Pioneer',
      description: 'Creative problem solver with unique approaches',
      percentage: 14.2,
      traits: ['Creative', 'Innovative', 'Flexible'],
      color: '#ff9800',
      bidStyle: 'Innovative solutions, premium pricing'
    }
  ];

  const detectionMetrics = {
    totalAnalyses: 1247,
    accuracyRate: 94.3,
    avgDetectionTime: '2.8s',
    confidenceLevel: 89.7,
    improvementRate: 23.4
  };

  const recentDetections = [
    { id: 1, user: 'John Smith', archetype: 'Strategic Analyzer', confidence: 92.1, timestamp: '5 min ago' },
    { id: 2, user: 'Sarah Johnson', archetype: 'Relationship Builder', confidence: 87.6, timestamp: '12 min ago' },
    { id: 3, user: 'Mike Chen', archetype: 'Aggressive Competitor', confidence: 94.8, timestamp: '18 min ago' },
    { id: 4, user: 'Lisa Brown', archetype: 'Innovation Pioneer', confidence: 89.3, timestamp: '25 min ago' }
  ];

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold" color="primary.main">
          🎯 Archetype Detection Dashboard
        </Typography>
        <Typography variant="h6" color="text.primary" sx={{ mb: 2 }}>
          AI-powered psychological archetype detection and behavioral pattern analysis
        </Typography>
        
        <Alert severity="success" sx={{ mb: 3 }}>
          <strong>Detection Active:</strong> 94.3% accuracy rate with 1,247 successful archetype analyses and continuous learning.
        </Alert>
      </Box>

      {/* Quick Actions */}
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item>
          <Button 
            variant="contained" 
            startIcon={isAnalyzing ? <CircularProgress size={20} /> : <Psychology />} 
            color="primary"
            onClick={startDetection}
            disabled={isAnalyzing}
          >
            {isAnalyzing ? 'Analyzing Archetype...' : 'Start Detection'}
          </Button>
        </Grid>
        <Grid item>
          <Button variant="outlined" startIcon={<Assessment />}>
            View Reports
          </Button>
        </Grid>
        <Grid item>
          <Button variant="outlined" startIcon={<Download />}>
            Export Data
          </Button>
        </Grid>
        <Grid item>
          <Button variant="outlined" startIcon={<Settings />}>
            Detection Settings
          </Button>
        </Grid>
      </Grid>

      {/* Current Archetype Profile */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom color="text.primary">Current Archetype Profile</Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Avatar sx={{ width: 100, height: 100, mx: 'auto', mb: 2, bgcolor: 'primary.main' }}>
                  <Psychology sx={{ fontSize: 50 }} />
                </Avatar>
                <Typography variant="h5" color="text.primary" fontWeight="bold">
                  {currentArchetype.primary}
                </Typography>
                <Typography variant="body1" color="text.secondary" gutterBottom>
                  Primary Archetype
                </Typography>
                <Typography variant="h6" color="success.main">
                  {currentArchetype.confidence}% Confidence
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={8}>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Typography variant="h6" color="text.primary" gutterBottom>
                    Secondary: {currentArchetype.secondary}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="subtitle2" color="text.primary" gutterBottom>Core Traits</Typography>
                    <Stack direction="row" spacing={1} flexWrap="wrap" gap={1}>
                      {currentArchetype.traits.map((trait, index) => (
                        <Chip key={index} label={trait} color="primary" size="small" />
                      ))}
                    </Stack>
                  </Paper>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="subtitle2" color="text.primary" gutterBottom>Strengths</Typography>
                    {currentArchetype.strengths.map((strength, index) => (
                      <Typography key={index} variant="body2" color="success.main">
                        • {strength}
                      </Typography>
                    ))}
                  </Paper>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="subtitle2" color="text.primary" gutterBottom>Growth Areas</Typography>
                    {currentArchetype.challenges.map((challenge, index) => (
                      <Typography key={index} variant="body2" color="warning.main">
                        • {challenge}
                      </Typography>
                    ))}
                  </Paper>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Detection Metrics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Assessment sx={{ fontSize: 32, color: 'primary.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {detectionMetrics.totalAnalyses}
              </Typography>
              <Typography variant="body2" color="text.secondary">Total Analyses</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <CheckCircle sx={{ fontSize: 32, color: 'success.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {detectionMetrics.accuracyRate}%
              </Typography>
              <Typography variant="body2" color="text.secondary">Accuracy Rate</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Timer sx={{ fontSize: 32, color: 'info.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {detectionMetrics.avgDetectionTime}
              </Typography>
              <Typography variant="body2" color="text.secondary">Avg Detection Time</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Psychology sx={{ fontSize: 32, color: 'secondary.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {detectionMetrics.confidenceLevel}%
              </Typography>
              <Typography variant="body2" color="text.secondary">Confidence Level</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <TrendingUp sx={{ fontSize: 32, color: 'warning.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {detectionMetrics.improvementRate}%
              </Typography>
              <Typography variant="body2" color="text.secondary">Improvement Rate</Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Archetype Distribution */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom color="text.primary">
                Archetype Distribution Analysis
              </Typography>
              <Stack spacing={3}>
                {archetypeProfiles.map((archetype) => (
                  <Box key={archetype.id}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="subtitle1" fontWeight="medium" color="text.primary">
                        {archetype.name}
                      </Typography>
                      <Typography variant="h6" color="primary.main" fontWeight="bold">
                        {archetype.percentage}%
                      </Typography>
                    </Box>
                    <LinearProgress 
                      variant="determinate" 
                      value={archetype.percentage} 
                      sx={{ 
                        height: 8, 
                        borderRadius: 4,
                        backgroundColor: 'rgba(255,255,255,0.1)',
                        '& .MuiLinearProgress-bar': {
                          backgroundColor: archetype.color
                        }
                      }}
                    />
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                      {archetype.description} • {archetype.bidStyle}
                    </Typography>
                    <Stack direction="row" spacing={1} sx={{ mt: 1 }}>
                      {archetype.traits.map((trait, index) => (
                        <Chip 
                          key={index} 
                          label={trait} 
                          size="small" 
                          variant="outlined"
                          sx={{ color: archetype.color, borderColor: archetype.color }}
                        />
                      ))}
                    </Stack>
                    <Divider sx={{ mt: 2 }} />
                  </Box>
                ))}
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom color="text.primary">
                Recent Detections
              </Typography>
              <Stack spacing={2}>
                {recentDetections.map((detection) => (
                  <Paper key={detection.id} sx={{ p: 2 }}>
                    <Typography variant="subtitle2" color="text.primary" fontWeight="medium">
                      {detection.user}
                    </Typography>
                    <Typography variant="body2" color="primary.main" gutterBottom>
                      {detection.archetype}
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="caption" color="text.secondary">
                        {detection.timestamp}
                      </Typography>
                      <Chip 
                        label={`${detection.confidence}%`} 
                        color="success" 
                        size="small"
                      />
                    </Box>
                  </Paper>
                ))}
              </Stack>
              
              <Button 
                variant="outlined" 
                fullWidth 
                startIcon={<Visibility />}
                sx={{ mt: 2 }}
              >
                View All Detections
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Detection Progress */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom color="text.primary">
            Real-time Detection Progress
          </Typography>
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" color="text.secondary">
              Current Analysis Progress: {detectionProgress}%
            </Typography>
            <LinearProgress 
              variant="determinate" 
              value={detectionProgress} 
              sx={{ mt: 1, height: 8, borderRadius: 4 }}
            />
          </Box>
          <Alert severity="info">
            <strong>AI Learning:</strong> Archetype detection accuracy improves with each analysis. Current model trained on 50,000+ behavioral patterns.
          </Alert>
        </CardContent>
      </Card>
    </Container>
  );
}
