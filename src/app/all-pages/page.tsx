'use client';

import React from 'react';
import {
  Box,
  Typography,
  Container,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Button,
  Grid,
  Alert,
  Chip,
  Divider
} from '@mui/material';
import {
  Home as HomeIcon,
  Dashboard as DashboardIcon,
  Analytics as AnalyticsIcon,
  WhatsApp as WhatsAppIcon,
  Security as SecurityIcon,
  Assignment as AssignmentIcon,
  Search as SearchIcon,
  Business as BusinessIcon,
  Login as LoginIcon,
  PersonAdd as RegisterIcon,
  Code as CodeIcon,
  OpenInNew as OpenIcon,
  School as SchoolIcon,
  Build as BuildIcon,
  Person as PersonIcon
} from '@mui/icons-material';

const allPages = [
  {
    title: 'Home / Landing Page',
    url: 'http://localhost:3001/',
    icon: <HomeIcon color="primary" />,
    description: 'Main landing page with company overview'
  },
  {
    title: 'Main Dashboard',
    url: 'http://localhost:3001/dashboard',
    icon: <DashboardIcon color="primary" />,
    description: 'Complex interactive dashboard with feature toggles'
  },
  {
    title: 'Simple Demo Page',
    url: 'http://localhost:3001/simple',
    icon: <CodeIcon color="info" />,
    description: 'Basic demonstration page'
  },
  {
    title: 'Advanced Analytics',
    url: 'http://localhost:3001/analytics',
    icon: <AnalyticsIcon color="info" />,
    description: 'Comprehensive bid performance insights'
  },
  {
    title: 'Tender Discovery',
    url: 'http://localhost:3001/tenders',
    icon: <SearchIcon color="success" />,
    description: 'AI-powered tender matching platform'
  },
  {
    title: 'Bid Management',
    url: 'http://localhost:3001/bids',
    icon: <AssignmentIcon color="success" />,
    description: 'Complete bid lifecycle management'
  },
  {
    title: 'WhatsApp Auto-Bidding',
    url: 'http://localhost:3001/whatsapp',
    icon: <WhatsAppIcon color="success" />,
    description: 'Automated bidding through WhatsApp'
  },
  {
    title: 'SA Compliance Tools',
    url: 'http://localhost:3001/compliance',
    icon: <SecurityIcon color="warning" />,
    description: 'Legal compliance management tools'
  },
  {
    title: 'Supplier Dashboard',
    url: 'http://localhost:3001/supplier',
    icon: <BusinessIcon color="secondary" />,
    description: 'Business performance tracking'
  },
  {
    title: 'Login Page',
    url: 'http://localhost:3001/auth/login',
    icon: <LoginIcon color="error" />,
    description: 'User authentication portal'
  },
  {
    title: 'Registration Page',
    url: 'http://localhost:3001/auth/register',
    icon: <RegisterIcon color="error" />,
    description: 'New user registration'
  },
  {
    title: '🧠 Sales Rep Psychological Center',
    url: 'http://localhost:3001/sales-rep-center',
    icon: <DashboardIcon color="secondary" />,
    description: 'Advanced psychological profiling and behavioral optimization'
  },
  {
    title: '🎯 Sales Rep Onboarding',
    url: 'http://localhost:3001/sales-rep-onboarding',
    icon: <PersonIcon color="secondary" />,
    description: 'Psychological archetype detection and onboarding'
  },
  {
    title: '🏆 Gamification Hub',
    url: 'http://localhost:3001/gamification',
    icon: <DashboardIcon color="warning" />,
    description: 'Achievements, leaderboards, and psychological rewards'
  },
  {
    title: '🤖 AI Insights Dashboard',
    url: 'http://localhost:3001/ai-insights',
    icon: <DashboardIcon color="info" />,
    description: 'AI recommendations and market intelligence'
  },
  {
    title: '📊 Advanced Analytics',
    url: 'http://localhost:3001/analytics/advanced',
    icon: <AnalyticsIcon color="primary" />,
    description: 'Comprehensive performance analytics with psychological insights'
  },
  {
    title: '📱 WhatsApp Dashboard',
    url: 'http://localhost:3001/whatsapp/dashboard',
    icon: <WhatsAppIcon color="success" />,
    description: 'WhatsApp automation and auto-bidding management'
  },
  {
    title: '⚖️ Bid Protests',
    url: 'http://localhost:3001/compliance/protests',
    icon: <SecurityIcon color="error" />,
    description: 'SA legal compliance and bid protest management'
  },
  {
    title: '🎯 SkillSync Marketplace',
    url: 'http://localhost:3001/skillsync',
    icon: <SchoolIcon color="info" />,
    description: 'AI-powered skill provider discovery and B-BBEE talent matching'
  },
  {
    title: '🔧 ToolSync Management',
    url: 'http://localhost:3001/toolsync',
    icon: <BuildIcon color="warning" />,
    description: 'Software license optimization and collaborative sharing'
  },
  {
    title: '📋 RFQ Management',
    url: 'http://localhost:3001/rfq',
    icon: <AssignmentIcon color="primary" />,
    description: 'Request for Quotations with 90-second challenge system'
  },
  {
    title: 'Application Sitemap',
    url: 'http://localhost:3000/sitemap',
    icon: <DashboardIcon color="info" />,
    description: 'Comprehensive page overview with features'
  },

  // ===== NEW SOPHISTICATED FEATURES =====
  {
    title: '🧠 Psychological Assessment Center',
    url: 'http://localhost:3001/psychological-assessment',
    icon: <PersonIcon color="secondary" />,
    description: 'Advanced psychological profiling and behavioral analysis'
  },
  {
    title: '🤖 AI Engine Dashboard',
    url: 'http://localhost:3001/ai-engine',
    icon: <DashboardIcon color="primary" />,
    description: 'Central command center for all AI-powered intelligence systems'
  },
  {
    title: '📊 Market Intelligence Center',
    url: 'http://localhost:3001/market-intelligence',
    icon: <AnalyticsIcon color="info" />,
    description: 'Competitive analysis and market trend prediction'
  },
  {
    title: '⚖️ Compliance Dashboard',
    url: 'http://localhost:3001/compliance-dashboard',
    icon: <SecurityIcon color="warning" />,
    description: 'Enhanced compliance management and legal framework'
  },
  {
    title: '🔧 Automation Hub',
    url: 'http://localhost:3001/automation-hub',
    icon: <BuildIcon color="success" />,
    description: 'Central automation control and configuration'
  },
  {
    title: '🌐 Ecosystem Overview',
    url: 'http://localhost:3001/ecosystem-overview',
    icon: <SchoolIcon color="primary" />,
    description: 'Comprehensive marketplace and ecosystem management'
  },
  {
    title: '👑 Queen Bee Management',
    url: 'http://localhost:3001/queen-bee-management',
    icon: <DashboardIcon color="warning" />,
    description: 'Task orchestration and bee assignment system'
  },
  {
    title: '📈 Performance Metrics',
    url: 'http://localhost:3001/performance-metrics',
    icon: <AnalyticsIcon color="success" />,
    description: 'Comprehensive system analytics and performance tracking'
  },
  {
    title: '🎛️ Feature Flags Manager',
    url: 'http://localhost:3001/feature-flags',
    icon: <BuildIcon color="info" />,
    description: 'Dynamic feature control and progressive rollout'
  },
  {
    title: '⚙️ System Administration',
    url: 'http://localhost:3001/system-admin',
    icon: <SecurityIcon color="error" />,
    description: 'Platform management and system configuration'
  },

  // ===== NEWLY ACTIVATED SOPHISTICATED PAGES =====
  {
    title: '🎯 Archetype Detection Dashboard',
    url: 'http://localhost:3001/archetype-detection',
    icon: <PersonIcon color="secondary" />,
    description: 'AI-powered psychological archetype analysis and detection'
  },
  {
    title: '📊 Behavioral Analytics',
    url: 'http://localhost:3001/behavioral-analytics',
    icon: <AnalyticsIcon color="info" />,
    description: 'Advanced behavioral pattern analysis and predictive modeling'
  },
  {
    title: '🎯 Win Probability Calculator',
    url: 'http://localhost:3001/win-probability',
    icon: <AnalyticsIcon color="success" />,
    description: 'AI-powered win probability analysis and strategic recommendations'
  },
  {
    title: '⚖️ Enhanced Compliance Dashboard',
    url: 'http://localhost:3001/compliance-dashboard',
    icon: <SecurityIcon color="warning" />,
    description: 'Comprehensive SA legal compliance management system'
  },
  {
    title: '🔧 Enhanced Automation Hub',
    url: 'http://localhost:3001/automation-hub',
    icon: <BuildIcon color="success" />,
    description: 'Central automation control center for all intelligent systems'
  },
  {
    title: '👑 Queen Bee Management System',
    url: 'http://localhost:3001/queen-bee-management',
    icon: <DashboardIcon color="warning" />,
    description: 'Intelligent task orchestration and bee worker assignment'
  },
  {
    title: '🗺️ Comprehensive Platform Sitemap',
    url: 'http://localhost:3001/comprehensive-sitemap',
    icon: <DashboardIcon color="info" />,
    description: 'Complete overview of all 92 sophisticated pages and services'
  },

  // ===== ADDITIONAL SOPHISTICATED PAGES =====
  {
    title: '💓 Stress Monitoring System',
    url: 'http://localhost:3001/stress-monitoring',
    icon: <PersonIcon color="error" />,
    description: 'Real-time psychological stress tracking and wellness optimization'
  },
  {
    title: '🏆 Confidence Coaching Hub',
    url: 'http://localhost:3001/confidence-coaching',
    icon: <PersonIcon color="warning" />,
    description: 'Personalized confidence building and performance optimization'
  },
  {
    title: '🌐 Ecosystem Overview',
    url: 'http://localhost:3001/ecosystem-overview',
    icon: <SchoolIcon color="primary" />,
    description: 'Comprehensive marketplace and ecosystem management'
  },
  {
    title: '📈 Performance Metrics Dashboard',
    url: 'http://localhost:3001/performance-metrics',
    icon: <AnalyticsIcon color="success" />,
    description: 'Comprehensive system performance analytics and monitoring'
  }
];

export default function AllPagesPage() {
  const openAllPages = () => {
    allPages.forEach((page, index) => {
      setTimeout(() => {
        window.open(page.url, '_blank');
      }, index * 500); // Stagger opening by 500ms
    });
  };

  const openPage = (url: string) => {
    window.open(url, '_blank');
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ textAlign: 'center', mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold" color="primary">
          🌐 All BidBeez Pages
        </Typography>
        <Typography variant="h6" color="text.secondary" sx={{ mb: 3 }}>
          Direct links to every page in the BidBeez platform
        </Typography>
        
        <Alert severity="info" sx={{ mb: 3, textAlign: 'left' }}>
          <Typography variant="body2">
            <strong>📋 How to view all pages:</strong><br/>
            1. Click "Open All Pages" to open every page in new tabs<br/>
            2. Or click individual links below to open specific pages<br/>
            3. All pages are running on <strong>localhost:3000</strong>
          </Typography>
        </Alert>

        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', mb: 4 }}>
          <Button
            variant="contained"
            size="large"
            startIcon={<OpenIcon />}
            onClick={openAllPages}
            color="primary"
            sx={{ px: 4 }}
          >
            Open All Pages
          </Button>
          <Button
            variant="outlined"
            size="large"
            startIcon={<DashboardIcon />}
            href="/dashboard"
            sx={{ px: 4 }}
          >
            Go to Dashboard
          </Button>
        </Box>
      </Box>

      {/* Page List */}
      <Paper elevation={2} sx={{ p: 3 }}>
        <Typography variant="h5" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
          📄 All Available Pages ({allPages.length})
        </Typography>
        
        <List>
          {allPages.map((page, index) => (
            <React.Fragment key={page.url}>
              <ListItem
                sx={{
                  borderRadius: 2,
                  mb: 1,
                  '&:hover': {
                    bgcolor: 'action.hover',
                    cursor: 'pointer'
                  }
                }}
                onClick={() => openPage(page.url)}
              >
                <ListItemIcon>
                  {page.icon}
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Box>
                      <Typography variant="h6" component="span" sx={{ fontWeight: 600 }}>
                        {page.title}
                      </Typography>
                      <Chip 
                        label="Live" 
                        size="small" 
                        color="success" 
                        sx={{ ml: 1 }}
                      />
                    </Box>
                  }
                  secondary={
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        {page.description}
                      </Typography>
                      <Typography 
                        variant="caption" 
                        color="primary" 
                        sx={{ 
                          fontFamily: 'monospace',
                          bgcolor: 'grey.100',
                          px: 1,
                          py: 0.5,
                          borderRadius: 1,
                          mt: 0.5,
                          display: 'inline-block'
                        }}
                      >
                        {page.url}
                      </Typography>
                    </Box>
                  }
                />
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<OpenIcon />}
                  onClick={(e) => {
                    e.stopPropagation();
                    openPage(page.url);
                  }}
                >
                  Open
                </Button>
              </ListItem>
              {index < allPages.length - 1 && <Divider />}
            </React.Fragment>
          ))}
        </List>
      </Paper>

      {/* Quick Stats */}
      <Grid container spacing={3} sx={{ mt: 4 }}>
        <Grid item xs={12} md={4}>
          <Paper elevation={1} sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="h4" color="primary" sx={{ fontWeight: 600 }}>
              {allPages.length}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Total Pages
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={4}>
          <Paper elevation={1} sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="h4" color="success.main" sx={{ fontWeight: 600 }}>
              100%
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Pages Functional
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={4}>
          <Paper elevation={1} sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="h4" color="info.main" sx={{ fontWeight: 600 }}>
              3000
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Port Number
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* Navigation Instructions */}
      <Paper elevation={2} sx={{ p: 3, mt: 4, bgcolor: 'primary.light', color: 'primary.contrastText' }}>
        <Typography variant="h6" gutterBottom>
          🚀 Navigation Tips
        </Typography>
        <Typography variant="body2">
          • All pages are fully functional with Material-UI design<br/>
          • Use the main dashboard to access features with the toggle switches<br/>
          • Each page has realistic mock data for demonstration<br/>
          • The application uses Next.js App Router for smooth navigation
        </Typography>
      </Paper>
    </Container>
  );
}