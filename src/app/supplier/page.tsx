'use client';

import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  Chip,
  Alert,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Avatar,
  Stack,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Badge
} from '@mui/material';
import {
  Business as BusinessIcon,
  TrendingUp as TrendingUpIcon,
  Assignment as AssignmentIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Schedule as ScheduleIcon,
  AttachMoney as MoneyIcon,
  Star as StarIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  LocationOn as LocationIcon,
  Verified as VerifiedIcon,
  EmojiEvents as AwardIcon,
  AutoAwesome as AIIcon,
  Analytics as AnalyticsIcon,
  Settings as SettingsIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';

// Mock supplier data
const mockSupplierData = {
  profile: {
    companyName: 'Construction Excellence (Pty) Ltd',
    registrationNumber: '2018/123456/07',
    bbbeeLevel: 4,
    industry: 'Construction & Infrastructure',
    established: '2018',
    employees: 47,
    rating: 4.8,
    completedProjects: 23,
    activeContracts: 5
  },
  performance: {
    totalRevenue: 18500000,
    activeContracts: 5,
    completionRate: 96.5,
    avgDeliveryTime: 14,
    clientSatisfaction: 4.8,
    growthRate: 28.5
  },
  recentActivity: [
    {
      id: 1,
      type: 'contract_awarded',
      title: 'Municipal Infrastructure Project awarded',
      date: '2024-01-15',
      value: 2800000,
      status: 'active'
    },
    {
      id: 2,
      type: 'project_completed',
      title: 'School Renovation Project completed',
      date: '2024-01-10',
      value: 1200000,
      status: 'completed'
    },
    {
      id: 3,
      type: 'quote_submitted',
      title: 'Highway Maintenance Quote submitted',
      date: '2024-01-08',
      value: 5600000,
      status: 'pending'
    }
  ],
  activeContracts: [
    {
      id: 'contract-001',
      projectName: 'Municipal Infrastructure Development',
      client: 'City of Cape Town',
      value: 2800000,
      progress: 35,
      startDate: '2024-01-15',
      expectedCompletion: '2024-08-15',
      status: 'on_track'
    },
    {
      id: 'contract-002',
      projectName: 'Office Building Construction',
      client: 'Private Developer',
      value: 4200000,
      progress: 78,
      startDate: '2023-09-01',
      expectedCompletion: '2024-03-30',
      status: 'ahead'
    },
    {
      id: 'contract-003',
      projectName: 'Bridge Maintenance',
      client: 'Department of Transport',
      value: 1800000,
      progress: 92,
      startDate: '2023-11-15',
      expectedCompletion: '2024-02-28',
      status: 'on_track'
    }
  ],
  capabilities: [
    'Civil Engineering',
    'Project Management',
    'Environmental Compliance',
    'Health & Safety',
    'Quality Assurance',
    'B-BBEE Compliance'
  ]
};

export default function SupplierDashboard() {
  const [selectedContract, setSelectedContract] = useState<string | null>(null);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'on_track': return 'success';
      case 'ahead': return 'info';
      case 'behind': return 'warning';
      case 'at_risk': return 'error';
      default: return 'default';
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'contract_awarded': return '🎉';
      case 'project_completed': return '✅';
      case 'quote_submitted': return '📝';
      case 'payment_received': return '💰';
      default: return '📋';
    }
  };

  return (
    <Box sx={{ p: 3, minHeight: '100vh', bgcolor: 'grey.50' }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main' }}>
            🏢 Supplier Dashboard
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage your supplier profile and track business performance
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<AnalyticsIcon />}
            onClick={() => alert('Analytics')}
          >
            Analytics
          </Button>
          <Button
            variant="outlined"
            startIcon={<SettingsIcon />}
            onClick={() => alert('Settings')}
          >
            Settings
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => alert('Submit Quote')}
          >
            Submit Quote
          </Button>
        </Box>
      </Box>

      {/* Performance Overview Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={2}>
          <Card elevation={2}>
            <CardContent sx={{ textAlign: 'center' }}>
              <MoneyIcon color="primary" sx={{ fontSize: 32, mb: 1 }} />
              <Typography variant="h5" sx={{ fontWeight: 600, mb: 0.5 }}>
                {formatCurrency(mockSupplierData.performance.totalRevenue)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Revenue
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={2}>
          <Card elevation={2}>
            <CardContent sx={{ textAlign: 'center' }}>
              <AssignmentIcon color="info" sx={{ fontSize: 32, mb: 1 }} />
              <Typography variant="h5" sx={{ fontWeight: 600, mb: 0.5 }}>
                {mockSupplierData.performance.activeContracts}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Active Contracts
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={2}>
          <Card elevation={2}>
            <CardContent sx={{ textAlign: 'center' }}>
              <CheckCircleIcon color="success" sx={{ fontSize: 32, mb: 1 }} />
              <Typography variant="h5" sx={{ fontWeight: 600, mb: 0.5 }}>
                {mockSupplierData.performance.completionRate}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Completion Rate
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={2}>
          <Card elevation={2}>
            <CardContent sx={{ textAlign: 'center' }}>
              <ScheduleIcon color="warning" sx={{ fontSize: 32, mb: 1 }} />
              <Typography variant="h5" sx={{ fontWeight: 600, mb: 0.5 }}>
                {mockSupplierData.performance.avgDeliveryTime}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Avg Days Delivery
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={2}>
          <Card elevation={2}>
            <CardContent sx={{ textAlign: 'center' }}>
              <StarIcon color="warning" sx={{ fontSize: 32, mb: 1 }} />
              <Typography variant="h5" sx={{ fontWeight: 600, mb: 0.5 }}>
                {mockSupplierData.performance.clientSatisfaction}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Client Rating
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={2}>
          <Card elevation={2}>
            <CardContent sx={{ textAlign: 'center' }}>
              <TrendingUpIcon color="success" sx={{ fontSize: 32, mb: 1 }} />
              <Typography variant="h5" sx={{ fontWeight: 600, mb: 0.5 }}>
                +{mockSupplierData.performance.growthRate}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Growth Rate
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Company Profile */}
        <Grid item xs={12} md={4}>
          <Card elevation={2} sx={{ mb: 3 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'primary.main', mr: 2, width: 56, height: 56 }}>
                  <BusinessIcon />
                </Avatar>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    {mockSupplierData.profile.companyName}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Est. {mockSupplierData.profile.established}
                  </Typography>
                </Box>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Stack spacing={2}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2">Registration:</Typography>
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                    {mockSupplierData.profile.registrationNumber}
                  </Typography>
                </Box>

                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2">B-BBEE Level:</Typography>
                  <Chip 
                    label={`Level ${mockSupplierData.profile.bbbeeLevel}`}
                    size="small"
                    color="success"
                  />
                </Box>

                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2">Industry:</Typography>
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                    {mockSupplierData.profile.industry}
                  </Typography>
                </Box>

                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2">Employees:</Typography>
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                    {mockSupplierData.profile.employees}
                  </Typography>
                </Box>

                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2">Rating:</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <StarIcon fontSize="small" color="warning" />
                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                      {mockSupplierData.profile.rating}
                    </Typography>
                  </Box>
                </Box>
              </Stack>

              <Button
                variant="outlined"
                fullWidth
                startIcon={<EditIcon />}
                sx={{ mt: 3 }}
                onClick={() => alert('Edit Profile')}
              >
                Edit Profile
              </Button>
            </CardContent>
          </Card>

          {/* Capabilities */}
          <Card elevation={2}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                <VerifiedIcon sx={{ mr: 1, color: 'success.main' }} />
                Core Capabilities
              </Typography>
              
              <Stack spacing={1}>
                {mockSupplierData.capabilities.map((capability, index) => (
                  <Chip
                    key={index}
                    label={capability}
                    size="small"
                    variant="outlined"
                    sx={{ alignSelf: 'flex-start' }}
                  />
                ))}
              </Stack>

              <Button
                variant="outlined"
                fullWidth
                startIcon={<AddIcon />}
                sx={{ mt: 2 }}
                onClick={() => alert('Add Capability')}
              >
                Add Capability
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* Main Content */}
        <Grid item xs={12} md={8}>
          {/* Active Contracts */}
          <Card elevation={2} sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
                <AssignmentIcon sx={{ mr: 1 }} />
                Active Contracts
              </Typography>

              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Project</TableCell>
                      <TableCell>Client</TableCell>
                      <TableCell>Value</TableCell>
                      <TableCell>Progress</TableCell>
                      <TableCell>Expected Completion</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {mockSupplierData.activeContracts.map((contract) => (
                      <TableRow key={contract.id} hover>
                        <TableCell>
                          <Typography variant="body2" sx={{ fontWeight: 600 }}>
                            {contract.projectName}
                          </Typography>
                        </TableCell>

                        <TableCell>
                          <Typography variant="body2">
                            {contract.client}
                          </Typography>
                        </TableCell>

                        <TableCell>
                          <Typography variant="body2" sx={{ fontWeight: 600 }}>
                            {formatCurrency(contract.value)}
                          </Typography>
                        </TableCell>

                        <TableCell>
                          <Box sx={{ width: '100%' }}>
                            <LinearProgress 
                              variant="determinate" 
                              value={contract.progress}
                              sx={{ mb: 0.5 }}
                            />
                            <Typography variant="caption">
                              {contract.progress}%
                            </Typography>
                          </Box>
                        </TableCell>

                        <TableCell>
                          <Typography variant="body2">
                            {new Date(contract.expectedCompletion).toLocaleDateString()}
                          </Typography>
                        </TableCell>

                        <TableCell>
                          <Chip 
                            label={contract.status.replace('_', ' ').toUpperCase()}
                            size="small"
                            color={getStatusColor(contract.status) as any}
                          />
                        </TableCell>

                        <TableCell>
                          <IconButton 
                            size="small" 
                            onClick={() => alert(`View contract ${contract.id}`)}
                          >
                            <ViewIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card elevation={2}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
                <TrendingUpIcon sx={{ mr: 1 }} />
                Recent Activity
              </Typography>

              <List>
                {mockSupplierData.recentActivity.map((activity, index) => (
                  <React.Fragment key={activity.id}>
                    <ListItem>
                      <ListItemIcon>
                        <Typography variant="h6">
                          {getActivityIcon(activity.type)}
                        </Typography>
                      </ListItemIcon>
                      <ListItemText
                        primary={activity.title}
                        secondary={
                          <Box>
                            <Typography variant="caption" display="block">
                              {new Date(activity.date).toLocaleDateString()}
                            </Typography>
                            <Typography variant="caption" display="block">
                              Value: {formatCurrency(activity.value)}
                            </Typography>
                          </Box>
                        }
                      />
                      <Chip 
                        label={activity.status.toUpperCase()}
                        size="small"
                        color={getStatusColor(activity.status) as any}
                      />
                    </ListItem>
                    {index < mockSupplierData.recentActivity.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}