'use client';

import React, { useState } from 'react';
import { 
  Con<PERSON>er, 
  Box, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  Alert,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TextField,
  InputAdornment
} from '@mui/material';
import { 
  Wrench, 
  Search, 
  Download, 
  CheckCircle, 
  Clock,
  DollarSign,
  Users,
  Shield,
  Zap
} from 'lucide-react';

export default function ToolSyncPage() {
  const [searchTerm, setSearchTerm] = useState('');

  // Mock ToolSync data
  const stats = {
    totalTools: 1247,
    activeLicenses: 89,
    monthlySavings: 'R45,600',
    complianceRate: 98.7,
    sharedLicenses: 156
  };

  const softwareTools = [
    {
      id: 1,
      name: 'Microsoft Project Professional',
      category: 'Project Management',
      license: 'Annual',
      cost: 'R2,400/year',
      users: 12,
      compliance: 'Compliant',
      availability: 'Available',
      description: 'Professional project management software'
    },
    {
      id: 2,
      name: 'AutoCAD Architecture',
      category: 'Design & Engineering',
      license: 'Subscription',
      cost: 'R8,900/year',
      users: 5,
      compliance: 'Compliant',
      availability: 'Limited',
      description: 'Architectural design and drafting software'
    },
    {
      id: 3,
      name: 'SAP Business One',
      category: 'ERP',
      license: 'Enterprise',
      cost: 'R15,600/year',
      users: 25,
      compliance: 'Compliant',
      availability: 'Available',
      description: 'Enterprise resource planning solution'
    },
    {
      id: 4,
      name: 'Sage Pastel Evolution',
      category: 'Accounting',
      license: 'Standard',
      cost: 'R3,200/year',
      users: 8,
      compliance: 'Compliant',
      availability: 'Available',
      description: 'Comprehensive accounting software'
    }
  ];

  const licenseSharing = [
    { tool: 'Microsoft Office 365', sharedWith: 'ABC Construction', savings: 'R12,000', status: 'Active' },
    { tool: 'Adobe Creative Suite', sharedWith: 'XYZ Marketing', savings: 'R8,500', status: 'Active' },
    { tool: 'Autodesk Revit', sharedWith: 'DEF Architects', savings: 'R15,200', status: 'Pending' }
  ];

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case 'Available': return 'success';
      case 'Limited': return 'warning';
      case 'Unavailable': return 'error';
      default: return 'default';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'success';
      case 'Pending': return 'warning';
      case 'Expired': return 'error';
      default: return 'default';
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
          🔧 ToolSync License Management
        </Typography>
        <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
          Software tools, license optimization, and collaborative license sharing platform
        </Typography>
        
        <Alert severity="info" sx={{ mb: 3 }}>
          <strong>License Optimization Active:</strong> Managing 1,247 software tools with R45,600 monthly savings through smart license sharing.
        </Alert>
      </Box>

      {/* Search Bar */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <TextField
            fullWidth
            placeholder="Search for software tools, licenses, or categories..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search size={20} />
                </InputAdornment>
              ),
            }}
            sx={{ mb: 2 }}
          />
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            <Chip label="Project Management" clickable />
            <Chip label="Design & Engineering" clickable />
            <Chip label="Accounting" clickable />
            <Chip label="ERP" clickable />
            <Chip label="Security" clickable />
          </Box>
        </CardContent>
      </Card>

      {/* Stats Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Wrench size={32} color="#2196f3" />
              <Typography variant="h4" fontWeight="bold">{stats.totalTools}</Typography>
              <Typography variant="body2" color="text.secondary">Total Tools</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <CheckCircle size={32} color="#4caf50" />
              <Typography variant="h4" fontWeight="bold">{stats.activeLicenses}</Typography>
              <Typography variant="body2" color="text.secondary">Active Licenses</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <DollarSign size={32} color="#ff9800" />
              <Typography variant="h4" fontWeight="bold">{stats.monthlySavings}</Typography>
              <Typography variant="body2" color="text.secondary">Monthly Savings</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Shield size={32} color="#9c27b0" />
              <Typography variant="h4" fontWeight="bold">{stats.complianceRate}%</Typography>
              <Typography variant="body2" color="text.secondary">Compliance Rate</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Users size={32} color="#f44336" />
              <Typography variant="h4" fontWeight="bold">{stats.sharedLicenses}</Typography>
              <Typography variant="body2" color="text.secondary">Shared Licenses</Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Software Tools */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6">Software Tools & Licenses</Typography>
                <Button variant="contained" startIcon={<Download />}>
                  Add New Tool
                </Button>
              </Box>
              
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Tool Name</TableCell>
                      <TableCell>Category</TableCell>
                      <TableCell>License Type</TableCell>
                      <TableCell>Cost</TableCell>
                      <TableCell>Users</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {softwareTools.map((tool) => (
                      <TableRow key={tool.id}>
                        <TableCell>
                          <Box>
                            <Typography variant="subtitle2">{tool.name}</Typography>
                            <Typography variant="caption" color="text.secondary">
                              {tool.description}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>{tool.category}</TableCell>
                        <TableCell>{tool.license}</TableCell>
                        <TableCell>{tool.cost}</TableCell>
                        <TableCell>{tool.users}</TableCell>
                        <TableCell>
                          <Chip 
                            label={tool.availability} 
                            color={getAvailabilityColor(tool.availability) as any}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Button size="small" variant="outlined">
                            Manage
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* License Sharing */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>License Sharing</Typography>
              <Alert severity="success" sx={{ mb: 2 }}>
                <strong>Smart Sharing:</strong> Save up to 60% on software costs through collaborative licensing.
              </Alert>
              
              {licenseSharing.map((share, index) => (
                <Box key={index} sx={{ mb: 3, p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                  <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                    {share.tool}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Shared with: {share.sharedWith}
                  </Typography>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="body2" color="success.main" fontWeight="medium">
                      Savings: {share.savings}
                    </Typography>
                    <Chip 
                      label={share.status} 
                      color={getStatusColor(share.status) as any}
                      size="small"
                    />
                  </Box>
                </Box>
              ))}
              
              <Button variant="outlined" fullWidth startIcon={<Users />}>
                Find Sharing Partners
              </Button>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card sx={{ mt: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>Quick Actions</Typography>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Button variant="outlined" fullWidth startIcon={<Zap />}>
                    License Audit
                  </Button>
                </Grid>
                <Grid item xs={12}>
                  <Button variant="outlined" fullWidth startIcon={<Clock />}>
                    Renewal Alerts
                  </Button>
                </Grid>
                <Grid item xs={12}>
                  <Button variant="outlined" fullWidth startIcon={<Shield />}>
                    Compliance Check
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* License Optimization Insights */}
      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>License Optimization Insights</Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Alert severity="info">
                <strong>Optimization Opportunity:</strong> 12 unused licenses detected worth R28,400/year
              </Alert>
            </Grid>
            <Grid item xs={12} md={4}>
              <Alert severity="warning">
                <strong>Renewal Alert:</strong> 5 licenses expiring in next 30 days
              </Alert>
            </Grid>
            <Grid item xs={12} md={4}>
              <Alert severity="success">
                <strong>Sharing Success:</strong> R45,600 saved this month through license sharing
              </Alert>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Container>
  );
}
