'use client';

import React from 'react';
import { Container, Ty<PERSON><PERSON>, Card, CardContent, Grid, Button, Box, TextField, Switch, FormControlLabel } from '@mui/material';
import { SmartToy, <PERSON>ting<PERSON>, Speed, Security } from '@mui/icons-material';

export default function AutoBiddingConfigPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
        Auto-Bidding Configuration
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        Configure automated bidding rules and parameters
      </Typography>
      
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Bidding Rules</Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField fullWidth label="Maximum Bid Amount" variant="outlined" type="number" />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField fullWidth label="Minimum Profit Margin %" variant="outlined" type="number" />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel control={<Switch defaultChecked />} label="Enable Auto-Bidding" />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel control={<Switch />} label="Bid on Municipal Tenders Only" />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Quick Stats</Typography>
              <Box sx={{ mt: 2 }}>
                <Typography variant="h4" color="primary.main">15</Typography>
                <Typography variant="body2" color="text.secondary">Auto-bids this month</Typography>
                <Typography variant="h4" color="success.main" sx={{ mt: 2 }}>87%</Typography>
                <Typography variant="body2" color="text.secondary">Success rate</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
}
