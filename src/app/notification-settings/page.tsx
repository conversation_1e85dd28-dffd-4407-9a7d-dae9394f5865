'use client';

import React from 'react';
import { Container, Typography, Card, CardContent, Grid, Box, Switch, FormControlLabel } from '@mui/material';
import { Notifications, Email, Sms, WhatsApp } from '@mui/icons-material';

export default function NotificationSettingsPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
        Notification Settings
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        Configure how and when you receive notifications
      </Typography>
      
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Email Notifications</Typography>
              <Box sx={{ mt: 2 }}>
                <FormControlLabel control={<Switch defaultChecked />} label="New Tender Alerts" />
                <FormControlLabel control={<Switch defaultChecked />} label="Bid Status Updates" />
                <FormControlLabel control={<Switch />} label="Daily Summary" />
                <FormControlLabel control={<Switch />} label="Weekly Reports" />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>WhatsApp Notifications</Typography>
              <Box sx={{ mt: 2 }}>
                <FormControlLabel control={<Switch defaultChecked />} label="Urgent Alerts" />
                <FormControlLabel control={<Switch />} label="Deadline Reminders" />
                <FormControlLabel control={<Switch />} label="Success Notifications" />
                <FormControlLabel control={<Switch />} label="System Updates" />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Notification Preferences</Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ textAlign: 'center', p: 2 }}>
                <Email sx={{ fontSize: 48, color: 'primary.main', mb: 1 }} />
                <Typography variant="h6">Email</Typography>
                <Typography variant="body2" color="text.secondary">Primary channel</Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ textAlign: 'center', p: 2 }}>
                <WhatsApp sx={{ fontSize: 48, color: 'success.main', mb: 1 }} />
                <Typography variant="h6">WhatsApp</Typography>
                <Typography variant="body2" color="text.secondary">Urgent only</Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Container>
  );
}
