'use client';

import React from 'react';
import { Container, Typography, Card, CardContent, Grid, Button, Box, Avatar, Chip, Rating } from '@mui/material';
import { Business, Star, Verified, Add } from '@mui/icons-material';

const providers = [
  { name: "TechSolutions SA", rating: 4.8, verified: true, category: "IT Services", projects: 45 },
  { name: "BuildCorp Ltd", rating: 4.6, verified: true, category: "Construction", projects: 32 },
  { name: "SupplyChain Pro", rating: 4.9, verified: false, category: "Logistics", projects: 28 }
];

export default function ProviderProfilesPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
            Provider Profiles
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Manage service provider profiles and partnerships
          </Typography>
        </Box>
        <Button variant="contained" startIcon={<Add />}>
          Add Provider
        </Button>
      </Box>
      
      <Grid container spacing={3}>
        {providers.map((provider, index) => (
          <Grid item xs={12} md={4} key={index}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <Avatar sx={{ bgcolor: 'primary.main' }}>
                    <Business />
                  </Avatar>
                  <Box>
                    <Typography variant="h6">{provider.name}</Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Rating value={provider.rating} precision={0.1} size="small" readOnly />
                      <Typography variant="caption">({provider.rating})</Typography>
                    </Box>
                  </Box>
                </Box>
                <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                  <Chip label={provider.category} color="primary" size="small" />
                  {provider.verified && <Chip icon={<Verified />} label="Verified" color="success" size="small" />}
                </Box>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {provider.projects} completed projects
                </Typography>
                <Button variant="outlined" size="small" fullWidth>
                  View Profile
                </Button>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Container>
  );
}
