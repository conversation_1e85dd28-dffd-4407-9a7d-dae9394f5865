'use client';

import React, { useState } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  Chip,
  LinearProgress,
  Alert,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Divider,
  Avatar,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  WhatsApp,
  CheckCircle,
  Warning,
  Error,
  AutoAwesome,
  Message,
  TrendingUp,
  Speed,
  Settings,
  Refresh,
  PlayArrow,
  Pause,
  Visibility,
  MoreVert
} from '@mui/icons-material';

// Mock data for demo
const mockStatus = {
  is_connected: true,
  connection_quality: 'excellent',
  messages_processed: 47,
  auto_bids_triggered: 12,
  pending_messages: 3,
  auto_bid_enabled: true,
  last_activity: '2 minutes ago'
};

const mockMessages = [
  {
    id: '1',
    message_type: 'bid_notification',
    from_number: '+27821234567',
    received_at: new Date().toISOString(),
    processing_status: 'processed',
    extracted_data: { tender_title: 'Road Maintenance Contract - Cape Town' }
  },
  {
    id: '2',
    message_type: 'tender_alert',
    from_number: '+27821234568',
    received_at: new Date(Date.now() - 3600000).toISOString(),
    processing_status: 'processed',
    extracted_data: { tender_title: 'Office Building Construction' }
  },
  {
    id: '3',
    message_type: 'deadline_reminder',
    from_number: '+27821234569',
    received_at: new Date(Date.now() - 7200000).toISOString(),
    processing_status: 'failed',
    extracted_data: { tender_title: 'School Renovation Project' }
  }
];

const mockActivities = [
  {
    id: '1',
    auto_bid_status: 'completed',
    trigger_source: 'WhatsApp',
    started_at: new Date().toISOString(),
    jobs_created: 15
  },
  {
    id: '2',
    auto_bid_status: 'completed',
    trigger_source: 'WhatsApp',
    started_at: new Date(Date.now() - 1800000).toISOString(),
    jobs_created: 8
  },
  {
    id: '3',
    auto_bid_status: 'failed',
    trigger_source: 'WhatsApp',
    started_at: new Date(Date.now() - 3600000).toISOString(),
    jobs_created: 0
  }
];

export default function WhatsAppDashboard() {
  const [autoBidEnabled, setAutoBidEnabled] = useState(mockStatus.auto_bid_enabled);

  const handleToggleAutoBid = () => {
    setAutoBidEnabled(!autoBidEnabled);
  };

  const getConnectionIcon = () => {
    if (!mockStatus.is_connected) return <Error color="error" />;
    if (mockStatus.connection_quality === 'excellent') return <CheckCircle color="success" />;
    if (mockStatus.connection_quality === 'good') return <CheckCircle color="info" />;
    return <Warning color="warning" />;
  };

  const getConnectionColor = () => {
    if (!mockStatus.is_connected) return 'error';
    if (mockStatus.connection_quality === 'excellent') return 'success';
    if (mockStatus.connection_quality === 'good') return 'info';
    return 'warning';
  };

  const getMessageTypeIcon = (type: string) => {
    switch (type) {
      case 'bid_notification': return '📋';
      case 'tender_alert': return '🚨';
      case 'deadline_reminder': return '⏰';
      case 'document_available': return '📄';
      default: return '💬';
    }
  };

  const getActivityStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'failed': return 'error';
      case 'cancelled': return 'warning';
      default: return 'info';
    }
  };

  return (
    <Box sx={{ p: 3, minHeight: '100vh', bgcolor: 'grey.50' }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main' }}>
            📱 WhatsApp Auto-Bidding
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Automated bidding intelligence through WhatsApp integration
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<Settings />}
            onClick={() => alert('WhatsApp Settings')}
          >
            Settings
          </Button>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={() => window.location.reload()}
          >
            Refresh
          </Button>
        </Box>
      </Box>

      {/* Status Overview */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={3}>
          <Card elevation={2}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                {getConnectionIcon()}
                <Typography variant="h6">Connection</Typography>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
                {mockStatus.is_connected ? 'Connected' : 'Disconnected'}
              </Typography>
              <Chip 
                label={mockStatus.connection_quality.toUpperCase()}
                color={getConnectionColor() as any}
                size="small"
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card elevation={2}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <Message color="primary" />
                <Typography variant="h6">Messages</Typography>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
                {mockStatus.messages_processed}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Processed today
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card elevation={2}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <AutoAwesome color="warning" />
                <Typography variant="h6">Auto-Bids</Typography>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
                {mockStatus.auto_bids_triggered}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Triggered today
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card elevation={2}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <Speed color="info" />
                <Typography variant="h6">Pending</Typography>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
                {mockStatus.pending_messages}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Messages in queue
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Auto-Bid Control */}
      <Paper sx={{ mb: 3 }} elevation={2}>
        <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
          <Typography variant="h6">🤖 Auto-Bid Control</Typography>
        </Box>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box>
              <Typography variant="h6" sx={{ mb: 1 }}>
                Auto-Bidding Status
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {autoBidEnabled 
                  ? 'Automatically processing bid opportunities from WhatsApp messages'
                  : 'Auto-bidding is currently disabled'
                }
              </Typography>
            </Box>
            
            <FormControlLabel
              control={
                <Switch
                  checked={autoBidEnabled}
                  onChange={handleToggleAutoBid}
                  color="primary"
                  size="medium"
                />
              }
              label=""
            />
          </Box>

          {autoBidEnabled && (
            <Alert severity="success" sx={{ mt: 2 }}>
              <Typography variant="subtitle2">✅ Auto-Bidding Active</Typography>
              <Typography variant="body2">
                WhatsApp messages are being monitored for bid opportunities. 
                Last activity: {mockStatus.last_activity}
              </Typography>
            </Alert>
          )}

          {!autoBidEnabled && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              <Typography variant="subtitle2">⚠️ Auto-Bidding Disabled</Typography>
              <Typography variant="body2">
                Enable auto-bidding to automatically process bid opportunities from WhatsApp messages.
              </Typography>
            </Alert>
          )}
        </CardContent>
      </Paper>

      {/* Recent Messages and Activities */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper elevation={2}>
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <Typography variant="h6">📨 Recent Messages</Typography>
            </Box>
            <List>
              {mockMessages.map((message, index) => (
                <React.Fragment key={message.id}>
                  <ListItem>
                    <ListItemIcon>
                      <Typography variant="h6">
                        {getMessageTypeIcon(message.message_type)}
                      </Typography>
                    </ListItemIcon>
                    <ListItemText
                      primary={message.extracted_data.tender_title || 'Message'}
                      secondary={
                        <Box>
                          <Typography variant="caption" display="block">
                            From: {message.from_number}
                          </Typography>
                          <Typography variant="caption" display="block">
                            {new Date(message.received_at).toLocaleString()}
                          </Typography>
                          <Chip 
                            label={message.processing_status}
                            size="small"
                            color={
                              message.processing_status === 'processed' ? 'success' :
                              message.processing_status === 'failed' ? 'error' : 'info'
                            }
                            sx={{ mt: 0.5 }}
                          />
                        </Box>
                      }
                    />
                    <ListItemSecondaryAction>
                      <IconButton edge="end" size="small">
                        <Visibility />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                  {index < mockMessages.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </Paper>
        </Grid>

        {/* Recent Auto-Bid Activities */}
        <Grid item xs={12} md={6}>
          <Paper elevation={2}>
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <Typography variant="h6">🤖 Auto-Bid Activities</Typography>
            </Box>
            <List>
              {mockActivities.map((activity, index) => (
                <React.Fragment key={activity.id}>
                  <ListItem>
                    <ListItemIcon>
                      <Avatar sx={{ bgcolor: 'primary.main', width: 32, height: 32 }}>
                        <AutoAwesome fontSize="small" />
                      </Avatar>
                    </ListItemIcon>
                    <ListItemText
                      primary={`Auto-bid ${activity.auto_bid_status}`}
                      secondary={
                        <Box>
                          <Typography variant="caption" display="block">
                            Source: {activity.trigger_source}
                          </Typography>
                          <Typography variant="caption" display="block">
                            {new Date(activity.started_at).toLocaleString()}
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 1, mt: 0.5 }}>
                            <Chip 
                              label={activity.auto_bid_status}
                              size="small"
                              color={getActivityStatusColor(activity.auto_bid_status) as any}
                            />
                            {activity.jobs_created > 0 && (
                              <Chip 
                                label={`${activity.jobs_created} jobs`}
                                size="small"
                                color="success"
                                variant="outlined"
                              />
                            )}
                          </Box>
                        </Box>
                      }
                    />
                    <ListItemSecondaryAction>
                      <IconButton edge="end" size="small">
                        <MoreVert />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                  {index < mockActivities.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
}