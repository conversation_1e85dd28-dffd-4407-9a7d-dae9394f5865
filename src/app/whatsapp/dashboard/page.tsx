'use client';

import React, { useState } from 'react';
import { 
  Con<PERSON>er, 
  <PERSON>, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  Alert,
  Button,
  Switch,
  FormControlLabel,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider
} from '@mui/material';
import { 
  MessageCircle, 
  Zap, 
  CheckCircle, 
  Clock, 
  AlertTriangle,
  Settings,
  BarChart3,
  Send,
  Bot,
  Smartphone
} from 'lucide-react';

export default function WhatsAppDashboardPage() {
  const [autoBidEnabled, setAutoBidEnabled] = useState(true);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);

  // Mock WhatsApp automation data
  const stats = {
    totalMessages: 1247,
    autoBids: 89,
    successRate: 76.4,
    avgResponseTime: '2.3 min',
    activeChats: 23,
    pendingBids: 7
  };

  const recentActivity = [
    { id: 1, type: 'auto_bid', message: 'Auto-bid submitted for Municipal IT Services', time: '2 min ago', status: 'success' },
    { id: 2, type: 'notification', message: 'New tender alert: Road Maintenance Contract', time: '15 min ago', status: 'info' },
    { id: 3, type: 'response', message: 'Bid confirmation received for Security Services', time: '1 hour ago', status: 'success' },
    { id: 4, type: 'error', message: 'Auto-bid failed: Missing compliance documents', time: '2 hours ago', status: 'error' },
    { id: 5, type: 'auto_bid', message: 'Auto-bid submitted for Consulting Services', time: '3 hours ago', status: 'success' }
  ];

  const activeBids = [
    { id: 1, tender: 'Municipal IT Services', value: 'R2.4M', status: 'Submitted', probability: 87 },
    { id: 2, tender: 'Security Services', value: 'R1.2M', status: 'Pending Review', probability: 74 },
    { id: 3, tender: 'Consulting Contract', value: 'R890K', status: 'Auto-Generated', probability: 92 }
  ];

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'auto_bid': return <Bot size={20} color="#4caf50" />;
      case 'notification': return <MessageCircle size={20} color="#2196f3" />;
      case 'response': return <CheckCircle size={20} color="#4caf50" />;
      case 'error': return <AlertTriangle size={20} color="#f44336" />;
      default: return <MessageCircle size={20} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'success';
      case 'error': return 'error';
      case 'info': return 'info';
      default: return 'default';
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
          📱 WhatsApp Auto-Bidding Dashboard
        </Typography>
        <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
          Automated bidding through WhatsApp with intelligent tender notifications
        </Typography>
        
        <Alert severity="success" sx={{ mb: 3 }}>
          <strong>WhatsApp Connected:</strong> Auto-bidding active with 76.4% success rate. 89 bids submitted automatically.
        </Alert>
      </Box>

      {/* Quick Controls */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Quick Controls</Typography>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={3}>
              <FormControlLabel
                control={
                  <Switch 
                    checked={autoBidEnabled} 
                    onChange={(e) => setAutoBidEnabled(e.target.checked)}
                    color="success"
                  />
                }
                label="Auto-Bidding"
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControlLabel
                control={
                  <Switch 
                    checked={notificationsEnabled} 
                    onChange={(e) => setNotificationsEnabled(e.target.checked)}
                    color="primary"
                  />
                }
                label="Notifications"
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <Button variant="outlined" startIcon={<Settings />} fullWidth>
                Settings
              </Button>
            </Grid>
            <Grid item xs={12} md={3}>
              <Button variant="contained" startIcon={<BarChart3 />} fullWidth>
                Analytics
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Stats Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <MessageCircle size={32} color="#25D366" />
              <Typography variant="h4" fontWeight="bold">{stats.totalMessages}</Typography>
              <Typography variant="body2" color="text.secondary">Total Messages</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Bot size={32} color="#2196f3" />
              <Typography variant="h4" fontWeight="bold">{stats.autoBids}</Typography>
              <Typography variant="body2" color="text.secondary">Auto Bids</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <CheckCircle size={32} color="#4caf50" />
              <Typography variant="h4" fontWeight="bold">{stats.successRate}%</Typography>
              <Typography variant="body2" color="text.secondary">Success Rate</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Clock size={32} color="#ff9800" />
              <Typography variant="h4" fontWeight="bold">{stats.avgResponseTime}</Typography>
              <Typography variant="body2" color="text.secondary">Avg Response</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Smartphone size={32} color="#9c27b0" />
              <Typography variant="h4" fontWeight="bold">{stats.activeChats}</Typography>
              <Typography variant="body2" color="text.secondary">Active Chats</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Zap size={32} color="#f44336" />
              <Typography variant="h4" fontWeight="bold">{stats.pendingBids}</Typography>
              <Typography variant="body2" color="text.secondary">Pending Bids</Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Recent Activity */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Recent Activity</Typography>
              <List>
                {recentActivity.map((activity, index) => (
                  <React.Fragment key={activity.id}>
                    <ListItem>
                      <ListItemIcon>
                        {getActivityIcon(activity.type)}
                      </ListItemIcon>
                      <ListItemText
                        primary={activity.message}
                        secondary={activity.time}
                      />
                      <Chip 
                        label={activity.status} 
                        color={getStatusColor(activity.status) as any}
                        size="small"
                      />
                    </ListItem>
                    {index < recentActivity.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Active Bids */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Active Auto-Bids</Typography>
              <List>
                {activeBids.map((bid, index) => (
                  <React.Fragment key={bid.id}>
                    <ListItem>
                      <ListItemText
                        primary={bid.tender}
                        secondary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                            <Typography variant="body2" color="success.main" fontWeight="bold">
                              {bid.value}
                            </Typography>
                            <Chip label={bid.status} size="small" />
                            <Typography variant="body2" color="text.secondary">
                              {bid.probability}% win probability
                            </Typography>
                          </Box>
                        }
                      />
                      <Button size="small" variant="outlined">
                        View
                      </Button>
                    </ListItem>
                    {index < activeBids.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
              
              <Box sx={{ mt: 2, textAlign: 'center' }}>
                <Button variant="contained" startIcon={<Send />}>
                  Send Test Message
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* WhatsApp Integration Status */}
      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>WhatsApp Integration Status</Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Alert severity="success">
                <strong>Connection:</strong> Active and stable
              </Alert>
            </Grid>
            <Grid item xs={12} md={4}>
              <Alert severity="info">
                <strong>API Limits:</strong> 847/1000 messages today
              </Alert>
            </Grid>
            <Grid item xs={12} md={4}>
              <Alert severity="warning">
                <strong>Queue:</strong> 3 messages pending
              </Alert>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Container>
  );
}
