'use client';

import React from 'react';
import { Container, Typography, Card, CardContent, Grid, Box, Avatar, Chip, LinearProgress } from '@mui/material';
import { TrendingUp, TrendingDown, Assessment, ShowChart } from '@mui/icons-material';

const trends = [
  { sector: "IT Services", growth: 18, direction: "up", volume: "R45.2M" },
  { sector: "Construction", growth: 12, direction: "up", volume: "R89.7M" },
  { sector: "Consulting", growth: -3, direction: "down", volume: "R23.1M" }
];

export default function MarketTrendsPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
        Market Trends
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        Real-time market analysis and trend forecasting
      </Typography>
      
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="primary">
                    R158M
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Market Volume
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <Assessment />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="success.main">
                    +15%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Market Growth
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <TrendingUp />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Sector Performance</Typography>
          <Grid container spacing={3}>
            {trends.map((trend, index) => (
              <Grid item xs={12} md={4} key={index}>
                <Card variant="outlined">
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Typography variant="h6">{trend.sector}</Typography>
                      {trend.direction === 'up' ? 
                        <TrendingUp sx={{ color: 'success.main' }} /> : 
                        <TrendingDown sx={{ color: 'error.main' }} />
                      }
                    </Box>
                    <Typography variant="h4" color={trend.direction === 'up' ? 'success.main' : 'error.main'} gutterBottom>
                      {trend.growth > 0 ? '+' : ''}{trend.growth}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Volume: {trend.volume}
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={Math.abs(trend.growth) * 5} 
                      color={trend.direction === 'up' ? 'success' : 'error'}
                    />
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>
    </Container>
  );
}
