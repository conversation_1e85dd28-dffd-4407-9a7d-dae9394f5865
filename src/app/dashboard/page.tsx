'use client';

import React from 'react';
import {
  Box,
  Container,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  LinearProgress,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  TrendingUp,
  Assignment,
  Psychology,
  SmartToy,
  WhatsApp,
  Analytics,
  Business,
  Notifications,
  CheckCircle,
  Gavel,
  RequestQuote,
  Balance,
  FlashOn,
  BarChart,
  ShowChart
} from '@mui/icons-material';
import MarketStatsDashboard from '@/components/statistics/MarketStatsDashboard';

export default function DashboardPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
          BidBeez Dashboard
        </Typography>
        <Typography variant="h6" color="text.secondary">
          Welcome to your comprehensive tendering intelligence platform
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                  <Assignment />
                </Avatar>
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    25
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active Tenders
                  </Typography>
                </Box>
              </Box>
              <Chip label="+12% this month" color="success" size="small" />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                  <TrendingUp />
                </Avatar>
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    94.7%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Success Rate
                  </Typography>
                </Box>
              </Box>
              <Chip label="Industry leading" color="success" size="small" />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                  <Psychology />
                </Avatar>
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    78%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Confidence Level
                  </Typography>
                </Box>
              </Box>
              <Chip label="+12% growth" color="warning" size="small" />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}>
                  <SmartToy />
                </Avatar>
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    15
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    AI Engines Active
                  </Typography>
                </Box>
              </Box>
              <Chip label="All operational" color="info" size="small" />
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Unified Opportunities Section */}
      <Card sx={{ mb: 4, background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
        <CardContent>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={8}>
              <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold' }}>
                🎯 Unified Opportunities Hub
              </Typography>
              <Typography variant="body1" sx={{ mb: 2, opacity: 0.9 }}>
                Access all bidding opportunities in one place - Government Tenders, Government RFQs, and Bidder RFQs
              </Typography>
              <Grid container spacing={2} sx={{ mb: 2 }}>
                <Grid item>
                  <Chip
                    icon={<Gavel />}
                    label="Tenders: 75% Success"
                    sx={{ backgroundColor: 'rgba(255,255,255,0.2)', color: 'white' }}
                  />
                </Grid>
                <Grid item>
                  <Chip
                    icon={<RequestQuote />}
                    label="Gov RFQs: 88% Success"
                    sx={{ backgroundColor: 'rgba(255,255,255,0.2)', color: 'white' }}
                  />
                </Grid>
                <Grid item>
                  <Chip
                    icon={<Balance />}
                    label="Portfolio Balance: 60/40"
                    sx={{ backgroundColor: 'rgba(255,255,255,0.2)', color: 'white' }}
                  />
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={12} md={4} sx={{ textAlign: 'center' }}>
              <Button
                variant="contained"
                size="large"
                href="/opportunities"
                startIcon={<FlashOn />}
                sx={{
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  color: 'white',
                  fontWeight: 'bold',
                  py: 2,
                  px: 4,
                  '&:hover': {
                    backgroundColor: 'rgba(255,255,255,0.3)',
                    transform: 'translateY(-2px)',
                    boxShadow: '0 8px 25px rgba(0,0,0,0.3)'
                  },
                  transition: 'all 0.3s ease'
                }}
              >
                🚀 Explore All Opportunities
              </Button>
              <Typography variant="caption" sx={{ display: 'block', mt: 1, opacity: 0.8 }}>
                AI-powered matching & portfolio optimization
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Main Content */}
      <Grid container spacing={3}>
        {/* Quick Actions */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Actions
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Button
                  variant="contained"
                  startIcon={<FlashOn />}
                  href="/opportunities"
                  fullWidth
                  sx={{
                    background: 'linear-gradient(45deg, #FF6B6B, #4ECDC4)',
                    fontWeight: 'bold'
                  }}
                >
                  🎯 All Opportunities
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<Assignment />}
                  href="/tenders"
                  fullWidth
                >
                  Browse Tenders Only
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<Psychology />}
                  href="/psychological-assessment"
                  fullWidth
                >
                  Psychological Assessment
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<SmartToy />}
                  href="/ai-engine"
                  fullWidth
                >
                  AI Engine
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<WhatsApp />}
                  href="/whatsapp"
                  fullWidth
                >
                  WhatsApp Auto-Bidding
                </Button>
                <Button
                  variant="contained"
                  color="warning"
                  startIcon={<Assignment />}
                  href="/client-bee-management"
                  fullWidth
                >
                  🐝 Manage Bee Workers
                </Button>
                <Button
                  variant="contained"
                  color="info"
                  startIcon={<Assignment />}
                  href="/available-bees"
                  fullWidth
                  sx={{
                    background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                    boxShadow: '0 3px 5px 2px rgba(33, 203, 243, .3)',
                    '&:hover': {
                      background: 'linear-gradient(45deg, #1976D2 30%, #0288D1 90%)',
                    }
                  }}
                >
                  🎯 Available Bees (Tender-Centric)
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Activity */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Activity
              </Typography>
              <List>
                <ListItem>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: 'success.main' }}>
                      <CheckCircle />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary="Bid submitted successfully"
                    secondary="2 hours ago"
                  />
                </ListItem>
                <Divider variant="inset" component="li" />
                <ListItem>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: 'info.main' }}>
                      <Notifications />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary="New tender available"
                    secondary="4 hours ago"
                  />
                </ListItem>
                <Divider variant="inset" component="li" />
                <ListItem>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: 'primary.main' }}>
                      <Analytics />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary="AI analysis completed"
                    secondary="6 hours ago"
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* System Health */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                System Health
              </Typography>
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">API Performance</Typography>
                  <Typography variant="body2">96.8%</Typography>
                </Box>
                <LinearProgress variant="determinate" value={96.8} color="success" />
              </Box>
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">AI Accuracy</Typography>
                  <Typography variant="body2">94.3%</Typography>
                </Box>
                <LinearProgress variant="determinate" value={94.3} color="primary" />
              </Box>
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Automation Success</Typography>
                  <Typography variant="body2">94.7%</Typography>
                </Box>
                <LinearProgress variant="determinate" value={94.7} color="warning" />
              </Box>
              <Button
                variant="outlined"
                href="/performance-metrics"
                fullWidth
                startIcon={<Analytics />}
              >
                View Detailed Metrics
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Navigation Links */}
      <Box sx={{ mt: 4, textAlign: 'center' }}>
        <Typography variant="h6" gutterBottom>
          Explore More Features
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
          <Button variant="outlined" href="/all-pages">
            All Pages Directory
          </Button>
          <Button variant="outlined" href="/comprehensive-sitemap">
            Comprehensive Sitemap
          </Button>
          <Button variant="outlined" href="/queen-bee-management">
            Queen Bee Management
          </Button>
        </Box>

        {/* Market Statistics Section */}
        <Box sx={{ mt: 6 }}>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <ShowChart color="primary" />
                📊 South African Market Intelligence
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Real-time statistics showing the massive scale of opportunities in South Africa
              </Typography>

              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="primary" sx={{ fontWeight: 'bold' }}>
                      36,637
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Total Active Opportunities
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="success.main" sx={{ fontWeight: 'bold' }}>
                      R89.5B
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Total Market Value
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="warning.main" sx={{ fontWeight: 'bold' }}>
                      23,456
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Active Bidders
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="error.main" sx={{ fontWeight: 'bold' }}>
                      127
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      New Today
                    </Typography>
                  </Box>
                </Grid>
              </Grid>

              <Alert severity="warning" sx={{ mb: 2 }}>
                <Typography variant="body2">
                  <strong>⚠️ MARKET PENETRATION ALERT:</strong> Most bidders engage with less than 0.1% of available opportunities.
                  Are you maximizing your market reach?
                </Typography>
              </Alert>

              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
                <Button
                  variant="contained"
                  startIcon={<BarChart />}
                  href="/market-statistics"
                  sx={{ fontWeight: 'bold' }}
                >
                  📊 View Full Market Analysis
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<TrendingUp />}
                  href="/opportunities"
                >
                  🎯 Explore Opportunities
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Box>
      </Box>
    </Container>
  );
}
