'use client';

import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Chip,
  LinearProgress,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Slider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  Psychology,
  Mood,
  TrendingUp,
  TrendingDown,
  Assessment,
  Timeline,
  Favorite,
  Speed,
  Warning,
  CheckCircle,
  Add,
  Insights
} from '@mui/icons-material';

const mentalStateData = [
  { category: "Stress Level", current: 34, target: 25, trend: "down", color: "warning" },
  { category: "Confidence", current: 78, target: 85, trend: "up", color: "success" },
  { category: "Focus", current: 82, target: 90, trend: "up", color: "primary" },
  { category: "Energy", current: 67, target: 75, trend: "stable", color: "info" }
];

const recentEntries = [
  { date: "2024-01-11", mood: "Confident", stress: 28, notes: "Great presentation today" },
  { date: "2024-01-10", mood: "Focused", stress: 35, notes: "Busy day with multiple deadlines" },
  { date: "2024-01-09", mood: "Optimistic", stress: 22, notes: "Won a major tender bid" }
];

export default function MentalStateTrackingPage() {
  const [open, setOpen] = useState(false);
  const [currentMood, setCurrentMood] = useState('');
  const [stressLevel, setStressLevel] = useState(30);

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
            Mental State Tracking
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Monitor and optimize your psychological well-being for peak performance
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => setOpen(true)}
          size="large"
        >
          Log Current State
        </Button>
      </Box>

      {/* Current Status Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="warning.main">
                    34%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Current Stress
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'warning.main' }}>
                  <Warning />
                </Avatar>
              </Box>
              <Box sx={{ mt: 2 }}>
                <Chip label="Moderate level" color="warning" size="small" />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="success.main">
                    78%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Confidence Level
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <CheckCircle />
                </Avatar>
              </Box>
              <Box sx={{ mt: 2 }}>
                <Chip label="+12% this week" color="success" size="small" />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="primary.main">
                    82%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Focus Score
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <Psychology />
                </Avatar>
              </Box>
              <Box sx={{ mt: 2 }}>
                <Chip label="High focus" color="primary" size="small" />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="info.main">
                    67%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Energy Level
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'info.main' }}>
                  <Speed />
                </Avatar>
              </Box>
              <Box sx={{ mt: 2 }}>
                <Chip label="Good energy" color="info" size="small" />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Mental State Progress */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Mental State Progress
              </Typography>
              <Box sx={{ mt: 3 }}>
                {mentalStateData.map((item, index) => (
                  <Box key={index} sx={{ mb: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="subtitle2" fontWeight="medium">
                        {item.category}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body2" fontWeight="bold">
                          {item.current}%
                        </Typography>
                        {item.trend === 'up' && <TrendingUp sx={{ color: 'success.main', fontSize: 16 }} />}
                        {item.trend === 'down' && <TrendingDown sx={{ color: 'error.main', fontSize: 16 }} />}
                      </Box>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={item.current}
                      color={item.color as any}
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 0.5 }}>
                      <Typography variant="caption" color="text.secondary">
                        Current: {item.current}%
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Target: {item.target}%
                      </Typography>
                    </Box>
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Entries */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Entries
              </Typography>
              <List>
                {recentEntries.map((entry, index) => (
                  <ListItem key={index} sx={{ px: 0 }}>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: 'primary.main' }}>
                        <Mood />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={entry.mood}
                      secondary={
                        <Box>
                          <Typography variant="caption" display="block">
                            {entry.date} • Stress: {entry.stress}%
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {entry.notes}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Insights and Recommendations */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            AI-Powered Insights & Recommendations
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} md={4}>
              <Box sx={{ p: 2, bgcolor: 'success.50', borderRadius: 1, border: '1px solid', borderColor: 'success.200' }}>
                <Typography variant="subtitle2" color="success.main" gutterBottom>
                  ✓ Positive Trend
                </Typography>
                <Typography variant="body2">
                  Your confidence levels have increased by 12% this week. Keep up the positive momentum!
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ p: 2, bgcolor: 'warning.50', borderRadius: 1, border: '1px solid', borderColor: 'warning.200' }}>
                <Typography variant="subtitle2" color="warning.main" gutterBottom>
                  ⚠ Attention Needed
                </Typography>
                <Typography variant="body2">
                  Stress levels are slightly elevated. Consider taking breaks between intense work sessions.
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ p: 2, bgcolor: 'info.50', borderRadius: 1, border: '1px solid', borderColor: 'info.200' }}>
                <Typography variant="subtitle2" color="info.main" gutterBottom>
                  💡 Suggestion
                </Typography>
                <Typography variant="body2">
                  Your focus peaks in the morning. Schedule important tasks between 9-11 AM.
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Log Entry Dialog */}
      <Dialog open={open} onClose={() => setOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Log Current Mental State</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Current Mood</InputLabel>
                <Select
                  value={currentMood}
                  onChange={(e) => setCurrentMood(e.target.value)}
                  label="Current Mood"
                >
                  <MenuItem value="Excited">Excited</MenuItem>
                  <MenuItem value="Confident">Confident</MenuItem>
                  <MenuItem value="Focused">Focused</MenuItem>
                  <MenuItem value="Calm">Calm</MenuItem>
                  <MenuItem value="Stressed">Stressed</MenuItem>
                  <MenuItem value="Tired">Tired</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <Typography gutterBottom>Stress Level: {stressLevel}%</Typography>
              <Slider
                value={stressLevel}
                onChange={(e, value) => setStressLevel(value as number)}
                min={0}
                max={100}
                marks={[
                  { value: 0, label: 'Low' },
                  { value: 50, label: 'Medium' },
                  { value: 100, label: 'High' }
                ]}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Notes (optional)"
                multiline
                rows={3}
                placeholder="How are you feeling? Any specific thoughts or observations..."
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpen(false)}>Cancel</Button>
          <Button variant="contained" onClick={() => setOpen(false)}>
            Save Entry
          </Button>
        </DialogActions>
      </Dialog>

      {/* Quick Actions */}
      <Box sx={{ mt: 4, textAlign: 'center' }}>
        <Typography variant="h6" gutterBottom>
          Mental Health Tools
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
          <Button variant="outlined" startIcon={<Psychology />} href="/psychological-assessment">
            Full Assessment
          </Button>
          <Button variant="outlined" startIcon={<Insights />} href="/behavioral-analytics">
            Behavioral Analytics
          </Button>
          <Button variant="outlined" startIcon={<Favorite />} href="/stress-monitoring">
            Stress Monitoring
          </Button>
          <Button variant="outlined" startIcon={<Timeline />} href="/confidence-coaching">
            Confidence Coaching
          </Button>
        </Box>
      </Box>
    </Container>
  );
}
