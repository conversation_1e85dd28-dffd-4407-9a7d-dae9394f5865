'use client';

import React, { useState } from 'react';
import { 
  Container, 
  Box, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  Button,
  Alert,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Tabs,
  Tab
} from '@mui/material';
import { 
  Shield, 
  FileText, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  Plus,
  Download,
  Eye,
  Edit
} from 'lucide-react';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`protests-tabpanel-${index}`}
      aria-labelledby={`protests-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

export default function BidProtestsPage() {
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Mock protest data
  const protestStats = {
    totalProtests: 12,
    activeProtests: 3,
    successfulProtests: 7,
    pendingReview: 2,
    successRate: 58.3
  };

  const protests = [
    {
      id: 1,
      tenderRef: 'MUN/2024/IT/001',
      tenderTitle: 'Municipal IT Services Contract',
      protestReason: 'Unfair evaluation criteria',
      status: 'Under Review',
      submittedDate: '2024-01-15',
      deadline: '2024-01-25',
      value: 'R2.4M',
      priority: 'high'
    },
    {
      id: 2,
      tenderRef: 'PROV/2024/ROAD/045',
      tenderTitle: 'Road Maintenance Contract',
      protestReason: 'Specification bias towards specific supplier',
      status: 'Successful',
      submittedDate: '2024-01-10',
      deadline: '2024-01-20',
      value: 'R890K',
      priority: 'medium'
    },
    {
      id: 3,
      tenderRef: 'NAT/2024/SEC/012',
      tenderTitle: 'National Security Services',
      protestReason: 'Inadequate tender notice period',
      status: 'Rejected',
      submittedDate: '2024-01-08',
      deadline: '2024-01-18',
      value: 'R1.2M',
      priority: 'low'
    }
  ];

  const protestTemplates = [
    { id: 1, name: 'Unfair Evaluation Criteria', category: 'Evaluation', usage: 45 },
    { id: 2, name: 'Specification Bias', category: 'Specifications', usage: 32 },
    { id: 3, name: 'Inadequate Notice Period', category: 'Process', usage: 28 },
    { id: 4, name: 'Conflict of Interest', category: 'Ethics', usage: 19 },
    { id: 5, name: 'Non-compliance with PFMA', category: 'Legal', usage: 15 }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Successful': return 'success';
      case 'Under Review': return 'warning';
      case 'Rejected': return 'error';
      case 'Pending': return 'info';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'info';
      default: return 'default';
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
          ⚖️ Bid Protest Management
        </Typography>
        <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
          SA legal compliance tools for bid protests and dispute resolution
        </Typography>
        
        <Alert severity="info" sx={{ mb: 3 }}>
          <strong>Legal Compliance Active:</strong> PFMA, PPPFA, and Municipal Systems Act compliance monitoring with automated protest generation.
        </Alert>
      </Box>

      {/* Quick Actions */}
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item>
          <Button variant="contained" startIcon={<Plus />} color="primary">
            New Protest
          </Button>
        </Grid>
        <Grid item>
          <Button variant="outlined" startIcon={<FileText />}>
            Protest Wizard
          </Button>
        </Grid>
        <Grid item>
          <Button variant="outlined" startIcon={<Download />}>
            Export Reports
          </Button>
        </Grid>
      </Grid>

      {/* Stats Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Shield size={32} color="#2196f3" />
              <Typography variant="h4" fontWeight="bold">{protestStats.totalProtests}</Typography>
              <Typography variant="body2" color="text.secondary">Total Protests</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Clock size={32} color="#ff9800" />
              <Typography variant="h4" fontWeight="bold">{protestStats.activeProtests}</Typography>
              <Typography variant="body2" color="text.secondary">Active Protests</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <CheckCircle size={32} color="#4caf50" />
              <Typography variant="h4" fontWeight="bold">{protestStats.successfulProtests}</Typography>
              <Typography variant="body2" color="text.secondary">Successful</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <AlertTriangle size={32} color="#f44336" />
              <Typography variant="h4" fontWeight="bold">{protestStats.pendingReview}</Typography>
              <Typography variant="body2" color="text.secondary">Pending Review</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <FileText size={32} color="#9c27b0" />
              <Typography variant="h4" fontWeight="bold">{protestStats.successRate}%</Typography>
              <Typography variant="body2" color="text.secondary">Success Rate</Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs for different sections */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="protests tabs">
          <Tab label="Active Protests" />
          <Tab label="Protest Templates" />
          <Tab label="Legal Guidelines" />
          <Tab label="Success Analytics" />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>Current Bid Protests</Typography>
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Tender Reference</TableCell>
                    <TableCell>Title</TableCell>
                    <TableCell>Reason</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Value</TableCell>
                    <TableCell>Deadline</TableCell>
                    <TableCell>Priority</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {protests.map((protest) => (
                    <TableRow key={protest.id}>
                      <TableCell>{protest.tenderRef}</TableCell>
                      <TableCell>{protest.tenderTitle}</TableCell>
                      <TableCell>{protest.protestReason}</TableCell>
                      <TableCell>
                        <Chip 
                          label={protest.status} 
                          color={getStatusColor(protest.status) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>{protest.value}</TableCell>
                      <TableCell>{protest.deadline}</TableCell>
                      <TableCell>
                        <Chip 
                          label={protest.priority.toUpperCase()} 
                          color={getPriorityColor(protest.priority) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Button size="small" startIcon={<Eye />} sx={{ mr: 1 }}>
                          View
                        </Button>
                        <Button size="small" startIcon={<Edit />}>
                          Edit
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Grid container spacing={3}>
          {protestTemplates.map((template) => (
            <Grid item xs={12} md={6} lg={4} key={template.id}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>{template.name}</Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Category: {template.category}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Used {template.usage} times
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button size="small" variant="contained">
                      Use Template
                    </Button>
                    <Button size="small" variant="outlined">
                      Preview
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>PFMA Compliance</Typography>
                <Alert severity="info" sx={{ mb: 2 }}>
                  <strong>Public Finance Management Act:</strong> Key requirements for government procurement protests
                </Alert>
                <Typography variant="body2" color="text.secondary">
                  • 14-day protest period from award notification<br/>
                  • Written protest with specific grounds<br/>
                  • Supporting documentation required<br/>
                  • Accounting officer must respond within 7 days
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>Municipal Systems Act</Typography>
                <Alert severity="warning" sx={{ mb: 2 }}>
                  <strong>Municipal Procurement:</strong> Different rules apply for municipal tenders
                </Alert>
                <Typography variant="body2" color="text.secondary">
                  • 10-day protest period for municipal tenders<br/>
                  • Must be submitted to municipal manager<br/>
                  • Council resolution may be required<br/>
                  • Appeal to MEC within 30 days
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      <TabPanel value={tabValue} index={3}>
        <Alert severity="success" sx={{ mb: 3 }}>
          <strong>Success Rate Analysis:</strong> Your protest success rate is 58.3%, above the national average of 42%.
        </Alert>
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>Success by Category</Typography>
                <Box sx={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: '#f5f5f5' }}>
                  <Typography variant="body1" color="text.secondary">
                    📊 Success rate chart by protest category
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>Timeline Analysis</Typography>
                <Box sx={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: '#f5f5f5' }}>
                  <Typography variant="body1" color="text.secondary">
                    📈 Protest success trends over time
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>
    </Container>
  );
}
