'use client';

import React from 'react';
import { Container, Typo<PERSON>, Card, CardContent, Grid, Button, Box, Avatar, Chip } from '@mui/material';
import { Group, Add, Person, Star } from '@mui/icons-material';

const teams = [
  { name: "Sales Team", members: 8, lead: "<PERSON>", performance: 94 },
  { name: "Technical Team", members: 12, lead: "<PERSON>", performance: 87 },
  { name: "Operations Team", members: 6, lead: "<PERSON>", performance: 91 }
];

export default function TeamManagementPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
            Team Management
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Organize and manage your teams effectively
          </Typography>
        </Box>
        <Button variant="contained" startIcon={<Add />}>
          Create Team
        </Button>
      </Box>
      
      <Grid container spacing={3}>
        {teams.map((team, index) => (
          <Grid item xs={12} md={4} key={index}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <Avatar sx={{ bgcolor: 'primary.main' }}>
                    <Group />
                  </Avatar>
                  <Box>
                    <Typography variant="h6">{team.name}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {team.members} members
                    </Typography>
                  </Box>
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" gutterBottom>
                    Team Lead: {team.lead}
                  </Typography>
                  <Chip 
                    label={`${team.performance}% performance`}
                    color={team.performance > 90 ? 'success' : 'primary'}
                    size="small"
                  />
                </Box>
                <Button variant="outlined" size="small" fullWidth>
                  Manage Team
                </Button>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Container>
  );
}
