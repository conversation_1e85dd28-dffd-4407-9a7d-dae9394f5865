'use client';

import React from 'react';
import { Container, Typo<PERSON>, Card, CardContent, Grid, Button, Box, Chip, TextField } from '@mui/material';
import { Email, Add, Edit, Delete } from '@mui/icons-material';

const templates = [
  { name: "Bid Confirmation", type: "Automated", usage: 45, lastModified: "2024-01-10" },
  { name: "Tender Alert", type: "Notification", usage: 128, lastModified: "2024-01-08" },
  { name: "Welcome Email", type: "Onboarding", usage: 12, lastModified: "2024-01-05" }
];

export default function EmailTemplatesPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
            Email Templates
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Manage email templates for automated communications
          </Typography>
        </Box>
        <Button variant="contained" startIcon={<Add />}>
          Create Template
        </Button>
      </Box>
      
      <Grid container spacing={3}>
        {templates.map((template, index) => (
          <Grid item xs={12} md={4} key={index}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>{template.name}</Typography>
                <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                  <Chip label={template.type} color="primary" size="small" />
                  <Chip label={`${template.usage} uses`} color="info" size="small" />
                </Box>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Last modified: {template.lastModified}
                </Typography>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button size="small" startIcon={<Edit />}>Edit</Button>
                  <Button size="small" color="error" startIcon={<Delete />}>Delete</Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Template Editor</Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField fullWidth label="Template Name" variant="outlined" />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField fullWidth label="Subject Line" variant="outlined" />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={8}
                label="Email Content"
                variant="outlined"
                placeholder="Enter your email template content here..."
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Container>
  );
}
