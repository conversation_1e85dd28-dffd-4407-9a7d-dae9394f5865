'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  Button,
  Stack,
  Avatar,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  LocationOn,
  Star,
  Schedule,
  AttachMoney,
  Assignment,
  CheckCircle,
  Warning,
  Visibility
} from '@mui/icons-material';

// TENDER-CENTRIC BEE AVAILABILITY SYSTEM
interface TenderSpecificBee {
  id: string;
  beeId: string;
  fullName: string;
  avatar?: string;
  rating: number;
  completedTasks: number;
  specialties: string[];
  currentLocation: string;
  status: 'available' | 'busy' | 'offline';
  verificationLevel: 'basic' | 'standard' | 'premium' | 'elite';
  
  // TENDER-SPECIFIC AVAILABILITY
  availableForTender: boolean;
  tenderMatchScore: number;
  estimatedCost: number;
  estimatedDuration: string;
  availabilityWindow: string;
  distanceFromTender: number;
  
  // TENDER REQUIREMENTS MATCH
  meetsRequirements: {
    location: boolean;
    skills: boolean;
    verification: boolean;
    availability: boolean;
    budget: boolean;
  };
  
  // CURRENT WORKLOAD
  currentTasks: number;
  maxConcurrentTasks: number;
  nextAvailable: string;
}

interface TenderContext {
  tenderId: string;
  tenderTitle: string;
  location: string;
  deadline: string;
  requiredSkills: string[];
  budget: number;
  urgency: 'low' | 'medium' | 'high' | 'urgent';
  taskType: string;
  verificationRequired: string;
}

export default function AvailableBeesPage() {
  const [loading, setLoading] = useState(true);
  const [selectedTender, setSelectedTender] = useState<TenderContext | null>(null);
  const [availableBees, setAvailableBees] = useState<TenderSpecificBee[]>([]);
  const [filteredBees, setFilteredBees] = useState<TenderSpecificBee[]>([]);
  const [selectedBee, setSelectedBee] = useState<TenderSpecificBee | null>(null);
  const [assignmentDialogOpen, setAssignmentDialogOpen] = useState(false);
  
  // FILTER STATES
  const [locationFilter, setLocationFilter] = useState('');
  const [skillFilter, setSkillFilter] = useState('');
  const [verificationFilter, setVerificationFilter] = useState('');
  const [availabilityFilter, setAvailabilityFilter] = useState('available');

  // MOCK TENDER CONTEXTS (In real app, this comes from URL params or tender selection)
  const [availableTenders] = useState<TenderContext[]>([
    {
      tenderId: 'BID-20240115-JHB001',
      tenderTitle: 'Municipal Infrastructure Development - Phase 3',
      location: 'Johannesburg, Gauteng',
      deadline: '2024-01-20T16:00:00',
      requiredSkills: ['document_collection', 'site_visit', 'compliance_checking'],
      budget: 1500,
      urgency: 'high',
      taskType: 'document_collection',
      verificationRequired: 'standard'
    },
    {
      tenderId: 'BID-20240116-DOH002',
      tenderTitle: 'Medical Equipment Procurement - Eastern Cape',
      location: 'Port Elizabeth, Eastern Cape',
      deadline: '2024-01-18T12:00:00',
      requiredSkills: ['courier_delivery', 'document_submission'],
      budget: 850,
      urgency: 'urgent',
      taskType: 'courier_delivery',
      verificationRequired: 'premium'
    }
  ]);

  const getVerificationLevel = (level: string): number => {
    const levels = { 'basic': 1, 'standard': 2, 'premium': 3, 'elite': 4 };
    return levels[level as keyof typeof levels] || 1;
  };

  const calculateTenderAvailability = (bee: TenderSpecificBee, tender: TenderContext): boolean => {
    // Check if bee meets ALL tender requirements
    const hasRequiredSkills = tender.requiredSkills.some(skill => bee.specialties.includes(skill));
    const isInRange = bee.distanceFromTender <= 50; // 50km range
    const isAvailable = bee.status === 'available' && bee.currentTasks < bee.maxConcurrentTasks;
    const meetsVerification = getVerificationLevel(bee.verificationLevel) >= getVerificationLevel(tender.verificationRequired);

    return hasRequiredSkills && isInRange && isAvailable && meetsVerification;
  };

  const calculateMatchScore = (bee: TenderSpecificBee, tender: TenderContext): number => {
    let score = 0;

    // Skill match (40%)
    const skillMatch = tender.requiredSkills.filter(skill => bee.specialties.includes(skill)).length / tender.requiredSkills.length;
    score += skillMatch * 40;

    // Distance (25%)
    const distanceScore = Math.max(0, (50 - bee.distanceFromTender) / 50);
    score += distanceScore * 25;

    // Rating (20%)
    score += (bee.rating / 5) * 20;

    // Availability (15%)
    const availabilityScore = bee.status === 'available' ? 1 : 0;
    score += availabilityScore * 15;

    return Math.round(score);
  };

  const calculateTenderSpecificCost = (bee: TenderSpecificBee, tender: TenderContext): number => {
    let baseCost = 400;

    // Urgency multiplier
    const urgencyMultiplier = {
      'low': 1.0,
      'medium': 1.1,
      'high': 1.3,
      'urgent': 1.5
    }[tender.urgency];

    // Distance cost
    const distanceCost = bee.distanceFromTender * 5; // R5 per km

    // Verification premium
    const verificationPremium = {
      'basic': 0,
      'standard': 50,
      'premium': 100,
      'elite': 200
    }[bee.verificationLevel];

    return Math.round((baseCost + distanceCost + verificationPremium) * urgencyMultiplier);
  };

  const loadBeesForTender = async (tender: TenderContext) => {
    setLoading(true);

    // Simulate API call to get tender-specific bee availability
    setTimeout(() => {
      const mockBees: TenderSpecificBee[] = [
        {
          id: 'bee-001',
          beeId: 'BEE-JHB-001',
          fullName: 'Sarah Mthembu',
          rating: 4.8,
          completedTasks: 47,
          specialties: ['document_collection', 'site_visit', 'compliance_checking'],
          currentLocation: 'Sandton, Johannesburg',
          status: 'available',
          verificationLevel: 'premium',
          
          // TENDER-SPECIFIC CALCULATIONS
          availableForTender: true,
          tenderMatchScore: 95,
          estimatedCost: 450,
          estimatedDuration: '2-3 hours',
          availabilityWindow: 'Available now - 6 PM today',
          distanceFromTender: 12,
          
          meetsRequirements: {
            location: true,
            skills: true,
            verification: true,
            availability: true,
            budget: true
          },
          
          currentTasks: 1,
          maxConcurrentTasks: 3,
          nextAvailable: 'Available now'
        },
        {
          id: 'bee-002',
          beeId: 'BEE-CPT-002',
          fullName: 'Michael Johnson',
          rating: 4.9,
          completedTasks: 63,
          specialties: ['technical_evaluation', 'compliance_checking', 'form_completion'],
          currentLocation: 'Cape Town CBD',
          status: 'busy',
          verificationLevel: 'elite',
          
          availableForTender: false,
          tenderMatchScore: 85,
          estimatedCost: 650,
          estimatedDuration: '3-4 hours',
          availabilityWindow: 'Available tomorrow 9 AM',
          distanceFromTender: 450,
          
          meetsRequirements: {
            location: false, // Too far from Johannesburg
            skills: true,
            verification: true,
            availability: false, // Currently busy
            budget: true
          },
          
          currentTasks: 3,
          maxConcurrentTasks: 3,
          nextAvailable: 'Tomorrow 9:00 AM'
        },
        {
          id: 'bee-003',
          beeId: 'BEE-JHB-003',
          fullName: 'Priya Patel',
          rating: 4.7,
          completedTasks: 34,
          specialties: ['document_collection', 'courier_delivery', 'briefing_attendance'],
          currentLocation: 'Midrand, Johannesburg',
          status: 'available',
          verificationLevel: 'standard',
          
          availableForTender: true,
          tenderMatchScore: 88,
          estimatedCost: 400,
          estimatedDuration: '2 hours',
          availabilityWindow: 'Available now - 8 PM today',
          distanceFromTender: 8,
          
          meetsRequirements: {
            location: true,
            skills: true,
            verification: true,
            availability: true,
            budget: true
          },
          
          currentTasks: 0,
          maxConcurrentTasks: 2,
          nextAvailable: 'Available now'
        }
      ];

        // Filter bees based on tender requirements
        const tenderFilteredBees = mockBees.map(bee => ({
          ...bee,
          availableForTender: calculateTenderAvailability(bee, tender),
          tenderMatchScore: calculateMatchScore(bee, tender),
          estimatedCost: calculateTenderSpecificCost(bee, tender)
        }));

      setAvailableBees(tenderFilteredBees);
      setFilteredBees(tenderFilteredBees);
      setLoading(false);
    }, 1000);
  };

  useEffect(() => {
    // Auto-select tender from URL params or default to first
    if (availableTenders.length > 0) {
      const defaultTender = availableTenders[0];
      setSelectedTender(defaultTender);
      loadBeesForTender(defaultTender);
    } else {
      // Fallback if no tenders available
      setLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleTenderChange = (tender: TenderContext) => {
    setSelectedTender(tender);
    loadBeesForTender(tender);
  };

  const handleAssignBee = (bee: TenderSpecificBee) => {
    setSelectedBee(bee);
    setAssignmentDialogOpen(true);
  };

  const confirmAssignment = () => {
    if (selectedBee && selectedTender) {
      // In real app, this would create the assignment
      alert(`Bee ${selectedBee.fullName} assigned to tender ${selectedTender.tenderTitle}!`);
      setAssignmentDialogOpen(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'success';
      case 'busy': return 'warning';
      case 'offline': return 'error';
      default: return 'default';
    }
  };

  const getMatchScoreColor = (score: number) => {
    if (score >= 90) return 'success';
    if (score >= 75) return 'info';
    if (score >= 60) return 'warning';
    return 'error';
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 3 }}>
        <LinearProgress />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Loading available bees for your tender...
        </Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" fontWeight="bold" gutterBottom>
          🐝 Available Bees for Your Tender
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Find the perfect bee worker for your specific tender requirements
        </Typography>
      </Box>

      {/* Tender Selection */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            📋 Select Your Tender
          </Typography>
          <FormControl fullWidth>
            <InputLabel>Choose Tender</InputLabel>
            <Select
              value={selectedTender?.tenderId || ''}
              onChange={(e) => {
                const tender = availableTenders.find(t => t.tenderId === e.target.value);
                if (tender) handleTenderChange(tender);
              }}
            >
              {availableTenders.map((tender) => (
                <MenuItem key={tender.tenderId} value={tender.tenderId}>
                  {tender.tenderTitle} - {tender.location}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          
          {selectedTender && (
            <Box sx={{ mt: 2 }}>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    <LocationOn sx={{ fontSize: 16, mr: 1 }} />
                    Location: {selectedTender.location}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    <Schedule sx={{ fontSize: 16, mr: 1 }} />
                    Deadline: {new Date(selectedTender.deadline).toLocaleString()}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    <Assignment sx={{ fontSize: 16, mr: 1 }} />
                    Task: {selectedTender.taskType.replace('_', ' ')}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    <AttachMoney sx={{ fontSize: 16, mr: 1 }} />
                    Budget: R{selectedTender.budget}
                  </Typography>
                </Grid>
              </Grid>
              
              <Box sx={{ mt: 2 }}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Required Skills:
                </Typography>
                <Stack direction="row" spacing={1} flexWrap="wrap" gap={1}>
                  {selectedTender.requiredSkills.map((skill, index) => (
                    <Chip 
                      key={index}
                      label={skill.replace('_', ' ')}
                      size="small"
                      color="primary"
                      variant="outlined"
                    />
                  ))}
                </Stack>
              </Box>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Available Bees Summary */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body1">
          <strong>{filteredBees.filter(bee => bee.availableForTender).length}</strong> bees available for your tender out of {filteredBees.length} total bees in the area.
        </Typography>
      </Alert>

      {/* Bees List */}
      <Grid container spacing={3}>
        {filteredBees.map((bee) => (
          <Grid item xs={12} md={6} key={bee.id}>
            <Card sx={{ 
              border: bee.availableForTender ? '2px solid' : '1px solid',
              borderColor: bee.availableForTender ? 'success.main' : 'divider',
              opacity: bee.availableForTender ? 1 : 0.7
            }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <Avatar sx={{ width: 60, height: 60, bgcolor: 'warning.main' }}>
                    🐝
                  </Avatar>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="h6" fontWeight="bold">
                      {bee.fullName}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {bee.beeId} • {bee.currentLocation}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                      <Star sx={{ color: 'gold', fontSize: 16 }} />
                      <Typography variant="body2">{bee.rating}</Typography>
                      <Chip 
                        label={bee.status.toUpperCase()}
                        color={getStatusColor(bee.status) as any}
                        size="small"
                      />
                      <Chip 
                        label={`${bee.tenderMatchScore}% MATCH`}
                        color={getMatchScoreColor(bee.tenderMatchScore) as any}
                        size="small"
                      />
                    </Box>
                  </Box>
                </Box>

                {/* Tender-Specific Information */}
                <Box sx={{ mb: 2, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    📋 Tender Suitability:
                  </Typography>
                  <Grid container spacing={1}>
                    <Grid item xs={6}>
                      <Typography variant="body2">
                        Cost: <strong>R{bee.estimatedCost}</strong>
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2">
                        Duration: <strong>{bee.estimatedDuration}</strong>
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2">
                        Distance: <strong>{bee.distanceFromTender}km</strong>
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2">
                        Available: <strong>{bee.nextAvailable}</strong>
                      </Typography>
                    </Grid>
                  </Grid>
                </Box>

                {/* Requirements Check */}
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    ✅ Requirements Check:
                  </Typography>
                  <Stack direction="row" spacing={1} flexWrap="wrap" gap={1}>
                    <Chip 
                      label="Location"
                      color={bee.meetsRequirements.location ? 'success' : 'error'}
                      size="small"
                      icon={bee.meetsRequirements.location ? <CheckCircle /> : <Warning />}
                    />
                    <Chip 
                      label="Skills"
                      color={bee.meetsRequirements.skills ? 'success' : 'error'}
                      size="small"
                      icon={bee.meetsRequirements.skills ? <CheckCircle /> : <Warning />}
                    />
                    <Chip 
                      label="Verification"
                      color={bee.meetsRequirements.verification ? 'success' : 'error'}
                      size="small"
                      icon={bee.meetsRequirements.verification ? <CheckCircle /> : <Warning />}
                    />
                    <Chip 
                      label="Availability"
                      color={bee.meetsRequirements.availability ? 'success' : 'error'}
                      size="small"
                      icon={bee.meetsRequirements.availability ? <CheckCircle /> : <Warning />}
                    />
                  </Stack>
                </Box>

                {/* Action Buttons */}
                <Stack direction="row" spacing={1}>
                  <Button
                    variant={bee.availableForTender ? 'contained' : 'outlined'}
                    color={bee.availableForTender ? 'success' : 'default'}
                    startIcon={<Assignment />}
                    onClick={() => handleAssignBee(bee)}
                    disabled={!bee.availableForTender}
                    fullWidth
                  >
                    {bee.availableForTender ? 'Assign to Tender' : 'Not Available'}
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<Visibility />}
                  >
                    View Profile
                  </Button>
                </Stack>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Assignment Confirmation Dialog */}
      <Dialog open={assignmentDialogOpen} onClose={() => setAssignmentDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          🐝 Assign Bee to Tender
        </DialogTitle>
        <DialogContent>
          {selectedBee && selectedTender && (
            <Box>
              <Typography variant="body1" gutterBottom>
                Assign <strong>{selectedBee.fullName}</strong> to tender:
              </Typography>
              <Typography variant="h6" gutterBottom>
                {selectedTender.tenderTitle}
              </Typography>
              
              <Box sx={{ mt: 2, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Assignment Details:
                </Typography>
                <Typography variant="body2">
                  Estimated Cost: <strong>R{selectedBee.estimatedCost}</strong>
                </Typography>
                <Typography variant="body2">
                  Estimated Duration: <strong>{selectedBee.estimatedDuration}</strong>
                </Typography>
                <Typography variant="body2">
                  Match Score: <strong>{selectedBee.tenderMatchScore}%</strong>
                </Typography>
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAssignmentDialogOpen(false)}>
            Cancel
          </Button>
          <Button 
            variant="contained" 
            onClick={confirmAssignment}
            color="success"
          >
            Confirm Assignment
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
}
