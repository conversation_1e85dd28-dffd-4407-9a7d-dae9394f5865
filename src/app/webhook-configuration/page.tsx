'use client';

import React from 'react';
import { Container, <PERSON><PERSON><PERSON>, Card, CardContent, Grid, Button, Box, TextField, Chip } from '@mui/material';
import { Webhook, Add, Edit, Delete } from '@mui/icons-material';

const webhooks = [
  { name: "Tender Updates", url: "https://api.example.com/tenders", status: "Active" },
  { name: "Bid Notifications", url: "https://api.example.com/bids", status: "Active" },
  { name: "Payment Alerts", url: "https://api.example.com/payments", status: "Inactive" }
];

export default function WebhookConfigurationPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
            Webhook Configuration
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Manage webhook endpoints and event notifications
          </Typography>
        </Box>
        <Button variant="contained" startIcon={<Add />}>
          Add Webhook
        </Button>
      </Box>
      
      <Grid container spacing={3}>
        {webhooks.map((webhook, index) => (
          <Grid item xs={12} key={index}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Box>
                    <Typography variant="h6">{webhook.name}</Typography>
                    <Typography variant="body2" color="text.secondary">{webhook.url}</Typography>
                    <Chip 
                      label={webhook.status}
                      color={webhook.status === 'Active' ? 'success' : 'default'}
                      size="small"
                      sx={{ mt: 1 }}
                    />
                  </Box>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button size="small" startIcon={<Edit />}>Edit</Button>
                    <Button size="small" color="error" startIcon={<Delete />}>Delete</Button>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Container>
  );
}
