'use client';

import React from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  Chip,
  LinearProgress,
  Avatar,
  List,
  ListItem,
  ListItemText,
  Divider,
  Paper
} from '@mui/material';
import {
  Psychology,
  Person,
  TrendingUp,
  Star,
  Assessment,
  Insights,
  Group,
  Business,
  EmojiObjects,
  Speed
} from '@mui/icons-material';

const personalityTraits = [
  { trait: "Analytical Thinking", score: 92, description: "Exceptional ability to break down complex problems", color: "primary" },
  { trait: "Leadership", score: 78, description: "Strong natural leadership qualities", color: "success" },
  { trait: "Risk Tolerance", score: 65, description: "Moderate comfort with uncertainty", color: "warning" },
  { trait: "Communication", score: 88, description: "Excellent verbal and written communication", color: "info" },
  { trait: "Adaptability", score: 74, description: "Good flexibility in changing situations", color: "secondary" }
];

const cognitiveProfile = [
  { area: "Strategic Planning", level: "Expert", score: 94 },
  { area: "Decision Making", level: "Advanced", score: 87 },
  { area: "Problem Solving", level: "Expert", score: 91 },
  { area: "Creative Thinking", level: "Intermediate", score: 72 }
];

export default function PersonalityInsightsPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
          Personality Insights
        </Typography>
        <Typography variant="h6" color="text.secondary">
          Deep psychological profiling for enhanced business performance
        </Typography>
      </Box>

      {/* Personality Overview */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Grid container spacing={3} alignItems="center">
            <Grid item>
              <Avatar sx={{ width: 80, height: 80, bgcolor: 'primary.main' }}>
                <Psychology sx={{ fontSize: 40 }} />
              </Avatar>
            </Grid>
            <Grid item xs>
              <Typography variant="h5" fontWeight="bold" gutterBottom>
                Strategic Analytical Leader
              </Typography>
              <Typography variant="body1" color="text.secondary" paragraph>
                Your personality profile indicates a highly analytical individual with strong strategic thinking 
                capabilities and natural leadership tendencies. You excel in complex problem-solving scenarios 
                and demonstrate exceptional business acumen.
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                <Chip icon={<Star />} label="Top 5% Analytical" color="primary" />
                <Chip icon={<Group />} label="Natural Leader" color="success" />
                <Chip icon={<Business />} label="Strategic Thinker" color="info" />
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <Grid container spacing={3}>
        {/* Personality Traits */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Core Personality Traits
              </Typography>
              <Box sx={{ mt: 3 }}>
                {personalityTraits.map((trait, index) => (
                  <Box key={index} sx={{ mb: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="subtitle2" fontWeight="medium">
                        {trait.trait}
                      </Typography>
                      <Typography variant="body2" fontWeight="bold">
                        {trait.score}%
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={trait.score}
                      color={trait.color as any}
                      sx={{ height: 8, borderRadius: 4, mb: 1 }}
                    />
                    <Typography variant="caption" color="text.secondary">
                      {trait.description}
                    </Typography>
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Cognitive Profile */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Cognitive Profile
              </Typography>
              <List>
                {cognitiveProfile.map((item, index) => (
                  <React.Fragment key={index}>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemText
                        primary={item.area}
                        secondary={
                          <Box>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 1 }}>
                              <Chip 
                                label={item.level} 
                                size="small"
                                color={item.level === 'Expert' ? 'success' : item.level === 'Advanced' ? 'primary' : 'default'}
                              />
                              <Typography variant="caption" fontWeight="bold">
                                {item.score}%
                              </Typography>
                            </Box>
                            <LinearProgress
                              variant="determinate"
                              value={item.score}
                              sx={{ mt: 1, height: 4 }}
                              color={item.score > 90 ? 'success' : item.score > 80 ? 'primary' : 'warning'}
                            />
                          </Box>
                        }
                      />
                    </ListItem>
                    {index < cognitiveProfile.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Behavioral Patterns */}
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Behavioral Patterns
              </Typography>
              <List>
                <ListItem>
                  <ListItemText
                    primary="Decision Making Style"
                    secondary="Data-driven with intuitive validation. Prefers comprehensive analysis before major decisions."
                  />
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemText
                    primary="Communication Preference"
                    secondary="Direct and concise. Values clarity and efficiency in all communications."
                  />
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemText
                    primary="Stress Response"
                    secondary="Maintains composure under pressure. Tends to become more analytical when stressed."
                  />
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemText
                    primary="Learning Style"
                    secondary="Visual and kinesthetic learner. Prefers hands-on experience with theoretical backing."
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Business Strengths
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Paper sx={{ p: 2, bgcolor: 'success.50', border: '1px solid', borderColor: 'success.200' }}>
                    <Typography variant="subtitle2" color="success.main" gutterBottom>
                      Strategic Vision
                    </Typography>
                    <Typography variant="body2">
                      Exceptional ability to see long-term opportunities and develop comprehensive strategies.
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={12}>
                  <Paper sx={{ p: 2, bgcolor: 'primary.50', border: '1px solid', borderColor: 'primary.200' }}>
                    <Typography variant="subtitle2" color="primary.main" gutterBottom>
                      Analytical Excellence
                    </Typography>
                    <Typography variant="body2">
                      Outstanding capability to analyze complex data and extract actionable insights.
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={12}>
                  <Paper sx={{ p: 2, bgcolor: 'info.50', border: '1px solid', borderColor: 'info.200' }}>
                    <Typography variant="subtitle2" color="info.main" gutterBottom>
                      Leadership Potential
                    </Typography>
                    <Typography variant="body2">
                      Natural ability to inspire and guide teams toward achieving ambitious goals.
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Development Recommendations */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Personalized Development Recommendations
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center', p: 2 }}>
                <Avatar sx={{ bgcolor: 'warning.main', mx: 'auto', mb: 2 }}>
                  <EmojiObjects />
                </Avatar>
                <Typography variant="subtitle2" gutterBottom>
                  Enhance Creativity
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Consider creative thinking workshops to complement your analytical strengths.
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center', p: 2 }}>
                <Avatar sx={{ bgcolor: 'info.main', mx: 'auto', mb: 2 }}>
                  <Group />
                </Avatar>
                <Typography variant="subtitle2" gutterBottom>
                  Team Dynamics
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Focus on emotional intelligence to enhance team collaboration skills.
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center', p: 2 }}>
                <Avatar sx={{ bgcolor: 'success.main', mx: 'auto', mb: 2 }}>
                  <Speed />
                </Avatar>
                <Typography variant="subtitle2" gutterBottom>
                  Agile Decision Making
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Practice rapid decision-making in low-risk scenarios to build confidence.
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Box sx={{ mt: 4, textAlign: 'center' }}>
        <Typography variant="h6" gutterBottom>
          Personality Development Tools
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
          <Button variant="contained" startIcon={<Psychology />} href="/psychological-assessment">
            Full Assessment
          </Button>
          <Button variant="outlined" startIcon={<Insights />} href="/behavioral-analytics">
            Behavioral Analytics
          </Button>
          <Button variant="outlined" startIcon={<Assessment />} href="/cognitive-training">
            Cognitive Training
          </Button>
          <Button variant="outlined" startIcon={<Person />} href="/personality-coaching">
            Personality Coaching
          </Button>
        </Box>
      </Box>
    </Container>
  );
}
