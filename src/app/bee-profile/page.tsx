'use client';

import React, { useState } from 'react';
import {
  Box,
  Container,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  TextField,
  Switch,
  FormControlLabel,
  Chip,
  Avatar,
  Stack,
  Divider,
  Alert
} from '@mui/material';
import {
  Person,
  LocationOn,
  Work,
  Star,
  Phone,
  Email,
  Save,
  Edit
} from '@mui/icons-material';

export default function BeeProfilePage() {
  const [isEditing, setIsEditing] = useState(false);

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" fontWeight="bold">
          🐝 My Profile
        </Typography>
        <Button
          variant={isEditing ? "contained" : "outlined"}
          startIcon={isEditing ? <Save /> : <Edit />}
          onClick={() => setIsEditing(!isEditing)}
        >
          {isEditing ? 'Save Changes' : 'Edit Profile'}
        </Button>
      </Box>

      {/* Profile Content */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar
                sx={{ width: 120, height: 120, mx: 'auto', mb: 2 }}
                src="/avatars/bee-worker.jpg"
              >
                <Person sx={{ fontSize: 60 }} />
              </Avatar>
              <Typography variant="h6" gutterBottom>
                Sarah Mthembu
              </Typography>
              <Chip label="Senior Bee" color="primary" sx={{ mb: 1 }} />
              <Typography variant="body2" color="text.secondary">
                BEE-JHB-001
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Personal Information
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Full Name"
                    defaultValue="Sarah Mthembu"
                    disabled={!isEditing}
                    variant="outlined"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Email"
                    defaultValue="<EMAIL>"
                    disabled={!isEditing}
                    variant="outlined"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Phone"
                    defaultValue="+27 82 123 4567"
                    disabled={!isEditing}
                    variant="outlined"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Location"
                    defaultValue="Johannesburg, Gauteng"
                    disabled={!isEditing}
                    variant="outlined"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          <Card sx={{ mt: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Work Information
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Star color="primary" />
                    <Typography>Rating: 4.8/5.0</Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Work color="primary" />
                    <Typography>Tasks Completed: 47</Typography>
                  </Box>
                </Grid>
              </Grid>
              
              <Divider sx={{ my: 2 }} />
              
              <Typography variant="subtitle1" gutterBottom>
                Specialties
              </Typography>
              <Stack direction="row" spacing={1} flexWrap="wrap" gap={1}>
                <Chip label="Document Collection" variant="outlined" />
                <Chip label="Site Visits" variant="outlined" />
                <Chip label="Briefing Attendance" variant="outlined" />
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {isEditing && (
        <Alert severity="info" sx={{ mt: 3 }}>
          <Typography variant="body2">
            Make sure to save your changes before leaving this page.
          </Typography>
        </Alert>
      )}
    </Container>
  );
}
