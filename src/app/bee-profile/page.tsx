'use client';

import React, { useState } from 'react';
import {
  <PERSON>,
  Container,
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Grid,
  TextField,
  Switch,
  FormControlLabel,
  Chip,
  Avatar,
  Stack,
  Divider,
  Alert,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Checkbox,
  ListItemText,
  OutlinedInput
} from '@mui/material';
import {
  Person,
  LocationOn,
  Work,
  Star,
  Phone,
  Email,
  Save,
  Edit,
  Camera,
  Verified,
  Schedule,
  Security
} from '@mui/icons-material';

interface BeeProfile {
  personalInfo: {
    fullName: string;
    beeId: string;
    email: string;
    phone: string;
    avatar?: string;
    idNumber: string;
    address: string;
  };
  workInfo: {
    level: string;
    rating: number;
    totalTasks: number;
    completionRate: number;
    specialties: string[];
    certifications: string[];
    languages: string[];
  };
  availability: {
    isAvailable: boolean;
    workingHours: {
      start: string;
      end: string;
    };
    workingDays: string[];
    maxTasksPerDay: number;
    travelRadius: number;
  };
  location: {
    currentAddress: string;
    coordinates: {
      latitude: number;
      longitude: number;
    };
    preferredAreas: string[];
  };
  preferences: {
    notifications: {
      email: boolean;
      sms: boolean;
      push: boolean;
    };
    autoAcceptTasks: boolean;
    preferredTaskTypes: string[];
    locationPrivacy: {
      trackingEnabled: boolean;
      shareWithClients: boolean;
      shareWithQueenBee: boolean;
      trackingMode: 'tasks_only' | 'working_hours' | 'always' | 'emergency_only';
      accuracyLevel: 'exact' | 'approximate' | 'city_only';
      shareLocationHistory: boolean;
      dataRetentionDays: number;
    };
  };
}

export default function BeeProfilePage() {
  const [isEditing, setIsEditing] = useState(false);
  const [profile, setProfile] = useState<BeeProfile>({
    personalInfo: {
      fullName: 'Sarah Mthembu',
      beeId: 'BEE-JHB-001',
      email: '<EMAIL>',
      phone: '+27 82 123 4567',
      avatar: '/avatars/bee-worker.jpg',
      idNumber: '9001015678901',
      address: '123 Main Street, Sandton, Johannesburg, 2196'
    },
    workInfo: {
      level: 'Senior Bee',
      rating: 4.8,
      totalTasks: 47,
      completionRate: 96,
      specialties: ['Document Collection', 'Site Visits', 'Briefing Attendance'],
      certifications: ['First Aid', 'Driver\'s License', 'Security Clearance'],
      languages: ['English', 'Zulu', 'Afrikaans']
    },
    availability: {
      isAvailable: true,
      workingHours: {
        start: '08:00',
        end: '17:00'
      },
      workingDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
      maxTasksPerDay: 3,
      travelRadius: 25
    },
    location: {
      currentAddress: 'Sandton, Johannesburg',
      coordinates: {
        latitude: -26.1076,
        longitude: 28.0567
      },
      preferredAreas: ['Sandton', 'Rosebank', 'Midrand', 'Fourways']
    },
    preferences: {
      notifications: {
        email: true,
        sms: true,
        push: true
      },
      autoAcceptTasks: false,
      preferredTaskTypes: ['document_collection', 'site_visit'],
      locationPrivacy: {
        trackingEnabled: true,
        shareWithClients: true,
        shareWithQueenBee: true,
        trackingMode: 'tasks_only',
        accuracyLevel: 'approximate',
        shareLocationHistory: false,
        dataRetentionDays: 30
      }
    }
  });

  const availableSpecialties = [
    'Document Collection',
    'Site Visits',
    'Briefing Attendance',
    'Delivery Services',
    'Inspection Tasks',
    'Photography',
    'Measurements',
    'Data Collection'
  ];

  const availableTaskTypes = [
    { value: 'document_collection', label: 'Document Collection' },
    { value: 'site_visit', label: 'Site Visits' },
    { value: 'briefing_attendance', label: 'Briefing Attendance' },
    { value: 'delivery', label: 'Delivery Services' },
    { value: 'inspection', label: 'Inspection Tasks' }
  ];

  const daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

  const handleSave = () => {
    setIsEditing(false);
    // Here you would typically save to backend
    console.log('Saving profile:', profile);
  };

  const handleSpecialtyChange = (specialty: string) => {
    setProfile(prev => ({
      ...prev,
      workInfo: {
        ...prev.workInfo,
        specialties: prev.workInfo.specialties.includes(specialty)
          ? prev.workInfo.specialties.filter(s => s !== specialty)
          : [...prev.workInfo.specialties, specialty]
      }
    }));
  };

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" fontWeight="bold">
          🐝 My Profile
        </Typography>
        <Button
          variant={isEditing ? "contained" : "outlined"}
          startIcon={isEditing ? <Save /> : <Edit />}
          onClick={isEditing ? handleSave : () => setIsEditing(true)}
        >
          {isEditing ? 'Save Changes' : 'Edit Profile'}
        </Button>
      </Box>

      <Grid container spacing={3}>
        {/* Personal Information */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Person /> Personal Information
              </Typography>
              
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                <Avatar 
                  src={profile.personalInfo.avatar}
                  sx={{ width: 80, height: 80 }}
                >
                  🐝
                </Avatar>
                {isEditing && (
                  <Button startIcon={<Camera />} size="small">
                    Change Photo
                  </Button>
                )}
              </Box>

              <Stack spacing={2}>
                <TextField
                  label="Full Name"
                  value={profile.personalInfo.fullName}
                  onChange={(e) => setProfile(prev => ({
                    ...prev,
                    personalInfo: { ...prev.personalInfo, fullName: e.target.value }
                  }))}
                  disabled={!isEditing}
                  fullWidth
                />
                
                <TextField
                  label="Bee ID"
                  value={profile.personalInfo.beeId}
                  disabled
                  fullWidth
                  InputProps={{
                    endAdornment: <Verified color="success" />
                  }}
                />
                
                <TextField
                  label="Email"
                  value={profile.personalInfo.email}
                  onChange={(e) => setProfile(prev => ({
                    ...prev,
                    personalInfo: { ...prev.personalInfo, email: e.target.value }
                  }))}
                  disabled={!isEditing}
                  fullWidth
                />
                
                <TextField
                  label="Phone"
                  value={profile.personalInfo.phone}
                  onChange={(e) => setProfile(prev => ({
                    ...prev,
                    personalInfo: { ...prev.personalInfo, phone: e.target.value }
                  }))}
                  disabled={!isEditing}
                  fullWidth
                />
                
                <TextField
                  label="Address"
                  value={profile.personalInfo.address}
                  onChange={(e) => setProfile(prev => ({
                    ...prev,
                    personalInfo: { ...prev.personalInfo, address: e.target.value }
                  }))}
                  disabled={!isEditing}
                  multiline
                  rows={2}
                  fullWidth
                />
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Work Information */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Work /> Work Information
              </Typography>
              
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="warning.main">
                      {profile.workInfo.rating}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Rating
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="primary.main">
                      {profile.workInfo.completionRate}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Completion Rate
                    </Typography>
                  </Box>
                </Grid>
              </Grid>

              <Stack spacing={2}>
                <TextField
                  label="Level"
                  value={profile.workInfo.level}
                  disabled
                  fullWidth
                />
                
                <TextField
                  label="Total Tasks Completed"
                  value={profile.workInfo.totalTasks}
                  disabled
                  fullWidth
                />

                <Box>
                  <Typography variant="body2" gutterBottom>
                    Specialties
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {availableSpecialties.map((specialty) => (
                      <Chip
                        key={specialty}
                        label={specialty}
                        color={profile.workInfo.specialties.includes(specialty) ? 'primary' : 'default'}
                        onClick={isEditing ? () => handleSpecialtyChange(specialty) : undefined}
                        clickable={isEditing}
                        size="small"
                      />
                    ))}
                  </Box>
                </Box>

                <Box>
                  <Typography variant="body2" gutterBottom>
                    Certifications
                  </Typography>
                  <Stack direction="row" spacing={1} flexWrap="wrap">
                    {profile.workInfo.certifications.map((cert, index) => (
                      <Chip 
                        key={index}
                        label={cert}
                        color="success"
                        size="small"
                        icon={<Verified />}
                      />
                    ))}
                  </Stack>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Availability Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Schedule /> Availability
              </Typography>
              
              <Stack spacing={3}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={profile.availability.isAvailable}
                      onChange={(e) => setProfile(prev => ({
                        ...prev,
                        availability: { ...prev.availability, isAvailable: e.target.checked }
                      }))}
                      disabled={!isEditing}
                    />
                  }
                  label="Available for Tasks"
                />

                <Box>
                  <Typography variant="body2" gutterBottom>
                    Working Hours
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <TextField
                        label="Start Time"
                        type="time"
                        value={profile.availability.workingHours.start}
                        onChange={(e) => setProfile(prev => ({
                          ...prev,
                          availability: {
                            ...prev.availability,
                            workingHours: { ...prev.availability.workingHours, start: e.target.value }
                          }
                        }))}
                        disabled={!isEditing}
                        fullWidth
                        InputLabelProps={{ shrink: true }}
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <TextField
                        label="End Time"
                        type="time"
                        value={profile.availability.workingHours.end}
                        onChange={(e) => setProfile(prev => ({
                          ...prev,
                          availability: {
                            ...prev.availability,
                            workingHours: { ...prev.availability.workingHours, end: e.target.value }
                          }
                        }))}
                        disabled={!isEditing}
                        fullWidth
                        InputLabelProps={{ shrink: true }}
                      />
                    </Grid>
                  </Grid>
                </Box>

                <Box>
                  <Typography variant="body2" gutterBottom>
                    Working Days
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {daysOfWeek.map((day) => (
                      <Chip
                        key={day}
                        label={day.slice(0, 3)}
                        color={profile.availability.workingDays.includes(day) ? 'primary' : 'default'}
                        size="small"
                        clickable={isEditing}
                        onClick={isEditing ? () => {
                          setProfile(prev => ({
                            ...prev,
                            availability: {
                              ...prev.availability,
                              workingDays: prev.availability.workingDays.includes(day)
                                ? prev.availability.workingDays.filter(d => d !== day)
                                : [...prev.availability.workingDays, day]
                            }
                          }));
                        } : undefined}
                      />
                    ))}
                  </Box>
                </Box>

                <TextField
                  label="Max Tasks Per Day"
                  type="number"
                  value={profile.availability.maxTasksPerDay}
                  onChange={(e) => setProfile(prev => ({
                    ...prev,
                    availability: { ...prev.availability, maxTasksPerDay: parseInt(e.target.value) }
                  }))}
                  disabled={!isEditing}
                  fullWidth
                />

                <TextField
                  label="Travel Radius (km)"
                  type="number"
                  value={profile.availability.travelRadius}
                  onChange={(e) => setProfile(prev => ({
                    ...prev,
                    availability: { ...prev.availability, travelRadius: parseInt(e.target.value) }
                  }))}
                  disabled={!isEditing}
                  fullWidth
                />
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Preferences */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Security /> Preferences
              </Typography>
              
              <Stack spacing={3}>
                <Box>
                  <Typography variant="body2" gutterBottom>
                    Notifications
                  </Typography>
                  <Stack spacing={1}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={profile.preferences.notifications.email}
                          onChange={(e) => setProfile(prev => ({
                            ...prev,
                            preferences: {
                              ...prev.preferences,
                              notifications: { ...prev.preferences.notifications, email: e.target.checked }
                            }
                          }))}
                          disabled={!isEditing}
                        />
                      }
                      label="Email Notifications"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={profile.preferences.notifications.sms}
                          onChange={(e) => setProfile(prev => ({
                            ...prev,
                            preferences: {
                              ...prev.preferences,
                              notifications: { ...prev.preferences.notifications, sms: e.target.checked }
                            }
                          }))}
                          disabled={!isEditing}
                        />
                      }
                      label="SMS Notifications"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={profile.preferences.notifications.push}
                          onChange={(e) => setProfile(prev => ({
                            ...prev,
                            preferences: {
                              ...prev.preferences,
                              notifications: { ...prev.preferences.notifications, push: e.target.checked }
                            }
                          }))}
                          disabled={!isEditing}
                        />
                      }
                      label="Push Notifications"
                    />
                  </Stack>
                </Box>

                <FormControlLabel
                  control={
                    <Switch
                      checked={profile.preferences.autoAcceptTasks}
                      onChange={(e) => setProfile(prev => ({
                        ...prev,
                        preferences: { ...prev.preferences, autoAcceptTasks: e.target.checked }
                      }))}
                      disabled={!isEditing}
                    />
                  }
                  label="Auto-Accept Suitable Tasks"
                />

                <FormControl fullWidth disabled={!isEditing}>
                  <InputLabel>Preferred Task Types</InputLabel>
                  <Select
                    multiple
                    value={profile.preferences.preferredTaskTypes}
                    onChange={(e) => setProfile(prev => ({
                      ...prev,
                      preferences: { ...prev.preferences, preferredTaskTypes: e.target.value as string[] }
                    }))}
                    input={<OutlinedInput label="Preferred Task Types" />}
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {selected.map((value) => (
                          <Chip
                            key={value}
                            label={availableTaskTypes.find(t => t.value === value)?.label || value}
                            size="small"
                          />
                        ))}
                      </Box>
                    )}
                  >
                    {availableTaskTypes.map((taskType) => (
                      <MenuItem key={taskType.value} value={taskType.value}>
                        <Checkbox checked={profile.preferences.preferredTaskTypes.indexOf(taskType.value) > -1} />
                        <ListItemText primary={taskType.label} />
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Location Privacy Settings */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <LocationOn /> Location Privacy & Tracking
              </Typography>

              <Alert severity="info" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  Control how your location is shared and tracked. You can change these settings anytime.
                  Location data helps with task assignment and client transparency.
                </Typography>
              </Alert>

              <Stack spacing={3}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={profile.preferences.locationPrivacy.trackingEnabled}
                      onChange={(e) => setProfile(prev => ({
                        ...prev,
                        preferences: {
                          ...prev.preferences,
                          locationPrivacy: { ...prev.preferences.locationPrivacy, trackingEnabled: e.target.checked }
                        }
                      }))}
                      disabled={!isEditing}
                    />
                  }
                  label={
                    <Box>
                      <Typography variant="body1">Enable Location Tracking</Typography>
                      <Typography variant="body2" color="text.secondary">
                        Allow BidBeez to track your location for task assignment and navigation
                      </Typography>
                    </Box>
                  }
                />

                {profile.preferences.locationPrivacy.trackingEnabled && (
                  <>
                    <Box>
                      <Typography variant="body2" gutterBottom>
                        Tracking Mode
                      </Typography>
                      <FormControl fullWidth disabled={!isEditing}>
                        <Select
                          value={profile.preferences.locationPrivacy.trackingMode}
                          onChange={(e) => setProfile(prev => ({
                            ...prev,
                            preferences: {
                              ...prev.preferences,
                              locationPrivacy: { ...prev.preferences.locationPrivacy, trackingMode: e.target.value as any }
                            }
                          }))}
                        >
                          <MenuItem value="tasks_only">During Tasks Only</MenuItem>
                          <MenuItem value="working_hours">Working Hours Only</MenuItem>
                          <MenuItem value="always">Always (24/7)</MenuItem>
                          <MenuItem value="emergency_only">Emergency Only</MenuItem>
                        </Select>
                      </FormControl>
                      <Typography variant="caption" color="text.secondary">
                        Choose when your location can be tracked
                      </Typography>
                    </Box>

                    <Box>
                      <Typography variant="body2" gutterBottom>
                        Location Accuracy
                      </Typography>
                      <FormControl fullWidth disabled={!isEditing}>
                        <Select
                          value={profile.preferences.locationPrivacy.accuracyLevel}
                          onChange={(e) => setProfile(prev => ({
                            ...prev,
                            preferences: {
                              ...prev.preferences,
                              locationPrivacy: { ...prev.preferences.locationPrivacy, accuracyLevel: e.target.value as any }
                            }
                          }))}
                        >
                          <MenuItem value="exact">Exact Location (GPS)</MenuItem>
                          <MenuItem value="approximate">Approximate Area (±1km)</MenuItem>
                          <MenuItem value="city_only">City Level Only</MenuItem>
                        </Select>
                      </FormControl>
                      <Typography variant="caption" color="text.secondary">
                        Control how precise your location sharing is
                      </Typography>
                    </Box>

                    <Stack spacing={1}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={profile.preferences.locationPrivacy.shareWithClients}
                            onChange={(e) => setProfile(prev => ({
                              ...prev,
                              preferences: {
                                ...prev.preferences,
                                locationPrivacy: { ...prev.preferences.locationPrivacy, shareWithClients: e.target.checked }
                              }
                            }))}
                            disabled={!isEditing}
                          />
                        }
                        label={
                          <Box>
                            <Typography variant="body2">Share with Clients</Typography>
                            <Typography variant="caption" color="text.secondary">
                              Allow clients to see your location during assigned tasks
                            </Typography>
                          </Box>
                        }
                      />

                      <FormControlLabel
                        control={
                          <Switch
                            checked={profile.preferences.locationPrivacy.shareWithQueenBee}
                            onChange={(e) => setProfile(prev => ({
                              ...prev,
                              preferences: {
                                ...prev.preferences,
                                locationPrivacy: { ...prev.preferences.locationPrivacy, shareWithQueenBee: e.target.checked }
                              }
                            }))}
                            disabled={!isEditing}
                          />
                        }
                        label={
                          <Box>
                            <Typography variant="body2">Share with Queen Bee</Typography>
                            <Typography variant="caption" color="text.secondary">
                              Allow your Queen Bee to see your location for task coordination
                            </Typography>
                          </Box>
                        }
                      />

                      <FormControlLabel
                        control={
                          <Switch
                            checked={profile.preferences.locationPrivacy.shareLocationHistory}
                            onChange={(e) => setProfile(prev => ({
                              ...prev,
                              preferences: {
                                ...prev.preferences,
                                locationPrivacy: { ...prev.preferences.locationPrivacy, shareLocationHistory: e.target.checked }
                              }
                            }))}
                            disabled={!isEditing}
                          />
                        }
                        label={
                          <Box>
                            <Typography variant="body2">Share Location History</Typography>
                            <Typography variant="caption" color="text.secondary">
                              Allow access to your past location data for analytics
                            </Typography>
                          </Box>
                        }
                      />
                    </Stack>

                    <TextField
                      label="Data Retention (Days)"
                      type="number"
                      value={profile.preferences.locationPrivacy.dataRetentionDays}
                      onChange={(e) => setProfile(prev => ({
                        ...prev,
                        preferences: {
                          ...prev.preferences,
                          locationPrivacy: { ...prev.preferences.locationPrivacy, dataRetentionDays: parseInt(e.target.value) }
                        }
                      }))}
                      disabled={!isEditing}
                      fullWidth
                      helperText="How long to keep your location data (1-365 days)"
                      inputProps={{ min: 1, max: 365 }}
                    />
                  </>
                )}
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {isEditing && (
        <Alert severity="warning" sx={{ mt: 3 }}>
          <Typography variant="body2">
            <strong>Privacy Notice:</strong> Your location data is encrypted and only shared according to your preferences.
            You can change these settings anytime. Disabling location tracking may limit available task assignments.
          </Typography>
        </Alert>
      )}

      {isEditing && (
        <Alert severity="info" sx={{ mt: 2 }}>
          <Typography variant="body2">
            Make sure to save your changes before leaving this page. Some changes may require verification.
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {isEditing && (
        <Alert severity="info" sx={{ mt: 3 }}>
          <Typography variant="body2">
            Make sure to save your changes before leaving this page. Some changes may require verification.
          </Typography>
        </Alert>
      )}
    </Container>
  );
}
