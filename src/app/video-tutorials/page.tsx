'use client';

import React from 'react';
import { Container, Typography, Card, CardContent, Grid, <PERSON>ton, Box, Chip } from '@mui/material';
import { PlayArrow, VideoLibrary, Schedule } from '@mui/icons-material';

const tutorials = [
  { title: "Platform Overview", duration: "5:30", difficulty: "Beginner", views: 1250 },
  { title: "Creating Your First Bid", duration: "8:45", difficulty: "Beginner", views: 980 },
  { title: "Advanced AI Features", duration: "12:20", difficulty: "Advanced", views: 650 },
  { title: "Analytics Dashboard", duration: "7:15", difficulty: "Intermediate", views: 820 }
];

export default function VideoTutorialsPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
        Video Tutorials
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        Learn Bid<PERSON>z through comprehensive video tutorials
      </Typography>
      
      <Grid container spacing={3} sx={{ mt: 2 }}>
        {tutorials.map((tutorial, index) => (
          <Grid item xs={12} md={6} key={index}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <VideoLibrary sx={{ color: 'primary.main' }} />
                  <Box>
                    <Typography variant="h6">{tutorial.title}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {tutorial.duration} • {tutorial.views} views
                    </Typography>
                  </Box>
                </Box>
                <Chip 
                  label={tutorial.difficulty}
                  color={tutorial.difficulty === 'Beginner' ? 'success' : tutorial.difficulty === 'Intermediate' ? 'warning' : 'error'}
                  size="small"
                  sx={{ mb: 2 }}
                />
                <Box>
                  <Button variant="contained" startIcon={<PlayArrow />} size="small">
                    Watch Tutorial
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Container>
  );
}
