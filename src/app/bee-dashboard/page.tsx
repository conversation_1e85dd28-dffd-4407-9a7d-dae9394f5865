'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Container,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Avatar,
  Chip,
  LinearProgress,
  Alert,
  Stack,
  IconButton,
  Badge,
  Paper,
  Divider
} from '@mui/material';
import {
  Assignment,
  LocationOn,
  Star,
  AttachMoney,
  Notifications,
  Navigation,
  CheckCircle,
  Timer,
  Message,
  Settings,
  Refresh,
  Phone,
  Camera,
  Upload,
  Security
} from '@mui/icons-material';

interface BeeWorker {
  id: string;
  beeId: string;
  fullName: string;
  avatar?: string;
  rating: number;
  level: string;
  specialties: string[];
  currentLocation: {
    latitude: number;
    longitude: number;
    address: string;
  };
  status: 'available' | 'busy' | 'offline';
  currentTask?: {
    id: string;
    title: string;
    progress: number;
    deadline: string;
    location: string;
  };
}

interface TaskSummary {
  total: number;
  completed: number;
  inProgress: number;
  pending: number;
}

interface EarningsSummary {
  today: number;
  thisWeek: number;
  thisMonth: number;
  pending: number;
}

export default function BeeWorkerDashboard() {
  const [beeWor<PERSON>, setBee<PERSON>or<PERSON>] = useState<BeeWorker>({
    id: 'bee-001',
    beeId: 'BEE-JHB-001',
    fullName: 'Sarah Mthembu',
    avatar: '/avatars/bee-worker.jpg',
    rating: 4.8,
    level: 'Senior Bee',
    specialties: ['Document Collection', 'Site Visits', 'Briefing Attendance'],
    currentLocation: {
      latitude: -26.2041,
      longitude: 28.0473,
      address: 'Sandton, Johannesburg'
    },
    status: 'available',
    currentTask: {
      id: 'task-001',
      title: 'Municipal Tender Document Collection',
      progress: 65,
      deadline: '2024-01-15 16:00',
      location: 'City of Johannesburg Offices'
    }
  });

  const [taskSummary, setTaskSummary] = useState<TaskSummary>({
    total: 47,
    completed: 42,
    inProgress: 2,
    pending: 3
  });

  const [earnings, setEarnings] = useState<EarningsSummary>({
    today: 450,
    thisWeek: 2100,
    thisMonth: 8750,
    pending: 650
  });

  const [notifications, setNotifications] = useState(3);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'success';
      case 'busy': return 'warning';
      case 'offline': return 'error';
      default: return 'default';
    }
  };

  const handleTaskAction = (action: string) => {
    switch (action) {
      case 'navigate':
        window.location.href = '/bee-navigation';
        break;
      case 'update':
        window.location.href = '/bee-tasks';
        break;
      case 'complete':
        alert('Task completion flow would start here');
        break;
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Avatar 
            src={beeWorker.avatar} 
            sx={{ width: 60, height: 60, bgcolor: 'warning.main' }}
          >
            🐝
          </Avatar>
          <Box>
            <Typography variant="h4" fontWeight="bold">
              Welcome, {beeWorker.fullName}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Chip 
                label={beeWorker.level}
                color="warning"
                size="small"
              />
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <Star sx={{ color: 'gold', fontSize: 16 }} />
                <Typography variant="body2">{beeWorker.rating}</Typography>
              </Box>
              <Chip 
                label={beeWorker.status.toUpperCase()}
                color={getStatusColor(beeWorker.status) as any}
                size="small"
              />
            </Box>
          </Box>
        </Box>

        <Box sx={{ display: 'flex', gap: 1 }}>
          <IconButton href="/bee-profile">
            <Settings />
          </IconButton>
          <IconButton>
            <Badge badgeContent={notifications} color="error">
              <Notifications />
            </Badge>
          </IconButton>
          <IconButton>
            <Refresh />
          </IconButton>
        </Box>
      </Box>

      {/* Current Task Alert */}
      {beeWorker.currentTask && (
        <Alert 
          severity="info" 
          sx={{ mb: 3 }}
          action={
            <Button 
              color="inherit" 
              size="small"
              onClick={() => handleTaskAction('update')}
            >
              VIEW TASK
            </Button>
          }
        >
          <strong>Active Task:</strong> {beeWorker.currentTask.title} - {beeWorker.currentTask.progress}% complete
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Quick Stats */}
        <Grid item xs={12}>
          <Grid container spacing={2}>
            <Grid item xs={6} sm={3}>
              <Card>
                <CardContent sx={{ textAlign: 'center', py: 2 }}>
                  <Assignment sx={{ fontSize: 32, color: 'primary.main', mb: 1 }} />
                  <Typography variant="h4" fontWeight="bold">
                    {taskSummary.total}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Tasks
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Card>
                <CardContent sx={{ textAlign: 'center', py: 2 }}>
                  <CheckCircle sx={{ fontSize: 32, color: 'success.main', mb: 1 }} />
                  <Typography variant="h4" fontWeight="bold">
                    {taskSummary.completed}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Completed
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Card>
                <CardContent sx={{ textAlign: 'center', py: 2 }}>
                  <AttachMoney sx={{ fontSize: 32, color: 'warning.main', mb: 1 }} />
                  <Typography variant="h4" fontWeight="bold">
                    R{earnings.thisMonth.toLocaleString()}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    This Month
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Card>
                <CardContent sx={{ textAlign: 'center', py: 2 }}>
                  <Star sx={{ fontSize: 32, color: 'gold', mb: 1 }} />
                  <Typography variant="h4" fontWeight="bold">
                    {beeWorker.rating}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Rating
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Grid>

        {/* Current Task Details */}
        {beeWorker.currentTask && (
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  🎯 Current Task
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="h5" fontWeight="bold" gutterBottom>
                    {beeWorker.currentTask.title}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                    <LocationOn sx={{ color: 'text.secondary' }} />
                    <Typography variant="body2" color="text.secondary">
                      {beeWorker.currentTask.location}
                    </Typography>
                    <Timer sx={{ color: 'text.secondary' }} />
                    <Typography variant="body2" color="text.secondary">
                      Due: {new Date(beeWorker.currentTask.deadline).toLocaleString()}
                    </Typography>
                  </Box>
                  
                  <Box sx={{ mb: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">Progress</Typography>
                      <Typography variant="body2">{beeWorker.currentTask.progress}%</Typography>
                    </Box>
                    <LinearProgress 
                      variant="determinate" 
                      value={beeWorker.currentTask.progress}
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                  </Box>

                  <Stack direction="row" spacing={2}>
                    <Button
                      variant="contained"
                      startIcon={<Navigation />}
                      onClick={() => handleTaskAction('navigate')}
                      sx={{ flex: 1 }}
                    >
                      Navigate
                    </Button>
                    <Button
                      variant="outlined"
                      startIcon={<Camera />}
                      onClick={() => handleTaskAction('update')}
                      sx={{ flex: 1 }}
                    >
                      Update Progress
                    </Button>
                    <Button
                      variant="contained"
                      color="success"
                      startIcon={<CheckCircle />}
                      onClick={() => handleTaskAction('complete')}
                      sx={{ flex: 1 }}
                    >
                      Complete Task
                    </Button>
                  </Stack>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        )}

        {/* Quick Actions */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                🚀 Quick Actions
              </Typography>
              <Stack spacing={2}>
                <Button
                  variant="outlined"
                  startIcon={<Assignment />}
                  href="/bee-tasks"
                  fullWidth
                >
                  View All Tasks
                </Button>
                <Button
                  variant="contained"
                  color="secondary"
                  startIcon={<Assignment />}
                  href="/bee-tasks-integrated"
                  fullWidth
                >
                  📊 Complete Tasks (DB Connected)
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<Message />}
                  href="/bee-communication"
                  fullWidth
                >
                  Messages
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<AttachMoney />}
                  href="/bee-earnings"
                  fullWidth
                >
                  Earnings
                </Button>
                <Button
                  variant="contained"
                  color="success"
                  startIcon={<AttachMoney />}
                  href="/bee-earnings-integrated"
                  fullWidth
                >
                  💰 Complete Earnings (DB Connected)
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<LocationOn />}
                  href="/bee-navigation"
                  fullWidth
                >
                  Navigation
                </Button>
                <Button
                  variant="contained"
                  color="info"
                  startIcon={<Person />}
                  href="/bee-profile-integrated"
                  fullWidth
                >
                  👤 Complete Profile (DB Connected)
                </Button>
                <Button
                  variant="contained"
                  color="warning"
                  startIcon={<Security />}
                  href="/bee-test"
                  fullWidth
                >
                  🧪 Test Integration
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<Star />}
                  href="/bee-performance"
                  fullWidth
                >
                  Performance
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<Security />}
                  href="/bee-insurance"
                  fullWidth
                  sx={{
                    borderColor: 'success.main',
                    color: 'success.main',
                    '&:hover': {
                      borderColor: 'success.dark',
                      backgroundColor: 'success.light'
                    }
                  }}
                >
                  🛡️ Insurance & Protection
                </Button>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
}
