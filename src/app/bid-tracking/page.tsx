'use client';

import React, { useState, useEffect } from 'react';
import {
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Box,
  Chip,
  Button,
  Alert,
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  Tabs,
  Tab,
  Badge,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Switch,
  FormControlLabel,
  Stack,
  LinearProgress
} from '@mui/material';
import {
  Notifications,
  NotificationsActive,
  CheckCircle,
  Warning,
  Error,
  Info,
  TrendingUp,
  Assignment,
  People,
  Schedule,
  Settings,
  Refresh,
  FilterList,
  Search
} from '@mui/icons-material';

interface BidTracking {
  id: string;
  bidReference: string;
  organizationName: string;
  bidTitle: string;
  bidValue: number;
  submissionDate: string;
  closingDate: string;
  currentStatus: string;
  trackingStatus: string;
  lastChecked: string;
  updateCount: number;
  criticalUpdates: number;
}

interface BidUpdate {
  id: string;
  updateType: string;
  priority: string;
  title: string;
  description: string;
  detectedAt: string;
  sourceType: string;
  verificationStatus: string;
  newStatus?: string;
}

export default function BidTrackingPage() {
  const [trackedBids, setTrackedBids] = useState<BidTracking[]>([]);
  const [selectedBid, setSelectedBid] = useState<BidTracking | null>(null);
  const [bidUpdates, setBidUpdates] = useState<BidUpdate[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState(0);
  const [alertSettingsOpen, setAlertSettingsOpen] = useState(false);
  const [alertPreferences, setAlertPreferences] = useState({
    emailEnabled: true,
    smsEnabled: true,
    whatsappEnabled: true,
    pushEnabled: true,
    immediateAlerts: true,
    dailyDigest: true
  });

  useEffect(() => {
    loadTrackedBids();
    loadAlertPreferences();
  }, []);

  const loadTrackedBids = async () => {
    try {
      // Simulate API call
      const mockBids: BidTracking[] = [
        {
          id: '1',
          bidReference: 'GT/2025/001',
          organizationName: 'City of Johannesburg',
          bidTitle: 'Municipal Office Equipment Supply',
          bidValue: 2500000,
          submissionDate: '2025-01-10',
          closingDate: '2025-01-20',
          currentStatus: 'under_evaluation',
          trackingStatus: 'active',
          lastChecked: '2025-01-15T10:30:00Z',
          updateCount: 3,
          criticalUpdates: 1
        },
        {
          id: '2',
          bidReference: 'WC/2025/045',
          organizationName: 'Western Cape Government',
          bidTitle: 'Road Infrastructure Development',
          bidValue: 45000000,
          submissionDate: '2025-01-08',
          closingDate: '2025-01-25',
          currentStatus: 'awarded',
          trackingStatus: 'completed',
          lastChecked: '2025-01-15T09:15:00Z',
          updateCount: 7,
          criticalUpdates: 2
        },
        {
          id: '3',
          bidReference: 'GP/2025/012',
          organizationName: 'Gauteng Provincial Government',
          bidTitle: 'IT Security Services',
          bidValue: 8500000,
          submissionDate: '2025-01-12',
          closingDate: '2025-01-18',
          currentStatus: 'submitted',
          trackingStatus: 'active',
          lastChecked: '2025-01-15T11:00:00Z',
          updateCount: 1,
          criticalUpdates: 0
        }
      ];
      
      setTrackedBids(mockBids);
      setLoading(false);
    } catch (error) {
      console.error('Error loading tracked bids:', error);
      setLoading(false);
    }
  };

  const loadBidUpdates = async (bidId: string) => {
    try {
      // Simulate API call for bid updates
      const mockUpdates: BidUpdate[] = [
        {
          id: '1',
          updateType: 'award_announcement',
          priority: 'critical',
          title: 'Award Announcement Published',
          description: 'The bid has been awarded to the successful bidder. Award notice published on official portal.',
          detectedAt: '2025-01-15T08:30:00Z',
          sourceType: 'official_portal',
          verificationStatus: 'verified',
          newStatus: 'awarded'
        },
        {
          id: '2',
          updateType: 'addendum',
          priority: 'high',
          title: 'New Addendum Available',
          description: 'Addendum #2 has been published with clarifications on technical specifications.',
          detectedAt: '2025-01-14T14:20:00Z',
          sourceType: 'official_portal',
          verificationStatus: 'verified'
        },
        {
          id: '3',
          updateType: 'competitor_activity',
          priority: 'medium',
          title: 'Competitor Site Visit Observed',
          description: 'Bee worker observed competitor conducting site visit at project location.',
          detectedAt: '2025-01-13T16:45:00Z',
          sourceType: 'bee_worker',
          verificationStatus: 'verified'
        }
      ];
      
      setBidUpdates(mockUpdates);
    } catch (error) {
      console.error('Error loading bid updates:', error);
    }
  };

  const loadAlertPreferences = async () => {
    try {
      // Load user alert preferences
      // This would be an API call in real implementation
    } catch (error) {
      console.error('Error loading alert preferences:', error);
    }
  };

  const getStatusColor = (status: string) => {
    const colors = {
      'submitted': 'info',
      'under_evaluation': 'warning',
      'shortlisted': 'primary',
      'awarded': 'success',
      'rejected': 'error',
      'cancelled': 'default'
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getPriorityColor = (priority: string) => {
    const colors = {
      'critical': 'error',
      'high': 'warning',
      'medium': 'info',
      'low': 'default'
    };
    return colors[priority as keyof typeof colors] || 'default';
  };

  const getPriorityIcon = (priority: string) => {
    const icons = {
      'critical': <Error />,
      'high': <Warning />,
      'medium': <Info />,
      'low': <CheckCircle />
    };
    return icons[priority as keyof typeof icons] || <Info />;
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleBidSelect = (bid: BidTracking) => {
    setSelectedBid(bid);
    loadBidUpdates(bid.id);
  };

  const handleRefresh = async () => {
    setLoading(true);
    await loadTrackedBids();
  };

  const handleAlertPreferenceChange = (preference: string, value: boolean) => {
    setAlertPreferences(prev => ({
      ...prev,
      [preference]: value
    }));
  };

  const saveAlertPreferences = async () => {
    try {
      // Save alert preferences via API
      console.log('Saving alert preferences:', alertPreferences);
      setAlertSettingsOpen(false);
    } catch (error) {
      console.error('Error saving alert preferences:', error);
    }
  };

  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ py: 4 }}>
        <Box sx={{ textAlign: 'center' }}>
          <LinearProgress sx={{ mb: 2 }} />
          <Typography variant="h6" color="text.secondary">
            Loading bid tracking information...
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold' }}>
            📊 Bid Tracking & Intelligence
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Real-time monitoring of your bid submissions with multi-channel alerts
          </Typography>
        </Box>
        
        <Stack direction="row" spacing={2}>
          <Button
            variant="outlined"
            startIcon={<Settings />}
            onClick={() => setAlertSettingsOpen(true)}
          >
            Alert Settings
          </Button>
          <Button
            variant="contained"
            startIcon={<Refresh />}
            onClick={handleRefresh}
          >
            Refresh All
          </Button>
        </Stack>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Assignment sx={{ fontSize: 48, color: 'primary.main', mb: 1 }} />
              <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                {trackedBids.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Tracked Bids
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <NotificationsActive sx={{ fontSize: 48, color: 'warning.main', mb: 1 }} />
              <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                {trackedBids.reduce((sum, bid) => sum + bid.criticalUpdates, 0)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Critical Updates
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <TrendingUp sx={{ fontSize: 48, color: 'success.main', mb: 1 }} />
              <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                {trackedBids.filter(bid => bid.currentStatus === 'awarded').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Awards Won
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <People sx={{ fontSize: 48, color: 'info.main', mb: 1 }} />
              <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                {trackedBids.filter(bid => bid.trackingStatus === 'active').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Active Monitoring
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Main Content */}
      <Grid container spacing={3}>
        {/* Tracked Bids List */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  📋 Tracked Bids
                </Typography>
                <IconButton size="small">
                  <FilterList />
                </IconButton>
              </Box>
              
              <Stack spacing={2}>
                {trackedBids.map((bid) => (
                  <Card 
                    key={bid.id}
                    variant="outlined"
                    sx={{ 
                      cursor: 'pointer',
                      '&:hover': { boxShadow: 2 },
                      border: selectedBid?.id === bid.id ? '2px solid' : '1px solid',
                      borderColor: selectedBid?.id === bid.id ? 'primary.main' : 'divider'
                    }}
                    onClick={() => handleBidSelect(bid)}
                  >
                    <CardContent>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                        <Typography variant="h6" sx={{ fontWeight: 'bold', fontSize: '1rem' }}>
                          {bid.bidReference}
                        </Typography>
                        <Badge badgeContent={bid.criticalUpdates} color="error">
                          <Chip 
                            label={bid.currentStatus.replace('_', ' ').toUpperCase()}
                            color={getStatusColor(bid.currentStatus) as any}
                            size="small"
                          />
                        </Badge>
                      </Box>
                      
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        {bid.organizationName}
                      </Typography>
                      
                      <Typography variant="body2" sx={{ fontWeight: 'medium', mb: 1 }}>
                        {bid.bidTitle}
                      </Typography>
                      
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="body2" color="success.main" sx={{ fontWeight: 'bold' }}>
                          {formatCurrency(bid.bidValue)}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {bid.updateCount} updates
                        </Typography>
                      </Box>
                      
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 1 }}>
                        <Typography variant="caption" color="text.secondary">
                          Closes: {formatDate(bid.closingDate)}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Last checked: {formatDateTime(bid.lastChecked)}
                        </Typography>
                      </Box>
                    </CardContent>
                  </Card>
                ))}
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Bid Updates Timeline */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
                🔔 Recent Updates
                {selectedBid && (
                  <Typography variant="body2" color="text.secondary" component="span" sx={{ ml: 1 }}>
                    for {selectedBid.bidReference}
                  </Typography>
                )}
              </Typography>
              
              {selectedBid ? (
                <Timeline>
                  {bidUpdates.map((update, index) => (
                    <TimelineItem key={update.id}>
                      <TimelineSeparator>
                        <TimelineDot color={getPriorityColor(update.priority) as any}>
                          {getPriorityIcon(update.priority)}
                        </TimelineDot>
                        {index < bidUpdates.length - 1 && <TimelineConnector />}
                      </TimelineSeparator>
                      <TimelineContent>
                        <Box sx={{ mb: 2 }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                            <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                              {update.title}
                            </Typography>
                            <Chip 
                              label={update.priority.toUpperCase()}
                              color={getPriorityColor(update.priority) as any}
                              size="small"
                            />
                          </Box>
                          
                          <Typography variant="body2" color="text.secondary" gutterBottom>
                            {update.description}
                          </Typography>
                          
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Typography variant="caption" color="text.secondary">
                              {formatDateTime(update.detectedAt)}
                            </Typography>
                            <Chip 
                              label={update.sourceType.replace('_', ' ')}
                              variant="outlined"
                              size="small"
                            />
                          </Box>
                        </Box>
                      </TimelineContent>
                    </TimelineItem>
                  ))}
                </Timeline>
              ) : (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Typography variant="body2" color="text.secondary">
                    Select a bid to view its update timeline
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Alert Settings Dialog */}
      <Dialog open={alertSettingsOpen} onClose={() => setAlertSettingsOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>🔔 Alert Preferences</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Configure how you want to receive bid update notifications
          </Typography>
          
          <Stack spacing={2} sx={{ mt: 2 }}>
            <Typography variant="h6">Notification Channels</Typography>
            
            <FormControlLabel
              control={
                <Switch
                  checked={alertPreferences.emailEnabled}
                  onChange={(e) => handleAlertPreferenceChange('emailEnabled', e.target.checked)}
                />
              }
              label="Email Notifications"
            />
            
            <FormControlLabel
              control={
                <Switch
                  checked={alertPreferences.smsEnabled}
                  onChange={(e) => handleAlertPreferenceChange('smsEnabled', e.target.checked)}
                />
              }
              label="SMS Alerts"
            />
            
            <FormControlLabel
              control={
                <Switch
                  checked={alertPreferences.whatsappEnabled}
                  onChange={(e) => handleAlertPreferenceChange('whatsappEnabled', e.target.checked)}
                />
              }
              label="WhatsApp Messages"
            />
            
            <FormControlLabel
              control={
                <Switch
                  checked={alertPreferences.pushEnabled}
                  onChange={(e) => handleAlertPreferenceChange('pushEnabled', e.target.checked)}
                />
              }
              label="Push Notifications"
            />
            
            <Typography variant="h6" sx={{ mt: 3 }}>Frequency</Typography>
            
            <FormControlLabel
              control={
                <Switch
                  checked={alertPreferences.immediateAlerts}
                  onChange={(e) => handleAlertPreferenceChange('immediateAlerts', e.target.checked)}
                />
              }
              label="Immediate Alerts"
            />
            
            <FormControlLabel
              control={
                <Switch
                  checked={alertPreferences.dailyDigest}
                  onChange={(e) => handleAlertPreferenceChange('dailyDigest', e.target.checked)}
                />
              }
              label="Daily Digest"
            />
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAlertSettingsOpen(false)}>Cancel</Button>
          <Button variant="contained" onClick={saveAlertPreferences}>Save Preferences</Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
}
