'use client';

import React from 'react';
import { Container, Typography, Card, CardContent, Grid, Box, Avatar, Chip } from '@mui/material';
import { Insights, TrendingUp, Business, Assessment } from '@mui/icons-material';

export default function IndustryInsightsPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
        Industry Insights
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        Deep industry analysis and market intelligence
      </Typography>
      
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Construction Sector</Typography>
              <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                <Chip label="+18% Growth" color="success" size="small" />
                <Chip label="High Demand" color="primary" size="small" />
              </Box>
              <Typography variant="body2" color="text.secondary">
                Strong growth in municipal infrastructure projects. Increased budget allocation for 2024.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>IT Services</Typography>
              <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                <Chip label="+25% Growth" color="success" size="small" />
                <Chip label="Digital Transformation" color="info" size="small" />
              </Box>
              <Typography variant="body2" color="text.secondary">
                Rapid digitalization driving demand for IT services across all sectors.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Market Opportunities</Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Avatar sx={{ bgcolor: 'primary.main', mx: 'auto', mb: 1 }}>
                  <Business />
                </Avatar>
                <Typography variant="h6">Municipal</Typography>
                <Typography variant="h4" color="primary.main">R45M</Typography>
                <Typography variant="caption">Available Budget</Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Avatar sx={{ bgcolor: 'success.main', mx: 'auto', mb: 1 }}>
                  <TrendingUp />
                </Avatar>
                <Typography variant="h6">Private Sector</Typography>
                <Typography variant="h4" color="success.main">R32M</Typography>
                <Typography variant="caption">Growth Potential</Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Container>
  );
}
