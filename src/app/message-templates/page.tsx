'use client';

import React from 'react';
import { Container, Typo<PERSON>, Card, CardContent, Grid, Button, Box, Chip, TextField } from '@mui/material';
import { Message, Add, Edit, Delete } from '@mui/icons-material';

const templates = [
  { id: 1, name: "Bid Submission", category: "Formal", usage: 45 },
  { id: 2, name: "Follow-up Inquiry", category: "Casual", usage: 32 },
  { id: 3, name: "Proposal Request", category: "Professional", usage: 28 }
];

export default function MessageTemplatesPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
            Message Templates
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Manage automated communication templates
          </Typography>
        </Box>
        <Button variant="contained" startIcon={<Add />}>
          Create Template
        </Button>
      </Box>
      
      <Grid container spacing={3}>
        {templates.map((template) => (
          <Grid item xs={12} md={4} key={template.id}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>{template.name}</Typography>
                <Chip label={template.category} color="primary" size="small" sx={{ mb: 2 }} />
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Used {template.usage} times this month
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
                  <Button size="small" startIcon={<Edit />}>Edit</Button>
                  <Button size="small" color="error" startIcon={<Delete />}>Delete</Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
      
      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Template Editor</Typography>
          <TextField
            fullWidth
            multiline
            rows={6}
            placeholder="Enter your message template here..."
            variant="outlined"
            sx={{ mt: 2 }}
          />
          <Box sx={{ mt: 2, display: 'flex', gap: 2 }}>
            <Button variant="contained">Save Template</Button>
            <Button variant="outlined">Preview</Button>
          </Box>
        </CardContent>
      </Card>
    </Container>
  );
}
