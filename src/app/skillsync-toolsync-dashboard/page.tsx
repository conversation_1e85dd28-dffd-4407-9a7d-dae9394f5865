'use client';

import React, { useState } from 'react';
import {
  Box,
  Container,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Chip,
  Stack,
  Divider,
  Alert,
  LinearProgress,
  Avatar,
  IconButton,
  Tooltip,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemAvatar
} from '@mui/material';
import {
  School,
  Build,
  TrendingUp,
  Warning,
  CheckCircle,
  Star,
  Group,
  Assignment,
  Speed,
  Security,
  Sync,
  Analytics,
  Notifications,
  Launch,
  Settings
} from '@mui/icons-material';

interface SkillGap {
  skill: string;
  currentLevel: number;
  requiredLevel: number;
  impact: 'high' | 'medium' | 'low';
  tenderCount: number;
}

interface ToolGap {
  tool: string;
  category: string;
  cost: string;
  impact: 'high' | 'medium' | 'low';
  tenderCount: number;
}

export default function SkillSyncToolSyncDashboard() {
  const [activeTab, setActiveTab] = useState('overview');

  // Mock data
  const skillGaps: SkillGap[] = [
    { skill: 'Project Management Professional (PMP)', currentLevel: 0, requiredLevel: 5, impact: 'high', tenderCount: 15 },
    { skill: 'ISO 9001:2015 Lead Auditor', currentLevel: 2, requiredLevel: 5, impact: 'high', tenderCount: 12 },
    { skill: 'Advanced Excel & Data Analysis', currentLevel: 3, requiredLevel: 5, impact: 'medium', tenderCount: 8 },
    { skill: 'AutoCAD Certified Professional', currentLevel: 1, requiredLevel: 4, impact: 'medium', tenderCount: 6 }
  ];

  const toolGaps: ToolGap[] = [
    { tool: 'Microsoft Project Professional', category: 'Project Management', cost: 'R1,200/month', impact: 'high', tenderCount: 18 },
    { tool: 'AutoCAD Architecture', category: 'Design Software', cost: 'R2,800/month', impact: 'high', tenderCount: 14 },
    { tool: 'Primavera P6', category: 'Project Management', cost: 'R3,500/month', impact: 'medium', tenderCount: 9 },
    { tool: 'Adobe Creative Suite', category: 'Design Software', cost: 'R950/month', impact: 'medium', tenderCount: 7 }
  ];

  const stats = {
    skillsCompliance: 72,
    toolsCompliance: 68,
    tenderEligibility: 45,
    potentialRevenue: 'R12.4M',
    skillProviders: 2847,
    toolLicenses: 1247,
    activeIntegrations: 89,
    complianceScore: 78
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'info';
      default: return 'default';
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
          🎯🔧 SkillSync & ToolSync Integration Dashboard
        </Typography>
        <Typography variant="h6" color="text.secondary" gutterBottom>
          Unified compliance management for skills and tools across your tender portfolio
        </Typography>
        
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="body2">
            <strong>⚠️ COMPLIANCE ALERT:</strong> You're missing critical skills and tools for 55% of available tenders. 
            Complete your profile to unlock R12.4M in potential revenue!
          </Typography>
        </Alert>
      </Box>

      {/* Quick Stats */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <School color="primary" sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h4" color="primary" gutterBottom>
                {stats.skillsCompliance}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Skills Compliance
              </Typography>
              <LinearProgress 
                variant="determinate" 
                value={stats.skillsCompliance} 
                sx={{ mt: 1 }}
                color="primary"
              />
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Build color="secondary" sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h4" color="secondary" gutterBottom>
                {stats.toolsCompliance}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Tools Compliance
              </Typography>
              <LinearProgress 
                variant="determinate" 
                value={stats.toolsCompliance} 
                sx={{ mt: 1 }}
                color="secondary"
              />
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Assignment color="success" sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h4" color="success.main" gutterBottom>
                {stats.tenderEligibility}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Tender Eligibility
              </Typography>
              <LinearProgress 
                variant="determinate" 
                value={stats.tenderEligibility} 
                sx={{ mt: 1 }}
                color="success"
              />
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <TrendingUp color="info" sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h4" color="info.main" gutterBottom>
                {stats.potentialRevenue}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Potential Revenue
              </Typography>
              <Typography variant="caption" color="success.main">
                +R8.2M with full compliance
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Main Content */}
      <Grid container spacing={4}>
        {/* Critical Skill Gaps */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" gutterBottom>
                  🚨 Critical Skill Gaps
                </Typography>
                <Button 
                  variant="contained" 
                  size="small"
                  startIcon={<Launch />}
                  href="/skillsync"
                >
                  Open SkillSync
                </Button>
              </Box>
              
              <List>
                {skillGaps.map((gap, index) => (
                  <ListItem key={index} divider>
                    <ListItemIcon>
                      <Warning color={getImpactColor(gap.impact)} />
                    </ListItemIcon>
                    <ListItemText
                      primary={gap.skill}
                      secondary={
                        <Box>
                          <Typography variant="caption" display="block">
                            Current: {gap.currentLevel}/5 | Required: {gap.requiredLevel}/5
                          </Typography>
                          <Typography variant="caption" color="error">
                            Blocking {gap.tenderCount} tenders
                          </Typography>
                        </Box>
                      }
                    />
                    <Chip 
                      label={gap.impact.toUpperCase()} 
                      color={getImpactColor(gap.impact)}
                      size="small"
                    />
                  </ListItem>
                ))}
              </List>
              
              <Button 
                variant="outlined" 
                fullWidth 
                sx={{ mt: 2 }}
                startIcon={<School />}
              >
                Find Skill Providers
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* Critical Tool Gaps */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" gutterBottom>
                  🔧 Critical Tool Gaps
                </Typography>
                <Button 
                  variant="contained" 
                  size="small"
                  startIcon={<Launch />}
                  href="/toolsync"
                >
                  Open ToolSync
                </Button>
              </Box>
              
              <List>
                {toolGaps.map((gap, index) => (
                  <ListItem key={index} divider>
                    <ListItemIcon>
                      <Build color={getImpactColor(gap.impact)} />
                    </ListItemIcon>
                    <ListItemText
                      primary={gap.tool}
                      secondary={
                        <Box>
                          <Typography variant="caption" display="block">
                            {gap.category} | {gap.cost}
                          </Typography>
                          <Typography variant="caption" color="error">
                            Blocking {gap.tenderCount} tenders
                          </Typography>
                        </Box>
                      }
                    />
                    <Chip 
                      label={gap.impact.toUpperCase()} 
                      color={getImpactColor(gap.impact)}
                      size="small"
                    />
                  </ListItem>
                ))}
              </List>
              
              <Button 
                variant="outlined" 
                fullWidth 
                sx={{ mt: 2 }}
                startIcon={<Build />}
              >
                Get Tool Licenses
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* Integration Status */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                🔄 Platform Integration Status
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Sync color="primary" sx={{ fontSize: 32, mb: 1 }} />
                    <Typography variant="h6">SkillSync Connected</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {stats.skillProviders} verified providers
                    </Typography>
                    <Chip label="Active" color="success" size="small" sx={{ mt: 1 }} />
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={4}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Security color="secondary" sx={{ fontSize: 32, mb: 1 }} />
                    <Typography variant="h6">ToolSync Connected</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {stats.toolLicenses} managed licenses
                    </Typography>
                    <Chip label="Active" color="success" size="small" sx={{ mt: 1 }} />
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={4}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Analytics color="info" sx={{ fontSize: 32, mb: 1 }} />
                    <Typography variant="h6">AI Analytics</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {stats.complianceScore}% compliance score
                    </Typography>
                    <Chip label="Optimizing" color="warning" size="small" sx={{ mt: 1 }} />
                  </Paper>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Action Buttons */}
      <Box sx={{ mt: 4, display: 'flex', gap: 2, justifyContent: 'center' }}>
        <Button 
          variant="contained" 
          size="large"
          startIcon={<School />}
          href="/skillsync"
        >
          Manage Skills
        </Button>
        <Button 
          variant="contained" 
          size="large"
          startIcon={<Build />}
          href="/toolsync"
        >
          Manage Tools
        </Button>
        <Button 
          variant="outlined" 
          size="large"
          startIcon={<Settings />}
        >
          Integration Settings
        </Button>
      </Box>
    </Container>
  );
}
