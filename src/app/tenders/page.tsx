'use client';

import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  Alert,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Slider,
  Stack,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tabs,
  Tab,
  Paper
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Bookmark as BookmarkIcon,
  BookmarkBorder as BookmarkBorderIcon,
  Visibility as ViewIcon,
  Send as ApplyIcon,
  TrendingUp as TrendingUpIcon,
  AccessTime as TimeIcon,
  LocationOn as LocationIcon,
  Business as BusinessIcon,
  Assignment as AssignmentIcon,
  Star as StarIcon,
  Map as MapIcon,
  ViewList as ListIcon,
  ViewModule as GridIcon
} from '@mui/icons-material';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tenders-tabpanel-${index}`}
      aria-labelledby={`tenders-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
}

// Mock tender data
const mockTenders = [
  {
    id: 'tender-001',
    title: 'Municipal Infrastructure Development - Phase 3',
    reference: 'MUN-INF-2024-001',
    organization: 'City of Cape Town Municipality',
    category: 'Construction',
    value: 15600000,
    location: 'Cape Town, Western Cape',
    publishDate: '2024-01-15',
    closingDate: '2024-02-20',
    description: 'Development of municipal infrastructure including roads, water systems, and electrical networks for new residential areas.',
    requirements: ['B-BBEE Level 4+', 'CIDB Grade 7+', 'ISO 9001:2015'],
    matchScore: 92,
    isBookmarked: true,
    urgency: 'high',
    estimatedBidders: 15,
    complianceStatus: 'compliant'
  },
  {
    id: 'tender-002',
    title: 'Highway Construction and Maintenance',
    reference: 'DOT-HWY-2024-007',
    organization: 'Department of Transport - Gauteng',
    category: 'Infrastructure',
    value: 22400000,
    location: 'Johannesburg, Gauteng',
    publishDate: '2024-01-20',
    closingDate: '2024-03-15',
    description: 'Construction of new highway sections and maintenance of existing infrastructure on the N1 corridor.',
    requirements: ['CIDB Grade 8+', 'Health & Safety Certificate', 'Environmental Compliance'],
    matchScore: 87,
    isBookmarked: false,
    urgency: 'medium',
    estimatedBidders: 22,
    complianceStatus: 'partial'
  },
  {
    id: 'tender-003',
    title: 'School Building Renovation Program',
    reference: 'EDU-BUILD-2024-012',
    organization: 'Department of Basic Education - Eastern Cape',
    category: 'Education',
    value: 8900000,
    location: 'Port Elizabeth, Eastern Cape',
    publishDate: '2024-01-25',
    closingDate: '2024-02-28',
    description: 'Renovation and modernization of primary school buildings including classroom upgrades and safety improvements.',
    requirements: ['B-BBEE Level 6+', 'Construction Experience', 'Safety Certification'],
    matchScore: 95,
    isBookmarked: true,
    urgency: 'high',
    estimatedBidders: 8,
    complianceStatus: 'compliant'
  },
  {
    id: 'tender-004',
    title: 'Water Treatment Plant Upgrade',
    reference: 'WAT-TREAT-2024-003',
    organization: 'Rand Water',
    category: 'Water & Sanitation',
    value: 45200000,
    location: 'Vereeniging, Gauteng',
    publishDate: '2024-01-30',
    closingDate: '2024-04-10',
    description: 'Upgrade and modernization of water treatment facilities to increase capacity and improve efficiency.',
    requirements: ['Technical Expertise', 'Water Engineering Certification', 'Environmental Compliance'],
    matchScore: 78,
    isBookmarked: false,
    urgency: 'low',
    estimatedBidders: 12,
    complianceStatus: 'non-compliant'
  }
];

export default function TendersDiscovery() {
  const [activeTab, setActiveTab] = useState(0);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [valueRange, setValueRange] = useState<number[]>([0, 50000000]);
  const [selectedTender, setSelectedTender] = useState<string | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [bookmarkedTenders, setBookmarkedTenders] = useState<string[]>(
    mockTenders.filter(t => t.isBookmarked).map(t => t.id)
  );

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const toggleBookmark = (tenderId: string) => {
    setBookmarkedTenders(prev => 
      prev.includes(tenderId) 
        ? prev.filter(id => id !== tenderId)
        : [...prev, tenderId]
    );
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const getDaysUntilClosing = (closingDate: string) => {
    const today = new Date();
    const closing = new Date(closingDate);
    const diffTime = closing.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'info';
      default: return 'default';
    }
  };

  const getComplianceColor = (status: string) => {
    switch (status) {
      case 'compliant': return 'success';
      case 'partial': return 'warning';
      case 'non-compliant': return 'error';
      default: return 'default';
    }
  };

  const filteredTenders = mockTenders.filter(tender => {
    const matchesSearch = tender.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tender.organization.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || tender.category === selectedCategory;
    const matchesValue = tender.value >= valueRange[0] && tender.value <= valueRange[1];
    
    return matchesSearch && matchesCategory && matchesValue;
  });

  const getTabTenders = () => {
    switch (activeTab) {
      case 0: return filteredTenders; // All
      case 1: return filteredTenders.filter(t => bookmarkedTenders.includes(t.id)); // Bookmarked
      case 2: return filteredTenders.filter(t => t.matchScore >= 85); // High Match
      case 3: return filteredTenders.filter(t => getDaysUntilClosing(t.closingDate) <= 14); // Closing Soon
      default: return filteredTenders;
    }
  };

  const currentTenders = getTabTenders();

  const TenderCard = ({ tender }: { tender: typeof mockTenders[0] }) => (
    <Card 
      elevation={2}
      sx={{ 
        height: '100%',
        position: 'relative',
        '&:hover': { elevation: 4 }
      }}
    >
      <CardContent>
        {/* Header with bookmark */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Box sx={{ flex: 1 }}>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
              {tender.title}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              {tender.organization}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Ref: {tender.reference}
            </Typography>
          </Box>
          
          <IconButton 
            size="small" 
            onClick={() => toggleBookmark(tender.id)}
            sx={{ ml: 1 }}
          >
            {bookmarkedTenders.includes(tender.id) ? 
              <BookmarkIcon color="primary" /> : 
              <BookmarkBorderIcon />
            }
          </IconButton>
        </Box>

        {/* Key metrics */}
        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">Value</Typography>
            <Typography variant="h6" color="primary" sx={{ fontWeight: 600 }}>
              {formatCurrency(tender.value)}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">Match Score</Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <Typography variant="h6" color="success.main" sx={{ fontWeight: 600 }}>
                {tender.matchScore}%
              </Typography>
              <StarIcon fontSize="small" color="success" />
            </Box>
          </Grid>
        </Grid>

        {/* Status chips */}
        <Stack direction="row" spacing={1} sx={{ mb: 2, flexWrap: 'wrap', gap: 1 }}>
          <Chip 
            label={tender.category}
            size="small"
            variant="outlined"
          />
          <Chip 
            label={tender.urgency.toUpperCase()}
            size="small"
            color={getUrgencyColor(tender.urgency) as any}
          />
          <Chip 
            label={tender.complianceStatus.replace('-', ' ').toUpperCase()}
            size="small"
            color={getComplianceColor(tender.complianceStatus) as any}
            variant="outlined"
          />
        </Stack>

        {/* Location and timing */}
        <Box sx={{ mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mb: 0.5 }}>
            <LocationIcon fontSize="small" color="action" />
            <Typography variant="body2" color="text.secondary">
              {tender.location}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <TimeIcon fontSize="small" color="action" />
            <Typography variant="body2" color="text.secondary">
              Closes in {getDaysUntilClosing(tender.closingDate)} days
            </Typography>
          </Box>
        </Box>

        {/* Requirements preview */}
        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
          Key Requirements:
        </Typography>
        <Box sx={{ mb: 2 }}>
          {tender.requirements.slice(0, 2).map((req, index) => (
            <Chip 
              key={index}
              label={req}
              size="small"
              sx={{ mr: 0.5, mb: 0.5 }}
              variant="outlined"
            />
          ))}
          {tender.requirements.length > 2 && (
            <Chip 
              label={`+${tender.requirements.length - 2} more`}
              size="small"
              variant="outlined"
            />
          )}
        </Box>

        {/* Actions */}
        <Stack direction="row" spacing={1}>
          <Button
            variant="outlined"
            size="small"
            startIcon={<ViewIcon />}
            onClick={() => {
              setSelectedTender(tender.id);
              setDialogOpen(true);
            }}
            sx={{ flex: 1 }}
          >
            View Details
          </Button>
          <Button
            variant="contained"
            size="small"
            startIcon={<ApplyIcon />}
            onClick={() => alert(`Apply for ${tender.title}`)}
            sx={{ flex: 1 }}
            disabled={tender.complianceStatus === 'non-compliant'}
          >
            Apply
          </Button>
        </Stack>
      </CardContent>
    </Card>
  );

  return (
    <Box sx={{ p: 3, minHeight: '100vh', bgcolor: 'grey.50' }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main' }}>
            🔍 Tender Discovery
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Find and track tender opportunities with AI-powered matching
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<MapIcon />}
            onClick={() => alert('Map View')}
          >
            Map View
          </Button>
          <Button
            variant={viewMode === 'grid' ? 'contained' : 'outlined'}
            startIcon={<GridIcon />}
            onClick={() => setViewMode('grid')}
          >
            Grid
          </Button>
          <Button
            variant={viewMode === 'list' ? 'contained' : 'outlined'}
            startIcon={<ListIcon />}
            onClick={() => setViewMode('list')}
          >
            List
          </Button>
        </Box>
      </Box>

      {/* Search and Filters */}
      <Card elevation={2} sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Search tenders..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'action.active' }} />
                }}
              />
            </Grid>

            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Category</InputLabel>
                <Select
                  value={selectedCategory}
                  label="Category"
                  onChange={(e) => setSelectedCategory(e.target.value)}
                >
                  <MenuItem value="all">All Categories</MenuItem>
                  <MenuItem value="Construction">Construction</MenuItem>
                  <MenuItem value="Infrastructure">Infrastructure</MenuItem>
                  <MenuItem value="Education">Education</MenuItem>
                  <MenuItem value="Water & Sanitation">Water & Sanitation</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={5}>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                Value Range: {formatCurrency(valueRange[0])} - {formatCurrency(valueRange[1])}
              </Typography>
              <Slider
                value={valueRange}
                onChange={(e, newValue) => setValueRange(newValue as number[])}
                valueLabelDisplay="auto"
                min={0}
                max={50000000}
                step={1000000}
                valueLabelFormat={(value) => formatCurrency(value)}
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Summary Stats */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={6} md={3}>
          <Card elevation={1}>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Typography variant="h4" color="primary" sx={{ fontWeight: 600 }}>
                {currentTenders.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Matching Tenders
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={6} md={3}>
          <Card elevation={1}>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Typography variant="h4" color="success.main" sx={{ fontWeight: 600 }}>
                {formatCurrency(currentTenders.reduce((sum, t) => sum + t.value, 0))}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Value
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={6} md={3}>
          <Card elevation={1}>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Typography variant="h4" color="warning.main" sx={{ fontWeight: 600 }}>
                {currentTenders.filter(t => getDaysUntilClosing(t.closingDate) <= 7).length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Closing This Week
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={6} md={3}>
          <Card elevation={1}>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Typography variant="h4" color="info.main" sx={{ fontWeight: 600 }}>
                {Math.round(currentTenders.reduce((sum, t) => sum + t.matchScore, 0) / currentTenders.length) || 0}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Avg Match Score
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs and Content */}
      <Paper elevation={2}>
        <Tabs value={activeTab} onChange={handleTabChange} variant="fullWidth">
          <Tab label={`All (${filteredTenders.length})`} />
          <Tab label={`Bookmarked (${filteredTenders.filter(t => bookmarkedTenders.includes(t.id)).length})`} />
          <Tab label={`High Match (${filteredTenders.filter(t => t.matchScore >= 85).length})`} />
          <Tab label={`Closing Soon (${filteredTenders.filter(t => getDaysUntilClosing(t.closingDate) <= 14).length})`} />
        </Tabs>

        <TabPanel value={activeTab} index={activeTab}>
          <Box sx={{ p: 3 }}>
            {currentTenders.length > 0 ? (
              <Grid container spacing={3}>
                {currentTenders.map((tender) => (
                  <Grid item xs={12} md={6} lg={4} key={tender.id}>
                    <TenderCard tender={tender} />
                  </Grid>
                ))}
              </Grid>
            ) : (
              <Box sx={{ textAlign: 'center', py: 6 }}>
                <AssignmentIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary" sx={{ mb: 1 }}>
                  No tenders found
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Try adjusting your search criteria or filters
                </Typography>
              </Box>
            )}
          </Box>
        </TabPanel>
      </Paper>

      {/* Tender Details Dialog */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
        {selectedTender && (
          <>
            <DialogTitle>
              {mockTenders.find(t => t.id === selectedTender)?.title}
            </DialogTitle>
            <DialogContent>
              <Typography variant="body1">
                Tender details would be displayed here with full specifications, 
                requirements, and application instructions.
              </Typography>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setDialogOpen(false)}>Close</Button>
              <Button variant="contained" startIcon={<ApplyIcon />}>
                Apply for Tender
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Box>
  );
}