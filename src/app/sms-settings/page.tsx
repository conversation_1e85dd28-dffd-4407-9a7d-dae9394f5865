'use client';

import React from 'react';
import { Container, Typography, Card, CardContent, Grid, Box, Switch, FormControlLabel, TextField } from '@mui/material';
import { Sms, Settings, Notifications } from '@mui/icons-material';

export default function SmsSettingsPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
        SMS Settings
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        Configure SMS notifications and messaging settings
      </Typography>
      
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>SMS Notifications</Typography>
              <Box sx={{ mt: 2 }}>
                <FormControlLabel control={<Switch defaultChecked />} label="Enable SMS Alerts" />
                <FormControlLabel control={<Switch />} label="Urgent Notifications Only" />
                <FormControlLabel control={<Switch />} label="Deadline Reminders" />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>SMS Configuration</Typography>
              <Box sx={{ mt: 2 }}>
                <TextField fullWidth label="Primary Phone Number" variant="outlined" sx={{ mb: 2 }} />
                <TextField fullWidth label="Backup Phone Number" variant="outlined" />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
}
