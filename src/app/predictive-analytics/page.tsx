'use client';

import React from 'react';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Chip,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Avatar
} from '@mui/material';
import {
  Timeline,
  TrendingUp,
  Assessment,
  SmartToy,
  Insights,
  Speed,
  GpsFixed,
  Psychology
} from '@mui/icons-material';

const predictions = [
  { tender: "Municipal Infrastructure", probability: 89, confidence: 94, value: "R2.4M", deadline: "2024-01-15" },
  { tender: "IT Equipment Procurement", probability: 76, confidence: 87, value: "R890K", deadline: "2024-01-20" },
  { tender: "Construction Services", probability: 92, confidence: 96, value: "R5.2M", deadline: "2024-01-12" }
];

export default function PredictiveAnalyticsPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
          Predictive Analytics
        </Typography>
        <Typography variant="h6" color="text.secondary">
          AI-powered predictions for optimal bidding strategies
        </Typography>
      </Box>

      {/* Key Metrics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="primary">
                    94.7%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Prediction Accuracy
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <GpsFixed />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="success.main">
                    15
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    AI Models Active
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <SmartToy />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="warning.main">
                    87%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Win Rate Improvement
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'warning.main' }}>
                  <TrendingUp />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="info.main">
                    2.3s
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Avg Analysis Time
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'info.main' }}>
                  <Speed />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Predictions Table */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Current Predictions
          </Typography>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Tender</TableCell>
                  <TableCell>Win Probability</TableCell>
                  <TableCell>Confidence Level</TableCell>
                  <TableCell>Value</TableCell>
                  <TableCell>Deadline</TableCell>
                  <TableCell>Recommendation</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {predictions.map((pred, index) => (
                  <TableRow key={index}>
                    <TableCell>{pred.tender}</TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <LinearProgress
                          variant="determinate"
                          value={pred.probability}
                          sx={{ width: 60, height: 6 }}
                          color={pred.probability > 85 ? 'success' : pred.probability > 70 ? 'warning' : 'error'}
                        />
                        <Typography variant="body2">{pred.probability}%</Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={`${pred.confidence}%`}
                        color={pred.confidence > 90 ? 'success' : 'primary'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell fontWeight="bold">{pred.value}</TableCell>
                    <TableCell>{pred.deadline}</TableCell>
                    <TableCell>
                      <Chip 
                        label={pred.probability > 85 ? 'High Priority' : pred.probability > 70 ? 'Medium Priority' : 'Low Priority'}
                        color={pred.probability > 85 ? 'success' : pred.probability > 70 ? 'warning' : 'default'}
                        size="small"
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* AI Insights */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Market Trend Analysis
              </Typography>
              <Box sx={{ 
                height: 200, 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                bgcolor: 'grey.50',
                borderRadius: 1
              }}>
                <Box sx={{ textAlign: 'center' }}>
                  <Timeline sx={{ fontSize: 48, color: 'grey.400', mb: 1 }} />
                  <Typography variant="body2" color="text.secondary">
                    Market trend visualization would be displayed here
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                AI Recommendations
              </Typography>
              <Box sx={{ mt: 2 }}>
                <Box sx={{ p: 2, bgcolor: 'success.50', borderRadius: 1, mb: 2 }}>
                  <Typography variant="subtitle2" color="success.main" gutterBottom>
                    High Confidence Prediction
                  </Typography>
                  <Typography variant="body2">
                    Municipal Infrastructure tender shows 89% win probability with 94% confidence.
                  </Typography>
                </Box>
                <Box sx={{ p: 2, bgcolor: 'warning.50', borderRadius: 1, mb: 2 }}>
                  <Typography variant="subtitle2" color="warning.main" gutterBottom>
                    Optimization Opportunity
                  </Typography>
                  <Typography variant="body2">
                    Consider adjusting pricing strategy for IT Equipment tender to increase win probability.
                  </Typography>
                </Box>
                <Box sx={{ p: 2, bgcolor: 'info.50', borderRadius: 1 }}>
                  <Typography variant="subtitle2" color="info.main" gutterBottom>
                    Market Intelligence
                  </Typography>
                  <Typography variant="body2">
                    Construction sector showing increased activity. Consider expanding capacity.
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Quick Actions */}
      <Box sx={{ mt: 4, textAlign: 'center' }}>
        <Typography variant="h6" gutterBottom>
          Predictive Analytics Tools
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
          <Button variant="contained" startIcon={<SmartToy />} href="/ai-engine">
            AI Engine
          </Button>
          <Button variant="outlined" startIcon={<Assessment />} href="/risk-assessment">
            Risk Assessment
          </Button>
          <Button variant="outlined" startIcon={<Insights />} href="/market-intelligence">
            Market Intelligence
          </Button>
          <Button variant="outlined" startIcon={<Psychology />} href="/behavioral-analytics">
            Behavioral Analytics
          </Button>
        </Box>
      </Box>
    </Container>
  );
}
