'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  LinearProgress,
  Paper,
  Alert,
  Stack,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Switch,
  FormControlLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  Star as StarIcon,
  TrendingUp as TrendingUpIcon,
  Group as GroupIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  CreditCard as CreditCardIcon,
  Receipt as ReceiptIcon,
  Settings as SettingsIcon,
  Upgrade as UpgradeIcon,
  Analytics as AnalyticsIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';

import { UserSubscription, SubscriptionPlan, SubscriptionTier } from '../../types/subscription';
import SubscriptionService from '../../services/SubscriptionService';

export default function SubscriptionPage() {
  const [subscription, setSubscription] = useState<UserSubscription | null>(null);
  const [currentPlan, setCurrentPlan] = useState<SubscriptionPlan | null>(null);
  const [availablePlans, setAvailablePlans] = useState<SubscriptionPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [showUpgradeDialog, setShowUpgradeDialog] = useState(false);
  const [selectedUpgradePlan, setSelectedUpgradePlan] = useState<string>('');

  const subscriptionService = SubscriptionService.getInstance();

  useEffect(() => {
    loadSubscriptionData();
  }, []);

  const loadSubscriptionData = async () => {
    try {
      setLoading(true);
      
      // Mock user ID - in real app, get from auth context
      const userId = 'user-123';
      
      // Load user subscription
      const userSub = await subscriptionService.getUserSubscription(userId);
      setSubscription(userSub);
      
      // Load current plan details
      if (userSub) {
        const plan = await subscriptionService.getSubscriptionPlan(userSub.planId);
        setCurrentPlan(plan);
      }
      
      // Load all available plans
      const plans = await subscriptionService.getSubscriptionPlans();
      setAvailablePlans(plans);
      
    } catch (error) {
      console.error('Error loading subscription data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpgrade = async (planId: string) => {
    if (!subscription) return;
    
    try {
      setLoading(true);
      
      // In real implementation, this would handle payment and plan change
      await subscriptionService.updateSubscription(subscription.userId, {
        planId,
        updatedAt: new Date().toISOString()
      });
      
      setShowUpgradeDialog(false);
      await loadSubscriptionData();
      
    } catch (error) {
      console.error('Error upgrading subscription:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const getUsagePercentage = (used: number, limit: number) => {
    if (limit === 999999) return 0; // Unlimited
    return Math.min((used / limit) * 100, 100);
  };

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'error';
    if (percentage >= 70) return 'warning';
    return 'success';
  };

  const getNextBillingDate = () => {
    if (!subscription) return '';
    return new Date(subscription.billing.nextBillingDate).toLocaleDateString('en-ZA');
  };

  const isTrialActive = () => {
    if (!subscription || !subscription.trialEnd) return false;
    return new Date(subscription.trialEnd) > new Date();
  };

  const getTrialDaysRemaining = () => {
    if (!subscription || !subscription.trialEnd) return 0;
    const trialEnd = new Date(subscription.trialEnd);
    const now = new Date();
    const diffTime = trialEnd.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  };

  if (loading) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <LinearProgress sx={{ mb: 2 }} />
        <Typography>Loading subscription details...</Typography>
      </Box>
    );
  }

  if (!subscription || !currentPlan) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Alert severity="warning">
          <Typography variant="body2">
            No active subscription found. Please contact support or start a new subscription.
          </Typography>
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3, minHeight: '100vh', bgcolor: 'grey.50' }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main', mb: 1 }}>
          💳 Subscription Management
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage your BidBeez subscription, usage, and billing
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Current Plan Overview */}
        <Grid item xs={12} md={8}>
          <Card elevation={2}>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 3 }}>
                <Box>
                  <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
                    {currentPlan.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {currentPlan.description}
                  </Typography>
                  <Stack direction="row" spacing={1}>
                    <Chip 
                      label={subscription.status.toUpperCase()} 
                      color={subscription.status === 'active' ? 'success' : 'warning'}
                    />
                    <Chip 
                      label={subscription.billingCycle.toUpperCase()} 
                      variant="outlined"
                    />
                    {isTrialActive() && (
                      <Chip 
                        label={`${getTrialDaysRemaining()} days trial left`} 
                        color="info"
                      />
                    )}
                  </Stack>
                </Box>
                <Box sx={{ textAlign: 'right' }}>
                  <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main' }}>
                    {formatCurrency(currentPlan.price.monthly)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    per month
                  </Typography>
                </Box>
              </Box>

              {/* Usage Metrics */}
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                Current Usage
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">Monthly Bids</Typography>
                      <Typography variant="body2">
                        {subscription.usage.monthlyBids} / {currentPlan.limits.monthlyBids === 999999 ? '∞' : currentPlan.limits.monthlyBids}
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={getUsagePercentage(subscription.usage.monthlyBids, currentPlan.limits.monthlyBids)}
                      color={getUsageColor(getUsagePercentage(subscription.usage.monthlyBids, currentPlan.limits.monthlyBids)) as any}
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">Team Members</Typography>
                      <Typography variant="body2">
                        {subscription.usage.teamMembers} / {currentPlan.limits.teamMembers === 999999 ? '∞' : currentPlan.limits.teamMembers}
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={getUsagePercentage(subscription.usage.teamMembers, currentPlan.limits.teamMembers)}
                      color={getUsageColor(getUsagePercentage(subscription.usage.teamMembers, currentPlan.limits.teamMembers)) as any}
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">Storage Used</Typography>
                      <Typography variant="body2">
                        {subscription.usage.storageGB}GB / {currentPlan.limits.storageGB === 999999 ? '∞' : `${currentPlan.limits.storageGB}GB`}
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={getUsagePercentage(subscription.usage.storageGB, currentPlan.limits.storageGB)}
                      color={getUsageColor(getUsagePercentage(subscription.usage.storageGB, currentPlan.limits.storageGB)) as any}
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">API Calls</Typography>
                      <Typography variant="body2">
                        {subscription.usage.apiCalls} / {currentPlan.limits.apiCalls === 999999 ? '∞' : currentPlan.limits.apiCalls}
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={getUsagePercentage(subscription.usage.apiCalls, currentPlan.limits.apiCalls)}
                      color={getUsageColor(getUsagePercentage(subscription.usage.apiCalls, currentPlan.limits.apiCalls)) as any}
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12} md={4}>
          <Stack spacing={2}>
            {/* Billing Info */}
            <Card elevation={2}>
              <CardContent>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                  Billing Information
                </Typography>
                <List dense>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon>
                      <ScheduleIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Next Billing Date"
                      secondary={getNextBillingDate()}
                    />
                  </ListItem>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon>
                      <CreditCardIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Payment Method"
                      secondary="•••• •••• •••• 1234"
                    />
                  </ListItem>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon>
                      <ReceiptIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Total Paid"
                      secondary={formatCurrency(subscription.billing.totalPaid)}
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>

            {/* Actions */}
            <Card elevation={2}>
              <CardContent>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                  Quick Actions
                </Typography>
                <Stack spacing={2}>
                  <Button
                    variant="contained"
                    startIcon={<UpgradeIcon />}
                    onClick={() => setShowUpgradeDialog(true)}
                    fullWidth
                  >
                    Upgrade Plan
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<SettingsIcon />}
                    fullWidth
                  >
                    Billing Settings
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<ReceiptIcon />}
                    fullWidth
                  >
                    Download Invoices
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<AnalyticsIcon />}
                    fullWidth
                  >
                    Usage Analytics
                  </Button>
                </Stack>
              </CardContent>
            </Card>

            {/* Tier Toggle */}
            <Card elevation={2}>
              <CardContent>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                  Workspace Mode
                </Typography>
                <FormControlLabel
                  control={<Switch defaultChecked />}
                  label="Team Collaboration Mode"
                />
                <Typography variant="caption" color="text.secondary" display="block" sx={{ mt: 1 }}>
                  Switch between Individual and Team workspace modes
                </Typography>
              </CardContent>
            </Card>
          </Stack>
        </Grid>

        {/* Plan Features */}
        <Grid item xs={12}>
          <Card elevation={2}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
                Your Plan Features
              </Typography>
              <Grid container spacing={2}>
                {currentPlan.features.map((feature, index) => (
                  <Grid item xs={12} sm={6} md={4} key={index}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <CheckIcon fontSize="small" color="success" />
                      <Typography variant="body2">
                        {feature.name}
                        {feature.limit && ` (${feature.limit})`}
                      </Typography>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Upgrade Dialog */}
      <Dialog 
        open={showUpgradeDialog} 
        onClose={() => setShowUpgradeDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Typography variant="h6">Upgrade Your Plan</Typography>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Choose a plan that better fits your growing needs
          </Typography>
          <Grid container spacing={2}>
            {availablePlans
              .filter(plan => plan.tier !== currentPlan?.tier)
              .map((plan) => (
                <Grid item xs={12} sm={6} key={plan.id}>
                  <Card 
                    variant="outlined"
                    sx={{ 
                      cursor: 'pointer',
                      border: selectedUpgradePlan === plan.id ? 2 : 1,
                      borderColor: selectedUpgradePlan === plan.id ? 'primary.main' : 'divider'
                    }}
                    onClick={() => setSelectedUpgradePlan(plan.id)}
                  >
                    <CardContent>
                      <Typography variant="h6" sx={{ fontWeight: 600 }}>
                        {plan.name}
                      </Typography>
                      <Typography variant="h4" color="primary.main" sx={{ fontWeight: 600, my: 1 }}>
                        {formatCurrency(plan.price.monthly)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        per month
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowUpgradeDialog(false)}>
            Cancel
          </Button>
          <Button 
            variant="contained" 
            onClick={() => handleUpgrade(selectedUpgradePlan)}
            disabled={!selectedUpgradePlan}
          >
            Upgrade Now
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
