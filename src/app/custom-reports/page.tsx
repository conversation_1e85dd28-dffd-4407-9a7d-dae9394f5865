'use client';

import React from 'react';
import { Container, Typography, Card, CardContent, Grid, Button, Box, Chip } from '@mui/material';
import { Assessment, Add, Download, Schedule } from '@mui/icons-material';

const reports = [
  { name: "Monthly Performance", type: "Automated", frequency: "Monthly", lastRun: "2024-01-01" },
  { name: "Bid Success Analysis", type: "Custom", frequency: "Weekly", lastRun: "2024-01-08" },
  { name: "Revenue Forecast", type: "Automated", frequency: "Quarterly", lastRun: "2024-01-01" }
];

export default function CustomReportsPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
            Custom Reports
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Create and manage custom business reports
          </Typography>
        </Box>
        <Button variant="contained" startIcon={<Add />}>
          Create Report
        </Button>
      </Box>
      
      <Grid container spacing={3}>
        {reports.map((report, index) => (
          <Grid item xs={12} md={4} key={index}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>{report.name}</Typography>
                <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                  <Chip label={report.type} color="primary" size="small" />
                  <Chip label={report.frequency} color="info" size="small" />
                </Box>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Last run: {report.lastRun}
                </Typography>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button size="small" startIcon={<Download />}>Download</Button>
                  <Button size="small" startIcon={<Schedule />}>Schedule</Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Container>
  );
}
