'use client';

import React, { useState } from 'react';
import { 
  Con<PERSON><PERSON>, 
  Box, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  Alert,
  Button,
  Chip,
  Switch,
  FormControlLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  LinearProgress
} from '@mui/material';
import { 
  AutoAwesome, 
  PlayArrow, 
  Stop, 
  Settings,
  Refresh,
  Download,
  Visibility,
  Timer,
  CheckCircle,
  Warning,
  Speed
} from '@mui/icons-material';

export default function AutomationHubPage() {
  const [automationEnabled, setAutomationEnabled] = useState(true);

  // Mock automation data
  const automationMetrics = {
    totalAutomations: 47,
    activeAutomations: 34,
    successRate: 94.7,
    timeSaved: '156 hours',
    tasksCompleted: 2847,
    errorRate: 2.3
  };

  const automationRules = [
    {
      id: 1,
      name: 'WhatsApp Auto-Bidding',
      description: 'Automated bid submissions via WhatsApp integration',
      status: 'Active',
      successRate: 96.8,
      lastRun: '5 min ago',
      frequency: 'Real-time',
      tasksCompleted: 847
    },
    {
      id: 2,
      name: 'Compliance Monitoring',
      description: 'Automated compliance checks and document validation',
      status: 'Active',
      successRate: 98.2,
      lastRun: '15 min ago',
      frequency: 'Hourly',
      tasksCompleted: 1247
    },
    {
      id: 3,
      name: 'Tender Alerts',
      description: 'Automated tender discovery and notification system',
      status: 'Active',
      successRate: 92.4,
      lastRun: '2 min ago',
      frequency: 'Continuous',
      tasksCompleted: 456
    },
    {
      id: 4,
      name: 'Document Generation',
      description: 'Automated bid document creation and formatting',
      status: 'Paused',
      successRate: 89.7,
      lastRun: '2 hours ago',
      frequency: 'On-demand',
      tasksCompleted: 234
    },
    {
      id: 5,
      name: 'Performance Reporting',
      description: 'Automated analytics and performance report generation',
      status: 'Active',
      successRate: 94.1,
      lastRun: '30 min ago',
      frequency: 'Daily',
      tasksCompleted: 63
    }
  ];

  const recentActivities = [
    { id: 1, action: 'WhatsApp bid submitted', tender: 'MUN/2024/IT/001', status: 'Success', timestamp: '2 min ago' },
    { id: 2, action: 'Compliance check completed', area: 'B-BBEE Verification', status: 'Success', timestamp: '8 min ago' },
    { id: 3, action: 'New tender alert sent', tender: 'PROV/2024/ROAD/045', status: 'Success', timestamp: '12 min ago' },
    { id: 4, action: 'Document generation failed', tender: 'NAT/2024/SEC/012', status: 'Error', timestamp: '18 min ago' },
    { id: 5, action: 'Performance report generated', report: 'Weekly Analytics', status: 'Success', timestamp: '25 min ago' }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'success';
      case 'Paused': return 'warning';
      case 'Error': return 'error';
      case 'Success': return 'success';
      default: return 'default';
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold" color="primary.main">
          🔧 Automation Hub
        </Typography>
        <Typography variant="h6" color="text.primary" sx={{ mb: 2 }}>
          Central automation control center for all BidBeez intelligent systems and workflows
        </Typography>
        
        <Alert severity="success" sx={{ mb: 3 }}>
          <strong>Automation Active:</strong> 34 of 47 automations running with 94.7% success rate and 156 hours saved this month.
        </Alert>
      </Box>

      {/* Automation Controls */}
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item>
          <FormControlLabel
            control={
              <Switch
                checked={automationEnabled}
                onChange={(e) => setAutomationEnabled(e.target.checked)}
                color="primary"
              />
            }
            label="Master Automation Control"
          />
        </Grid>
        <Grid item>
          <Button variant="contained" startIcon={<Refresh />} color="primary">
            Refresh Status
          </Button>
        </Grid>
        <Grid item>
          <Button variant="outlined" startIcon={<Download />}>
            Export Logs
          </Button>
        </Grid>
        <Grid item>
          <Button variant="outlined" startIcon={<Settings />}>
            Automation Settings
          </Button>
        </Grid>
      </Grid>

      {/* Automation Metrics Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <AutoAwesome sx={{ fontSize: 32, color: 'primary.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {automationMetrics.totalAutomations}
              </Typography>
              <Typography variant="body2" color="text.secondary">Total Automations</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <PlayArrow sx={{ fontSize: 32, color: 'success.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {automationMetrics.activeAutomations}
              </Typography>
              <Typography variant="body2" color="text.secondary">Active</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <CheckCircle sx={{ fontSize: 32, color: 'success.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {automationMetrics.successRate}%
              </Typography>
              <Typography variant="body2" color="text.secondary">Success Rate</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Timer sx={{ fontSize: 32, color: 'info.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {automationMetrics.timeSaved}
              </Typography>
              <Typography variant="body2" color="text.secondary">Time Saved</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Speed sx={{ fontSize: 32, color: 'warning.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {automationMetrics.tasksCompleted}
              </Typography>
              <Typography variant="body2" color="text.secondary">Tasks Completed</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Warning sx={{ fontSize: 32, color: 'error.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {automationMetrics.errorRate}%
              </Typography>
              <Typography variant="body2" color="text.secondary">Error Rate</Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Automation Rules */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom color="text.primary">
                Automation Rules & Status
              </Typography>
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell><Typography color="text.primary" fontWeight="bold">Automation</Typography></TableCell>
                      <TableCell><Typography color="text.primary" fontWeight="bold">Status</Typography></TableCell>
                      <TableCell><Typography color="text.primary" fontWeight="bold">Success Rate</Typography></TableCell>
                      <TableCell><Typography color="text.primary" fontWeight="bold">Frequency</Typography></TableCell>
                      <TableCell><Typography color="text.primary" fontWeight="bold">Tasks</Typography></TableCell>
                      <TableCell><Typography color="text.primary" fontWeight="bold">Actions</Typography></TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {automationRules.map((rule) => (
                      <TableRow key={rule.id}>
                        <TableCell>
                          <Box>
                            <Typography variant="subtitle2" fontWeight="medium" color="text.primary">
                              {rule.name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {rule.description}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip 
                            label={rule.status} 
                            color={getStatusColor(rule.status) as any}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <LinearProgress 
                              variant="determinate" 
                              value={rule.successRate} 
                              sx={{ width: 60, height: 6 }}
                              color="success"
                            />
                            <Typography variant="body2" color="text.primary">
                              {rule.successRate}%
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" color="text.secondary">
                            {rule.frequency}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" color="text.primary">
                            {rule.tasksCompleted}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <Button size="small" startIcon={<Settings />}>
                              Config
                            </Button>
                            <Button size="small" startIcon={<Visibility />}>
                              Logs
                            </Button>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom color="text.primary">
                Recent Activity
              </Typography>
              <Box sx={{ maxHeight: 400, overflow: 'auto' }}>
                {recentActivities.map((activity) => (
                  <Box key={activity.id} sx={{ mb: 2, p: 2, border: '1px solid #333', borderRadius: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="subtitle2" color="text.primary">
                        {activity.action}
                      </Typography>
                      <Chip 
                        label={activity.status} 
                        color={getStatusColor(activity.status) as any}
                        size="small"
                      />
                    </Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      {activity.tender || activity.area || activity.report}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {activity.timestamp}
                    </Typography>
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Automation Performance */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom color="text.primary">
                Automation Performance Trends
              </Typography>
              <Box sx={{ height: 250, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: 'rgba(255,255,255,0.05)' }}>
                <Typography variant="body1" color="text.secondary">
                  📈 Automation performance analytics
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom color="text.primary">
                Task Distribution
              </Typography>
              <Box sx={{ height: 250, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: 'rgba(255,255,255,0.05)' }}>
                <Typography variant="body1" color="text.secondary">
                  📊 Task distribution by automation type
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Automation Insights */}
      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom color="text.primary">
            Automation Insights
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Alert severity="success">
                <strong>High Performance:</strong> WhatsApp automation achieving 96.8% success rate with 847 completed tasks.
              </Alert>
            </Grid>
            <Grid item xs={12} md={4}>
              <Alert severity="warning">
                <strong>Attention Needed:</strong> Document generation automation paused due to template updates.
              </Alert>
            </Grid>
            <Grid item xs={12} md={4}>
              <Alert severity="info">
                <strong>Optimization:</strong> Compliance monitoring saving 45 hours per week through automation.
              </Alert>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Container>
  );
}
