'use client';

import React from 'react';
import { Container, Typo<PERSON>, Card, CardContent, Grid, Button, Box, Switch, FormControlLabel, Table, TableBody, TableCell, TableHead, TableRow } from '@mui/material';
import { Security, Person, Edit, Add } from '@mui/icons-material';

const roles = [
  { name: "Admin", users: 3, permissions: ["Full Access", "User Management", "System Config"] },
  { name: "Manager", users: 8, permissions: ["Bid Management", "Reports", "Team Access"] },
  { name: "User", users: 15, permissions: ["View Tenders", "Submit Bids", "Basic Reports"] }
];

export default function RolePermissionsPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
            Role & Permissions
          </Typography>
          <Typography variant="h6" color="text.secondary">
            Manage user roles and access permissions
          </Typography>
        </Box>
        <Button variant="contained" startIcon={<Add />}>
          Create Role
        </Button>
      </Box>
      
      <Grid container spacing={3}>
        {roles.map((role, index) => (
          <Grid item xs={12} md={4} key={index}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>{role.name}</Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {role.users} users assigned
                </Typography>
                <Box sx={{ mt: 2 }}>
                  {role.permissions.map((permission, idx) => (
                    <FormControlLabel
                      key={idx}
                      control={<Switch defaultChecked />}
                      label={permission}
                      sx={{ display: 'block' }}
                    />
                  ))}
                </Box>
                <Button variant="outlined" size="small" startIcon={<Edit />} sx={{ mt: 2 }}>
                  Edit Role
                </Button>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Container>
  );
}
