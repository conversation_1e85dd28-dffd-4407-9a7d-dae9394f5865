'use client';

import React from 'react';
import {
  Container,
  Typography,
  Box,
  Breadcrumbs,
  Link
} from '@mui/material';
import { Home, BarChart } from '@mui/icons-material';
import MarketStatsDashboard from '@/components/statistics/MarketStatsDashboard';

const MarketStatisticsPage: React.FC = () => {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 3 }}>
        <Link 
          href="/" 
          sx={{ display: 'flex', alignItems: 'center', gap: 0.5, textDecoration: 'none' }}
        >
          <Home fontSize="small" />
          Dashboard
        </Link>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
          <BarChart fontSize="small" />
          Market Statistics
        </Box>
      </Breadcrumbs>

      {/* Market Statistics Dashboard */}
      <MarketStatsDashboard />
    </Container>
  );
};

export default MarketStatisticsPage;
