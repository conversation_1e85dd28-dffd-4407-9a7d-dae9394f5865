'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  LinearProgress,
  Tabs,
  Tab,
  Paper,
  Alert,
  Stack,
  Divider,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Avatar,
  Badge
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Assessment as AnalysisIcon,
  Security as RiskIcon,
  Gavel as ComplianceIcon,
  TrendingUp as CompetitiveIcon,
  Assignment as TaskIcon,
  People as BeeIcon,
  Description as DocumentIcon,
  Timeline as ProgressIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Schedule as ClockIcon,
  Star as StarIcon,
  LocationOn as LocationIcon,
  AttachMoney as MoneyIcon
} from '@mui/icons-material';

import { ActiveBidWorkspace, BidWorkflowState } from '../../types/bidWorkflow';
import BidWorkflowService from '../../services/BidWorkflowService';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`active-bids-tabpanel-${index}`}
      aria-labelledby={`active-bids-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

export default function MyActiveBidsPage() {
  const [activeTab, setActiveTab] = useState(0);
  const [activeBids, setActiveBids] = useState<ActiveBidWorkspace[]>([]);
  const [selectedBid, setSelectedBid] = useState<ActiveBidWorkspace | null>(null);
  const [loading, setLoading] = useState(true);

  const workflowService = BidWorkflowService.getInstance();

  useEffect(() => {
    loadActiveBids();
  }, []);

  const loadActiveBids = async () => {
    try {
      setLoading(true);
      // Mock user ID - in real app, get from auth context
      const bids = await workflowService.getActiveBids('user-123');
      setActiveBids(bids);
      if (bids.length > 0 && !selectedBid) {
        setSelectedBid(bids[0]);
      }
    } catch (error) {
      console.error('Error loading active bids:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const getStateColor = (state: BidWorkflowState) => {
    switch (state) {
      case BidWorkflowState.INTERESTED: return 'info';
      case BidWorkflowState.ANALYZING: return 'warning';
      case BidWorkflowState.PREPARING: return 'primary';
      case BidWorkflowState.READY: return 'success';
      case BidWorkflowState.SUBMITTED: return 'success';
      default: return 'default';
    }
  };

  const getStateIcon = (state: BidWorkflowState) => {
    switch (state) {
      case BidWorkflowState.INTERESTED: return <StarIcon />;
      case BidWorkflowState.ANALYZING: return <AnalysisIcon />;
      case BidWorkflowState.PREPARING: return <TaskIcon />;
      case BidWorkflowState.READY: return <CheckIcon />;
      case BidWorkflowState.SUBMITTED: return <CheckIcon />;
      default: return <ClockIcon />;
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'success';
      case 'medium': return 'warning';
      case 'high': return 'error';
      case 'critical': return 'error';
      default: return 'default';
    }
  };

  if (loading) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <LinearProgress sx={{ mb: 2 }} />
        <Typography>Loading your active bids...</Typography>
      </Box>
    );
  }

  if (activeBids.length === 0) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <TaskIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h5" color="text.secondary" gutterBottom>
          No Active Bids
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          You haven't expressed interest in any tenders yet. Check your notifications for new opportunities!
        </Typography>
        <Button variant="contained" href="/notifications">
          View Notifications
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3, minHeight: '100vh', bgcolor: 'grey.50' }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main', mb: 1 }}>
          🎯 My Active Bids
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Comprehensive workspace for your interested tenders with AI analysis and bee worker coordination
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Left Panel - Bid List */}
        <Grid item xs={12} md={4}>
          <Paper elevation={2} sx={{ height: 'fit-content' }}>
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <Typography variant="h6">Active Bids ({activeBids.length})</Typography>
            </Box>
            <List sx={{ p: 0 }}>
              {activeBids.map((bid, index) => (
                <React.Fragment key={bid.bidInterest.id}>
                  <ListItem
                    button
                    selected={selectedBid?.bidInterest.id === bid.bidInterest.id}
                    onClick={() => setSelectedBid(bid)}
                    sx={{ py: 2 }}
                  >
                    <ListItemIcon>
                      <Badge
                        badgeContent={bid.bidInterest.commitmentLevel}
                        color="primary"
                        max={100}
                      >
                        {getStateIcon(bid.bidInterest.state)}
                      </Badge>
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                          {bid.tender.title}
                        </Typography>
                      }
                      secondary={
                        <Box>
                          <Typography variant="caption" color="text.secondary" display="block">
                            {bid.tender.organization}
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                            <Chip
                              label={bid.bidInterest.state.replace('_', ' ').toUpperCase()}
                              size="small"
                              color={getStateColor(bid.bidInterest.state) as any}
                              variant="outlined"
                            />
                            <Typography variant="caption" color="success.main">
                              {formatCurrency(bid.tender.estimatedValue)}
                            </Typography>
                          </Box>
                        </Box>
                      }
                    />
                  </ListItem>
                  {index < activeBids.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </Paper>
        </Grid>

        {/* Right Panel - Detailed Analysis */}
        <Grid item xs={12} md={8}>
          {selectedBid && (
            <Paper elevation={2}>
              {/* Bid Header */}
              <Box sx={{ p: 3, borderBottom: 1, borderColor: 'divider' }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} md={8}>
                    <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
                      {selectedBid.tender.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {selectedBid.tender.organization} • {selectedBid.tender.location}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <Chip
                        icon={getStateIcon(selectedBid.bidInterest.state)}
                        label={selectedBid.bidInterest.state.replace('_', ' ').toUpperCase()}
                        color={getStateColor(selectedBid.bidInterest.state) as any}
                      />
                      <Chip
                        icon={<MoneyIcon />}
                        label={formatCurrency(selectedBid.tender.estimatedValue)}
                        variant="outlined"
                      />
                      <Chip
                        icon={<LocationIcon />}
                        label={selectedBid.tender.category}
                        variant="outlined"
                      />
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Box sx={{ textAlign: 'right' }}>
                      <Typography variant="h4" color="primary.main" sx={{ fontWeight: 600 }}>
                        {selectedBid.bidInterest.commitmentLevel}%
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Commitment Level
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={selectedBid.bidInterest.commitmentLevel}
                        sx={{ mt: 1, height: 8, borderRadius: 4 }}
                      />
                    </Box>
                  </Grid>
                </Grid>
              </Box>

              {/* Analysis Tabs */}
              <Tabs value={activeTab} onChange={handleTabChange} variant="scrollable">
                <Tab icon={<AnalysisIcon />} label="AI Analysis" />
                <Tab icon={<RiskIcon />} label="Risk & Governance" />
                <Tab icon={<BeeIcon />} label="Bee Workers" />
                <Tab icon={<DocumentIcon />} label="Documents" />
                <Tab icon={<ProgressIcon />} label="Progress" />
              </Tabs>

              {/* Tab Content */}
              <TabPanel value={activeTab} index={0}>
                {/* AI Analysis Tab */}
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Card variant="outlined">
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                          <AnalysisIcon color="primary" />
                          <Typography variant="h6">Feasibility Analysis</Typography>
                        </Box>
                        <Box sx={{ textAlign: 'center', mb: 2 }}>
                          <Typography variant="h3" color="primary.main" sx={{ fontWeight: 600 }}>
                            {selectedBid.tenderAnalysis.feasibilityScore}%
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Overall Feasibility Score
                          </Typography>
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={selectedBid.tenderAnalysis.feasibilityScore}
                          sx={{ height: 8, borderRadius: 4 }}
                        />
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Card variant="outlined">
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                          <CompetitiveIcon color="success" />
                          <Typography variant="h6">Win Probability</Typography>
                        </Box>
                        <Box sx={{ textAlign: 'center', mb: 2 }}>
                          <Typography variant="h3" color="success.main" sx={{ fontWeight: 600 }}>
                            {selectedBid.tenderAnalysis.competitiveAnalysis.winProbability}%
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Estimated Win Chance
                          </Typography>
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={selectedBid.tenderAnalysis.competitiveAnalysis.winProbability}
                          color="success"
                          sx={{ height: 8, borderRadius: 4 }}
                        />
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>

                {selectedBid.bidInterest.analysisCompleted ? (
                  <Alert severity="success" sx={{ mt: 2 }}>
                    <Typography variant="body2">
                      ✅ AI analysis completed! Review the insights above and proceed to assign bee workers or prepare your bid.
                    </Typography>
                  </Alert>
                ) : (
                  <Alert severity="info" sx={{ mt: 2 }}>
                    <Typography variant="body2">
                      🤖 AI analysis in progress... This usually takes 2-3 minutes to complete.
                    </Typography>
                  </Alert>
                )}
              </TabPanel>

              <TabPanel value={activeTab} index={1}>
                {/* Risk & Governance Tab */}
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Card variant="outlined">
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                          <RiskIcon color="warning" />
                          <Typography variant="h6">Risk Assessment</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                          <Chip
                            label={selectedBid.tenderAnalysis.riskAssessment.overallRisk.toUpperCase()}
                            color={getRiskColor(selectedBid.tenderAnalysis.riskAssessment.overallRisk) as any}
                          />
                          <Typography variant="h4" sx={{ fontWeight: 600 }}>
                            {selectedBid.tenderAnalysis.riskAssessment.riskScore}/100
                          </Typography>
                        </Box>
                        <Typography variant="body2" color="text.secondary">
                          Risk factors identified and mitigation strategies available
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Card variant="outlined">
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                          <ComplianceIcon color="info" />
                          <Typography variant="h6">Compliance Status</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                          <Typography variant="h4" color="info.main" sx={{ fontWeight: 600 }}>
                            {selectedBid.tenderAnalysis.complianceStatus.overallScore}%
                          </Typography>
                          <Chip
                            label={selectedBid.tenderAnalysis.complianceStatus.riskLevel.toUpperCase()}
                            color={getRiskColor(selectedBid.tenderAnalysis.complianceStatus.riskLevel) as any}
                            variant="outlined"
                          />
                        </Box>
                        <Typography variant="body2" color="text.secondary">
                          Compliance requirements analysis and gap identification
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </TabPanel>

              <TabPanel value={activeTab} index={2}>
                {/* Bee Workers Tab */}
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Available Bee Workers for This Tender
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    Specialized workers filtered by tender requirements, location, and skills
                  </Typography>
                  
                  {selectedBid.beeWorkerTasks.availableBees.length === 0 ? (
                    <Alert severity="info">
                      <Typography variant="body2">
                        🔍 Searching for suitable bee workers based on tender requirements...
                      </Typography>
                    </Alert>
                  ) : (
                    <Grid container spacing={2}>
                      {selectedBid.beeWorkerTasks.availableBees.map((bee) => (
                        <Grid item xs={12} md={6} key={bee.id}>
                          <Card variant="outlined">
                            <CardContent>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                                <Avatar>{bee.name.charAt(0)}</Avatar>
                                <Box>
                                  <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                                    {bee.name}
                                  </Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    {bee.location} • {bee.distance}km away
                                  </Typography>
                                </Box>
                                <Box sx={{ ml: 'auto', textAlign: 'right' }}>
                                  <Typography variant="h6" color="primary.main">
                                    {bee.matchScore}%
                                  </Typography>
                                  <Typography variant="caption">Match</Typography>
                                </Box>
                              </Box>
                              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}>
                                {bee.skills.slice(0, 3).map((skill) => (
                                  <Chip key={skill} label={skill} size="small" variant="outlined" />
                                ))}
                              </Box>
                              <Button variant="outlined" size="small" fullWidth>
                                Assign Tasks
                              </Button>
                            </CardContent>
                          </Card>
                        </Grid>
                      ))}
                    </Grid>
                  )}
                </Box>
              </TabPanel>

              <TabPanel value={activeTab} index={3}>
                {/* Documents Tab */}
                <Typography variant="h6" gutterBottom>
                  Tender Documents & Workspace
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  Original tender documents and your bid preparation workspace
                </Typography>
                
                <Alert severity="info" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    📄 Document processing and workspace features will be available once AI analysis is complete.
                  </Typography>
                </Alert>
              </TabPanel>

              <TabPanel value={activeTab} index={4}>
                {/* Progress Tab */}
                <Typography variant="h6" gutterBottom>
                  Bid Preparation Progress
                </Typography>
                <Box sx={{ mb: 3 }}>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    Overall Progress: {selectedBid.progressTracking.overallProgress}%
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={selectedBid.progressTracking.overallProgress}
                    sx={{ height: 12, borderRadius: 6 }}
                  />
                </Box>

                <Alert severity="info">
                  <Typography variant="body2">
                    📊 Detailed progress tracking and milestone management will be available as you advance through the bid preparation process.
                  </Typography>
                </Alert>
              </TabPanel>

              {/* Action Buttons */}
              <Box sx={{ p: 3, borderTop: 1, borderColor: 'divider', bgcolor: 'grey.50' }}>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Button
                      variant="outlined"
                      fullWidth
                      startIcon={<BeeIcon />}
                      onClick={() => setActiveTab(2)}
                      disabled={!selectedBid.bidInterest.analysisCompleted}
                    >
                      Assign Bee Workers
                    </Button>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Button
                      variant="contained"
                      fullWidth
                      startIcon={<TaskIcon />}
                      disabled={selectedBid.bidInterest.state !== BidWorkflowState.PREPARING}
                    >
                      Start Bid Preparation
                    </Button>
                  </Grid>
                </Grid>
              </Box>
            </Paper>
          )}
        </Grid>
      </Grid>
    </Box>
  );
}
