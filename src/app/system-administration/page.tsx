'use client';

import React from 'react';
import { Container, Typography, Card, CardContent, Grid, Button, Box, Chip, List, ListItem, ListItemText } from '@mui/material';
import { Settings, Security, Storage, People } from '@mui/icons-material';

export default function SystemAdministrationPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
        System Administration
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        Manage system settings and configurations
      </Typography>
      
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>System Status</Typography>
              <List>
                <ListItem>
                  <ListItemText primary="Database" secondary="Operational" />
                  <Chip label="Online" color="success" size="small" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="API Services" secondary="All endpoints active" />
                  <Chip label="Healthy" color="success" size="small" />
                </ListItem>
                <ListItem>
                  <ListItemText primary="Background Jobs" secondary="Processing normally" />
                  <Chip label="Running" color="success" size="small" />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Quick Actions</Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Button variant="contained" startIcon={<People />}>
                  User Management
                </Button>
                <Button variant="outlined" startIcon={<Security />}>
                  Security Settings
                </Button>
                <Button variant="outlined" startIcon={<Storage />}>
                  Database Backup
                </Button>
                <Button variant="outlined" startIcon={<Settings />}>
                  System Configuration
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
}
