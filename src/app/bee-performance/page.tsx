'use client';

import React, { useState } from 'react';
import {
  Box,
  Container,
  Card,
  CardContent,
  Typography,
  Grid,
  LinearProgress,
  Chip,
  Avatar,
  Stack,
  Tab,
  Tabs,
  Alert,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  Star,
  Timer,
  CheckCircle,
  AttachMoney,
  LocationOn,
  Assignment,
  EmojiEvents,
  Warning,
  ThumbUp,
  Speed,
  Target
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON>hart, Bar, <PERSON>hart, <PERSON>, Cell } from 'recharts';

interface PerformanceMetrics {
  overall: {
    rating: number;
    totalTasks: number;
    completionRate: number;
    onTimeRate: number;
    averageTaskTime: string;
    totalEarnings: number;
  };
  monthly: {
    tasksCompleted: number;
    earnings: number;
    rating: number;
    improvement: number;
  };
  strengths: string[];
  improvementAreas: string[];
  achievements: Array<{
    id: string;
    title: string;
    description: string;
    dateEarned: string;
    icon: string;
  }>;
  recentFeedback: Array<{
    id: string;
    queenBee: string;
    rating: number;
    comment: string;
    taskType: string;
    date: string;
  }>;
}

export default function BeePerformancePage() {
  const [currentTab, setCurrentTab] = useState(0);

  const [performance, setPerformance] = useState<PerformanceMetrics>({
    overall: {
      rating: 4.8,
      totalTasks: 47,
      completionRate: 96,
      onTimeRate: 94,
      averageTaskTime: '2.3 hours',
      totalEarnings: 23450
    },
    monthly: {
      tasksCompleted: 12,
      earnings: 5400,
      rating: 4.9,
      improvement: 8
    },
    strengths: [
      'Excellent punctuality',
      'High-quality documentation',
      'Professional communication',
      'Efficient task completion',
      'Strong attention to detail'
    ],
    improvementAreas: [
      'GPS navigation efficiency',
      'Photo quality consistency',
      'Report submission timing'
    ],
    achievements: [
      {
        id: 'ach-001',
        title: 'Speed Demon',
        description: 'Completed 10 tasks in under estimated time',
        dateEarned: '2024-01-10',
        icon: '⚡'
      },
      {
        id: 'ach-002',
        title: 'Perfect Week',
        description: '7 consecutive 5-star ratings',
        dateEarned: '2024-01-05',
        icon: '🌟'
      },
      {
        id: 'ach-003',
        title: 'Distance Champion',
        description: 'Traveled 500km for task completion',
        dateEarned: '2023-12-28',
        icon: '🚗'
      },
      {
        id: 'ach-004',
        title: 'Documentation Expert',
        description: 'Perfect documentation score for 20 tasks',
        dateEarned: '2023-12-15',
        icon: '📋'
      }
    ],
    recentFeedback: [
      {
        id: 'fb-001',
        queenBee: 'Queen Bee Sarah',
        rating: 5,
        comment: 'Excellent work on the municipal document collection. Very professional and efficient.',
        taskType: 'Document Collection',
        date: '2024-01-14'
      },
      {
        id: 'fb-002',
        queenBee: 'Queen Bee Mike',
        rating: 4,
        comment: 'Good site visit report, but photos could be clearer. Overall great work.',
        taskType: 'Site Visit',
        date: '2024-01-12'
      },
      {
        id: 'fb-003',
        queenBee: 'Queen Bee Lisa',
        rating: 5,
        comment: 'Outstanding briefing attendance. Took excellent notes and asked relevant questions.',
        taskType: 'Briefing Attendance',
        date: '2024-01-10'
      }
    ]
  });

  // Sample data for charts
  const monthlyPerformanceData = [
    { month: 'Aug', tasks: 8, rating: 4.5, earnings: 3200 },
    { month: 'Sep', tasks: 10, rating: 4.6, earnings: 4100 },
    { month: 'Oct', tasks: 12, rating: 4.7, earnings: 4800 },
    { month: 'Nov', tasks: 15, rating: 4.8, earnings: 6200 },
    { month: 'Dec', tasks: 14, rating: 4.9, earnings: 5900 },
    { month: 'Jan', tasks: 12, rating: 4.9, earnings: 5400 }
  ];

  const taskTypeData = [
    { name: 'Document Collection', value: 45, color: '#8884d8' },
    { name: 'Site Visits', value: 30, color: '#82ca9d' },
    { name: 'Briefing Attendance', value: 15, color: '#ffc658' },
    { name: 'Other', value: 10, color: '#ff7300' }
  ];

  const ratingDistribution = [
    { rating: '5 Stars', count: 32 },
    { rating: '4 Stars', count: 12 },
    { rating: '3 Stars', count: 2 },
    { rating: '2 Stars', count: 1 },
    { rating: '1 Star', count: 0 }
  ];

  const getPerformanceColor = (value: number, threshold: number = 80) => {
    if (value >= 95) return 'success';
    if (value >= threshold) return 'warning';
    return 'error';
  };

  const getTrendIcon = (improvement: number) => {
    if (improvement > 0) return <TrendingUp color="success" />;
    if (improvement < 0) return <TrendingDown color="error" />;
    return <TrendingUp color="disabled" />;
  };

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" fontWeight="bold" gutterBottom>
          📊 My Performance
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Track your progress and identify areas for improvement
        </Typography>
      </Box>

      {/* Performance Overview Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Star sx={{ fontSize: 32, color: 'gold', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold">
                {performance.overall.rating}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Overall Rating
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 1 }}>
                {getTrendIcon(performance.monthly.improvement)}
                <Typography variant="caption" color="success.main">
                  +{performance.monthly.improvement}% this month
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <CheckCircle sx={{ fontSize: 32, color: 'success.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold">
                {performance.overall.completionRate}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Completion Rate
              </Typography>
              <LinearProgress 
                variant="determinate" 
                value={performance.overall.completionRate}
                color={getPerformanceColor(performance.overall.completionRate) as any}
                sx={{ mt: 1, height: 6, borderRadius: 3 }}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Timer sx={{ fontSize: 32, color: 'primary.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold">
                {performance.overall.onTimeRate}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                On-Time Rate
              </Typography>
              <LinearProgress 
                variant="determinate" 
                value={performance.overall.onTimeRate}
                color={getPerformanceColor(performance.overall.onTimeRate) as any}
                sx={{ mt: 1, height: 6, borderRadius: 3 }}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <AttachMoney sx={{ fontSize: 32, color: 'warning.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold">
                R{performance.overall.totalEarnings.toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Earnings
              </Typography>
              <Typography variant="caption" color="success.main">
                R{performance.monthly.earnings} this month
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Performance Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={currentTab} onChange={(e, newValue) => setCurrentTab(newValue)}>
          <Tab label="Performance Trends" />
          <Tab label="Achievements" />
          <Tab label="Feedback" />
          <Tab label="Improvement Plan" />
        </Tabs>
      </Box>

      {/* Tab Content */}
      {currentTab === 0 && (
        <Grid container spacing={3}>
          {/* Monthly Performance Chart */}
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  📈 Monthly Performance Trends
                </Typography>
                <Box sx={{ height: 300 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={monthlyPerformanceData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis yAxisId="left" />
                      <YAxis yAxisId="right" orientation="right" />
                      <Tooltip />
                      <Line yAxisId="left" type="monotone" dataKey="tasks" stroke="#8884d8" strokeWidth={2} name="Tasks Completed" />
                      <Line yAxisId="right" type="monotone" dataKey="rating" stroke="#82ca9d" strokeWidth={2} name="Average Rating" />
                    </LineChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Task Type Distribution */}
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  📋 Task Type Distribution
                </Typography>
                <Box sx={{ height: 300 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={taskTypeData}
                        cx="50%"
                        cy="50%"
                        innerRadius={40}
                        outerRadius={80}
                        dataKey="value"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {taskTypeData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Rating Distribution */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  ⭐ Rating Distribution
                </Typography>
                <Box sx={{ height: 200 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={ratingDistribution}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="rating" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="count" fill="#ffd700" />
                    </BarChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {currentTab === 1 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  🏆 Achievements & Badges
                </Typography>
                <Grid container spacing={2}>
                  {performance.achievements.map((achievement) => (
                    <Grid item xs={12} sm={6} md={3} key={achievement.id}>
                      <Paper 
                        sx={{ 
                          p: 2, 
                          textAlign: 'center',
                          background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 100%)',
                          color: '#333'
                        }}
                      >
                        <Typography variant="h3" sx={{ mb: 1 }}>
                          {achievement.icon}
                        </Typography>
                        <Typography variant="h6" fontWeight="bold" gutterBottom>
                          {achievement.title}
                        </Typography>
                        <Typography variant="body2" sx={{ mb: 1 }}>
                          {achievement.description}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Earned: {new Date(achievement.dateEarned).toLocaleDateString()}
                        </Typography>
                      </Paper>
                    </Grid>
                  ))}
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {currentTab === 2 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  💬 Recent Feedback from Queen Bees
                </Typography>
                <List>
                  {performance.recentFeedback.map((feedback, index) => (
                    <React.Fragment key={feedback.id}>
                      <ListItem alignItems="flex-start">
                        <ListItemIcon>
                          <Avatar sx={{ bgcolor: 'warning.main' }}>
                            👑
                          </Avatar>
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                              <Typography variant="subtitle1" fontWeight="bold">
                                {feedback.queenBee}
                              </Typography>
                              <Chip label={feedback.taskType} size="small" />
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                {[...Array(5)].map((_, i) => (
                                  <Star 
                                    key={i}
                                    sx={{ 
                                      fontSize: 16,
                                      color: i < feedback.rating ? 'gold' : 'grey.300'
                                    }}
                                  />
                                ))}
                              </Box>
                            </Box>
                          }
                          secondary={
                            <Box>
                              <Typography variant="body2" sx={{ mb: 1 }}>
                                "{feedback.comment}"
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {new Date(feedback.date).toLocaleDateString()}
                              </Typography>
                            </Box>
                          }
                        />
                      </ListItem>
                      {index < performance.recentFeedback.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {currentTab === 3 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <ThumbUp color="success" /> Strengths
                </Typography>
                <Stack spacing={1}>
                  {performance.strengths.map((strength, index) => (
                    <Alert key={index} severity="success" variant="outlined">
                      {strength}
                    </Alert>
                  ))}
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Target color="warning" /> Improvement Areas
                </Typography>
                <Stack spacing={1}>
                  {performance.improvementAreas.map((area, index) => (
                    <Alert key={index} severity="warning" variant="outlined">
                      {area}
                    </Alert>
                  ))}
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12}>
            <Alert severity="info">
              <Typography variant="body1" fontWeight="bold" gutterBottom>
                💡 Performance Tips
              </Typography>
              <Typography variant="body2">
                • Focus on improving photo quality by ensuring good lighting and clear focus<br/>
                • Use GPS navigation more efficiently to reduce travel time<br/>
                • Submit reports within 2 hours of task completion for better ratings<br/>
                • Maintain professional communication with Queen Bees and clients
              </Typography>
            </Alert>
          </Grid>
        </Grid>
      )}
    </Container>
  );
}
