'use client';

import React, { useState } from 'react';
import { 
  Container, 
  Box, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  Alert,
  Button,
  Chip,
  Avatar,
  Rating,
  TextField,
  InputAdornment
} from '@mui/material';
import { 
  School, 
  Star, 
  Search, 
  Users, 
  Award,
  TrendingUp,
  CheckCircle,
  Clock
} from 'lucide-react';

export default function SkillSyncPage() {
  const [searchTerm, setSearchTerm] = useState('');

  // Mock SkillSync data
  const stats = {
    totalProviders: 2847,
    verifiedSkills: 15420,
    activeMatches: 89,
    avgRating: 4.7,
    successRate: 92.3
  };

  const skillProviders = [
    {
      id: 1,
      name: '<PERSON>',
      title: 'Project Management Professional',
      skills: ['PMP', 'Agile', 'Risk Management', 'Stakeholder Management'],
      rating: 4.9,
      reviews: 127,
      hourlyRate: 'R850/hour',
      availability: 'Available',
      bbbeeLevel: 'Level 2',
      verified: true
    },
    {
      id: 2,
      name: '<PERSON>',
      title: 'IT Security Specialist',
      skills: ['Cybersecurity', 'ISO 27001', 'Penetration Testing', 'CISSP'],
      rating: 4.8,
      reviews: 94,
      hourlyRate: 'R1200/hour',
      availability: 'Busy',
      bbbeeLevel: 'Level 1',
      verified: true
    },
    {
      id: 3,
      name: 'Lisa Kruger',
      title: 'Financial Analyst',
      skills: ['Financial Modeling', 'CFA', 'Risk Analysis', 'Compliance'],
      rating: 4.7,
      reviews: 156,
      hourlyRate: 'R750/hour',
      availability: 'Available',
      bbbeeLevel: 'Level 3',
      verified: true
    }
  ];

  const skillDemand = [
    { skill: 'Project Management', demand: 'High', growth: '+23%', avgRate: 'R850/hour' },
    { skill: 'Cybersecurity', demand: 'Very High', growth: '+45%', avgRate: 'R1200/hour' },
    { skill: 'Data Analysis', demand: 'High', growth: '+31%', avgRate: 'R900/hour' },
    { skill: 'Compliance', demand: 'Medium', growth: '+12%', avgRate: 'R700/hour' }
  ];

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case 'Available': return 'success';
      case 'Busy': return 'warning';
      case 'Unavailable': return 'error';
      default: return 'default';
    }
  };

  const getDemandColor = (demand: string) => {
    switch (demand) {
      case 'Very High': return 'error';
      case 'High': return 'warning';
      case 'Medium': return 'info';
      case 'Low': return 'default';
      default: return 'default';
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
          🎯 SkillSync Marketplace
        </Typography>
        <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
          AI-powered skill provider discovery and B-BBEE compliant talent matching
        </Typography>
        
        <Alert severity="info" sx={{ mb: 3 }}>
          <strong>Smart Matching Active:</strong> AI analyzing 15,420 verified skills with 92.3% successful tender-to-skill matching rate.
        </Alert>
      </Box>

      {/* Search Bar */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <TextField
            fullWidth
            placeholder="Search for skills, certifications, or expertise..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search size={20} />
                </InputAdornment>
              ),
            }}
            sx={{ mb: 2 }}
          />
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            <Chip label="Project Management" clickable />
            <Chip label="Cybersecurity" clickable />
            <Chip label="Financial Analysis" clickable />
            <Chip label="Legal Compliance" clickable />
            <Chip label="Engineering" clickable />
          </Box>
        </CardContent>
      </Card>

      {/* Stats Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Users size={32} color="#2196f3" />
              <Typography variant="h4" fontWeight="bold">{stats.totalProviders}</Typography>
              <Typography variant="body2" color="text.secondary">Skill Providers</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <School size={32} color="#4caf50" />
              <Typography variant="h4" fontWeight="bold">{stats.verifiedSkills}</Typography>
              <Typography variant="body2" color="text.secondary">Verified Skills</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <TrendingUp size={32} color="#ff9800" />
              <Typography variant="h4" fontWeight="bold">{stats.activeMatches}</Typography>
              <Typography variant="body2" color="text.secondary">Active Matches</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Star size={32} color="#ffc107" />
              <Typography variant="h4" fontWeight="bold">{stats.avgRating}</Typography>
              <Typography variant="body2" color="text.secondary">Avg Rating</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <CheckCircle size={32} color="#9c27b0" />
              <Typography variant="h4" fontWeight="bold">{stats.successRate}%</Typography>
              <Typography variant="body2" color="text.secondary">Success Rate</Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Skill Providers */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Featured Skill Providers</Typography>
              <Grid container spacing={3}>
                {skillProviders.map((provider) => (
                  <Grid item xs={12} key={provider.id}>
                    <Card variant="outlined">
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                          <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                            {provider.name.charAt(0)}
                          </Avatar>
                          <Box sx={{ flexGrow: 1 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                              <Typography variant="h6">{provider.name}</Typography>
                              {provider.verified && (
                                <CheckCircle size={16} color="#4caf50" style={{ marginLeft: 8 }} />
                              )}
                            </Box>
                            <Typography variant="body2" color="text.secondary" gutterBottom>
                              {provider.title}
                            </Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                              <Rating value={provider.rating} precision={0.1} size="small" readOnly />
                              <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                                {provider.rating} ({provider.reviews} reviews)
                              </Typography>
                            </Box>
                          </Box>
                          <Box sx={{ textAlign: 'right' }}>
                            <Typography variant="h6" color="primary.main">
                              {provider.hourlyRate}
                            </Typography>
                            <Chip 
                              label={provider.availability} 
                              color={getAvailabilityColor(provider.availability) as any}
                              size="small"
                            />
                          </Box>
                        </Box>
                        
                        <Box sx={{ mb: 2 }}>
                          <Typography variant="body2" color="text.secondary" gutterBottom>
                            Skills:
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                            {provider.skills.map((skill, index) => (
                              <Chip key={index} label={skill} size="small" variant="outlined" />
                            ))}
                          </Box>
                        </Box>
                        
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Chip label={provider.bbbeeLevel} color="info" size="small" />
                          <Box>
                            <Button size="small" variant="outlined" sx={{ mr: 1 }}>
                              View Profile
                            </Button>
                            <Button size="small" variant="contained">
                              Contact
                            </Button>
                          </Box>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Skill Demand Analytics */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Skill Demand Analytics</Typography>
              {skillDemand.map((item, index) => (
                <Box key={index} sx={{ mb: 3, p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Typography variant="subtitle1" fontWeight="medium">
                      {item.skill}
                    </Typography>
                    <Chip 
                      label={item.demand} 
                      color={getDemandColor(item.demand) as any}
                      size="small"
                    />
                  </Box>
                  <Typography variant="body2" color="success.main" gutterBottom>
                    Growth: {item.growth}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Avg Rate: {item.avgRate}
                  </Typography>
                </Box>
              ))}
              
              <Alert severity="success" sx={{ mt: 2 }}>
                <strong>Market Opportunity:</strong> Cybersecurity skills showing 45% growth in tender requirements.
              </Alert>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* B-BBEE Integration */}
      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>B-BBEE Skills Integration</Typography>
          <Alert severity="info" sx={{ mb: 2 }}>
            <strong>B-BBEE Compliance:</strong> All skill providers are verified for B-BBEE levels and contribute to your tender scoring.
          </Alert>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Award size={32} color="#4caf50" />
                <Typography variant="h6">Level 1-3 Providers</Typography>
                <Typography variant="h4" color="success.main">847</Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Clock size={32} color="#ff9800" />
                <Typography variant="h6">Verification Time</Typography>
                <Typography variant="h4" color="warning.main">24h</Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <CheckCircle size={32} color="#2196f3" />
                <Typography variant="h6">Compliance Rate</Typography>
                <Typography variant="h4" color="primary.main">98.7%</Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Container>
  );
}
