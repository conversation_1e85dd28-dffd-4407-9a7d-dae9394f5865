'use client';

import React from 'react';
import { Container, Typo<PERSON>, Card, CardContent, Grid, Button, Box, Avatar, Chip, List, ListItem, ListItemText } from '@mui/material';
import { SmartToy, TrendingUp, Lightbulb, Assessment, Psychology, Speed } from '@mui/icons-material';

const recommendations = [
  { type: "Pricing", title: "Reduce bid by 8% for higher win probability", confidence: 94, impact: "High" },
  { type: "Timing", title: "Submit bid 2 days before deadline for optimal positioning", confidence: 87, impact: "Medium" },
  { type: "Strategy", title: "Focus on municipal tenders this quarter", confidence: 91, impact: "High" }
];

export default function AIRecommendationsPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
        AI Recommendations
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        Intelligent suggestions powered by machine learning algorithms
      </Typography>
      
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="primary">
                    15
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active Recommendations
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <SmartToy />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={9}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Current Recommendations</Typography>
              <List>
                {recommendations.map((rec, index) => (
                  <ListItem key={index}>
                    <ListItemText
                      primary={rec.title}
                      secondary={
                        <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                          <Chip label={rec.type} color="primary" size="small" />
                          <Chip label={`${rec.confidence}% confidence`} color="success" size="small" />
                          <Chip label={`${rec.impact} impact`} color="warning" size="small" />
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
}
