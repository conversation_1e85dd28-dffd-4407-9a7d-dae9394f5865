'use client';

import React from 'react';
import { Container, Typography, Card, CardContent, Grid, Box, Avatar, Chip } from '@mui/material';
import { TrendingUp, AttachMoney, Assessment, Timeline } from '@mui/icons-material';

export default function RoiAnalysisPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
        ROI Analysis
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        Return on Investment analysis and performance metrics
      </Typography>

      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="success.main">
                    247%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total ROI
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <TrendingUp />
                </Avatar>
              </Box>
              <Chip label="+18% growth" color="success" size="small" sx={{ mt: 1 }} />
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="primary">
                    R12.4M
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Revenue
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <AttachMoney />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>ROI Performance Summary</Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Box sx={{ p: 2, bgcolor: 'primary.50', borderRadius: 1 }}>
                <Typography variant="h6" color="primary.main">Construction Sector</Typography>
                <Typography variant="h4" fontWeight="bold">287% ROI</Typography>
                <Typography variant="body2" color="text.secondary">Highest performing sector</Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box sx={{ p: 2, bgcolor: 'success.50', borderRadius: 1 }}>
                <Typography variant="h6" color="success.main">IT Services</Typography>
                <Typography variant="h4" fontWeight="bold">234% ROI</Typography>
                <Typography variant="body2" color="text.secondary">Strong growth potential</Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Container>
  );
}