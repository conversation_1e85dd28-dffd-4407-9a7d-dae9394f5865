'use client';

import React, { useState } from 'react';
import { 
  Container, 
  Box, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  Tabs, 
  Tab, 
  Alert,
  Chip,
  LinearProgress,
  Avatar,
  Button
} from '@mui/material';
import { 
  Trophy, 
  Star, 
  Target, 
  Users, 
  TrendingUp,
  Award,
  Crown,
  Zap
} from 'lucide-react';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`gamification-tabpanel-${index}`}
      aria-labelledby={`gamification-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

export default function GamificationPage() {
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Mock data for demonstration
  const userStats = {
    level: 8,
    xp: 2847,
    nextLevelXp: 3000,
    totalAchievements: 23,
    rank: 156,
    streak: 12
  };

  const achievements = [
    { id: 1, name: 'First Bid Winner', tier: 'Common', xp: 100, unlocked: true },
    { id: 2, name: 'Speed Demon', tier: 'Rare', xp: 250, unlocked: true },
    { id: 3, name: 'Compliance Master', tier: 'Epic', xp: 500, unlocked: false },
    { id: 4, name: 'Legendary Bidder', tier: 'Legendary', xp: 1000, unlocked: false }
  ];

  const leaderboard = [
    { rank: 1, name: 'Sarah M.', xp: 15420, level: 15, archetype: 'Hunter' },
    { rank: 2, name: 'John D.', xp: 14890, level: 14, archetype: 'Achiever' },
    { rank: 3, name: 'Lisa K.', xp: 13567, level: 13, archetype: 'Analyst' },
    { rank: 4, name: 'Mike R.', xp: 12234, level: 12, archetype: 'Relationship Builder' },
    { rank: 5, name: 'You', xp: userStats.xp, level: userStats.level, archetype: 'Hunter' }
  ];

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
          🏆 Gamification Hub
        </Typography>
        <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
          Achievements, leaderboards, and psychological rewards system
        </Typography>
        
        <Alert severity="info" sx={{ mb: 3 }}>
          <strong>Level {userStats.level} Hunter:</strong> You're on a {userStats.streak}-day streak! Complete daily challenges to maintain momentum.
        </Alert>
      </Box>

      {/* User Stats Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Crown size={32} color="#ffc107" />
              <Typography variant="h4" fontWeight="bold">{userStats.level}</Typography>
              <Typography variant="body2" color="text.secondary">Current Level</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Zap size={32} color="#ff5722" />
              <Typography variant="h4" fontWeight="bold">{userStats.xp}</Typography>
              <Typography variant="body2" color="text.secondary">Total XP</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Trophy size={32} color="#4caf50" />
              <Typography variant="h4" fontWeight="bold">{userStats.totalAchievements}</Typography>
              <Typography variant="body2" color="text.secondary">Achievements</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Target size={32} color="#2196f3" />
              <Typography variant="h4" fontWeight="bold">#{userStats.rank}</Typography>
              <Typography variant="body2" color="text.secondary">Global Rank</Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Level Progress */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="h6">Level Progress</Typography>
            <Typography variant="body2" color="text.secondary">
              {userStats.xp} / {userStats.nextLevelXp} XP
            </Typography>
          </Box>
          <LinearProgress 
            variant="determinate" 
            value={(userStats.xp / userStats.nextLevelXp) * 100} 
            sx={{ height: 10, borderRadius: 5 }}
          />
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            {userStats.nextLevelXp - userStats.xp} XP to next level
          </Typography>
        </CardContent>
      </Card>

      {/* Tabs for different sections */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="gamification tabs">
          <Tab label="Achievements" />
          <Tab label="Leaderboard" />
          <Tab label="Challenges" />
          <Tab label="Rewards" />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        <Grid container spacing={3}>
          {achievements.map((achievement) => (
            <Grid item xs={12} md={6} lg={4} key={achievement.id}>
              <Card sx={{ 
                opacity: achievement.unlocked ? 1 : 0.6,
                border: achievement.unlocked ? '2px solid #4caf50' : '1px solid #e0e0e0'
              }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Award size={24} color={achievement.unlocked ? '#4caf50' : '#9e9e9e'} />
                    <Box sx={{ ml: 2 }}>
                      <Typography variant="h6">{achievement.name}</Typography>
                      <Chip 
                        label={achievement.tier} 
                        size="small" 
                        color={achievement.tier === 'Legendary' ? 'warning' : 'primary'}
                      />
                    </Box>
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    +{achievement.xp} XP
                  </Typography>
                  {achievement.unlocked && (
                    <Chip label="Unlocked" color="success" size="small" sx={{ mt: 1 }} />
                  )}
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>Global Leaderboard</Typography>
            {leaderboard.map((user) => (
              <Box 
                key={user.rank} 
                sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  p: 2, 
                  borderBottom: '1px solid #e0e0e0',
                  backgroundColor: user.name === 'You' ? '#f3e5f5' : 'transparent'
                }}
              >
                <Typography variant="h6" sx={{ minWidth: 40 }}>#{user.rank}</Typography>
                <Avatar sx={{ mx: 2 }}>{user.name.charAt(0)}</Avatar>
                <Box sx={{ flexGrow: 1 }}>
                  <Typography variant="subtitle1">{user.name}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Level {user.level} • {user.archetype}
                  </Typography>
                </Box>
                <Typography variant="h6" color="primary">{user.xp} XP</Typography>
              </Box>
            ))}
          </CardContent>
        </Card>
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <Alert severity="info" sx={{ mb: 3 }}>
          <strong>Daily Challenges Available!</strong> Complete challenges to earn bonus XP and maintain your streak.
        </Alert>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>Submit 3 Bids Today</Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Submit at least 3 competitive bids to complete this challenge.
                </Typography>
                <LinearProgress variant="determinate" value={66} sx={{ mb: 2 }} />
                <Typography variant="body2">Progress: 2/3 bids</Typography>
                <Chip label="+150 XP" color="primary" size="small" sx={{ mt: 1 }} />
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>Perfect Compliance Score</Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Achieve 100% compliance score on any bid submission.
                </Typography>
                <Button variant="outlined" size="small">Start Challenge</Button>
                <Chip label="+300 XP" color="secondary" size="small" sx={{ mt: 1, ml: 1 }} />
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      <TabPanel value={tabValue} index={3}>
        <Alert severity="success" sx={{ mb: 3 }}>
          <strong>Psychological Benefits Active:</strong> Your achievements unlock real psychological rewards and platform perks.
        </Alert>
        <Typography variant="h6" gutterBottom>Available Rewards</Typography>
        <Typography variant="body1" color="text.secondary">
          Rewards system coming soon! Your achievements will unlock exclusive platform features, 
          priority support, and advanced psychological insights.
        </Typography>
      </TabPanel>
    </Container>
  );
}
