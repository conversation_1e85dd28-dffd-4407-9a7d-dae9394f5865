'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  LinearProgress,
  Tabs,
  Tab,
  Paper,
  Alert,
  Stack,
  Divider,
  IconButton,
  Avatar,
  AvatarGroup,
  Badge,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemAvatar,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Assessment as AnalysisIcon,
  Security as RiskIcon,
  Gavel as ComplianceIcon,
  TrendingUp as CompetitiveIcon,
  Assignment as TaskIcon,
  People as TeamIcon,
  Description as DocumentIcon,
  Timeline as ProgressIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Schedule as ClockIcon,
  Star as StarIcon,
  LocationOn as LocationIcon,
  AttachMoney as MoneyIcon,
  Chat as ChatIcon,
  VideoCall as MeetingIcon,
  Dashboard as BoardIcon,
  Approval as ApprovalIcon,
  Group as GroupIcon,
  Business as BusinessIcon
} from '@mui/icons-material';

import { TeamActiveWorkspace, TeamMember, UserRole } from '../../types/teamCollaboration';
import { BidWorkflowState } from '../../types/bidWorkflow';
import TeamCollaborationService from '../../services/TeamCollaborationService';
import BidWorkflowService from '../../services/BidWorkflowService';
import TenderCommunicationPanel from '../../components/communication/TenderCommunicationPanel';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`team-bids-tabpanel-${index}`}
      aria-labelledby={`team-bids-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

export default function TeamActiveBidsPage() {
  const [activeTab, setActiveTab] = useState(0);
  const [teamWorkspaces, setTeamWorkspaces] = useState<TeamActiveWorkspace[]>([]);
  const [selectedWorkspace, setSelectedWorkspace] = useState<TeamActiveWorkspace | null>(null);
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [isTeamMode, setIsTeamMode] = useState(true);

  const teamService = TeamCollaborationService.getInstance();
  const bidService = BidWorkflowService.getInstance();

  useEffect(() => {
    loadTeamWorkspaces();
    loadTeamMembers();
  }, []);

  const loadTeamWorkspaces = async () => {
    try {
      setLoading(true);
      // Mock organization ID - in real app, get from auth context
      const workspaces = await teamService.getTeamActiveWorkspaces('org-123');
      setTeamWorkspaces(workspaces);
      if (workspaces.length > 0 && !selectedWorkspace) {
        setSelectedWorkspace(workspaces[0]);
      }
    } catch (error) {
      console.error('Error loading team workspaces:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadTeamMembers = async () => {
    try {
      const members = await teamService.getTeamMembers('org-123');
      setTeamMembers(members);
    } catch (error) {
      console.error('Error loading team members:', error);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const getStateColor = (state: BidWorkflowState) => {
    switch (state) {
      case BidWorkflowState.INTERESTED: return 'info';
      case BidWorkflowState.ANALYZING: return 'warning';
      case BidWorkflowState.PREPARING: return 'primary';
      case BidWorkflowState.READY: return 'success';
      case BidWorkflowState.SUBMITTED: return 'success';
      default: return 'default';
    }
  };

  const getStateIcon = (state: BidWorkflowState) => {
    switch (state) {
      case BidWorkflowState.INTERESTED: return <StarIcon />;
      case BidWorkflowState.ANALYZING: return <AnalysisIcon />;
      case BidWorkflowState.PREPARING: return <TaskIcon />;
      case BidWorkflowState.READY: return <CheckIcon />;
      case BidWorkflowState.SUBMITTED: return <CheckIcon />;
      default: return <ClockIcon />;
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const getRoleColor = (role: UserRole) => {
    switch (role) {
      case 'owner': return 'error';
      case 'admin': return 'warning';
      case 'project_manager': return 'primary';
      case 'estimator': return 'info';
      case 'legal_counsel': return 'secondary';
      case 'technical_lead': return 'success';
      default: return 'default';
    }
  };

  const getTeamMembersByRole = (role: UserRole) => {
    return teamMembers.filter(member => member.role === role);
  };

  if (loading) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <LinearProgress sx={{ mb: 2 }} />
        <Typography>Loading team workspaces...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3, minHeight: '100vh', bgcolor: 'grey.50' }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main', mb: 1 }}>
              {isTeamMode ? '👥 Team Active Bids' : '🎯 My Active Bids'}
            </Typography>
            <Typography variant="body1" color="text.secondary">
              {isTeamMode 
                ? 'Collaborative workspace for team tender management with role-based coordination'
                : 'Personal workspace for individual tender management'
              }
            </Typography>
          </Box>
          <FormControlLabel
            control={
              <Switch
                checked={isTeamMode}
                onChange={(e) => setIsTeamMode(e.target.checked)}
                color="primary"
              />
            }
            label={isTeamMode ? "Team Mode" : "Individual Mode"}
          />
        </Box>

        {/* Team Overview */}
        {isTeamMode && (
          <Card elevation={1} sx={{ mb: 2 }}>
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12} md={3}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                    <BusinessIcon color="primary" />
                    <Typography variant="h6">Organization</Typography>
                  </Box>
                  <Typography variant="h5" sx={{ fontWeight: 600 }}>
                    BidBeez Construction
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    CIDB Grade 7 • B-BBEE Level 4
                  </Typography>
                </Grid>
                <Grid item xs={12} md={3}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                    <GroupIcon color="success" />
                    <Typography variant="h6">Team Size</Typography>
                  </Box>
                  <Typography variant="h5" sx={{ fontWeight: 600 }}>
                    {teamMembers.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active members
                  </Typography>
                </Grid>
                <Grid item xs={12} md={3}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                    <TaskIcon color="warning" />
                    <Typography variant="h6">Active Bids</Typography>
                  </Box>
                  <Typography variant="h5" sx={{ fontWeight: 600 }}>
                    {teamWorkspaces.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    In progress
                  </Typography>
                </Grid>
                <Grid item xs={12} md={3}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                    <TrendingUp color="info" />
                    <Typography variant="h6">Success Rate</Typography>
                  </Box>
                  <Typography variant="h5" sx={{ fontWeight: 600 }}>
                    73%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Last 6 months
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        )}
      </Box>

      {teamWorkspaces.length === 0 ? (
        <Box sx={{ textAlign: 'center', py: 8 }}>
          <TeamIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h5" color="text.secondary" gutterBottom>
            No Active Team Bids
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
            Your team hasn't expressed interest in any tenders yet. Check notifications for new opportunities!
          </Typography>
          <Button variant="contained" href="/notifications">
            View Notifications
          </Button>
        </Box>
      ) : (
        <Grid container spacing={3}>
          {/* Left Panel - Workspace List */}
          <Grid item xs={12} md={4}>
            <Paper elevation={2} sx={{ height: 'fit-content' }}>
              <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
                <Typography variant="h6">
                  {isTeamMode ? 'Team Workspaces' : 'Active Bids'} ({teamWorkspaces.length})
                </Typography>
              </Box>
              <List sx={{ p: 0 }}>
                {teamWorkspaces.map((workspace, index) => (
                  <React.Fragment key={workspace.teamBidInterest.id}>
                    <ListItem
                      button
                      selected={selectedWorkspace?.teamBidInterest.id === workspace.teamBidInterest.id}
                      onClick={() => setSelectedWorkspace(workspace)}
                      sx={{ py: 2 }}
                    >
                      <ListItemIcon>
                        <Badge
                          badgeContent={workspace.teamBidInterest.commitmentLevel}
                          color="primary"
                          max={100}
                        >
                          {getStateIcon(workspace.teamBidInterest.state)}
                        </Badge>
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                            {workspace.tender.title || 'Municipal Infrastructure Project'}
                          </Typography>
                        }
                        secondary={
                          <Box>
                            <Typography variant="caption" color="text.secondary" display="block">
                              {workspace.tender.organization || 'City of Johannesburg'}
                            </Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5, mb: 1 }}>
                              <Chip
                                label={workspace.teamBidInterest.state.replace('_', ' ').toUpperCase()}
                                size="small"
                                color={getStateColor(workspace.teamBidInterest.state) as any}
                                variant="outlined"
                              />
                            </Box>
                            {isTeamMode && (
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <AvatarGroup max={3} sx={{ '& .MuiAvatar-root': { width: 20, height: 20, fontSize: '0.75rem' } }}>
                                  {workspace.teamBidInterest.teamMembers.slice(0, 3).map((member, idx) => (
                                    <Avatar key={idx}>{member.memberId.charAt(0)}</Avatar>
                                  ))}
                                </AvatarGroup>
                                <Typography variant="caption" color="text.secondary">
                                  {workspace.teamBidInterest.teamMembers.length} members
                                </Typography>
                              </Box>
                            )}
                          </Box>
                        }
                      />
                    </ListItem>
                    {index < teamWorkspaces.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </Paper>
          </Grid>

          {/* Right Panel - Detailed Workspace */}
          <Grid item xs={12} md={8}>
            {selectedWorkspace && (
              <Paper elevation={2}>
                {/* Workspace Header */}
                <Box sx={{ p: 3, borderBottom: 1, borderColor: 'divider' }}>
                  <Grid container spacing={2} alignItems="center">
                    <Grid item xs={12} md={8}>
                      <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
                        {selectedWorkspace.tender.title || 'Municipal Infrastructure Development Project'}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        {selectedWorkspace.tender.organization || 'City of Johannesburg'} • 
                        {selectedWorkspace.tender.location || 'Johannesburg, GP'}
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                        <Chip
                          icon={getStateIcon(selectedWorkspace.teamBidInterest.state)}
                          label={selectedWorkspace.teamBidInterest.state.replace('_', ' ').toUpperCase()}
                          color={getStateColor(selectedWorkspace.teamBidInterest.state) as any}
                        />
                        <Chip
                          icon={<MoneyIcon />}
                          label={formatCurrency(selectedWorkspace.tender.estimatedValue || 15600000)}
                          variant="outlined"
                        />
                        {isTeamMode && (
                          <Chip
                            icon={<TeamIcon />}
                            label={`${selectedWorkspace.teamBidInterest.teamMembers.length} Team Members`}
                            variant="outlined"
                          />
                        )}
                      </Box>
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <Box sx={{ textAlign: 'right' }}>
                        <Typography variant="h4" color="primary.main" sx={{ fontWeight: 600 }}>
                          {selectedWorkspace.teamBidInterest.commitmentLevel}%
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {isTeamMode ? 'Team Commitment' : 'Commitment Level'}
                        </Typography>
                        <LinearProgress
                          variant="determinate"
                          value={selectedWorkspace.teamBidInterest.commitmentLevel}
                          sx={{ mt: 1, height: 8, borderRadius: 4 }}
                        />
                      </Box>
                    </Grid>
                  </Grid>
                </Box>

                {/* Enhanced Tabs for Team Mode */}
                <Tabs value={activeTab} onChange={handleTabChange} variant="scrollable">
                  <Tab icon={<AnalysisIcon />} label="Team Analysis" />
                  <Tab icon={<TeamIcon />} label="Team Members" />
                  <Tab icon={<BoardIcon />} label="Task Board" />
                  <Tab icon={<ChatIcon />} label="Discussion" />
                  <Tab icon={<MeetingIcon />} label="Meetings" />
                  <Tab icon={<DocumentIcon />} label="Documents" />
                  <Tab icon={<ApprovalIcon />} label="Approvals" />
                  <Tab icon={<ProgressIcon />} label="Progress" />
                </Tabs>

                {/* Tab Content */}
                <TabPanel value={activeTab} index={0}>
                  {/* Team Analysis Tab */}
                  <Typography variant="h6" gutterBottom>
                    Collaborative Tender Analysis
                  </Typography>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Card variant="outlined">
                        <CardContent>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                            <AnalysisIcon color="primary" />
                            <Typography variant="h6">Team Consensus</Typography>
                          </Box>
                          <Box sx={{ textAlign: 'center', mb: 2 }}>
                            <Typography variant="h3" color="primary.main" sx={{ fontWeight: 600 }}>
                              {selectedWorkspace.teamAnalysis.consensusAnalysis.consensusLevel}%
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Team Agreement Level
                            </Typography>
                          </Box>
                          <LinearProgress
                            variant="determinate"
                            value={selectedWorkspace.teamAnalysis.consensusAnalysis.consensusLevel}
                            sx={{ height: 8, borderRadius: 4 }}
                          />
                        </CardContent>
                      </Card>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Card variant="outlined">
                        <CardContent>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                            <CompetitiveIcon color="success" />
                            <Typography variant="h6">Team Recommendation</Typography>
                          </Box>
                          <Box sx={{ textAlign: 'center', mb: 2 }}>
                            <Chip
                              label={selectedWorkspace.teamAnalysis.consensusAnalysis.majorityRecommendation.replace('_', ' ').toUpperCase()}
                              color="primary"
                              sx={{ fontSize: '1rem', py: 2, px: 3 }}
                            />
                          </Box>
                          <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center' }}>
                            Based on {selectedWorkspace.teamAnalysis.individualAnalyses.length} team member analyses
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                  </Grid>

                  {selectedWorkspace.teamBidInterest.analysisCompleted ? (
                    <Alert severity="success" sx={{ mt: 2 }}>
                      <Typography variant="body2">
                        ✅ Team analysis completed! Review consensus results and proceed to task assignment.
                      </Typography>
                    </Alert>
                  ) : (
                    <Alert severity="info" sx={{ mt: 2 }}>
                      <Typography variant="body2">
                        🤖 Team analysis in progress... Waiting for all team members to complete their individual analyses.
                      </Typography>
                    </Alert>
                  )}
                </TabPanel>

                <TabPanel value={activeTab} index={1}>
                  {/* Team Members Tab */}
                  <Typography variant="h6" gutterBottom>
                    Team Member Assignments
                  </Typography>
                  <Grid container spacing={2}>
                    {selectedWorkspace.teamBidInterest.teamMembers.map((assignment) => {
                      const member = teamMembers.find(m => m.id === assignment.memberId);
                      return (
                        <Grid item xs={12} md={6} key={assignment.memberId}>
                          <Card variant="outlined">
                            <CardContent>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                                <Avatar>{member?.title.charAt(0) || 'U'}</Avatar>
                                <Box>
                                  <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                                    {member?.title || 'Team Member'}
                                  </Typography>
                                  <Chip
                                    label={assignment.role.replace('_', ' ').toUpperCase()}
                                    size="small"
                                    color={getRoleColor(assignment.role) as any}
                                    variant="outlined"
                                  />
                                </Box>
                                <Box sx={{ ml: 'auto', textAlign: 'right' }}>
                                  <Typography variant="body2" color="text.secondary">
                                    {assignment.workloadImpact}% workload
                                  </Typography>
                                  <Chip
                                    label={assignment.status.replace('_', ' ').toUpperCase()}
                                    size="small"
                                    color={assignment.status === 'accepted' ? 'success' : 'default'}
                                  />
                                </Box>
                              </Box>
                              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                                Responsibilities:
                              </Typography>
                              <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                                {assignment.responsibilities.slice(0, 2).map((resp, idx) => (
                                  <Chip key={idx} label={resp} size="small" variant="outlined" />
                                ))}
                                {assignment.responsibilities.length > 2 && (
                                  <Chip label={`+${assignment.responsibilities.length - 2} more`} size="small" />
                                )}
                              </Box>
                            </CardContent>
                          </Card>
                        </Grid>
                      );
                    })}
                  </Grid>

                  {selectedWorkspace.teamBidInterest.teamMembers.length === 0 && (
                    <Alert severity="info">
                      <Typography variant="body2">
                        👥 No team members assigned yet. Assign team members to start collaborative bid preparation.
                      </Typography>
                    </Alert>
                  )}
                </TabPanel>

                <TabPanel value={activeTab} index={2}>
                  {/* Task Board Tab */}
                  <Typography variant="h6" gutterBottom>
                    Team Task Board
                  </Typography>
                  <Alert severity="info">
                    <Typography variant="body2">
                      📋 Kanban-style task board for collaborative bid preparation will be available here.
                    </Typography>
                  </Alert>
                </TabPanel>

                <TabPanel value={activeTab} index={3}>
                  {/* Discussion Tab */}
                  <TenderCommunicationPanel teamWorkspace={selectedWorkspace} />
                </TabPanel>

                <TabPanel value={activeTab} index={4}>
                  {/* Meetings Tab */}
                  <Typography variant="h6" gutterBottom>
                    Team Meetings
                  </Typography>
                  <Alert severity="info">
                    <Typography variant="body2">
                      📅 Meeting scheduling, video calls, and meeting minutes will be available here.
                    </Typography>
                  </Alert>
                </TabPanel>

                <TabPanel value={activeTab} index={5}>
                  {/* Documents Tab */}
                  <Typography variant="h6" gutterBottom>
                    Collaborative Documents
                  </Typography>
                  <Alert severity="info">
                    <Typography variant="body2">
                      📄 Real-time collaborative document editing and version control will be available here.
                    </Typography>
                  </Alert>
                </TabPanel>

                <TabPanel value={activeTab} index={6}>
                  {/* Approvals Tab */}
                  <Typography variant="h6" gutterBottom>
                    Approval Workflow
                  </Typography>
                  <Alert severity="info">
                    <Typography variant="body2">
                      ✅ Multi-step approval workflow with role-based permissions will be available here.
                    </Typography>
                  </Alert>
                </TabPanel>

                <TabPanel value={activeTab} index={7}>
                  {/* Progress Tab */}
                  <Typography variant="h6" gutterBottom>
                    Team Progress Tracking
                  </Typography>
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      Overall Team Progress: {selectedWorkspace.teamProgress.overallProgress}%
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={selectedWorkspace.teamProgress.overallProgress}
                      sx={{ height: 12, borderRadius: 6 }}
                    />
                  </Box>

                  <Alert severity="info">
                    <Typography variant="body2">
                      📊 Detailed team progress tracking, milestone management, and performance metrics will be available here.
                    </Typography>
                  </Alert>
                </TabPanel>

                {/* Action Buttons */}
                <Box sx={{ p: 3, borderTop: 1, borderColor: 'divider', bgcolor: 'grey.50' }}>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={4}>
                      <Button
                        variant="outlined"
                        fullWidth
                        startIcon={<TeamIcon />}
                        onClick={() => setActiveTab(1)}
                      >
                        Manage Team
                      </Button>
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <Button
                        variant="outlined"
                        fullWidth
                        startIcon={<BoardIcon />}
                        onClick={() => setActiveTab(2)}
                        disabled={selectedWorkspace.teamBidInterest.teamMembers.length === 0}
                      >
                        Open Task Board
                      </Button>
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <Button
                        variant="contained"
                        fullWidth
                        startIcon={<TaskIcon />}
                        disabled={selectedWorkspace.teamBidInterest.state !== BidWorkflowState.PREPARING}
                      >
                        Start Team Preparation
                      </Button>
                    </Grid>
                  </Grid>
                </Box>
              </Paper>
            )}
          </Grid>
        </Grid>
      )}
    </Box>
  );
}
