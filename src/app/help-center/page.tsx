'use client';

import React from 'react';
import { Container, Typo<PERSON>, Card, CardContent, Grid, Button, Box, TextField } from '@mui/material';
import { Help, Search, ContactSupport, Book } from '@mui/icons-material';

const helpTopics = [
  { title: "Getting Started", description: "Learn the basics of using BidBeez platform" },
  { title: "Bid Management", description: "How to create and manage your bids effectively" },
  { title: "AI Features", description: "Understanding and using AI-powered tools" },
  { title: "Troubleshooting", description: "Common issues and their solutions" }
];

export default function HelpCenterPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
        Help Center
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        Find answers and get support for all your questions
      </Typography>
      
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <TextField 
              fullWidth 
              placeholder="Search for help articles..." 
              variant="outlined"
            />
            <Button variant="contained" startIcon={<Search />}>
              Search
            </Button>
          </Box>
        </CardContent>
      </Card>

      <Grid container spacing={3}>
        {helpTopics.map((topic, index) => (
          <Grid item xs={12} md={6} key={index}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>{topic.title}</Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {topic.description}
                </Typography>
                <Button variant="outlined" size="small" startIcon={<Book />}>
                  View Articles
                </Button>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Need More Help?</Typography>
          <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
            <Button variant="contained" startIcon={<ContactSupport />}>
              Contact Support
            </Button>
            <Button variant="outlined" startIcon={<Help />}>
              Live Chat
            </Button>
          </Box>
        </CardContent>
      </Card>
    </Container>
  );
}
