'use client';

import React from 'react';
import { Container, Typography, Card, CardContent, Grid, Button, Box, Chip, Table, TableBody, TableCell, TableHead, TableRow } from '@mui/material';
import { Assignment, Edit, Visibility, Download } from '@mui/icons-material';

const contracts = [
  { id: 1, title: "Municipal IT Services", value: "R890K", status: "Active", startDate: "2024-01-01", endDate: "2024-12-31" },
  { id: 2, title: "Office Supplies Contract", value: "R125K", status: "Pending", startDate: "2024-02-01", endDate: "2024-07-31" },
  { id: 3, title: "Construction Services", value: "R2.4M", status: "Completed", startDate: "2023-06-01", endDate: "2023-12-31" }
];

export default function ContractManagementPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
        Contract Management
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        Manage and track all your contracts and agreements
      </Typography>
      
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" fontWeight="bold" color="primary">
                12
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Active Contracts
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" fontWeight="bold" color="success.main">
                R3.2M
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Value
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Contract Overview</Typography>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Contract Title</TableCell>
                <TableCell>Value</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Start Date</TableCell>
                <TableCell>End Date</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {contracts.map((contract) => (
                <TableRow key={contract.id}>
                  <TableCell>{contract.title}</TableCell>
                  <TableCell fontWeight="bold">{contract.value}</TableCell>
                  <TableCell>
                    <Chip 
                      label={contract.status}
                      color={contract.status === 'Active' ? 'success' : contract.status === 'Pending' ? 'warning' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>{contract.startDate}</TableCell>
                  <TableCell>{contract.endDate}</TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Button size="small" startIcon={<Visibility />}>View</Button>
                      <Button size="small" startIcon={<Edit />}>Edit</Button>
                      <Button size="small" startIcon={<Download />}>Download</Button>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </Container>
  );
}
