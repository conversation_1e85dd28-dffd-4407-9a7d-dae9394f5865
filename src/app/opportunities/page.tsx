'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Grid,
  Chip,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Alert,
  Stack,
  IconButton,
  Tooltip,
  LinearProgress,
  Divider
} from '@mui/material';
import {
  Search,
  FilterList,
  TrendingUp,
  Psychology,
  FlashOn,
  Warning,
  CheckCircle,
  Schedule,
  LocationOn,
  Business,
  AttachMoney,
  Refresh
} from '@mui/icons-material';
import { useRouter } from 'next/navigation';

interface UnifiedOpportunity {
  id: string;
  opportunity_type: 'tender' | 'government_rfq' | 'bidder_rfq';
  title: string;
  description: string;
  organization: string;
  estimated_value: number;
  closing_date: string;
  category: string;
  province: string;
  status: 'active' | 'closing_soon' | 'closed';
  submission_type: string;
  match_score: number;
  success_probability: number;
  competition_level: string;
  ai_recommendation: 'bid' | 'create_rfq' | 'watch' | 'skip';
  portfolio_category: 'rfq_activity' | 'tender_activity';
  balance_impact: {
    improvement: number;
    moves_toward_target: boolean;
  };
}

interface PortfolioBalance {
  current_rfq_ratio: number;
  current_tender_ratio: number;
  target_rfq_ratio: number;
  target_tender_ratio: number;
  balance_status: string;
  urgency_level: string;
  missed_earnings: number;
}

interface PsychologicalTrigger {
  type: string;
  urgency: string;
  message: string;
  action: string;
  psychological_principle: string;
}

const UnifiedOpportunitiesPage: React.FC = () => {
  const router = useRouter();
  
  // State management
  const [opportunities, setOpportunities] = useState<UnifiedOpportunity[]>([]);
  const [portfolioBalance, setPortfolioBalance] = useState<PortfolioBalance | null>(null);
  const [psychologicalTriggers, setPsychologicalTriggers] = useState<PsychologicalTrigger[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  
  // Filter state
  const [filters, setFilters] = useState({
    search: '',
    category: '',
    province: '',
    opportunity_types: [] as string[],
    status: 'active',
    max_value: ''
  });

  // Load opportunities and portfolio data
  useEffect(() => {
    loadOpportunities();
  }, [filters]);

  const loadOpportunities = async () => {
    try {
      setLoading(true);
      
      // Build query parameters
      const params = new URLSearchParams();
      if (filters.status) params.append('status', filters.status);
      if (filters.category) params.append('category', filters.category);
      if (filters.province) params.append('province', filters.province);
      if (filters.max_value) params.append('max_value', filters.max_value);
      if (filters.opportunity_types.length > 0) {
        params.append('opportunity_types', filters.opportunity_types.join(','));
      }
      
      const response = await fetch(`/api/opportunities?${params.toString()}`);
      
      if (response.ok) {
        const data = await response.json();
        setOpportunities(data.opportunities);
        setPortfolioBalance(data.portfolio_balance);
        setPsychologicalTriggers(data.psychological_triggers);
      } else {
        console.error('Failed to load opportunities');
      }
    } catch (error) {
      console.error('Error loading opportunities:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadOpportunities();
    setRefreshing(false);
  };

  const handleOpportunityAction = (opportunity: UnifiedOpportunity) => {
    switch (opportunity.ai_recommendation) {
      case 'bid':
        if (opportunity.opportunity_type === 'tender') {
          router.push(`/bids/create?tender=${opportunity.id}`);
        } else if (opportunity.opportunity_type === 'government_rfq') {
          router.push(`/government-rfqs/${opportunity.id}/respond`);
        } else {
          router.push(`/rfqs/${opportunity.id}/respond`);
        }
        break;
      case 'create_rfq':
        router.push('/rfq/create');
        break;
      case 'watch':
        router.push(`/opportunities/${opportunity.id}?type=${opportunity.opportunity_type}`);
        break;
      default:
        console.log('No action for:', opportunity.ai_recommendation);
    }
  };

  const handleTriggerAction = (trigger: PsychologicalTrigger) => {
    switch (trigger.action) {
      case 'create_rfq_or_bid_government_rfq':
        router.push('/rfq/create');
        break;
      case 'review_closing_opportunities':
        setFilters(prev => ({ ...prev, status: 'closing_soon' }));
        break;
      case 'bid_high_success_rfqs':
        setFilters(prev => ({ 
          ...prev, 
          opportunity_types: ['government_rfq', 'bidder_rfq'] 
        }));
        break;
      default:
        console.log('No action for trigger:', trigger.action);
    }
  };

  const getOpportunityTypeColor = (type: string) => {
    switch (type) {
      case 'tender': return '#2196F3';
      case 'government_rfq': return '#4CAF50';
      case 'bidder_rfq': return '#FF9800';
      default: return '#757575';
    }
  };

  const getOpportunityTypeLabel = (type: string) => {
    switch (type) {
      case 'tender': return 'TENDER';
      case 'government_rfq': return 'GOV RFQ';
      case 'bidder_rfq': return 'RFQ';
      default: return type.toUpperCase();
    }
  };

  const getRecommendationColor = (recommendation: string) => {
    switch (recommendation) {
      case 'bid': return '#4CAF50';
      case 'create_rfq': return '#FF9800';
      case 'watch': return '#2196F3';
      case 'skip': return '#757575';
      default: return '#757575';
    }
  };

  const getRecommendationText = (recommendation: string) => {
    switch (recommendation) {
      case 'bid': return 'BID NOW';
      case 'create_rfq': return 'CREATE RFQ';
      case 'watch': return 'WATCH';
      case 'skip': return 'SKIP';
      default: return recommendation.toUpperCase();
    }
  };

  const formatCurrency = (value: number) => {
    if (value >= 1000000) return `R${(value / 1000000).toFixed(1)}M`;
    if (value >= 1000) return `R${(value / 1000).toFixed(0)}k`;
    return `R${value}`;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) return 'CLOSED';
    if (diffDays === 0) return 'TODAY';
    if (diffDays === 1) return 'TOMORROW';
    if (diffDays <= 7) return `${diffDays} DAYS`;
    return date.toLocaleDateString();
  };

  return (
    <Box sx={{ p: 3, maxWidth: 1400, mx: 'auto' }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold' }}>
          🎯 Unified Opportunities
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          All bidding opportunities in one place - Tenders, Government RFQs, and Bidder RFQs
        </Typography>
      </Box>

      {/* Portfolio Balance Alert */}
      {portfolioBalance && (
        <Alert 
          severity={portfolioBalance.urgency_level === 'critical' ? 'error' : 'warning'}
          sx={{ mb: 3 }}
          action={
            <Button 
              color="inherit" 
              size="small"
              onClick={() => router.push('/portfolio/optimize')}
            >
              OPTIMIZE
            </Button>
          }
        >
          <Typography variant="body2">
            <strong>Portfolio Balance:</strong> {portfolioBalance.current_rfq_ratio.toFixed(1)}% RFQ / {portfolioBalance.current_tender_ratio.toFixed(1)}% Tender 
            (Target: {portfolioBalance.target_rfq_ratio}% / {portfolioBalance.target_tender_ratio}%)
            {portfolioBalance.missed_earnings > 0 && (
              <span style={{ color: '#f44336', marginLeft: 8 }}>
                Missed Earnings: {formatCurrency(portfolioBalance.missed_earnings)}
              </span>
            )}
          </Typography>
        </Alert>
      )}

      {/* Psychological Triggers */}
      {psychologicalTriggers.length > 0 && (
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Psychology color="primary" />
            AI Insights
          </Typography>
          <Stack spacing={1}>
            {psychologicalTriggers.map((trigger, index) => (
              <Alert 
                key={index}
                severity={trigger.urgency === 'critical' ? 'error' : trigger.urgency === 'high' ? 'warning' : 'info'}
                action={
                  <Button 
                    color="inherit" 
                    size="small"
                    onClick={() => handleTriggerAction(trigger)}
                  >
                    ACT NOW
                  </Button>
                }
              >
                {trigger.message}
              </Alert>
            ))}
          </Stack>
        </Box>
      )}

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                placeholder="Search opportunities..."
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                InputProps={{
                  startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>
            
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Category</InputLabel>
                <Select
                  value={filters.category}
                  onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
                >
                  <MenuItem value="">All Categories</MenuItem>
                  <MenuItem value="IT Services">IT Services</MenuItem>
                  <MenuItem value="Construction">Construction</MenuItem>
                  <MenuItem value="Office Supplies">Office Supplies</MenuItem>
                  <MenuItem value="Security">Security</MenuItem>
                  <MenuItem value="Professional Services">Professional Services</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Province</InputLabel>
                <Select
                  value={filters.province}
                  onChange={(e) => setFilters(prev => ({ ...prev, province: e.target.value }))}
                >
                  <MenuItem value="">All Provinces</MenuItem>
                  <MenuItem value="Gauteng">Gauteng</MenuItem>
                  <MenuItem value="Western Cape">Western Cape</MenuItem>
                  <MenuItem value="KwaZulu-Natal">KwaZulu-Natal</MenuItem>
                  <MenuItem value="Eastern Cape">Eastern Cape</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.status}
                  onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                >
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="closing_soon">Closing Soon</MenuItem>
                  <MenuItem value="">All Status</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={1}>
              <Tooltip title="Refresh Opportunities">
                <IconButton 
                  onClick={handleRefresh} 
                  disabled={refreshing}
                  sx={{ 
                    animation: refreshing ? 'spin 1s linear infinite' : 'none',
                    '@keyframes spin': {
                      '0%': { transform: 'rotate(0deg)' },
                      '100%': { transform: 'rotate(360deg)' }
                    }
                  }}
                >
                  <Refresh />
                </IconButton>
              </Tooltip>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Loading State */}
      {loading && (
        <Box sx={{ mb: 3 }}>
          <LinearProgress />
          <Typography variant="body2" sx={{ mt: 1, textAlign: 'center' }}>
            Loading opportunities...
          </Typography>
        </Box>
      )}

      {/* Opportunities Grid */}
      <Grid container spacing={3}>
        {opportunities.map((opportunity) => (
          <Grid item xs={12} md={6} lg={4} key={opportunity.id}>
            <Card 
              sx={{ 
                height: '100%',
                border: `2px solid ${getOpportunityTypeColor(opportunity.opportunity_type)}40`,
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: 6
                },
                transition: 'all 0.3s ease'
              }}
            >
              <CardContent>
                {/* Header */}
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Chip 
                    label={getOpportunityTypeLabel(opportunity.opportunity_type)}
                    sx={{ 
                      backgroundColor: getOpportunityTypeColor(opportunity.opportunity_type),
                      color: 'white',
                      fontWeight: 'bold'
                    }}
                  />
                  <Chip 
                    label={`${opportunity.match_score}% MATCH`}
                    size="small"
                    color={opportunity.match_score > 80 ? 'success' : opportunity.match_score > 60 ? 'warning' : 'default'}
                  />
                </Box>

                {/* Title and Organization */}
                <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold', minHeight: 48 }}>
                  {opportunity.title}
                </Typography>
                
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <Business fontSize="small" color="action" />
                  <Typography variant="body2" color="text.secondary">
                    {opportunity.organization}
                  </Typography>
                </Box>

                {/* Key Metrics */}
                <Grid container spacing={1} sx={{ mb: 2 }}>
                  <Grid item xs={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      <AttachMoney fontSize="small" color="action" />
                      <Typography variant="body2">
                        {formatCurrency(opportunity.estimated_value)}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      <Schedule fontSize="small" color="action" />
                      <Typography variant="body2">
                        {formatDate(opportunity.closing_date)}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      <LocationOn fontSize="small" color="action" />
                      <Typography variant="body2">
                        {opportunity.province}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      <TrendingUp fontSize="small" color="action" />
                      <Typography variant="body2">
                        {opportunity.success_probability}% Success
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>

                {/* Portfolio Impact */}
                {opportunity.balance_impact.moves_toward_target && (
                  <Alert severity="success" sx={{ mb: 2, py: 0 }}>
                    <Typography variant="caption">
                      ✅ Improves portfolio balance (+{opportunity.balance_impact.improvement.toFixed(1)}%)
                    </Typography>
                  </Alert>
                )}

                <Divider sx={{ my: 2 }} />

                {/* Action Button */}
                <Button
                  variant="contained"
                  fullWidth
                  onClick={() => handleOpportunityAction(opportunity)}
                  sx={{
                    backgroundColor: getRecommendationColor(opportunity.ai_recommendation),
                    fontWeight: 'bold',
                    py: 1.5,
                    '&:hover': {
                      backgroundColor: getRecommendationColor(opportunity.ai_recommendation),
                      opacity: 0.8
                    }
                  }}
                  startIcon={
                    opportunity.ai_recommendation === 'bid' ? <FlashOn /> :
                    opportunity.ai_recommendation === 'create_rfq' ? <CheckCircle /> :
                    <Schedule />
                  }
                >
                  {getRecommendationText(opportunity.ai_recommendation)}
                  {opportunity.ai_recommendation === 'bid' && opportunity.opportunity_type === 'government_rfq' && (
                    <Chip 
                      label="88% SUCCESS" 
                      size="small" 
                      sx={{ ml: 1, backgroundColor: 'rgba(255,255,255,0.2)', color: 'white' }}
                    />
                  )}
                </Button>

                {/* Success Rate Boost */}
                {opportunity.opportunity_type === 'government_rfq' && (
                  <Typography variant="caption" color="success.main" sx={{ mt: 1, display: 'block', textAlign: 'center' }}>
                    🚀 Government RFQs have 88% success rate!
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Empty State */}
      {!loading && opportunities.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 8 }}>
          <Typography variant="h6" color="text.secondary" gutterBottom>
            No opportunities found
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Try adjusting your filters or check back later for new opportunities
          </Typography>
          <Button 
            variant="contained" 
            onClick={() => setFilters({
              search: '',
              category: '',
              province: '',
              opportunity_types: [],
              status: 'active',
              max_value: ''
            })}
          >
            Clear Filters
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default UnifiedOpportunitiesPage;
