'use client';

import React, { useState } from 'react';
import {
  Box,
  Container,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  Button,
  Stack,
  Alert,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Tab,
  Tabs,
  LinearProgress,
  Avatar,
  Stepper,
  Step,
  StepLabel,
  StepContent
} from '@mui/material';
import {
  Verified,
  Security,
  Assignment,
  School,
  LocationOn,
  Phone,
  Email,
  CreditCard,
  Fingerprint,
  Badge,
  CheckCircle,
  Warning,
  Info,
  Star,
  Shield,
  Gavel,
  HealthAndSafety,
  DirectionsCar,
  Camera
} from '@mui/icons-material';

interface VerificationCheck {
  id: number;
  bee_id: number;
  verification_type: string;
  verification_method: string;
  status: 'pending' | 'verified' | 'failed' | 'expired';
  confidence_level: number;
  verification_score: number;
  ai_analysis_result: string | null;
  fraud_indicators: string | null;
  verification_provider: string | null;
  document_type: string | null;
  document_number: string | null;
  document_expiry_date: string | null;
  verified_at: string | null;
  expires_at: string | null;
  created_at: string;
  updated_at: string;
}

interface BeeWorkerProfile {
  beeId: string;
  fullName: string;
  photo: string;
  overallTrustScore: number;
  verificationLevel: 'basic' | 'standard' | 'premium' | 'elite';
  joinDate: string;
  totalTasks: number;
  successRate: number;
  clientRating: number;
}

export default function BeeVerificationPage() {
  const [currentTab, setCurrentTab] = useState(0);

  const [beeProfile, setBeeProfile] = useState<BeeWorkerProfile>({
    beeId: 'BEE-JHB-001',
    fullName: 'Sarah Mthembu',
    photo: '/avatars/bee-worker.jpg',
    overallTrustScore: 96,
    verificationLevel: 'premium',
    joinDate: '2023-08-15',
    totalTasks: 47,
    successRate: 98,
    clientRating: 4.8
  });

  const [verificationChecks, setVerificationChecks] = useState<VerificationCheck[]>([
    {
      id: 1,
      bee_id: 1,
      verification_type: 'identity',
      verification_method: 'biometric_id_check',
      status: 'verified',
      confidence_level: 98,
      verification_score: 95,
      ai_analysis_result: '{"face_match": 0.98, "document_authenticity": 0.96, "liveness_check": 0.99}',
      fraud_indicators: null,
      verification_provider: 'home_affairs_db',
      document_type: 'south_african_id',
      document_number: '9001015678901',
      document_expiry_date: null,
      verified_at: '2023-08-15T10:30:00Z',
      expires_at: null,
      created_at: '2023-08-15T09:00:00Z',
      updated_at: '2023-08-15T10:30:00Z'
    },
    {
      id: 'criminal',
      category: 'Background Screening',
      name: 'Criminal Background Check',
      description: 'Comprehensive criminal history check through SAPS database',
      status: 'verified',
      verificationMethod: 'SAPS Database Check',
      completedDate: '2023-08-16',
      verifiedBy: 'South African Police Service',
      trustScore: 100,
      documents: ['Police Clearance Certificate', 'Fingerprint Verification']
    },
    {
      id: 'credit',
      category: 'Financial Verification',
      name: 'Credit & Financial Check',
      description: 'Credit history and financial standing verification',
      status: 'verified',
      verificationMethod: 'Credit Bureau Check',
      completedDate: '2023-08-17',
      verifiedBy: 'TransUnion Credit Bureau',
      trustScore: 88,
      documents: ['Credit Report', 'Bank Statement Verification']
    },
    {
      id: 'address',
      category: 'Address Verification',
      name: 'Residential Address Verification',
      description: 'Physical address verification through multiple sources',
      status: 'verified',
      verificationMethod: 'Multi-Source Verification',
      completedDate: '2023-08-18',
      verifiedBy: 'Address Verification Service',
      trustScore: 92,
      documents: ['Utility Bill', 'Municipal Account', 'GPS Verification']
    },
    {
      id: 'education',
      category: 'Educational Verification',
      name: 'Educational Qualifications',
      description: 'Academic qualifications verified with institutions',
      status: 'verified',
      verificationMethod: 'Institution Direct Verification',
      completedDate: '2023-08-20',
      verifiedBy: 'University of Witwatersrand',
      trustScore: 90,
      documents: ['Matric Certificate', 'Diploma Certificate', 'Academic Transcript']
    },
    {
      id: 'employment',
      category: 'Employment History',
      name: 'Previous Employment Verification',
      description: 'Employment history and references verified',
      status: 'verified',
      verificationMethod: 'Employer Reference Check',
      completedDate: '2023-08-22',
      verifiedBy: 'Previous Employers',
      trustScore: 94,
      documents: ['Employment Letters', 'Reference Contacts', 'Payslip Verification']
    },
    {
      id: 'driving',
      category: 'Licensing & Permits',
      name: 'Driving License Verification',
      description: 'Valid driving license verified with traffic department',
      status: 'verified',
      verificationMethod: 'Traffic Department Database',
      completedDate: '2023-08-19',
      verifiedBy: 'Department of Transport',
      trustScore: 98,
      documents: ['Driving License', 'Traffic Fine History', 'License Validity Check']
    },
    {
      id: 'health',
      category: 'Health & Safety',
      name: 'Medical Fitness Certificate',
      description: 'Medical fitness for field work verified by registered practitioner',
      status: 'verified',
      verificationMethod: 'Medical Examination',
      completedDate: '2023-08-25',
      expiryDate: '2024-08-25',
      verifiedBy: 'Dr. John Smith (HPCSA Registered)',
      trustScore: 96,
      documents: ['Medical Certificate', 'Vision Test', 'Physical Fitness Assessment']
    },
    {
      id: 'training',
      category: 'Professional Training',
      name: 'BidBeez Training Certification',
      description: 'Completed comprehensive BidBeez training program',
      status: 'verified',
      verificationMethod: 'Training Assessment',
      completedDate: '2023-09-01',
      verifiedBy: 'BidBeez Training Academy',
      trustScore: 97,
      documents: ['Training Certificate', 'Assessment Results', 'Practical Evaluation']
    },
    {
      id: 'insurance',
      category: 'Insurance & Bonding',
      name: 'Professional Indemnity Insurance',
      description: 'Professional liability insurance coverage verified',
      status: 'verified',
      verificationMethod: 'Insurance Provider Verification',
      completedDate: '2023-09-05',
      expiryDate: '2024-09-05',
      verifiedBy: 'Santam Insurance',
      trustScore: 100,
      documents: ['Insurance Certificate', 'Coverage Details', 'Premium Payment Proof']
    }
  ]);

  const verificationSteps = [
    {
      label: 'Identity Verification',
      description: 'Government ID and biometric verification',
      checks: ['Government ID', 'Biometric Scan', 'Liveness Detection']
    },
    {
      label: 'Background Screening',
      description: 'Criminal and financial background checks',
      checks: ['Criminal History', 'Credit Check', 'Reference Verification']
    },
    {
      label: 'Professional Verification',
      description: 'Education, employment, and licensing verification',
      checks: ['Educational Qualifications', 'Employment History', 'Professional Licenses']
    },
    {
      label: 'Health & Safety Certification',
      description: 'Medical fitness and safety training verification',
      checks: ['Medical Certificate', 'Safety Training', 'Insurance Coverage']
    },
    {
      label: 'BidBeez Training',
      description: 'Platform-specific training and certification',
      checks: ['Training Completion', 'Assessment Pass', 'Practical Evaluation']
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'verified': return 'success';
      case 'pending': return 'warning';
      case 'failed': return 'error';
      case 'not_started': return 'default';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'verified': return <CheckCircle color="success" />;
      case 'pending': return <Warning color="warning" />;
      case 'failed': return <Warning color="error" />;
      case 'not_started': return <Info color="disabled" />;
      default: return <Info />;
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Identity Verification': return <Fingerprint />;
      case 'Background Screening': return <Security />;
      case 'Financial Verification': return <CreditCard />;
      case 'Address Verification': return <LocationOn />;
      case 'Educational Verification': return <School />;
      case 'Employment History': return <Assignment />;
      case 'Licensing & Permits': return <DirectionsCar />;
      case 'Health & Safety': return <HealthAndSafety />;
      case 'Professional Training': return <Badge />;
      case 'Insurance & Bonding': return <Shield />;
      default: return <Verified />;
    }
  };

  const getVerificationLevel = (score: number) => {
    if (score >= 95) return { level: 'Elite', color: 'gold', description: 'Highest trust level' };
    if (score >= 90) return { level: 'Premium', color: 'primary', description: 'High trust level' };
    if (score >= 80) return { level: 'Standard', color: 'info', description: 'Good trust level' };
    return { level: 'Basic', color: 'warning', description: 'Basic trust level' };
  };

  const verificationLevel = getVerificationLevel(beeProfile.overallTrustScore);
  const completedChecks = verificationChecks.filter(check => check.status === 'verified').length;
  const totalChecks = verificationChecks.length;

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" fontWeight="bold" gutterBottom>
          🔍 Bee Worker Verification System
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Comprehensive verification and trust scoring for bee workers
        </Typography>
      </Box>

      {/* Bee Profile Summary */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Grid container spacing={3} alignItems="center">
            <Grid item>
              <Avatar 
                src={beeProfile.photo}
                sx={{ width: 80, height: 80 }}
              >
                🐝
              </Avatar>
            </Grid>
            <Grid item xs>
              <Typography variant="h5" fontWeight="bold" gutterBottom>
                {beeProfile.fullName}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <Chip 
                  label={`${verificationLevel.level} Verified`}
                  color={verificationLevel.color as any}
                  icon={<Verified />}
                />
                <Chip 
                  label={`Trust Score: ${beeProfile.overallTrustScore}%`}
                  color="success"
                  variant="outlined"
                />
                <Chip 
                  label={`${completedChecks}/${totalChecks} Checks Passed`}
                  color="info"
                  variant="outlined"
                />
              </Box>
              <Grid container spacing={4}>
                <Grid item>
                  <Typography variant="body2" color="text.secondary">
                    Bee ID
                  </Typography>
                  <Typography variant="body1" fontWeight="bold">
                    {beeProfile.beeId}
                  </Typography>
                </Grid>
                <Grid item>
                  <Typography variant="body2" color="text.secondary">
                    Member Since
                  </Typography>
                  <Typography variant="body1" fontWeight="bold">
                    {new Date(beeProfile.joinDate).toLocaleDateString()}
                  </Typography>
                </Grid>
                <Grid item>
                  <Typography variant="body2" color="text.secondary">
                    Success Rate
                  </Typography>
                  <Typography variant="body1" fontWeight="bold" color="success.main">
                    {beeProfile.successRate}%
                  </Typography>
                </Grid>
                <Grid item>
                  <Typography variant="body2" color="text.secondary">
                    Client Rating
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <Star sx={{ color: 'gold', fontSize: 16 }} />
                    <Typography variant="body1" fontWeight="bold">
                      {beeProfile.clientRating}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Trust Score Breakdown */}
      <Alert severity="success" sx={{ mb: 3 }}>
        <Typography variant="body1" fontWeight="bold" gutterBottom>
          🛡️ Premium Verified Bee Worker - Maximum Trust Level
        </Typography>
        <Typography variant="body2">
          This bee worker has completed our most comprehensive verification process including government database checks, 
          biometric verification, criminal background screening, and professional certification. You can trust this worker 
          with your most sensitive business tasks.
        </Typography>
      </Alert>

      {/* Verification Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={currentTab} onChange={(e, newValue) => setCurrentTab(newValue)}>
          <Tab label="Verification Details" />
          <Tab label="Verification Process" />
          <Tab label="Trust Metrics" />
        </Tabs>
      </Box>

      {/* Tab Content */}
      {currentTab === 0 && (
        <Grid container spacing={3}>
          {verificationChecks.map((check) => (
            <Grid item xs={12} md={6} key={check.id}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                    <Avatar sx={{ bgcolor: 'primary.main' }}>
                      {getCategoryIcon(check.category)}
                    </Avatar>
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="h6" fontWeight="bold">
                        {check.name}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {getStatusIcon(check.status)}
                        <Chip 
                          label={check.status.toUpperCase()}
                          color={getStatusColor(check.status) as any}
                          size="small"
                        />
                        <Chip 
                          label={`${check.trustScore}% Trust`}
                          color="success"
                          variant="outlined"
                          size="small"
                        />
                      </Box>
                    </Box>
                  </Box>

                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {check.description}
                  </Typography>

                  <Grid container spacing={2} sx={{ mb: 2 }}>
                    <Grid item xs={12}>
                      <Typography variant="body2" color="text.secondary">
                        Verification Method
                      </Typography>
                      <Typography variant="body1" fontWeight="bold">
                        {check.verificationMethod}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">
                        Verified By
                      </Typography>
                      <Typography variant="body1">
                        {check.verifiedBy}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">
                        Completed
                      </Typography>
                      <Typography variant="body1">
                        {check.completedDate ? new Date(check.completedDate).toLocaleDateString() : 'N/A'}
                      </Typography>
                    </Grid>
                  </Grid>

                  {check.expiryDate && (
                    <Alert severity="info" sx={{ mb: 2 }}>
                      <Typography variant="body2">
                        Expires: {new Date(check.expiryDate).toLocaleDateString()}
                      </Typography>
                    </Alert>
                  )}

                  <Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Supporting Documents
                    </Typography>
                    <Stack direction="row" spacing={1} flexWrap="wrap">
                      {check.documents.map((doc, index) => (
                        <Chip 
                          key={index}
                          label={doc}
                          size="small"
                          variant="outlined"
                        />
                      ))}
                    </Stack>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {currentTab === 1 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  🔍 BidBeez 5-Step Verification Process
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  Our comprehensive verification process ensures the highest standards of trust and reliability.
                </Typography>
                
                <Stepper orientation="vertical">
                  {verificationSteps.map((step, index) => (
                    <Step key={index} active={true} completed={true}>
                      <StepLabel>
                        <Typography variant="h6" fontWeight="bold">
                          {step.label}
                        </Typography>
                      </StepLabel>
                      <StepContent>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          {step.description}
                        </Typography>
                        <List dense>
                          {step.checks.map((check, checkIndex) => (
                            <ListItem key={checkIndex}>
                              <ListItemIcon>
                                <CheckCircle color="success" />
                              </ListItemIcon>
                              <ListItemText primary={check} />
                            </ListItem>
                          ))}
                        </List>
                      </StepContent>
                    </Step>
                  ))}
                </Stepper>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {currentTab === 2 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  📊 Trust Score Breakdown
                </Typography>
                
                <Box sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">Overall Trust Score</Typography>
                    <Typography variant="body2">{beeProfile.overallTrustScore}%</Typography>
                  </Box>
                  <LinearProgress 
                    variant="determinate" 
                    value={beeProfile.overallTrustScore}
                    sx={{ height: 12, borderRadius: 6 }}
                  />
                </Box>

                <Stack spacing={2}>
                  {verificationChecks.map((check) => (
                    <Box key={check.id}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                        <Typography variant="body2">{check.name}</Typography>
                        <Typography variant="body2">{check.trustScore}%</Typography>
                      </Box>
                      <LinearProgress 
                        variant="determinate" 
                        value={check.trustScore}
                        sx={{ height: 6, borderRadius: 3 }}
                      />
                    </Box>
                  ))}
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  🏆 Verification Achievements
                </Typography>
                
                <Stack spacing={2}>
                  <Paper sx={{ p: 2, bgcolor: 'success.light' }}>
                    <Typography variant="subtitle1" fontWeight="bold" color="success.dark">
                      🥇 Elite Verification Status
                    </Typography>
                    <Typography variant="body2">
                      Achieved the highest level of verification with 96% trust score
                    </Typography>
                  </Paper>

                  <Paper sx={{ p: 2, bgcolor: 'info.light' }}>
                    <Typography variant="subtitle1" fontWeight="bold" color="info.dark">
                      🛡️ Security Clearance Verified
                    </Typography>
                    <Typography variant="body2">
                      Passed comprehensive background and security screening
                    </Typography>
                  </Paper>

                  <Paper sx={{ p: 2, bgcolor: 'warning.light' }}>
                    <Typography variant="subtitle1" fontWeight="bold" color="warning.dark">
                      📚 Professional Certification
                    </Typography>
                    <Typography variant="body2">
                      Completed advanced BidBeez training and certification program
                    </Typography>
                  </Paper>

                  <Paper sx={{ p: 2, bgcolor: 'primary.light' }}>
                    <Typography variant="subtitle1" fontWeight="bold" color="primary.dark">
                      💼 Insurance & Bonding
                    </Typography>
                    <Typography variant="body2">
                      Fully insured and bonded for professional liability protection
                    </Typography>
                  </Paper>
                </Stack>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
    </Container>
  );
}
