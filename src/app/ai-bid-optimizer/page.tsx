'use client';

import React, { useState } from 'react';
import {
  <PERSON>,
  Container,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Grid,
  <PERSON><PERSON>,
  Step,
  <PERSON><PERSON>abel,
  StepContent,
  TextField,
  Chip,
  Stack,
  Alert,
  LinearProgress,
  Divider,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  IconButton,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  AutoAwesome,
  Psychology,
  TrendingUp,
  Assessment,
  CheckCircle,
  Warning,
  Info,
  Lightbulb,
  Speed,
  TrackChanges as Target,
  MonetizationOn,
  ExpandMore,
  Upload,
  Analytics,
  SmartToy,
  Insights
} from '@mui/icons-material';

interface OptimizationStep {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  progress: number;
  insights?: string[];
  recommendations?: string[];
}

interface BidOptimization {
  tenderId: string;
  tenderTitle: string;
  currentScore: number;
  optimizedScore: number;
  improvement: number;
  riskLevel: 'low' | 'medium' | 'high';
  winProbability: number;
  steps: OptimizationStep[];
}

export default function AIBidOptimizerPage() {
  const [activeStep, setActiveStep] = useState(0);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [selectedTender, setSelectedTender] = useState<string>('');
  
  const mockOptimization: BidOptimization = {
    tenderId: 'tender-001',
    tenderTitle: 'Municipal Infrastructure Development - Phase 3',
    currentScore: 72,
    optimizedScore: 89,
    improvement: 17,
    riskLevel: 'medium',
    winProbability: 78,
    steps: [
      {
        id: 'document-analysis',
        title: 'Document Analysis',
        description: 'AI analysis of tender documents and requirements',
        status: 'completed',
        progress: 100,
        insights: [
          'Identified 23 key requirements across technical and commercial sections',
          'Found 3 critical compliance requirements that must be addressed',
          'Detected emphasis on B-BBEE level 4+ requirement (15% weighting)'
        ]
      },
      {
        id: 'competitive-intelligence',
        title: 'Competitive Intelligence',
        description: 'Analysis of market competition and positioning',
        status: 'completed',
        progress: 100,
        insights: [
          'Estimated 12-15 competitors based on tender complexity',
          'Your company profile matches 87% of requirements',
          'Pricing sweet spot identified: R2.2M - R2.6M range'
        ]
      },
      {
        id: 'psychological-optimization',
        title: 'Psychological Optimization',
        description: 'Behavioral analysis and stress-reduction recommendations',
        status: 'processing',
        progress: 65,
        insights: [
          'Detected high-stress indicators in your bidding pattern',
          'Recommended submission timing: 2-3 days before deadline',
          'Confidence coaching available for presentation phase'
        ]
      },
      {
        id: 'bid-strategy',
        title: 'Bid Strategy Generation',
        description: 'AI-generated optimal bidding strategy',
        status: 'pending',
        progress: 0,
        recommendations: [
          'Focus on technical excellence over lowest price',
          'Emphasize local content and job creation',
          'Highlight previous municipal project experience'
        ]
      }
    ]
  };

  const handleOptimize = () => {
    setIsOptimizing(true);
    // Simulate optimization process
    setTimeout(() => {
      setIsOptimizing(false);
      setActiveStep(activeStep + 1);
    }, 3000);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'processing': return 'warning';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle color="success" />;
      case 'processing': return <Speed color="warning" />;
      case 'error': return <Warning color="error" />;
      default: return <Info color="action" />;
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
          🤖 AI Bid Optimizer
        </Typography>
        <Typography variant="h6" color="text.secondary" gutterBottom>
          Advanced AI-powered bid optimization with psychological insights and competitive intelligence
        </Typography>
        
        <Alert severity="info" sx={{ mt: 2 }}>
          <Typography variant="body2">
            <strong>🧠 Psychological AI:</strong> Our advanced AI analyzes your behavioral patterns, stress indicators, 
            and decision-making processes to optimize not just your bid content, but your entire bidding experience.
          </Typography>
        </Alert>
      </Box>

      <Grid container spacing={4}>
        {/* Left Panel - Optimization Process */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Optimization Process
              </Typography>
              
              {/* Current Tender Selection */}
              <Paper sx={{ p: 3, mb: 3, bgcolor: 'primary.50' }}>
                <Typography variant="subtitle1" gutterBottom>
                  Selected Tender
                </Typography>
                <Typography variant="h6" color="primary">
                  {mockOptimization.tenderTitle}
                </Typography>
                <Stack direction="row" spacing={2} sx={{ mt: 2 }}>
                  <Chip 
                    label={`Current Score: ${mockOptimization.currentScore}%`} 
                    color="default" 
                    variant="outlined"
                  />
                  <Chip 
                    label={`Win Probability: ${mockOptimization.winProbability}%`} 
                    color="primary"
                  />
                  <Chip 
                    label={`Risk: ${mockOptimization.riskLevel}`} 
                    color={mockOptimization.riskLevel === 'high' ? 'error' : mockOptimization.riskLevel === 'medium' ? 'warning' : 'success'}
                  />
                </Stack>
              </Paper>

              {/* Optimization Steps */}
              <Stepper activeStep={activeStep} orientation="vertical">
                {mockOptimization.steps.map((step, index) => (
                  <Step key={step.id}>
                    <StepLabel 
                      icon={getStatusIcon(step.status)}
                      optional={
                        step.status === 'processing' && (
                          <LinearProgress 
                            variant="determinate" 
                            value={step.progress} 
                            sx={{ mt: 1, width: 200 }}
                          />
                        )
                      }
                    >
                      <Typography variant="subtitle1">{step.title}</Typography>
                    </StepLabel>
                    <StepContent>
                      <Typography variant="body2" color="text.secondary" paragraph>
                        {step.description}
                      </Typography>
                      
                      {step.insights && (
                        <Box sx={{ mb: 2 }}>
                          <Typography variant="subtitle2" gutterBottom>
                            🔍 Key Insights:
                          </Typography>
                          <List dense>
                            {step.insights.map((insight, idx) => (
                              <ListItem key={idx} sx={{ py: 0.5 }}>
                                <ListItemIcon sx={{ minWidth: 32 }}>
                                  <Lightbulb color="primary" fontSize="small" />
                                </ListItemIcon>
                                <ListItemText 
                                  primary={insight}
                                  primaryTypographyProps={{ variant: 'body2' }}
                                />
                              </ListItem>
                            ))}
                          </List>
                        </Box>
                      )}
                      
                      {step.recommendations && (
                        <Box sx={{ mb: 2 }}>
                          <Typography variant="subtitle2" gutterBottom>
                            💡 Recommendations:
                          </Typography>
                          <List dense>
                            {step.recommendations.map((rec, idx) => (
                              <ListItem key={idx} sx={{ py: 0.5 }}>
                                <ListItemIcon sx={{ minWidth: 32 }}>
                                  <Target color="success" fontSize="small" />
                                </ListItemIcon>
                                <ListItemText 
                                  primary={rec}
                                  primaryTypographyProps={{ variant: 'body2' }}
                                />
                              </ListItem>
                            ))}
                          </List>
                        </Box>
                      )}
                      
                      {step.status === 'pending' && (
                        <Button
                          variant="contained"
                          onClick={handleOptimize}
                          disabled={isOptimizing}
                          startIcon={<AutoAwesome />}
                          sx={{ mt: 1 }}
                        >
                          {isOptimizing ? 'Optimizing...' : 'Start Optimization'}
                        </Button>
                      )}
                    </StepContent>
                  </Step>
                ))}
              </Stepper>
            </CardContent>
          </Card>
        </Grid>

        {/* Right Panel - Optimization Results */}
        <Grid item xs={12} md={4}>
          <Stack spacing={3}>
            {/* Score Improvement */}
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  📈 Score Improvement
                </Typography>
                
                <Box sx={{ textAlign: 'center', mb: 2 }}>
                  <Typography variant="h2" color="primary">
                    +{mockOptimization.improvement}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Potential Improvement
                  </Typography>
                </Box>
                
                <Divider sx={{ my: 2 }} />
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Current Score:</Typography>
                  <Typography variant="body2" fontWeight="bold">
                    {mockOptimization.currentScore}%
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="body2">Optimized Score:</Typography>
                  <Typography variant="body2" fontWeight="bold" color="success.main">
                    {mockOptimization.optimizedScore}%
                  </Typography>
                </Box>
                
                <LinearProgress 
                  variant="determinate" 
                  value={mockOptimization.optimizedScore} 
                  sx={{ height: 8, borderRadius: 4 }}
                  color="success"
                />
              </CardContent>
            </Card>

            {/* Psychological Insights */}
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  🧠 Psychological Insights
                </Typography>
                
                <Stack spacing={2}>
                  <Paper sx={{ p: 2, bgcolor: 'info.50' }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Stress Level: Moderate
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Consider breaking work into smaller chunks and taking breaks every 45 minutes.
                    </Typography>
                  </Paper>
                  
                  <Paper sx={{ p: 2, bgcolor: 'success.50' }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Confidence: High
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Your confidence levels are optimal for this bid. Trust your expertise.
                    </Typography>
                  </Paper>
                  
                  <Paper sx={{ p: 2, bgcolor: 'warning.50' }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Decision Fatigue: Low
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Good time for complex decisions. Consider tackling technical sections now.
                    </Typography>
                  </Paper>
                </Stack>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  🚀 Quick Actions
                </Typography>
                
                <Stack spacing={2}>
                  <Button 
                    variant="outlined" 
                    fullWidth 
                    startIcon={<Upload />}
                  >
                    Upload Documents
                  </Button>
                  
                  <Button 
                    variant="outlined" 
                    fullWidth 
                    startIcon={<Analytics />}
                  >
                    View Detailed Analysis
                  </Button>
                  
                  <Button 
                    variant="outlined" 
                    fullWidth 
                    startIcon={<SmartToy />}
                  >
                    AI Writing Assistant
                  </Button>
                  
                  <Button 
                    variant="contained" 
                    fullWidth 
                    startIcon={<Insights />}
                  >
                    Generate Final Report
                  </Button>
                </Stack>
              </CardContent>
            </Card>
          </Stack>
        </Grid>
      </Grid>

      {/* Advanced Features */}
      <Box sx={{ mt: 4 }}>
        <Typography variant="h5" gutterBottom>
          🔬 Advanced Optimization Features
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Typography variant="subtitle1">🎯 Competitive Intelligence</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Typography variant="body2" color="text.secondary">
                  AI-powered analysis of competitor strategies, pricing patterns, and market positioning 
                  to give you a competitive edge in your bidding approach.
                </Typography>
              </AccordionDetails>
            </Accordion>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Typography variant="subtitle1">🧠 Behavioral Optimization</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Typography variant="body2" color="text.secondary">
                  Psychological analysis of your decision-making patterns, stress indicators, and 
                  optimal working conditions to maximize your bidding performance.
                </Typography>
              </AccordionDetails>
            </Accordion>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Typography variant="subtitle1">📊 Predictive Analytics</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Typography variant="body2" color="text.secondary">
                  Machine learning models that predict bid success probability, optimal pricing 
                  strategies, and risk assessment based on historical data and market trends.
                </Typography>
              </AccordionDetails>
            </Accordion>
          </Grid>
        </Grid>
      </Box>
    </Container>
  );
}
