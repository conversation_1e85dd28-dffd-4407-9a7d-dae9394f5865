'use client';

import React, { useState } from 'react';
import { 
  Container, 
  Box, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  Alert,
  Button,
  Chip,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Stack
} from '@mui/material';
import { 
  TrendingUp, 
  Assessment, 
  Calculate, 
  Visibility,
  Refresh,
  Download,
  Settings,
  CheckCircle,
  Warning,
  Error,
  Timeline
} from '@mui/icons-material';

export default function WinProbabilityPage() {
  const [isCalculating, setIsCalculating] = useState(false);

  const startCalculation = () => {
    setIsCalculating(true);
    setTimeout(() => setIsCalculating(false), 3000);
  };

  // Mock win probability data
  const probabilityMetrics = {
    averageWinRate: 78.4,
    totalCalculations: 3247,
    accuracyRate: 94.2,
    highProbabilityTenders: 23,
    mediumProbabilityTenders: 45,
    lowProbabilityTenders: 12
  };

  const tenderProbabilities = [
    {
      id: 1,
      tenderRef: 'MUN/2024/IT/001',
      title: 'Municipal IT Infrastructure Upgrade',
      winProbability: 87.3,
      confidence: 'High',
      factors: ['Strong Technical Match', 'Competitive Pricing', 'Local Presence'],
      riskFactors: ['High Competition'],
      recommendedAction: 'Submit Aggressive Bid',
      estimatedValue: 'R2.4M'
    },
    {
      id: 2,
      tenderRef: 'PROV/2024/ROAD/045',
      title: 'Provincial Road Maintenance Contract',
      winProbability: 64.7,
      confidence: 'Medium',
      factors: ['Previous Experience', 'Good Relationship'],
      riskFactors: ['Price Sensitivity', 'New Competitors'],
      recommendedAction: 'Strategic Positioning',
      estimatedValue: 'R1.8M'
    },
    {
      id: 3,
      tenderRef: 'NAT/2024/SEC/012',
      title: 'National Security Services',
      winProbability: 34.2,
      confidence: 'Low',
      factors: ['Compliance Ready'],
      riskFactors: ['Incumbent Advantage', 'High Requirements'],
      recommendedAction: 'Consider Partnership',
      estimatedValue: 'R3.2M'
    },
    {
      id: 4,
      tenderRef: 'MUN/2024/CLEAN/078',
      title: 'Municipal Cleaning Services',
      winProbability: 91.8,
      confidence: 'Very High',
      factors: ['Perfect Match', 'Lowest Cost', 'Local Preference'],
      riskFactors: ['None Identified'],
      recommendedAction: 'Immediate Submission',
      estimatedValue: 'R890K'
    }
  ];

  const probabilityFactors = [
    { factor: 'Technical Capability Match', weight: 25, impact: 'Very High' },
    { factor: 'Pricing Competitiveness', weight: 20, impact: 'High' },
    { factor: 'Previous Experience', weight: 15, impact: 'High' },
    { factor: 'Local Presence/B-BBEE', weight: 15, impact: 'Medium' },
    { factor: 'Relationship Strength', weight: 10, impact: 'Medium' },
    { factor: 'Compliance Readiness', weight: 10, impact: 'Medium' },
    { factor: 'Market Conditions', weight: 5, impact: 'Low' }
  ];

  const getProbabilityColor = (probability: number) => {
    if (probability >= 80) return 'success';
    if (probability >= 60) return 'warning';
    if (probability >= 40) return 'info';
    return 'error';
  };

  const getConfidenceColor = (confidence: string) => {
    switch (confidence) {
      case 'Very High': return 'success';
      case 'High': return 'success';
      case 'Medium': return 'warning';
      case 'Low': return 'error';
      default: return 'default';
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'Very High': return 'error';
      case 'High': return 'warning';
      case 'Medium': return 'info';
      case 'Low': return 'success';
      default: return 'default';
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold" color="primary.main">
          🎯 Win Probability Calculator
        </Typography>
        <Typography variant="h6" color="text.primary" sx={{ mb: 2 }}>
          AI-powered win probability analysis and strategic bidding recommendations
        </Typography>
        
        <Alert severity="success" sx={{ mb: 3 }}>
          <strong>Probability Engine Active:</strong> 94.2% prediction accuracy with 3,247 calculations and real-time market analysis.
        </Alert>
      </Box>

      {/* Quick Actions */}
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item>
          <Button 
            variant="contained" 
            startIcon={isCalculating ? <CircularProgress size={20} /> : <Calculate />} 
            color="primary"
            onClick={startCalculation}
            disabled={isCalculating}
          >
            {isCalculating ? 'Calculating...' : 'Calculate Probabilities'}
          </Button>
        </Grid>
        <Grid item>
          <Button variant="outlined" startIcon={<Assessment />}>
            View Analysis
          </Button>
        </Grid>
        <Grid item>
          <Button variant="outlined" startIcon={<Download />}>
            Export Report
          </Button>
        </Grid>
        <Grid item>
          <Button variant="outlined" startIcon={<Settings />}>
            Model Settings
          </Button>
        </Grid>
      </Grid>

      {/* Probability Metrics Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <TrendingUp sx={{ fontSize: 32, color: 'success.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {probabilityMetrics.averageWinRate}%
              </Typography>
              <Typography variant="body2" color="text.secondary">Average Win Rate</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Calculate sx={{ fontSize: 32, color: 'primary.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {probabilityMetrics.totalCalculations}
              </Typography>
              <Typography variant="body2" color="text.secondary">Total Calculations</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <CheckCircle sx={{ fontSize: 32, color: 'info.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {probabilityMetrics.accuracyRate}%
              </Typography>
              <Typography variant="body2" color="text.secondary">Accuracy Rate</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <CheckCircle sx={{ fontSize: 32, color: 'success.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {probabilityMetrics.highProbabilityTenders}
              </Typography>
              <Typography variant="body2" color="text.secondary">High Probability</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Warning sx={{ fontSize: 32, color: 'warning.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {probabilityMetrics.mediumProbabilityTenders}
              </Typography>
              <Typography variant="body2" color="text.secondary">Medium Probability</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Error sx={{ fontSize: 32, color: 'error.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {probabilityMetrics.lowProbabilityTenders}
              </Typography>
              <Typography variant="body2" color="text.secondary">Low Probability</Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tender Probability Analysis */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom color="text.primary">
                Current Tender Win Probabilities
              </Typography>
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell><Typography color="text.primary" fontWeight="bold">Tender</Typography></TableCell>
                      <TableCell><Typography color="text.primary" fontWeight="bold">Win Probability</Typography></TableCell>
                      <TableCell><Typography color="text.primary" fontWeight="bold">Confidence</Typography></TableCell>
                      <TableCell><Typography color="text.primary" fontWeight="bold">Value</Typography></TableCell>
                      <TableCell><Typography color="text.primary" fontWeight="bold">Recommendation</Typography></TableCell>
                      <TableCell><Typography color="text.primary" fontWeight="bold">Actions</Typography></TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {tenderProbabilities.map((tender) => (
                      <TableRow key={tender.id}>
                        <TableCell>
                          <Box>
                            <Typography variant="subtitle2" fontWeight="medium" color="text.primary">
                              {tender.tenderRef}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {tender.title}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <LinearProgress 
                              variant="determinate" 
                              value={tender.winProbability} 
                              sx={{ width: 80, height: 8 }}
                              color={getProbabilityColor(tender.winProbability) as any}
                            />
                            <Typography variant="body2" fontWeight="bold" color="text.primary">
                              {tender.winProbability}%
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip 
                            label={tender.confidence} 
                            color={getConfidenceColor(tender.confidence) as any}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" color="success.main" fontWeight="bold">
                            {tender.estimatedValue}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" color="text.primary">
                            {tender.recommendedAction}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Button size="small" startIcon={<Visibility />}>
                            Details
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom color="text.primary">
                Probability Factors
              </Typography>
              <Stack spacing={2}>
                {probabilityFactors.map((factor, index) => (
                  <Box key={index}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="body2" color="text.primary">
                        {factor.factor}
                      </Typography>
                      <Chip 
                        label={factor.impact} 
                        color={getImpactColor(factor.impact) as any}
                        size="small"
                      />
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <LinearProgress 
                        variant="determinate" 
                        value={factor.weight * 4} 
                        sx={{ flexGrow: 1, height: 6 }}
                      />
                      <Typography variant="caption" color="text.secondary">
                        {factor.weight}%
                      </Typography>
                    </Box>
                  </Box>
                ))}
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Detailed Analysis */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom color="text.primary">
                Win Probability Trends
              </Typography>
              <Box sx={{ height: 250, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: 'rgba(255,255,255,0.05)' }}>
                <Typography variant="body1" color="text.secondary">
                  📈 Win probability trends over time
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom color="text.primary">
                Success Factor Analysis
              </Typography>
              <Box sx={{ height: 250, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: 'rgba(255,255,255,0.05)' }}>
                <Typography variant="body1" color="text.secondary">
                  📊 Factor impact on win probability
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* AI Insights */}
      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom color="text.primary">
            AI-Powered Insights
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Alert severity="success">
                <strong>High Opportunity:</strong> MUN/2024/CLEAN/078 shows 91.8% win probability with perfect technical match.
              </Alert>
            </Grid>
            <Grid item xs={12} md={4}>
              <Alert severity="warning">
                <strong>Strategic Focus:</strong> Improve pricing competitiveness to increase average win rate by 12%.
              </Alert>
            </Grid>
            <Grid item xs={12} md={4}>
              <Alert severity="info">
                <strong>Market Trend:</strong> Municipal tenders showing 23% higher win rates than provincial contracts.
              </Alert>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Container>
  );
}
