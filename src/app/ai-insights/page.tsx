'use client';

import React, { useState } from 'react';
import { 
  Container, 
  Box, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  Tabs, 
  Tab, 
  Alert,
  Chip,
  Button,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import { 
  Brain, 
  TrendingUp, 
  Target, 
  Users, 
  DollarSign,
  AlertTriangle,
  CheckCircle,
  Clock,
  Zap,
  BarChart3
} from 'lucide-react';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`ai-insights-tabpanel-${index}`}
      aria-labelledby={`ai-insights-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

export default function AIInsightsPage() {
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Mock AI insights data
  const aiRecommendations = [
    {
      id: 1,
      title: 'High-Value Opportunity Detected',
      description: 'Municipal infrastructure tender with 87% win probability based on your profile',
      confidence: 87,
      value: 'R2.4M',
      urgency: 'high',
      action: 'Submit bid within 48 hours'
    },
    {
      id: 2,
      title: 'Pricing Optimization Alert',
      description: 'Reduce bid by 8% to increase win probability from 65% to 82%',
      confidence: 82,
      value: 'R890K',
      urgency: 'medium',
      action: 'Adjust pricing strategy'
    },
    {
      id: 3,
      title: 'Competitor Intelligence',
      description: 'Main competitor ABC Corp showing weakness in compliance scores',
      confidence: 74,
      value: 'R1.2M',
      urgency: 'low',
      action: 'Leverage compliance advantage'
    }
  ];

  const marketTrends = [
    { category: 'IT Services', trend: '+15%', confidence: 92 },
    { category: 'Construction', trend: '-3%', confidence: 78 },
    { category: 'Consulting', trend: '+8%', confidence: 85 },
    { category: 'Security', trend: '+22%', confidence: 94 }
  ];

  const riskFactors = [
    { factor: 'Compliance Gap', severity: 'high', impact: 'Bid rejection risk' },
    { factor: 'Pricing Aggressive', severity: 'medium', impact: 'Profit margin concern' },
    { factor: 'Timeline Tight', severity: 'low', impact: 'Delivery pressure' }
  ];

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
          🤖 AI Insights Dashboard
        </Typography>
        <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
          AI-powered recommendations, market intelligence, and win probability analysis
        </Typography>
        
        <Alert severity="info" sx={{ mb: 3 }}>
          <strong>AI Engine Active:</strong> Real-time analysis of 15,000+ tenders with 87% prediction accuracy.
        </Alert>
      </Box>

      {/* AI Stats Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Brain size={32} color="#2196f3" />
              <Typography variant="h4" fontWeight="bold">87%</Typography>
              <Typography variant="body2" color="text.secondary">AI Accuracy</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Target size={32} color="#4caf50" />
              <Typography variant="h4" fontWeight="bold">23</Typography>
              <Typography variant="body2" color="text.secondary">Active Recommendations</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <TrendingUp size={32} color="#ff9800" />
              <Typography variant="h4" fontWeight="bold">+15%</Typography>
              <Typography variant="body2" color="text.secondary">Win Rate Improvement</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <DollarSign size={32} color="#9c27b0" />
              <Typography variant="h4" fontWeight="bold">R8.2M</Typography>
              <Typography variant="body2" color="text.secondary">Potential Value</Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs for different AI insights */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="ai insights tabs">
          <Tab label="Recommendations" />
          <Tab label="Market Intelligence" />
          <Tab label="Risk Analysis" />
          <Tab label="Competitor Insights" />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        <Grid container spacing={3}>
          {aiRecommendations.map((rec) => (
            <Grid item xs={12} key={rec.id}>
              <Card sx={{ 
                border: rec.urgency === 'high' ? '2px solid #f44336' : 
                       rec.urgency === 'medium' ? '2px solid #ff9800' : '1px solid #e0e0e0'
              }}>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="h6" gutterBottom>{rec.title}</Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        {rec.description}
                      </Typography>
                    </Box>
                    <Box sx={{ textAlign: 'right', ml: 2 }}>
                      <Chip 
                        label={`${rec.confidence}% confidence`} 
                        color="primary" 
                        size="small" 
                        sx={{ mb: 1 }}
                      />
                      <Typography variant="h6" color="success.main">{rec.value}</Typography>
                    </Box>
                  </Box>
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Box>
                      <Chip 
                        label={rec.urgency.toUpperCase()} 
                        color={rec.urgency === 'high' ? 'error' : rec.urgency === 'medium' ? 'warning' : 'default'}
                        size="small"
                      />
                    </Box>
                    <Button variant="contained" size="small">
                      {rec.action}
                    </Button>
                  </Box>
                  
                  <LinearProgress 
                    variant="determinate" 
                    value={rec.confidence} 
                    sx={{ mt: 2, height: 6, borderRadius: 3 }}
                  />
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>Market Trends</Typography>
                {marketTrends.map((trend, index) => (
                  <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', py: 1 }}>
                    <Typography variant="body1">{trend.category}</Typography>
                    <Box sx={{ textAlign: 'right' }}>
                      <Typography 
                        variant="h6" 
                        color={trend.trend.startsWith('+') ? 'success.main' : 'error.main'}
                      >
                        {trend.trend}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {trend.confidence}% confidence
                      </Typography>
                    </Box>
                  </Box>
                ))}
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>Market Intelligence</Typography>
                <Alert severity="success" sx={{ mb: 2 }}>
                  <strong>Opportunity Alert:</strong> IT Services sector showing 15% growth
                </Alert>
                <Alert severity="warning" sx={{ mb: 2 }}>
                  <strong>Market Shift:</strong> Construction tenders decreasing by 3%
                </Alert>
                <Alert severity="info">
                  <strong>Emerging Trend:</strong> Security services demand up 22%
                </Alert>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>Risk Assessment</Typography>
            <List>
              {riskFactors.map((risk, index) => (
                <ListItem key={index}>
                  <ListItemIcon>
                    {risk.severity === 'high' ? 
                      <AlertTriangle color="#f44336" /> : 
                      risk.severity === 'medium' ? 
                      <Clock color="#ff9800" /> : 
                      <CheckCircle color="#4caf50" />
                    }
                  </ListItemIcon>
                  <ListItemText
                    primary={risk.factor}
                    secondary={risk.impact}
                  />
                  <Chip 
                    label={risk.severity.toUpperCase()} 
                    color={risk.severity === 'high' ? 'error' : risk.severity === 'medium' ? 'warning' : 'success'}
                    size="small"
                  />
                </ListItem>
              ))}
            </List>
          </CardContent>
        </Card>
      </TabPanel>

      <TabPanel value={tabValue} index={3}>
        <Alert severity="info" sx={{ mb: 3 }}>
          <strong>Competitor Intelligence:</strong> Real-time analysis of competitor behavior and market positioning.
        </Alert>
        <Typography variant="h6" gutterBottom>Competitor Analysis</Typography>
        <Typography variant="body1" color="text.secondary">
          Advanced competitor intelligence features are being processed. 
          AI is analyzing competitor bid patterns, pricing strategies, and market positioning.
        </Typography>
      </TabPanel>
    </Container>
  );
}
