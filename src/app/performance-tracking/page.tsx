'use client';

import React from 'react';
import { Container, Typography, Card, CardContent, Grid, Box, Avatar, LinearProgress } from '@mui/material';
import { Timeline, Speed, Assessment, TrendingUp } from '@mui/icons-material';

export default function PerformanceTrackingPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
        Performance Tracking
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        Monitor and track business performance metrics
      </Typography>
      
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="primary">
                    94.7%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Success Rate
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <TrendingUp />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="success.main">
                    2.3s
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Avg Response
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <Speed />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Performance Trends</Typography>
          <Box sx={{ mt: 2 }}>
            <Box sx={{ mb: 3 }}>
              <Typography variant="body2" gutterBottom>Bid Success Rate: 94.7%</Typography>
              <LinearProgress variant="determinate" value={94.7} color="success" sx={{ height: 8 }} />
            </Box>
            <Box sx={{ mb: 3 }}>
              <Typography variant="body2" gutterBottom>Client Satisfaction: 96.1%</Typography>
              <LinearProgress variant="determinate" value={96.1} color="primary" sx={{ height: 8 }} />
            </Box>
            <Box sx={{ mb: 3 }}>
              <Typography variant="body2" gutterBottom>Process Efficiency: 87.3%</Typography>
              <LinearProgress variant="determinate" value={87.3} color="warning" sx={{ height: 8 }} />
            </Box>
          </Box>
        </CardContent>
      </Card>
    </Container>
  );
}
