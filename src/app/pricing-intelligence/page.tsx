'use client';

import React from 'react';
import { Container, Typography, Card, CardContent, Grid, Box, Avatar, LinearProgress } from '@mui/material';
import { AttachMoney, TrendingUp, Assessment, Speed } from '@mui/icons-material';

export default function PricingIntelligencePage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
        Pricing Intelligence
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom>
        Smart pricing analysis and competitive intelligence
      </Typography>
      
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="primary">
                    R2.4M
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Optimal Price
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <AttachMoney />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="success.main">
                    89%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Win Probability
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <TrendingUp />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Card sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Market Price Analysis</Typography>
          <Box sx={{ mt: 2 }}>
            <Box sx={{ mb: 3 }}>
              <Typography variant="body2" gutterBottom>Construction Services: R2.1M - R2.8M</Typography>
              <LinearProgress variant="determinate" value={75} color="primary" sx={{ height: 8 }} />
            </Box>
            <Box sx={{ mb: 3 }}>
              <Typography variant="body2" gutterBottom>IT Services: R450K - R890K</Typography>
              <LinearProgress variant="determinate" value={60} color="success" sx={{ height: 8 }} />
            </Box>
            <Box sx={{ mb: 3 }}>
              <Typography variant="body2" gutterBottom>Consulting: R125K - R350K</Typography>
              <LinearProgress variant="determinate" value={45} color="warning" sx={{ height: 8 }} />
            </Box>
          </Box>
        </CardContent>
      </Card>
    </Container>
  );
}
