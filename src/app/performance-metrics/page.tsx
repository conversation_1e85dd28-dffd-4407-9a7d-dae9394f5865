'use client';

import React, { useState } from 'react';
import { 
  Container, 
  Box, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  Alert,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  LinearProgress,
  Tabs,
  Tab
} from '@mui/material';
import { 
  Dashboard, 
  TrendingUp, 
  Speed, 
  Assessment,
  Timeline,
  CheckCircle,
  Warning,
  Error,
  Refresh,
  Download,
  Settings,
  Visibility
} from '@mui/icons-material';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`metrics-tabpanel-${index}`}
      aria-labelledby={`metrics-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

export default function PerformanceMetricsPage() {
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Mock performance metrics data
  const systemMetrics = {
    overallHealth: 96.8,
    uptime: 99.9,
    responseTime: '1.2s',
    throughput: '15.4K req/min',
    errorRate: 0.03,
    userSatisfaction: 94.7
  };

  const servicePerformance = [
    {
      service: 'Psychological Profiling',
      health: 98.2,
      uptime: 99.8,
      responseTime: '0.8s',
      requests: 12450,
      errors: 2,
      status: 'Excellent'
    },
    {
      service: 'AI Engine',
      health: 96.5,
      uptime: 99.9,
      responseTime: '1.1s',
      requests: 8920,
      errors: 5,
      status: 'Good'
    },
    {
      service: 'Market Intelligence',
      health: 94.7,
      uptime: 99.7,
      responseTime: '1.5s',
      requests: 6780,
      errors: 8,
      status: 'Good'
    },
    {
      service: 'Automation Hub',
      health: 97.8,
      uptime: 99.9,
      responseTime: '0.9s',
      requests: 15670,
      errors: 3,
      status: 'Excellent'
    },
    {
      service: 'Compliance Dashboard',
      health: 95.3,
      uptime: 99.6,
      responseTime: '1.3s',
      requests: 4560,
      errors: 6,
      status: 'Good'
    }
  ];

  const performanceAlerts = [
    {
      id: 1,
      type: 'Warning',
      service: 'Market Intelligence',
      message: 'Response time increased by 15% in last hour',
      timestamp: '5 min ago',
      severity: 'Medium'
    },
    {
      id: 2,
      type: 'Info',
      service: 'AI Engine',
      message: 'Model training completed successfully',
      timestamp: '15 min ago',
      severity: 'Low'
    },
    {
      id: 3,
      type: 'Success',
      service: 'Automation Hub',
      message: 'Performance optimization applied',
      timestamp: '1 hour ago',
      severity: 'Low'
    },
    {
      id: 4,
      type: 'Warning',
      service: 'Database',
      message: 'Connection pool utilization at 85%',
      timestamp: '2 hours ago',
      severity: 'Medium'
    }
  ];

  const resourceUtilization = [
    { resource: 'CPU Usage', current: 67, average: 58, peak: 89, status: 'Normal' },
    { resource: 'Memory Usage', current: 74, average: 69, peak: 92, status: 'Normal' },
    { resource: 'Disk I/O', current: 45, average: 42, peak: 78, status: 'Normal' },
    { resource: 'Network Bandwidth', current: 52, average: 48, peak: 85, status: 'Normal' },
    { resource: 'Database Connections', current: 85, average: 72, peak: 95, status: 'Warning' }
  ];

  const getHealthColor = (health: number) => {
    if (health >= 95) return 'success';
    if (health >= 85) return 'warning';
    return 'error';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Excellent': return 'success';
      case 'Good': return 'info';
      case 'Warning': return 'warning';
      case 'Critical': return 'error';
      case 'Normal': return 'success';
      default: return 'default';
    }
  };

  const getAlertColor = (type: string) => {
    switch (type) {
      case 'Success': return 'success';
      case 'Info': return 'info';
      case 'Warning': return 'warning';
      case 'Error': return 'error';
      default: return 'default';
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold" color="primary.main">
          📈 Performance Metrics Dashboard
        </Typography>
        <Typography variant="h6" color="text.primary" sx={{ mb: 2 }}>
          Comprehensive system performance analytics and monitoring for optimal platform operation
        </Typography>
        
        <Alert severity="success" sx={{ mb: 3 }}>
          <strong>System Health:</strong> {systemMetrics.overallHealth}% overall health with {systemMetrics.uptime}% uptime and {systemMetrics.responseTime} average response time.
        </Alert>
      </Box>

      {/* Quick Actions */}
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item>
          <Button variant="contained" startIcon={<Refresh />} color="primary">
            Refresh Metrics
          </Button>
        </Grid>
        <Grid item>
          <Button variant="outlined" startIcon={<Download />}>
            Export Report
          </Button>
        </Grid>
        <Grid item>
          <Button variant="outlined" startIcon={<Settings />}>
            Configure Alerts
          </Button>
        </Grid>
      </Grid>

      {/* System Overview Metrics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Dashboard sx={{ fontSize: 32, color: 'success.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {systemMetrics.overallHealth}%
              </Typography>
              <Typography variant="body2" color="text.secondary">System Health</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <CheckCircle sx={{ fontSize: 32, color: 'success.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {systemMetrics.uptime}%
              </Typography>
              <Typography variant="body2" color="text.secondary">Uptime</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Speed sx={{ fontSize: 32, color: 'info.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {systemMetrics.responseTime}
              </Typography>
              <Typography variant="body2" color="text.secondary">Response Time</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <TrendingUp sx={{ fontSize: 32, color: 'warning.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {systemMetrics.throughput}
              </Typography>
              <Typography variant="body2" color="text.secondary">Throughput</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Error sx={{ fontSize: 32, color: 'error.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {systemMetrics.errorRate}%
              </Typography>
              <Typography variant="body2" color="text.secondary">Error Rate</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Assessment sx={{ fontSize: 32, color: 'secondary.main', mb: 1 }} />
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {systemMetrics.userSatisfaction}%
              </Typography>
              <Typography variant="body2" color="text.secondary">User Satisfaction</Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Performance Analysis Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="performance tabs">
          <Tab label="Service Performance" />
          <Tab label="Resource Utilization" />
          <Tab label="Performance Alerts" />
          <Tab label="Trends & Analytics" />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom color="text.primary">
              Service Performance Overview
            </Typography>
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell><Typography color="text.primary" fontWeight="bold">Service</Typography></TableCell>
                    <TableCell><Typography color="text.primary" fontWeight="bold">Health</Typography></TableCell>
                    <TableCell><Typography color="text.primary" fontWeight="bold">Uptime</Typography></TableCell>
                    <TableCell><Typography color="text.primary" fontWeight="bold">Response Time</Typography></TableCell>
                    <TableCell><Typography color="text.primary" fontWeight="bold">Requests</Typography></TableCell>
                    <TableCell><Typography color="text.primary" fontWeight="bold">Errors</Typography></TableCell>
                    <TableCell><Typography color="text.primary" fontWeight="bold">Status</Typography></TableCell>
                    <TableCell><Typography color="text.primary" fontWeight="bold">Actions</Typography></TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {servicePerformance.map((service, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <Typography variant="subtitle2" fontWeight="medium" color="text.primary">
                          {service.service}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <LinearProgress 
                            variant="determinate" 
                            value={service.health} 
                            sx={{ width: 60, height: 6 }}
                            color={getHealthColor(service.health) as any}
                          />
                          <Typography variant="body2" color="text.primary">
                            {service.health}%
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="text.primary">
                          {service.uptime}%
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="text.primary">
                          {service.responseTime}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="text.primary">
                          {service.requests.toLocaleString()}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color={service.errors > 5 ? 'error.main' : 'success.main'}>
                          {service.errors}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={service.status} 
                          color={getStatusColor(service.status) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Button size="small" startIcon={<Visibility />}>
                          Details
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom color="text.primary">
              System Resource Utilization
            </Typography>
            <Grid container spacing={3}>
              {resourceUtilization.map((resource, index) => (
                <Grid item xs={12} md={6} key={index}>
                  <Paper sx={{ p: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Typography variant="h6" color="text.primary">
                        {resource.resource}
                      </Typography>
                      <Chip 
                        label={resource.status} 
                        color={getStatusColor(resource.status) as any}
                        size="small"
                      />
                    </Box>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.primary" gutterBottom>
                        Current: {resource.current}%
                      </Typography>
                      <LinearProgress 
                        variant="determinate" 
                        value={resource.current} 
                        sx={{ height: 8, borderRadius: 4 }}
                        color={resource.current > 80 ? 'warning' : 'success'}
                      />
                    </Box>
                    <Grid container spacing={2}>
                      <Grid item xs={6}>
                        <Typography variant="caption" color="text.secondary">Average</Typography>
                        <Typography variant="body2" color="text.primary">{resource.average}%</Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="caption" color="text.secondary">Peak</Typography>
                        <Typography variant="body2" color="text.primary">{resource.peak}%</Typography>
                      </Grid>
                    </Grid>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom color="text.primary">
              Performance Alerts & Notifications
            </Typography>
            <Grid container spacing={2}>
              {performanceAlerts.map((alert) => (
                <Grid item xs={12} key={alert.id}>
                  <Alert 
                    severity={getAlertColor(alert.type) as any}
                    action={
                      <Button color="inherit" size="small">
                        Investigate
                      </Button>
                    }
                  >
                    <Box>
                      <Typography variant="subtitle2" fontWeight="medium">
                        {alert.service}: {alert.message}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {alert.timestamp} • Severity: {alert.severity}
                      </Typography>
                    </Box>
                  </Alert>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      </TabPanel>

      <TabPanel value={tabValue} index={3}>
        <Alert severity="info" sx={{ mb: 3 }}>
          <strong>Performance Trends:</strong> System performance has improved by 15% over the last month with optimized resource allocation.
        </Alert>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom color="text.primary">
                  Performance Trends
                </Typography>
                <Box sx={{ height: 250, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: 'rgba(255,255,255,0.05)' }}>
                  <Typography variant="body1" color="text.secondary">
                    📈 Performance metrics over time
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom color="text.primary">
                  Resource Usage Analytics
                </Typography>
                <Box sx={{ height: 250, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: 'rgba(255,255,255,0.05)' }}>
                  <Typography variant="body1" color="text.secondary">
                    📊 Resource utilization patterns
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>
    </Container>
  );
}
