'use client';

import React from 'react';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Chip,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar
} from '@mui/material';
import {
  Analytics,
  TrendingUp,
  TrendingDown,
  Assessment,
  Timeline,
  PieChart,
  BarChart,
  ShowChart,
  Speed,
  Target,
  Psychology,
  SmartToy
} from '@mui/icons-material';

const performanceData = [
  { metric: "Bid Success Rate", value: 94.7, change: ****, trend: "up" },
  { metric: "Response Time", value: 87.3, change: -2.1, trend: "down" },
  { metric: "Client Satisfaction", value: 96.1, change: ****, trend: "up" },
  { metric: "Revenue Growth", value: 78.9, change: +12.4, trend: "up" }
];

const topTenders = [
  { name: "Municipal Infrastructure", value: "R2.4M", probability: 89, status: "active" },
  { name: "IT Equipment Procurement", value: "R890K", probability: 76, status: "submitted" },
  { name: "Construction Services", value: "R5.2M", probability: 92, status: "won" }
];

export default function AnalyticsDashboardPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
          Analytics Dashboard
        </Typography>
        <Typography variant="h6" color="text.secondary">
          Comprehensive business intelligence and performance analytics
        </Typography>
      </Box>

      {/* Key Metrics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="primary">
                    R12.4M
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Revenue
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <Assessment />
                </Avatar>
              </Box>
              <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                <TrendingUp sx={{ color: 'success.main', fontSize: 16 }} />
                <Typography variant="caption" color="success.main">
                  +18.2% from last month
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="success.main">
                    94.7%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Success Rate
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <Target />
                </Avatar>
              </Box>
              <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                <TrendingUp sx={{ color: 'success.main', fontSize: 16 }} />
                <Typography variant="caption" color="success.main">
                  ****% improvement
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="warning.main">
                    156
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Hours Saved
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'warning.main' }}>
                  <Speed />
                </Avatar>
              </Box>
              <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                <TrendingUp sx={{ color: 'success.main', fontSize: 16 }} />
                <Typography variant="caption" color="success.main">
                  Through automation
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" fontWeight="bold" color="info.main">
                    15
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    AI Engines
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'info.main' }}>
                  <SmartToy />
                </Avatar>
              </Box>
              <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                <Typography variant="caption" color="info.main">
                  All operational
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Performance Metrics */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Performance Metrics
              </Typography>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Metric</TableCell>
                      <TableCell>Current Value</TableCell>
                      <TableCell>Progress</TableCell>
                      <TableCell>Change</TableCell>
                      <TableCell>Trend</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {performanceData.map((item, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Typography variant="subtitle2" fontWeight="medium">
                            {item.metric}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" fontWeight="bold">
                            {item.value}%
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ width: 100 }}>
                            <LinearProgress
                              variant="determinate"
                              value={item.value}
                              color={item.value > 90 ? 'success' : item.value > 70 ? 'warning' : 'error'}
                            />
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography
                            variant="body2"
                            color={item.change > 0 ? 'success.main' : 'error.main'}
                            fontWeight="medium"
                          >
                            {item.change > 0 ? '+' : ''}{item.change}%
                          </Typography>
                        </TableCell>
                        <TableCell>
                          {item.trend === 'up' ? (
                            <TrendingUp sx={{ color: 'success.main' }} />
                          ) : (
                            <TrendingDown sx={{ color: 'error.main' }} />
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Top Performing Tenders */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Top Performing Tenders
              </Typography>
              <List>
                {topTenders.map((tender, index) => (
                  <ListItem key={index}>
                    <ListItemAvatar>
                      <Avatar sx={{ 
                        bgcolor: tender.status === 'won' ? 'success.main' : 
                                tender.status === 'submitted' ? 'warning.main' : 'primary.main' 
                      }}>
                        <Timeline />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={tender.name}
                      secondary={
                        <Box>
                          <Typography variant="body2" fontWeight="bold">
                            {tender.value}
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                            <LinearProgress
                              variant="determinate"
                              value={tender.probability}
                              sx={{ width: 60, height: 4 }}
                              color={tender.probability > 85 ? 'success' : 'warning'}
                            />
                            <Typography variant="caption">
                              {tender.probability}%
                            </Typography>
                          </Box>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Chart Placeholders */}
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Revenue Trends
              </Typography>
              <Box sx={{ 
                height: 200, 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                bgcolor: 'grey.50',
                borderRadius: 1
              }}>
                <Box sx={{ textAlign: 'center' }}>
                  <ShowChart sx={{ fontSize: 48, color: 'grey.400', mb: 1 }} />
                  <Typography variant="body2" color="text.secondary">
                    Revenue trend chart would be displayed here
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Bid Distribution
              </Typography>
              <Box sx={{ 
                height: 200, 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                bgcolor: 'grey.50',
                borderRadius: 1
              }}>
                <Box sx={{ textAlign: 'center' }}>
                  <PieChart sx={{ fontSize: 48, color: 'grey.400', mb: 1 }} />
                  <Typography variant="body2" color="text.secondary">
                    Bid distribution pie chart would be displayed here
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Quick Actions */}
      <Box sx={{ mt: 4, textAlign: 'center' }}>
        <Typography variant="h6" gutterBottom>
          Analytics Tools
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
          <Button variant="contained" startIcon={<Psychology />} href="/behavioral-analytics">
            Behavioral Analytics
          </Button>
          <Button variant="outlined" startIcon={<SmartToy />} href="/ai-insights">
            AI Insights
          </Button>
          <Button variant="outlined" startIcon={<Assessment />} href="/performance-metrics">
            Performance Metrics
          </Button>
          <Button variant="outlined" startIcon={<BarChart />} href="/market-intelligence">
            Market Intelligence
          </Button>
        </Box>
      </Box>
    </Container>
  );
}
