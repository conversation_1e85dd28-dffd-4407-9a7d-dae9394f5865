'use client';

import React from 'react';
import { Container, Box, Typography, Alert } from '@mui/material';
import SalesRepCentre from '../../components/psychological/SalesRepCentre';

export default function SalesRepCenterPage() {
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
          🧠 Sales Rep Psychological Center
        </Typography>
        <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
          Advanced psychological profiling and behavioral optimization for sales representatives
        </Typography>
        
        <Alert severity="info" sx={{ mb: 3 }}>
          <strong>Psychological Intelligence Active:</strong> Real-time archetype detection, stress monitoring, and personalized recommendations based on your psychological profile.
        </Alert>
      </Box>

      <SalesRepCentre />
    </Container>
  );
}
