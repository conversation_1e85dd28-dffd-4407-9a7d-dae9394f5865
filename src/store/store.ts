/**
 * Redux Store Configuration with SA Compliance Tool Integration
 * Includes NeuroMarketing state management and compliance API
 */

import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import { dashboardApi } from '../services/api/dashboard.api';

// Existing BidBeez API slices (to be imported when available)
// import { bidbeezApi } from '../services/api/bidbeez.api';
// import { tenderApi } from '../services/api/tender.api';
// import { userApi } from '../services/api/user.api';

// NeuroMarketing slice
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
  UserPsychologicalState,
  PsychologicalProfile,
  AdaptiveSettings
} from '../services/NeuroMarketingEngine';

// NeuroMarketing State Interface
interface NeuroMarketingState {
  psychologicalState: UserPsychologicalState;
  psychologicalProfile: PsychologicalProfile | null;
  adaptiveSettings: AdaptiveSettings;
  isTracking: boolean;
  sessionData: {
    sessionId: string;
    startTime: number;
    interactions: number;
    stressEvents: number;
    cognitiveLoadEvents: number;
  };
}

// Initial NeuroMarketing State
const initialNeuroMarketingState: NeuroMarketingState = {
  psychologicalState: {
    cognitiveLoad: 0.5,
    emotionalArousal: 0.5,
    stressLevel: 0.3,
    attentionSpan: 0.7,
    decisionFatigue: 0.2,
    frustrationLevel: 0.1,
    engagementLevel: 0.6,
    confidenceLevel: 0.5
  },
  psychologicalProfile: null,
  adaptiveSettings: {
    uiComplexity: 'standard' as any,
    colorScheme: 'neutral' as any,
    animationLevel: 'moderate' as any,
    informationDensity: 'balanced' as any,
    navigationStyle: 'hierarchical' as any,
    gamificationLevel: 'moderate' as any
  },
  isTracking: false,
  sessionData: {
    sessionId: '',
    startTime: 0,
    interactions: 0,
    stressEvents: 0,
    cognitiveLoadEvents: 0
  }
};

// NeuroMarketing Slice
const neuroMarketingSlice = createSlice({
  name: 'neuroMarketing',
  initialState: initialNeuroMarketingState,
  reducers: {
    updatePsychologicalState: (state, action: PayloadAction<Partial<UserPsychologicalState>>) => {
      state.psychologicalState = { ...state.psychologicalState, ...action.payload };
    },
    
    setPsychologicalProfile: (state, action: PayloadAction<PsychologicalProfile>) => {
      state.psychologicalProfile = action.payload;
    },
    
    updateAdaptiveSettings: (state, action: PayloadAction<Partial<AdaptiveSettings>>) => {
      state.adaptiveSettings = { ...state.adaptiveSettings, ...action.payload };
    },
    
    startTracking: (state) => {
      state.isTracking = true;
      state.sessionData.sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      state.sessionData.startTime = Date.now();
    },
    
    stopTracking: (state) => {
      state.isTracking = false;
    },
    
    incrementInteractions: (state) => {
      state.sessionData.interactions += 1;
    },
    
    recordStressEvent: (state) => {
      state.sessionData.stressEvents += 1;
    },
    
    recordCognitiveLoadEvent: (state) => {
      state.sessionData.cognitiveLoadEvents += 1;
    },
    
    resetSession: (state) => {
      state.sessionData = {
        sessionId: '',
        startTime: 0,
        interactions: 0,
        stressEvents: 0,
        cognitiveLoadEvents: 0
      };
    }
  }
});

// Auth slice (simplified for integration)
interface AuthState {
  user: any | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

const initialAuthState: AuthState = {
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: false
};

const authSlice = createSlice({
  name: 'auth',
  initialState: initialAuthState,
  reducers: {
    setCredentials: (state, action: PayloadAction<{ user: any; token: string }>) => {
      state.user = action.payload.user;
      state.token = action.payload.token;
      state.isAuthenticated = true;
      state.isLoading = false;
    },
    
    logout: (state) => {
      state.user = null;
      state.token = null;
      state.isAuthenticated = false;
      state.isLoading = false;
    },
    
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    }
  }
});

// UI slice for general UI state
interface UIState {
  sidebarOpen: boolean;
  theme: 'light' | 'dark';
  language: string;
  notifications: any[];
  loading: {
    global: boolean;
    compliance: boolean;
    tenders: boolean;
  };
}

const initialUIState: UIState = {
  sidebarOpen: true,
  theme: 'light',
  language: 'en',
  notifications: [],
  loading: {
    global: false,
    compliance: false,
    tenders: false
  }
};

const uiSlice = createSlice({
  name: 'ui',
  initialState: initialUIState,
  reducers: {
    toggleSidebar: (state) => {
      state.sidebarOpen = !state.sidebarOpen;
    },
    
    setTheme: (state, action: PayloadAction<'light' | 'dark'>) => {
      state.theme = action.payload;
    },
    
    setLanguage: (state, action: PayloadAction<string>) => {
      state.language = action.payload;
    },
    
    addNotification: (state, action: PayloadAction<any>) => {
      state.notifications.push(action.payload);
    },
    
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(n => n.id !== action.payload);
    },
    
    setLoading: (state, action: PayloadAction<{ key: keyof UIState['loading']; value: boolean }>) => {
      state.loading[action.payload.key] = action.payload.value;
    }
  }
});

// Configure Store
export const store = configureStore({
  reducer: {
    // NeuroMarketing
    neuroMarketing: neuroMarketingSlice.reducer,
    
    // Auth
    auth: authSlice.reducer,
    
    // UI
    ui: uiSlice.reducer,
    
    // API slices
    [dashboardApi.reducerPath]: dashboardApi.reducer,

    // Future BidBeez API slices will be added here
    // [bidbeezApi.reducerPath]: bidbeezApi.reducer,
    // [tenderApi.reducerPath]: tenderApi.reducer,
    // [userApi.reducerPath]: userApi.reducer,
  },
  
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [
          // Ignore these action types
          'persist/PERSIST',
          'persist/REHYDRATE',
          'persist/REGISTER',
        ],
      },
    }).concat(
      dashboardApi.middleware,
      // Future middleware will be added here
      // bidbeezApi.middleware,
      // tenderApi.middleware,
      // userApi.middleware,
    ),
  
  devTools: process.env.NODE_ENV !== 'production',
});

// Setup listeners for automatic refetching
setupListeners(store.dispatch);

// Export types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Export action creators
export const {
  updatePsychologicalState,
  setPsychologicalProfile,
  updateAdaptiveSettings,
  startTracking,
  stopTracking,
  incrementInteractions,
  recordStressEvent,
  recordCognitiveLoadEvent,
  resetSession
} = neuroMarketingSlice.actions;

export const {
  setCredentials,
  logout,
  setLoading: setAuthLoading
} = authSlice.actions;

export const {
  toggleSidebar,
  setTheme,
  setLanguage,
  addNotification,
  removeNotification,
  setLoading: setUILoading
} = uiSlice.actions;

// Selectors
export const selectNeuroMarketingState = (state: RootState) => state.neuroMarketing;
export const selectPsychologicalState = (state: RootState) => state.neuroMarketing.psychologicalState;
export const selectPsychologicalProfile = (state: RootState) => state.neuroMarketing.psychologicalProfile;
export const selectAdaptiveSettings = (state: RootState) => state.neuroMarketing.adaptiveSettings;
export const selectIsTracking = (state: RootState) => state.neuroMarketing.isTracking;
export const selectSessionData = (state: RootState) => state.neuroMarketing.sessionData;

export const selectAuth = (state: RootState) => state.auth;
export const selectUser = (state: RootState) => state.auth.user;
export const selectIsAuthenticated = (state: RootState) => state.auth.isAuthenticated;

export const selectUI = (state: RootState) => state.ui;
export const selectTheme = (state: RootState) => state.ui.theme;
export const selectLanguage = (state: RootState) => state.ui.language;
export const selectNotifications = (state: RootState) => state.ui.notifications;
export const selectLoading = (state: RootState) => state.ui.loading;

export default store;
