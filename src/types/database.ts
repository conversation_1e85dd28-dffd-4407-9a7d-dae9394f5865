/**
 * Database Types - Generated from Supabase Schema
 * Type-safe database operations
 */

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          auth_user_id: string;
          email: string;
          full_name: string;
          avatar_url: string | null;
          phone: string | null;
          user_type: 'individual' | 'team_lead' | 'organization';
          account_status: 'active' | 'suspended' | 'pending' | 'deactivated';
          profile_completed: boolean;
          onboarding_completed: boolean;
          kyc_verified: boolean;
          timezone: string;
          language: string;
          notification_preferences: any;
          created_at: string;
          updated_at: string;
          last_login_at: string | null;
        };
        Insert: {
          id?: string;
          auth_user_id: string;
          email: string;
          full_name: string;
          avatar_url?: string | null;
          phone?: string | null;
          user_type: 'individual' | 'team_lead' | 'organization';
          account_status?: 'active' | 'suspended' | 'pending' | 'deactivated';
          profile_completed?: boolean;
          onboarding_completed?: boolean;
          kyc_verified?: boolean;
          timezone?: string;
          language?: string;
          notification_preferences?: any;
          created_at?: string;
          updated_at?: string;
          last_login_at?: string | null;
        };
        Update: {
          id?: string;
          auth_user_id?: string;
          email?: string;
          full_name?: string;
          avatar_url?: string | null;
          phone?: string | null;
          user_type?: 'individual' | 'team_lead' | 'organization';
          account_status?: 'active' | 'suspended' | 'pending' | 'deactivated';
          profile_completed?: boolean;
          onboarding_completed?: boolean;
          kyc_verified?: boolean;
          timezone?: string;
          language?: string;
          notification_preferences?: any;
          created_at?: string;
          updated_at?: string;
          last_login_at?: string | null;
        };
      };
      user_profiles: {
        Row: {
          id: string;
          user_id: string;
          date_of_birth: string | null;
          gender: string | null;
          nationality: string | null;
          id_number: string | null;
          address_line_1: string | null;
          address_line_2: string | null;
          city: string | null;
          province: string | null;
          postal_code: string | null;
          country: string;
          job_title: string | null;
          company_name: string | null;
          industry: string | null;
          years_of_experience: number;
          skills: any;
          specializations: any;
          certifications: any;
          portfolio_settings: any;
          bid_preferences: any;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          date_of_birth?: string | null;
          gender?: string | null;
          nationality?: string | null;
          id_number?: string | null;
          address_line_1?: string | null;
          address_line_2?: string | null;
          city?: string | null;
          province?: string | null;
          postal_code?: string | null;
          country?: string;
          job_title?: string | null;
          company_name?: string | null;
          industry?: string | null;
          years_of_experience?: number;
          skills?: any;
          specializations?: any;
          certifications?: any;
          portfolio_settings?: any;
          bid_preferences?: any;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          date_of_birth?: string | null;
          gender?: string | null;
          nationality?: string | null;
          id_number?: string | null;
          address_line_1?: string | null;
          address_line_2?: string | null;
          city?: string | null;
          province?: string | null;
          postal_code?: string | null;
          country?: string;
          job_title?: string | null;
          company_name?: string | null;
          industry?: string | null;
          years_of_experience?: number;
          skills?: any;
          specializations?: any;
          certifications?: any;
          portfolio_settings?: any;
          bid_preferences?: any;
          created_at?: string;
          updated_at?: string;
        };
      };
      organizations: {
        Row: {
          id: string;
          name: string;
          slug: string;
          description: string | null;
          logo_url: string | null;
          registration_number: string | null;
          tax_number: string | null;
          industry: string | null;
          company_size: '1-10' | '11-50' | '51-200' | '201-1000' | '1000+' | null;
          email: string | null;
          phone: string | null;
          website: string | null;
          address_line_1: string | null;
          address_line_2: string | null;
          city: string | null;
          province: string | null;
          postal_code: string | null;
          country: string;
          settings: any;
          subscription_tier: string;
          status: 'active' | 'suspended' | 'pending' | 'deactivated';
          verified: boolean;
          owner_id: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          slug: string;
          description?: string | null;
          logo_url?: string | null;
          registration_number?: string | null;
          tax_number?: string | null;
          industry?: string | null;
          company_size?: '1-10' | '11-50' | '51-200' | '201-1000' | '1000+' | null;
          email?: string | null;
          phone?: string | null;
          website?: string | null;
          address_line_1?: string | null;
          address_line_2?: string | null;
          city?: string | null;
          province?: string | null;
          postal_code?: string | null;
          country?: string;
          settings?: any;
          subscription_tier?: string;
          status?: 'active' | 'suspended' | 'pending' | 'deactivated';
          verified?: boolean;
          owner_id: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          slug?: string;
          description?: string | null;
          logo_url?: string | null;
          registration_number?: string | null;
          tax_number?: string | null;
          industry?: string | null;
          company_size?: '1-10' | '11-50' | '51-200' | '201-1000' | '1000+' | null;
          email?: string | null;
          phone?: string | null;
          website?: string | null;
          address_line_1?: string | null;
          address_line_2?: string | null;
          city?: string | null;
          province?: string | null;
          postal_code?: string | null;
          country?: string;
          settings?: any;
          subscription_tier?: string;
          status?: 'active' | 'suspended' | 'pending' | 'deactivated';
          verified?: boolean;
          owner_id?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      team_members: {
        Row: {
          id: string;
          user_id: string;
          organization_id: string;
          role: 'owner' | 'admin' | 'project_manager' | 'estimator' | 'technical_lead' | 'legal_counsel' | 'business_dev' | 'finance' | 'viewer' | 'guest';
          permissions: any;
          title: string | null;
          department: string | null;
          specializations: any;
          availability: any;
          workload: 'available' | 'busy' | 'overloaded' | 'unavailable';
          max_concurrent_bids: number;
          status: 'active' | 'inactive' | 'suspended';
          joined_at: string;
          last_active: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          organization_id: string;
          role: 'owner' | 'admin' | 'project_manager' | 'estimator' | 'technical_lead' | 'legal_counsel' | 'business_dev' | 'finance' | 'viewer' | 'guest';
          permissions?: any;
          title?: string | null;
          department?: string | null;
          specializations?: any;
          availability?: any;
          workload?: 'available' | 'busy' | 'overloaded' | 'unavailable';
          max_concurrent_bids?: number;
          status?: 'active' | 'inactive' | 'suspended';
          joined_at?: string;
          last_active?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          organization_id?: string;
          role?: 'owner' | 'admin' | 'project_manager' | 'estimator' | 'technical_lead' | 'legal_counsel' | 'business_dev' | 'finance' | 'viewer' | 'guest';
          permissions?: any;
          title?: string | null;
          department?: string | null;
          specializations?: any;
          availability?: any;
          workload?: 'available' | 'busy' | 'overloaded' | 'unavailable';
          max_concurrent_bids?: number;
          status?: 'active' | 'inactive' | 'suspended';
          joined_at?: string;
          last_active?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      tenders: {
        Row: {
          id: string;
          title: string;
          reference: string;
          description: string | null;
          issuing_organization: string | null;
          tender_type: string | null;
          category: string | null;
          sector: string | null;
          estimated_value: number | null;
          currency: string;
          published_date: string | null;
          closing_date: string | null;
          validity_period: number | null;
          requirements: any;
          specifications: any;
          evaluation_criteria: any;
          documents: any;
          delivery_location: string | null;
          project_duration: number | null;
          status: 'draft' | 'published' | 'closed' | 'awarded' | 'cancelled';
          source: string | null;
          source_url: string | null;
          scraped_at: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          title: string;
          reference: string;
          description?: string | null;
          issuing_organization?: string | null;
          tender_type?: string | null;
          category?: string | null;
          sector?: string | null;
          estimated_value?: number | null;
          currency?: string;
          published_date?: string | null;
          closing_date?: string | null;
          validity_period?: number | null;
          requirements?: any;
          specifications?: any;
          evaluation_criteria?: any;
          documents?: any;
          delivery_location?: string | null;
          project_duration?: number | null;
          status?: 'draft' | 'published' | 'closed' | 'awarded' | 'cancelled';
          source?: string | null;
          source_url?: string | null;
          scraped_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          title?: string;
          reference?: string;
          description?: string | null;
          issuing_organization?: string | null;
          tender_type?: string | null;
          category?: string | null;
          sector?: string | null;
          estimated_value?: number | null;
          currency?: string;
          published_date?: string | null;
          closing_date?: string | null;
          validity_period?: number | null;
          requirements?: any;
          specifications?: any;
          evaluation_criteria?: any;
          documents?: any;
          delivery_location?: string | null;
          project_duration?: number | null;
          status?: 'draft' | 'published' | 'closed' | 'awarded' | 'cancelled';
          source?: string | null;
          source_url?: string | null;
          scraped_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      subscriptions: {
        Row: {
          id: string;
          user_id: string;
          organization_id: string | null;
          plan_id: string;
          status: 'active' | 'cancelled' | 'past_due' | 'unpaid' | 'trialing';
          current_period_start: string;
          current_period_end: string;
          amount: number;
          currency: string;
          stripe_subscription_id: string | null;
          stripe_customer_id: string | null;
          trial_start: string | null;
          trial_end: string | null;
          cancelled_at: string | null;
          cancel_at_period_end: boolean;
          usage_data: any;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          organization_id?: string | null;
          plan_id: string;
          status: 'active' | 'cancelled' | 'past_due' | 'unpaid' | 'trialing';
          current_period_start: string;
          current_period_end: string;
          amount: number;
          currency?: string;
          stripe_subscription_id?: string | null;
          stripe_customer_id?: string | null;
          trial_start?: string | null;
          trial_end?: string | null;
          cancelled_at?: string | null;
          cancel_at_period_end?: boolean;
          usage_data?: any;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          organization_id?: string | null;
          plan_id?: string;
          status?: 'active' | 'cancelled' | 'past_due' | 'unpaid' | 'trialing';
          current_period_start?: string;
          current_period_end?: string;
          amount?: number;
          currency?: string;
          stripe_subscription_id?: string | null;
          stripe_customer_id?: string | null;
          trial_start?: string | null;
          trial_end?: string | null;
          cancelled_at?: string | null;
          cancel_at_period_end?: boolean;
          usage_data?: any;
          created_at?: string;
          updated_at?: string;
        };
      };
      messages: {
        Row: {
          id: string;
          channel_id: string;
          sender_id: string | null;
          content: string;
          message_type: 'text' | 'file' | 'image' | 'voice' | 'video' | 'system' | 'tender_update';
          attachments: any;
          mentions: any;
          reactions: any;
          parent_message_id: string | null;
          thread_count: number;
          edited: boolean;
          edited_at: string | null;
          deleted: boolean;
          deleted_at: string | null;
          tender_context: any;
          linked_tasks: any;
          linked_documents: any;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          channel_id: string;
          sender_id?: string | null;
          content: string;
          message_type?: 'text' | 'file' | 'image' | 'voice' | 'video' | 'system' | 'tender_update';
          attachments?: any;
          mentions?: any;
          reactions?: any;
          parent_message_id?: string | null;
          thread_count?: number;
          edited?: boolean;
          edited_at?: string | null;
          deleted?: boolean;
          deleted_at?: string | null;
          tender_context?: any;
          linked_tasks?: any;
          linked_documents?: any;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          channel_id?: string;
          sender_id?: string | null;
          content?: string;
          message_type?: 'text' | 'file' | 'image' | 'voice' | 'video' | 'system' | 'tender_update';
          attachments?: any;
          mentions?: any;
          reactions?: any;
          parent_message_id?: string | null;
          thread_count?: number;
          edited?: boolean;
          edited_at?: string | null;
          deleted?: boolean;
          deleted_at?: string | null;
          tender_context?: any;
          linked_tasks?: any;
          linked_documents?: any;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
}
