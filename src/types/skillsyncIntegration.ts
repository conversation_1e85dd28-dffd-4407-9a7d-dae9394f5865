/**
 * SkillSync-BidBeez Cross-Platform Integration Types
 * Bidirectional talent marketplace for tender preparation teams
 */

import { UserRole, TeamMember } from './teamCollaboration';
import { TenderNotification } from './notifications';

// ===== CROSS-PLATFORM USER INTEGRATION =====

export interface CrossPlatformUser {
  id: string;
  email: string;
  
  // Platform presence
  bidbeezProfile?: BidBeezProfile;
  skillsyncProfile?: SkillSyncProfile;
  
  // Cross-platform settings
  crossPlatformEnabled: boolean;
  dataSharing: DataSharingSettings;
  
  // Integration status
  linkedAt?: string;
  lastSyncAt?: string;
  syncStatus: 'active' | 'paused' | 'error';
  
  createdAt: string;
  updatedAt: string;
}

export interface BidBeezProfile {
  userId: string;
  organizationId?: string;
  role?: UserRole;
  subscriptionTier: string;
  
  // Tender activity
  activeBids: number;
  completedBids: number;
  winRate: number;
  
  // Team leadership
  isTeamLead: boolean;
  canHireExternal: boolean;
  hiringBudget?: number;
  
  // Preferences
  preferredSkills: string[];
  budgetRange: BudgetRange;
  projectTypes: string[];
}

export interface SkillSyncProfile {
  userId: string;
  professionalTitle: string;
  skills: Skill[];
  experience: Experience[];
  certifications: Certification[];
  
  // Availability
  availableForTenders: boolean;
  hourlyRate: number;
  currency: string;
  availability: AvailabilitySchedule;
  
  // Tender experience
  tenderExperience: TenderExperience;
  portfolioItems: PortfolioItem[];
  
  // Ratings and reviews
  rating: number;
  reviewCount: number;
  completionRate: number;
  
  // Preferences
  preferredProjectTypes: string[];
  minimumProjectValue: number;
  maximumTeamSize: number;
}

// ===== TALENT MARKETPLACE =====

export interface TenderTalentRequest {
  id: string;
  tenderId: string;
  tenderTitle: string;
  tenderReference: string;
  organizationId: string;
  requestedBy: string;
  
  // Talent requirements
  requiredSkills: SkillRequirement[];
  preferredExperience: number; // years
  projectDuration: number; // days
  workload: number; // hours per week
  
  // Compensation
  budgetRange: BudgetRange;
  paymentTerms: PaymentTerms;
  
  // Project details
  projectDescription: string;
  responsibilities: string[];
  deliverables: string[];
  timeline: ProjectTimeline;
  
  // Requirements
  securityClearance?: string;
  locationRequirement: 'remote' | 'hybrid' | 'onsite';
  travelRequired: boolean;
  
  // Request status
  status: TalentRequestStatus;
  urgency: 'low' | 'medium' | 'high' | 'urgent';
  
  // Responses
  applications: TalentApplication[];
  shortlistedCandidates: string[];
  selectedCandidate?: string;
  
  // Timing
  applicationDeadline: string;
  projectStartDate: string;
  tenderSubmissionDate: string;
  
  createdAt: string;
  updatedAt: string;
}

export type TalentRequestStatus = 
  | 'draft'
  | 'published'
  | 'receiving_applications'
  | 'reviewing'
  | 'interviewing'
  | 'candidate_selected'
  | 'project_started'
  | 'completed'
  | 'cancelled';

export interface SkillRequirement {
  skill: string;
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  required: boolean;
  weight: number; // 1-10 importance
  yearsRequired?: number;
  certificationRequired?: boolean;
}

export interface BudgetRange {
  min: number;
  max: number;
  currency: string;
  type: 'hourly' | 'daily' | 'project' | 'monthly';
}

export interface PaymentTerms {
  type: 'hourly' | 'milestone' | 'completion' | 'monthly';
  milestones?: PaymentMilestone[];
  paymentSchedule: string;
  currency: string;
  invoicingRequirements: string[];
}

export interface PaymentMilestone {
  id: string;
  name: string;
  description: string;
  percentage: number;
  amount: number;
  dueDate: string;
  deliverables: string[];
}

export interface ProjectTimeline {
  phases: ProjectPhase[];
  keyMilestones: TimelineMilestone[];
  criticalPath: string[];
  bufferTime: number; // days
}

export interface ProjectPhase {
  id: string;
  name: string;
  description: string;
  startDate: string;
  endDate: string;
  dependencies: string[];
  deliverables: string[];
}

export interface TimelineMilestone {
  id: string;
  name: string;
  date: string;
  critical: boolean;
  description: string;
}

// ===== TALENT APPLICATIONS =====

export interface TalentApplication {
  id: string;
  talentRequestId: string;
  applicantId: string;
  skillsyncUserId: string;
  
  // Application details
  coverLetter: string;
  proposedRate: number;
  currency: string;
  availability: ApplicationAvailability;
  
  // Qualifications
  relevantExperience: RelevantExperience[];
  portfolioSamples: PortfolioSample[];
  references: Reference[];
  
  // Proposal
  approachDescription: string;
  timelineProposal: string;
  valueProposition: string;
  riskMitigation: string[];
  
  // Application status
  status: ApplicationStatus;
  submittedAt: string;
  reviewedAt?: string;
  responseAt?: string;
  
  // Evaluation
  skillsMatch: number; // 0-100
  experienceMatch: number; // 0-100
  budgetFit: number; // 0-100
  overallScore: number; // 0-100
  
  // Communication
  messages: ApplicationMessage[];
  interviewScheduled?: InterviewDetails;
  
  createdAt: string;
  updatedAt: string;
}

export type ApplicationStatus = 
  | 'submitted'
  | 'under_review'
  | 'shortlisted'
  | 'interview_scheduled'
  | 'interview_completed'
  | 'selected'
  | 'rejected'
  | 'withdrawn';

export interface ApplicationAvailability {
  startDate: string;
  endDate: string;
  hoursPerWeek: number;
  timezone: string;
  workingHours: any;
  unavailableDates: string[];
  conflictingProjects: ConflictingProject[];
}

export interface ConflictingProject {
  name: string;
  endDate: string;
  hoursPerWeek: number;
  impact: 'none' | 'minimal' | 'moderate' | 'significant';
}

export interface RelevantExperience {
  projectName: string;
  role: string;
  duration: string;
  skills: string[];
  achievements: string[];
  similarityScore: number; // 0-100
}

export interface PortfolioSample {
  title: string;
  description: string;
  url?: string;
  fileUrl?: string;
  skills: string[];
  outcome: string;
  relevanceScore: number; // 0-100
}

export interface Reference {
  name: string;
  title: string;
  company: string;
  email: string;
  phone?: string;
  relationship: string;
  projectContext: string;
  verified: boolean;
}

export interface ApplicationMessage {
  id: string;
  senderId: string;
  senderType: 'applicant' | 'employer';
  message: string;
  timestamp: string;
  read: boolean;
}

export interface InterviewDetails {
  scheduledAt: string;
  duration: number; // minutes
  type: 'phone' | 'video' | 'in_person';
  interviewers: Interviewer[];
  agenda: string[];
  meetingLink?: string;
  location?: string;
  status: 'scheduled' | 'completed' | 'cancelled' | 'rescheduled';
  feedback?: InterviewFeedback;
}

export interface Interviewer {
  userId: string;
  name: string;
  role: string;
  focus: string[]; // areas they'll assess
}

export interface InterviewFeedback {
  technicalSkills: number; // 1-10
  communication: number; // 1-10
  culturalFit: number; // 1-10
  experience: number; // 1-10
  overallRating: number; // 1-10
  strengths: string[];
  concerns: string[];
  recommendation: 'strong_hire' | 'hire' | 'maybe' | 'no_hire';
  notes: string;
}

// ===== SKILLSYNC OPPORTUNITY FEED =====

export interface SkillSyncOpportunityFeed {
  userId: string;
  opportunities: TenderOpportunity[];
  filters: OpportunityFilters;
  preferences: OpportunityPreferences;
  lastUpdated: string;
}

export interface TenderOpportunity {
  id: string;
  talentRequestId: string;
  
  // Basic info
  title: string;
  company: string;
  location: string;
  type: 'tender_support' | 'estimation' | 'technical_review' | 'compliance' | 'project_management';
  
  // Project details
  description: string;
  duration: string;
  startDate: string;
  workload: string;
  
  // Requirements
  skills: string[];
  experience: string;
  qualifications: string[];
  
  // Compensation
  budget: string;
  paymentType: string;
  
  // Matching
  matchScore: number; // 0-100
  matchReasons: string[];
  
  // Status
  applicants: number;
  timeLeft: string;
  urgency: 'low' | 'medium' | 'high' | 'urgent';
  
  // Engagement
  viewed: boolean;
  bookmarked: boolean;
  applied: boolean;
  
  postedAt: string;
  deadline: string;
}

export interface OpportunityFilters {
  skills: string[];
  experienceLevel: string[];
  projectType: string[];
  duration: string[];
  budget: BudgetRange;
  location: string[];
  workType: ('remote' | 'hybrid' | 'onsite')[];
  urgency: string[];
}

export interface OpportunityPreferences {
  autoApply: boolean;
  autoApplyFilters: OpportunityFilters;
  notificationSettings: OpportunityNotificationSettings;
  blacklistedCompanies: string[];
  preferredCompanies: string[];
  minimumMatchScore: number;
}

export interface OpportunityNotificationSettings {
  newOpportunities: boolean;
  matchingOpportunities: boolean;
  applicationUpdates: boolean;
  interviewInvites: boolean;
  projectStartReminders: boolean;
  
  // Delivery preferences
  inApp: boolean;
  email: boolean;
  sms: boolean;
  push: boolean;
  
  // Timing
  immediateNotification: boolean;
  dailyDigest: boolean;
  weeklyDigest: boolean;
}

// ===== CROSS-PLATFORM MONETIZATION =====

export interface CrossPlatformMonetization {
  // BidBeez side (employers)
  talentRequestFees: TalentRequestFees;
  
  // SkillSync side (talent)
  platformCommission: PlatformCommission;
  
  // Shared revenue
  revenueSharing: RevenueSharing;
  
  // Payment processing
  paymentProcessing: PaymentProcessing;
}

export interface TalentRequestFees {
  // Posting fees
  basicPostingFee: number;
  featuredPostingFee: number;
  urgentPostingFee: number;
  
  // Success fees
  hiringSuccessFee: number; // percentage of project value
  minimumSuccessFee: number;
  maximumSuccessFee: number;
  
  // Premium features
  talentSearchAccess: number; // monthly
  directMessaging: number; // per message
  prioritySupport: number; // monthly
  
  // Subscription tiers
  basicTier: SubscriptionTier;
  professionalTier: SubscriptionTier;
  enterpriseTier: SubscriptionTier;
}

export interface PlatformCommission {
  // Commission rates
  standardCommission: number; // percentage
  premiumMemberCommission: number; // reduced rate
  volumeDiscounts: VolumeDiscount[];
  
  // Fee structure
  minimumCommission: number;
  maximumCommission: number;
  
  // Payment timing
  commissionTiming: 'immediate' | 'milestone' | 'completion';
  
  // Incentives
  newUserDiscount: number; // percentage off first project
  loyaltyBonus: number; // percentage bonus after X projects
  referralBonus: number; // flat fee for referrals
}

export interface VolumeDiscount {
  minimumProjects: number;
  discountPercentage: number;
  period: 'monthly' | 'quarterly' | 'annually';
}

export interface SubscriptionTier {
  name: string;
  monthlyFee: number;
  features: string[];
  talentRequestsIncluded: number;
  additionalRequestFee: number;
  successFeeDiscount: number; // percentage
}

export interface RevenueSharing {
  bidbeezShare: number; // percentage
  skillsyncShare: number; // percentage
  
  // Revenue streams
  subscriptionRevenue: RevenueShare;
  transactionRevenue: RevenueShare;
  advertisingRevenue: RevenueShare;
  
  // Settlement
  settlementPeriod: 'weekly' | 'monthly';
  minimumPayout: number;
}

export interface RevenueShare {
  bidbeezPercentage: number;
  skillsyncPercentage: number;
  sharedCostsPercentage: number;
}

export interface PaymentProcessing {
  provider: 'stripe' | 'paypal' | 'bank';
  processingFees: ProcessingFees;
  payoutSchedule: PayoutSchedule;
  disputeHandling: DisputeHandling;
}

export interface ProcessingFees {
  creditCardFee: number; // percentage + fixed
  bankTransferFee: number;
  internationalFee: number;
  chargebackFee: number;
}

export interface PayoutSchedule {
  frequency: 'daily' | 'weekly' | 'monthly';
  holdPeriod: number; // days
  minimumPayout: number;
  currency: string;
}

export interface DisputeHandling {
  mediationFee: number;
  arbitrationFee: number;
  resolutionTimeframe: number; // days
  escrowHoldPeriod: number; // days
}

// ===== INTEGRATION ANALYTICS =====

export interface CrossPlatformAnalytics {
  period: {
    start: string;
    end: string;
  };
  
  // User engagement
  crossPlatformUsers: number;
  activeIntegrations: number;
  userRetention: number;
  
  // Marketplace metrics
  talentRequestsPosted: number;
  applicationsSubmitted: number;
  hiresCompleted: number;
  successRate: number;
  
  // Financial metrics
  totalTransactionValue: number;
  platformRevenue: number;
  averageProjectValue: number;
  revenueGrowth: number;
  
  // Quality metrics
  averageRating: number;
  completionRate: number;
  disputeRate: number;
  userSatisfaction: number;
  
  // Platform distribution
  bidbeezOriginatedProjects: number;
  skillsyncOriginatedProjects: number;
  crossPlatformProjects: number;
}

// ===== INTEGRATION EVENTS =====

export interface CrossPlatformEvent {
  id: string;
  type: CrossPlatformEventType;
  sourceUserId: string;
  targetUserId?: string;
  platform: 'bidbeez' | 'skillsync' | 'both';
  
  // Event data
  data: Record<string, any>;
  timestamp: string;
  
  // Processing
  processed: boolean;
  syncRequired: boolean;
  
  // Notifications
  notificationsSent: string[];
  followUpActions: string[];
}

export type CrossPlatformEventType = 
  | 'talent_request_posted'
  | 'application_submitted'
  | 'candidate_shortlisted'
  | 'interview_scheduled'
  | 'hire_completed'
  | 'project_started'
  | 'milestone_completed'
  | 'project_completed'
  | 'payment_processed'
  | 'rating_submitted'
  | 'dispute_raised'
  | 'user_linked'
  | 'user_unlinked';

// ===== HELPER TYPES =====

export interface Skill {
  name: string;
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  yearsOfExperience: number;
  verified: boolean;
  endorsements: number;
}

export interface Experience {
  company: string;
  position: string;
  startDate: string;
  endDate?: string;
  description: string;
  skills: string[];
  achievements: string[];
}

export interface Certification {
  name: string;
  issuingOrganization: string;
  issueDate: string;
  expiryDate?: string;
  credentialId?: string;
  verified: boolean;
}

export interface AvailabilitySchedule {
  timezone: string;
  workingHours: any;
  unavailableDates: string[];
  maxHoursPerWeek: number;
  preferredProjectDuration: string;
}

export interface TenderExperience {
  totalProjects: number;
  totalValue: number;
  averageProjectSize: number;
  successRate: number;
  specializations: string[];
  clientTypes: string[];
  averageRating: number;
}

export interface PortfolioItem {
  title: string;
  description: string;
  client: string;
  value: number;
  duration: string;
  role: string;
  skills: string[];
  outcomes: string[];
  testimonial?: string;
  images?: string[];
  documents?: string[];
}

export interface DataSharingSettings {
  shareProfile: boolean;
  shareRatings: boolean;
  sharePortfolio: boolean;
  shareAvailability: boolean;
  sharePreferences: boolean;
  allowDirectContact: boolean;
  showInSearch: boolean;
}
