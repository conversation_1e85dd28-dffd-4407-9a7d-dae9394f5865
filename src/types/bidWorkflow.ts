/**
 * Bid Interest Workflow Types
 * Comprehensive type definitions for the "INTERESTED IN BID" workflow system
 */

export interface TenderNotification {
  id: string;
  tenderId: string;
  title: string;
  organization: string;
  estimatedValue: number;
  closingDate: string;
  location: string;
  category: string;
  matchScore: number;
  urgencyLevel: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  requirements: string[];
  documents: TenderDocument[];
  createdAt: string;
  status: 'new' | 'viewed' | 'interested' | 'dismissed';
}

export interface TenderDocument {
  id: string;
  name: string;
  type: 'specification' | 'terms' | 'drawings' | 'addendum' | 'other';
  url: string;
  size: number;
  downloadedAt?: string;
  processed: boolean;
}

// Bid Commitment Funnel States
export enum BidWorkflowState {
  DISCOVERED = 'discovered',      // In notifications feed
  INTERESTED = 'interested',      // Moved to active workspace
  ANALYZING = 'analyzing',        // AI analysis in progress
  PREPARING = 'preparing',        // User working on bid
  READY = 'ready',               // Ready for submission
  SUBMITTED = 'submitted',        // Bid submitted
  AWARDED = 'awarded',           // Bid won
  LOST = 'lost'                  // Bid lost
}

export interface BidInterest {
  id: string;
  tenderId: string;
  userId: string;
  state: BidWorkflowState;
  interestedAt: string;
  lastUpdated: string;
  commitmentLevel: number; // 0-100 scale
  analysisCompleted: boolean;
  beeWorkersAssigned: string[];
  notes: string;
  metadata: Record<string, any>;
}

export interface ActiveBidWorkspace {
  bidInterest: BidInterest;
  tender: TenderNotification;
  
  // AI Analysis Results
  tenderAnalysis: {
    aiInsights: DocumentAnalysis;
    riskAssessment: RiskAssessment;
    complianceStatus: ComplianceReport;
    competitiveAnalysis: CompetitorAnalysis;
    feasibilityScore: number;
    recommendedActions: string[];
  };
  
  // Governance Integration
  governanceEngine: {
    complianceChecklist: ComplianceItem[];
    riskMitigation: MitigationStrategy[];
    approvalWorkflow: ApprovalStep[];
    governanceScore: number;
  };
  
  // Bee Worker Assignment
  beeWorkerTasks: {
    documentCollection: BeeTask[];
    siteVisits: BeeTask[];
    complianceVerification: BeeTask[];
    marketResearch: BeeTask[];
    availableBees: AvailableBee[];
  };
  
  // Document Workspace
  documentWorkspace: {
    originalDocs: TenderDocument[];
    workingDrafts: BidDocument[];
    submissionPackage: FinalBid[];
    processingStatus: DocumentProcessingStatus;
  };
  
  // Progress Tracking
  progressTracking: {
    overallProgress: number;
    milestones: Milestone[];
    deadlines: Deadline[];
    criticalPath: string[];
  };
}

// Supporting interfaces from existing systems
export interface DocumentAnalysis {
  keyRequirements: string[];
  technicalSpecs: TechnicalSpec[];
  complianceItems: ComplianceItem[];
  extractedData: Record<string, any>;
  confidence: number;
}

export interface RiskAssessment {
  overallRisk: 'low' | 'medium' | 'high' | 'critical';
  riskFactors: RiskFactor[];
  mitigationStrategies: string[];
  contingencyPlan: string;
  riskScore: number;
}

export interface ComplianceReport {
  overallScore: number;
  mandatoryItems: ComplianceItem[];
  optionalItems: ComplianceItem[];
  missingDocuments: string[];
  riskLevel: 'low' | 'medium' | 'high';
  complianceGaps: ComplianceGap[];
}

export interface CompetitorAnalysis {
  competitorCount: number;
  threatLevel: 'low' | 'medium' | 'high';
  marketPosition: string;
  winProbability: number;
  competitiveAdvantages: string[];
  recommendations: string[];
}

export interface ComplianceItem {
  id: string;
  name: string;
  description: string;
  mandatory: boolean;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  evidence: string[];
  deadline?: string;
}

export interface TechnicalSpec {
  id: string;
  requirement: string;
  mandatory: boolean;
  measurable: boolean;
  ourCapability: 'excellent' | 'good' | 'adequate' | 'poor' | 'missing';
  complianceStrategy: string;
}

export interface RiskFactor {
  category: 'timeline' | 'financial' | 'technical' | 'compliance' | 'market';
  description: string;
  probability: number; // 0-1
  impact: number; // 0-1
  mitigation: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface MitigationStrategy {
  id: string;
  riskId: string;
  strategy: string;
  cost: number;
  timeRequired: string;
  effectiveness: number; // 0-1
  responsible: string;
}

export interface ApprovalStep {
  id: string;
  name: string;
  description: string;
  required: boolean;
  completed: boolean;
  approver: string;
  deadline: string;
  dependencies: string[];
}

export interface BeeTask {
  id: string;
  type: 'document_collection' | 'site_visit' | 'compliance_check' | 'market_research';
  title: string;
  description: string;
  location?: string;
  deadline: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'assigned' | 'in_progress' | 'completed' | 'failed';
  assignedBee?: string;
  estimatedCost: number;
  estimatedDuration: string;
  requirements: string[];
}

export interface AvailableBee {
  id: string;
  name: string;
  skills: string[];
  location: string;
  availability: string;
  rating: number;
  cost: number;
  distance: number;
  matchScore: number;
  verificationLevel: string;
}

export interface BidDocument {
  id: string;
  name: string;
  type: 'technical' | 'commercial' | 'compliance' | 'supporting';
  status: 'draft' | 'review' | 'approved' | 'final';
  content: string;
  lastModified: string;
  author: string;
  version: number;
}

export interface FinalBid {
  id: string;
  documentId: string;
  name: string;
  type: string;
  finalVersion: boolean;
  submissionReady: boolean;
  checksum: string;
}

export interface DocumentProcessingStatus {
  totalDocuments: number;
  processed: number;
  analyzing: number;
  failed: number;
  lastUpdate: string;
}

export interface Milestone {
  id: string;
  name: string;
  description: string;
  targetDate: string;
  completed: boolean;
  completedDate?: string;
  critical: boolean;
}

export interface Deadline {
  id: string;
  name: string;
  date: string;
  type: 'submission' | 'document' | 'briefing' | 'site_visit' | 'other';
  priority: 'low' | 'medium' | 'high' | 'critical';
  daysRemaining: number;
  status: 'upcoming' | 'due_soon' | 'overdue' | 'completed';
}

export interface ComplianceGap {
  id: string;
  requirement: string;
  currentStatus: string;
  requiredStatus: string;
  gap: string;
  actionRequired: string;
  estimatedCost: number;
  estimatedTime: string;
  canAutoResolve: boolean;
}

// Workflow Actions
export interface BidWorkflowAction {
  type: 'SHOW_INTEREST' | 'START_ANALYSIS' | 'ASSIGN_BEES' | 'PREPARE_SUBMISSION' | 'SUBMIT_BID';
  payload: any;
  timestamp: string;
  userId: string;
}

// Notification Enhancement
export interface EnhancedNotification extends TenderNotification {
  psychologicalTriggers: {
    scarcity: string;
    urgency: string;
    social_proof: string;
    authority: string;
    commitment: string;
  };
  actionButtons: {
    primary: 'INTERESTED_IN_BID';
    secondary: 'DISMISS' | 'REMIND_LATER';
  };
}
