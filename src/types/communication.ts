/**
 * Tender-Integrated Communication Types
 * Context-aware communication system integrated with bid workflows
 */

// ===== TENDER-CENTRIC COMMUNICATION =====

export interface TenderCommunicationWorkspace {
  id: string;
  tenderId: string;
  tenderTitle: string;
  tenderReference: string;
  organizationId: string;
  teamBidInterestId: string;
  
  // Auto-created channels based on tender workflow
  channels: TenderChannel[];
  directMessages: DirectMessageThread[];
  
  // Tender-specific context
  tenderContext: TenderContext;
  
  // Communication settings
  settings: CommunicationSettings;
  
  // Activity tracking
  lastActivity: string;
  totalMessages: number;
  activeParticipants: string[];
  
  createdAt: string;
  updatedAt: string;
}

export interface TenderChannel {
  id: string;
  tenderId: string;
  name: string;
  type: ChannelType;
  description: string;
  
  // Auto-creation rules
  autoCreated: boolean;
  createdTrigger?: 'team_interest' | 'role_assignment' | 'milestone_reached' | 'manual';
  
  // Channel participants
  participants: ChannelParticipant[];
  
  // Messages and threads
  messages: TenderMessage[];
  pinnedMessages: string[];
  
  // Tender integration
  linkedTasks: string[];
  linkedDocuments: string[];
  linkedMilestones: string[];
  
  // Channel settings
  settings: ChannelSettings;
  
  createdAt: string;
  updatedAt: string;
}

export type ChannelType = 
  | 'main_discussion'      // Primary tender discussion
  | 'technical'           // Technical specifications and drawings
  | 'commercial'          // Pricing, costs, commercial terms
  | 'compliance'          // Legal, compliance, regulatory
  | 'project_management'  // Timeline, milestones, coordination
  | 'document_review'     // Document-specific discussions
  | 'task_coordination'   // Task-specific communication
  | 'client_communication' // External client discussions
  | 'supplier_coordination' // Subcontractor/supplier discussions
  | 'announcements'       // Important updates and announcements
  | 'general'            // General team chat
  | 'private';           // Private discussions

export interface ChannelParticipant {
  userId: string;
  memberId: string;
  role: string;
  joinedAt: string;
  lastReadAt: string;
  permissions: ChannelPermission[];
  notificationSettings: ParticipantNotificationSettings;
}

export interface ChannelPermission {
  action: 'read' | 'write' | 'delete' | 'manage' | 'invite' | 'moderate';
  granted: boolean;
}

export interface TenderMessage {
  id: string;
  channelId: string;
  tenderId: string;
  
  // Message content
  content: string;
  messageType: MessageType;
  
  // Author information
  authorId: string;
  authorName: string;
  authorRole: string;
  
  // Tender context integration
  tenderContext: MessageTenderContext;
  
  // Message features
  mentions: MessageMention[];
  attachments: MessageAttachment[];
  reactions: MessageReaction[];
  
  // Threading
  threadId?: string;
  parentMessageId?: string;
  replies: TenderMessage[];
  
  // Message status
  edited: boolean;
  editedAt?: string;
  deleted: boolean;
  deletedAt?: string;
  
  // Tender workflow integration
  linkedTasks: string[];
  linkedDocuments: string[];
  linkedMilestones: string[];
  actionItems: MessageActionItem[];
  
  timestamp: string;
  readBy: MessageReadStatus[];
}

export type MessageType = 
  | 'text'
  | 'file'
  | 'image'
  | 'document'
  | 'task_update'
  | 'milestone_update'
  | 'tender_update'
  | 'meeting_invite'
  | 'approval_request'
  | 'system_notification'
  | 'voice_note'
  | 'video_message';

export interface MessageTenderContext {
  tenderReference: string;
  tenderTitle: string;
  relatedSection?: string; // Which part of tender this relates to
  urgencyLevel: 'low' | 'medium' | 'high' | 'critical';
  deadline?: string;
  requiresAction: boolean;
  actionType?: 'review' | 'approve' | 'complete' | 'respond' | 'escalate';
}

export interface MessageMention {
  userId: string;
  userName: string;
  type: 'user' | 'role' | 'channel' | 'tender' | 'task' | 'document';
  context?: string;
  notified: boolean;
}

export interface MessageAttachment {
  id: string;
  name: string;
  type: 'file' | 'image' | 'document' | 'drawing' | 'spreadsheet' | 'presentation';
  size: number;
  url: string;
  thumbnailUrl?: string;
  
  // Tender integration
  linkedToTender: boolean;
  documentCategory?: 'specification' | 'drawing' | 'schedule' | 'pricing' | 'compliance' | 'other';
  
  uploadedBy: string;
  uploadedAt: string;
}

export interface MessageReaction {
  emoji: string;
  userId: string;
  userName: string;
  timestamp: string;
}

export interface MessageActionItem {
  id: string;
  description: string;
  assignedTo: string;
  dueDate?: string;
  priority: 'low' | 'medium' | 'high';
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  linkedTaskId?: string;
  createdAt: string;
}

export interface MessageReadStatus {
  userId: string;
  readAt: string;
}

// ===== TENDER CONTEXT =====

export interface TenderContext {
  tenderId: string;
  tenderTitle: string;
  tenderReference: string;
  organization: string;
  
  // Key tender information
  closingDate: string;
  estimatedValue: number;
  location: string;
  category: string;
  
  // Bid status
  bidStatus: 'interested' | 'analyzing' | 'preparing' | 'ready' | 'submitted';
  commitmentLevel: number;
  
  // Team information
  teamMembers: TenderTeamMember[];
  assignedRoles: string[];
  
  // Important dates and milestones
  keyDates: TenderKeyDate[];
  milestones: TenderMilestone[];
  
  // Quick access links
  quickLinks: TenderQuickLink[];
}

export interface TenderTeamMember {
  userId: string;
  name: string;
  role: string;
  responsibilities: string[];
  avatar?: string;
  status: 'online' | 'offline' | 'busy' | 'away';
}

export interface TenderKeyDate {
  id: string;
  name: string;
  date: string;
  type: 'deadline' | 'milestone' | 'meeting' | 'submission';
  critical: boolean;
}

export interface TenderMilestone {
  id: string;
  name: string;
  description: string;
  dueDate: string;
  status: 'pending' | 'in_progress' | 'completed' | 'overdue';
  assignedTo: string[];
  progress: number;
}

export interface TenderQuickLink {
  id: string;
  name: string;
  url: string;
  type: 'document' | 'task' | 'meeting' | 'external' | 'analysis';
  icon: string;
}

// ===== DIRECT MESSAGES =====

export interface DirectMessageThread {
  id: string;
  tenderId?: string; // Optional tender context
  participants: string[];
  
  // Thread context
  subject?: string;
  tenderContext?: TenderContext;
  
  messages: TenderMessage[];
  lastMessage?: TenderMessage;
  
  // Thread settings
  muted: boolean;
  archived: boolean;
  
  createdAt: string;
  updatedAt: string;
}

// ===== COMMUNICATION SETTINGS =====

export interface CommunicationSettings {
  // Auto-channel creation
  autoCreateChannels: boolean;
  channelCreationRules: ChannelCreationRule[];
  
  // Notifications
  globalNotifications: boolean;
  mentionNotifications: boolean;
  deadlineAlerts: boolean;
  milestoneNotifications: boolean;
  
  // Integration settings
  emailIntegration: boolean;
  mobileNotifications: boolean;
  desktopNotifications: boolean;
  
  // Tender-specific settings
  tenderContextAlways: boolean;
  autoLinkDocuments: boolean;
  autoLinkTasks: boolean;
  
  // Privacy settings
  allowExternalGuests: boolean;
  requireApprovalForGuests: boolean;
  dataRetentionPeriod: number; // days
}

export interface ChannelCreationRule {
  trigger: 'team_interest' | 'role_assignment' | 'milestone_reached' | 'document_uploaded';
  channelType: ChannelType;
  autoInvite: string[]; // roles to auto-invite
  template?: string; // channel template to use
}

export interface ChannelSettings {
  // Access control
  private: boolean;
  inviteOnly: boolean;
  moderationRequired: boolean;
  
  // Features
  allowFileUploads: boolean;
  allowVoiceMessages: boolean;
  allowVideoMessages: boolean;
  allowScreenSharing: boolean;
  
  // Integration
  linkedToTasks: boolean;
  linkedToDocuments: boolean;
  linkedToMilestones: boolean;
  
  // Notifications
  notifyOnMention: boolean;
  notifyOnFileUpload: boolean;
  notifyOnTaskUpdate: boolean;
  
  // Retention
  messageRetentionDays: number;
  autoArchiveInactive: boolean;
  inactivityThresholdDays: number;
}

export interface ParticipantNotificationSettings {
  allMessages: boolean;
  mentionsOnly: boolean;
  importantOnly: boolean;
  muted: boolean;
  
  // Tender-specific notifications
  deadlineAlerts: boolean;
  milestoneUpdates: boolean;
  taskAssignments: boolean;
  documentUpdates: boolean;
  
  // Delivery preferences
  inApp: boolean;
  email: boolean;
  sms: boolean;
  push: boolean;
  
  // Timing
  quietHours: {
    enabled: boolean;
    startTime: string;
    endTime: string;
    timezone: string;
  };
}

// ===== REAL-TIME FEATURES =====

export interface TypingIndicator {
  channelId: string;
  userId: string;
  userName: string;
  timestamp: string;
}

export interface OnlineStatus {
  userId: string;
  status: 'online' | 'offline' | 'busy' | 'away';
  lastSeen: string;
  currentChannel?: string;
}

export interface VoiceCall {
  id: string;
  channelId?: string;
  tenderId?: string;
  participants: string[];
  initiator: string;
  status: 'ringing' | 'active' | 'ended' | 'missed';
  startTime: string;
  endTime?: string;
  duration?: number;
  recordingUrl?: string;
  tenderContext?: TenderContext;
}

export interface VideoMeeting {
  id: string;
  title: string;
  tenderId?: string;
  channelId?: string;
  
  // Meeting details
  agenda?: string;
  participants: MeetingParticipant[];
  organizer: string;
  
  // Scheduling
  scheduledStart: string;
  scheduledEnd: string;
  actualStart?: string;
  actualEnd?: string;
  
  // Meeting features
  recordingEnabled: boolean;
  recordingUrl?: string;
  transcriptUrl?: string;
  screenSharingEnabled: boolean;
  
  // Tender integration
  tenderContext?: TenderContext;
  linkedTasks: string[];
  linkedDocuments: string[];
  
  // Meeting outcomes
  actionItems: MeetingActionItem[];
  decisions: MeetingDecision[];
  nextSteps: string[];
  
  status: 'scheduled' | 'active' | 'ended' | 'cancelled';
  createdAt: string;
}

export interface MeetingParticipant {
  userId: string;
  name: string;
  role: string;
  status: 'invited' | 'accepted' | 'declined' | 'tentative' | 'joined' | 'left';
  joinTime?: string;
  leaveTime?: string;
}

export interface MeetingActionItem {
  id: string;
  description: string;
  assignedTo: string;
  dueDate?: string;
  priority: 'low' | 'medium' | 'high';
  linkedTaskId?: string;
  status: 'pending' | 'in_progress' | 'completed';
}

export interface MeetingDecision {
  id: string;
  description: string;
  decidedBy: string;
  impact: 'low' | 'medium' | 'high';
  implementationRequired: boolean;
  linkedTasks: string[];
}

// ===== COMMUNICATION ANALYTICS =====

export interface CommunicationAnalytics {
  tenderId: string;
  period: {
    start: string;
    end: string;
  };
  
  // Message statistics
  totalMessages: number;
  messagesPerChannel: Record<string, number>;
  messagesPerUser: Record<string, number>;
  
  // Engagement metrics
  activeParticipants: number;
  averageResponseTime: number;
  participationRate: number;
  
  // Collaboration patterns
  peakActivityHours: number[];
  channelUsageDistribution: Record<ChannelType, number>;
  filesSharingFrequency: number;
  
  // Tender-specific metrics
  tenderDiscussionHealth: number; // 0-100 score
  decisionMakingSpeed: number;
  issueResolutionTime: number;
  
  // Effectiveness indicators
  actionItemsCreated: number;
  actionItemsCompleted: number;
  decisionsTracked: number;
  meetingsHeld: number;
}

// ===== INTEGRATION EVENTS =====

export interface CommunicationEvent {
  id: string;
  type: CommunicationEventType;
  tenderId?: string;
  channelId?: string;
  userId: string;
  
  data: Record<string, any>;
  timestamp: string;
  processed: boolean;
}

export type CommunicationEventType = 
  | 'message_sent'
  | 'channel_created'
  | 'user_joined'
  | 'user_left'
  | 'file_uploaded'
  | 'task_mentioned'
  | 'deadline_approaching'
  | 'milestone_reached'
  | 'meeting_scheduled'
  | 'approval_requested'
  | 'tender_updated';
