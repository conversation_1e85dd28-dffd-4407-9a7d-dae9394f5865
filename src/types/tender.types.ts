/**
 * Tender Management Types with Behavioral Psychology Integration
 * Extends existing BidderCentric architecture with psychological optimization
 */

// Psychological Profile Types (moved from onboarding)
export interface PsychologicalProfile {
  archetype: 'achiever' | 'hunter' | 'relationship_builder' | 'analyst';
  motivationType: 'achievement' | 'social' | 'financial' | 'recognition';
  traits: {
    competitiveness: number;
    analyticalThinking: number;
    socialOrientation: number;
    riskTolerance: number;
  };
  confidence: number;
  motivationTriggers: string[];
}

export interface BehavioralPattern {
  mouseMovements: MouseMovement[];
  clickPatterns: ClickPattern[];
  scrollBehavior: ScrollBehavior[];
  interactionSpeed: number;
  errorRate: number;
  hesitationTime: number;
  backtrackingFrequency: number;
}

export interface MouseMovement {
  x: number;
  y: number;
  timestamp: number;
  velocity: number;
  acceleration: number;
}

export interface ClickPattern {
  x: number;
  y: number;
  timestamp: number;
  duration: number;
  force: number;
  element: string;
}

export interface ScrollBehavior {
  direction: 'up' | 'down';
  speed: number;
  timestamp: number;
  position: number;
}

// Core Tender Status with Psychological Triggers
export enum TenderStatus {
  OPEN = 'open',
  CLOSING_SOON = 'closing_soon', // Creates urgency psychology
  CLOSING_TODAY = 'closing_today', // High urgency trigger
  CLOSED = 'closed',
  AWARDED = 'awarded',
  CANCELLED = 'cancelled'
}

// Document Status for compliance tracking
export enum DocumentStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  VERIFIED = 'verified',
  REJECTED = 'rejected'
}

// Bid Status with Gamification Elements
export enum BidStatus {
  DRAFT = 'draft',
  IN_PROGRESS = 'in_progress',
  READY_TO_SUBMIT = 'ready_to_submit', // Achievement trigger
  SUBMITTED = 'submitted', // Completion satisfaction
  UNDER_REVIEW = 'under_review', // Anticipation psychology
  SHORTLISTED = 'shortlisted', // Social proof trigger
  WON = 'won', // Victory psychology
  LOST = 'lost',
  WITHDRAWN = 'withdrawn'
}

// Psychological Priority Levels
export enum Priority {
  LOW = 'low',
  MEDIUM = 'medium', 
  HIGH = 'high',
  CRITICAL = 'critical', // Stress/urgency trigger
  PERFECT_MATCH = 'perfect_match' // Excitement trigger
}

// Behavioral Engagement Triggers
export enum EngagementTrigger {
  SCARCITY = 'scarcity', // "Only 2 days left"
  SOCIAL_PROOF = 'social_proof', // "15 others viewing"
  AUTHORITY = 'authority', // "Government tender"
  RECIPROCITY = 'reciprocity', // "Recommended for you"
  COMMITMENT = 'commitment', // "Complete your profile"
  LIKING = 'liking', // "Similar to your interests"
  FOMO = 'fomo', // "Don't miss out"
  ACHIEVEMENT = 'achievement', // "Unlock new level"
  PROGRESS = 'progress', // "80% complete"
  CURIOSITY = 'curiosity' // "See what others missed"
}

// Psychological Tender Categories with Emotional Associations
export enum TenderCategory {
  CONSTRUCTION = 'construction', // Stability, building
  IT_SERVICES = 'it_services', // Innovation, future
  CONSULTING = 'consulting', // Expertise, authority
  SUPPLIES = 'supplies', // Reliability, trust
  MAINTENANCE = 'maintenance', // Care, responsibility
  SECURITY = 'security', // Protection, safety
  CLEANING = 'cleaning', // Order, cleanliness
  CATERING = 'catering', // Nourishment, care
  TRANSPORT = 'transport', // Movement, progress
  HEALTHCARE = 'healthcare', // Healing, compassion
  EDUCATION = 'education', // Growth, development
  OTHER = 'other'
}

// Behavioral Nudge Configuration
export interface BehavioralNudge {
  id: string;
  type: EngagementTrigger;
  message: string;
  intensity: 'subtle' | 'moderate' | 'strong';
  timing: 'immediate' | 'delayed' | 'contextual';
  personalizedFor: PsychologicalProfile;
  effectivenessScore: number; // 0-1 based on user response
}

// Psychological Tender Matching
export interface TenderMatch {
  tenderId: string;
  matchScore: number; // 0-100
  psychologicalFit: number; // How well it fits user's psychology
  behavioralPrediction: number; // Likelihood of engagement
  emotionalResonance: number; // Emotional appeal score
  cognitiveLoad: number; // Mental effort required
  stressLevel: number; // Potential stress from this tender
  motivationTriggers: EngagementTrigger[];
  personalizedReasons: string[]; // Why this matches their psychology
}

// Enhanced Tender with Behavioral Data
export interface Tender {
  // Core tender data
  id: string;
  title: string;
  description: string;
  organization: string;
  organizationType: 'government' | 'private' | 'parastatal';
  category: TenderCategory;
  value: number;
  currency: string;
  location: string;
  province: string;
  publishDate: string;
  closingDate: string;
  briefingDate?: string;
  briefingLocation?: string;
  status: TenderStatus;
  priority: Priority;
  referenceNumber: string;
  
  // Contact information
  contactPerson?: string;
  contactEmail?: string;
  contactPhone?: string;
  website?: string;
  
  // Behavioral & Psychological Data
  psychologicalTriggers: EngagementTrigger[];
  behavioralNudges: BehavioralNudge[];
  emotionalTone: 'urgent' | 'exciting' | 'stable' | 'innovative' | 'prestigious';
  socialProofData: {
    viewCount: number;
    interestedCount: number;
    recentViewers: number;
    competitorCount: number;
  };
  
  // Personalization Data
  matchData?: TenderMatch;
  personalizedTitle?: string; // AI-optimized for user psychology
  personalizedDescription?: string;
  recommendationReason?: string;
  
  // Gamification Elements
  difficultyLevel: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  estimatedEffort: number; // Hours
  potentialXP: number; // Experience points for gamification
  achievementUnlocks: string[]; // Achievements this tender can unlock
  
  // Tracking Data
  viewedDate?: string;
  bookmarked: boolean;
  swipeDirection?: 'left' | 'right'; // For mobile swipe interface
  engagementScore: number; // How engaging this tender is for user
  
  // Compliance and Requirements
  complianceRequirements: ComplianceRequirement[];
  technicalRequirements: string[];
  financialRequirements: string[];
  
  // Documents and Communication
  tenderDocuments: Document[];
  clarificationDeadline?: string;
  clarifications: Clarification[];
}

// Enhanced Bid with Psychological Tracking
export interface Bid {
  // Core bid data
  id: string;
  tenderId: string;
  tenderTitle: string;
  status: BidStatus;
  priority: Priority;
  bidAmount: number;
  currency: string;
  validityPeriod: number;
  deliveryPeriod: number;
  
  // Dates and Timeline
  createdDate: string;
  lastModified: string;
  submissionDeadline: string;
  submittedDate?: string;
  
  // Team and Resources
  leadPerson: string;
  teamMembers: string[];
  
  // Progress and Completion
  completionPercentage: number;
  estimatedHoursRemaining: number;
  
  // Financial Data
  estimatedCost: number;
  actualCost?: number;
  profitMargin: number;
  
  // Behavioral & Psychological Data
  psychologicalJourney: BehavioralPattern[]; // User's emotional journey
  stressPoints: string[]; // What caused stress during bid creation
  motivationFactors: EngagementTrigger[]; // What kept user engaged
  cognitiveLoadPoints: number[]; // Cognitive load at different stages
  
  // Gamification Data
  xpEarned: number;
  achievementsUnlocked: string[];
  milestonesCelebrated: string[];
  
  // Submission Data
  submissionMethod: 'online' | 'physical' | 'email';
  submissionReference?: string;
  submissionConfirmation?: string;
  
  // Outcome and Learning
  result?: 'won' | 'lost' | 'pending';
  feedback?: string;
  awardDate?: string;
  awardAmount?: number;
  lessonsLearned?: string[]; // For psychological improvement
  
  // Documents and Compliance
  documents: Document[];
  requiredDocuments: ComplianceRequirement[];
  
  // Notes and Analysis
  notes: string[];
  riskAssessment?: string;
  competitorAnalysis?: string;
  psychologicalInsights?: string; // AI insights about user behavior
}

// Supporting Interfaces
export interface ComplianceRequirement {
  id: string;
  name: string;
  description: string;
  mandatory: boolean;
  documentType: string;
  status: 'not_started' | 'in_progress' | 'completed' | 'verified' | 'rejected';
  dueDate?: string;
  submittedDate?: string;
  verifiedDate?: string;
  rejectionReason?: string;
  psychologicalImpact: 'low' | 'medium' | 'high'; // Stress level this requirement causes
}

export interface Document {
  id: string;
  name: string;
  type: string;
  size: number;
  uploadDate: string;
  status: DocumentStatus;
  url?: string;
  version: number;
  uploadedBy: string;
  verifiedBy?: string;
  notes?: string;
}

export interface Clarification {
  id: string;
  question: string;
  answer?: string;
  askedBy: string;
  askedDate: string;
  answeredDate?: string;
  public: boolean;
  psychologicalTone: 'confident' | 'uncertain' | 'concerned' | 'curious';
}

// Activity Logging with Behavioral Context
export interface ActivityLog {
  id: string;
  type: 'tender_viewed' | 'bid_created' | 'document_uploaded' | 'bid_submitted' | 'deadline_reminder' | 'status_changed';
  title: string;
  description: string;
  timestamp: string;
  userId: string;
  relatedId?: string;
  priority: Priority;
  read: boolean;
  
  // Behavioral Context
  psychologicalState: {
    stressLevel: number;
    cognitiveLoad: number;
    engagementLevel: number;
    emotionalState: string;
  };
  behavioralTriggers: EngagementTrigger[];
  userResponse: 'positive' | 'negative' | 'neutral';
}

// Notification with Psychological Optimization
export interface Notification {
  id: string;
  type: 'deadline' | 'status_update' | 'new_tender' | 'document_required' | 'compliance_issue' | 'achievement' | 'social_proof';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  actionRequired: boolean;
  actionUrl?: string;
  priority: Priority;
  relatedId?: string;
  
  // Behavioral Optimization
  personalizedFor: PsychologicalProfile;
  emotionalTone: 'urgent' | 'encouraging' | 'celebratory' | 'informative' | 'warning';
  behavioralNudge?: BehavioralNudge;
  optimalTiming?: string; // When to show for maximum impact
  psychologicalTriggers: EngagementTrigger[];
}

// Dashboard with Behavioral Intelligence
export interface DashboardSummary {
  // Core Metrics
  activeBids: number;
  pendingTenders: number;
  successRate: number;
  totalEarnings: number;
  
  // Trend Data
  bidsThisMonth: number;
  bidsLastMonth: number;
  successRateChange: number;
  earningsChange: number;
  
  // Urgent Items
  urgentDeadlines: number;
  missingDocuments: number;
  complianceIssues: number;
  
  // Behavioral Intelligence
  psychologicalInsights: {
    currentMood: 'motivated' | 'stressed' | 'confident' | 'overwhelmed' | 'focused';
    engagementTrend: 'increasing' | 'decreasing' | 'stable';
    optimalWorkingHours: string[];
    stressFactors: string[];
    motivationFactors: string[];
    recommendedActions: string[];
  };
  
  // Gamification Data
  currentLevel: number;
  xpToNextLevel: number;
  recentAchievements: string[];
  streakCount: number;
  leaderboardPosition?: number;
  
  // Activity and Notifications
  recentActivity: ActivityLog[];
  notifications: Notification[];
  
  // Performance Stats
  totalBidsSubmitted: number;
  totalTendersViewed: number;
  averageBidValue: number;
  winRate: number;
  averageResponseTime: number; // How quickly user responds to opportunities
}

// Calendar Event with Behavioral Context
export interface CalendarEvent {
  id: string;
  title: string;
  type: 'deadline' | 'briefing' | 'submission' | 'meeting' | 'reminder' | 'celebration';
  date: string;
  time?: string;
  description: string;
  location?: string;
  priority: Priority;
  relatedId?: string;
  completed: boolean;
  
  // Behavioral Elements
  stressLevel: 'low' | 'medium' | 'high'; // How stressful this event is
  preparationTime: number; // Minutes needed to prepare
  psychologicalImpact: 'positive' | 'negative' | 'neutral';
  motivationalMessage?: string; // Personalized encouragement
  celebrationTrigger?: boolean; // Should this trigger a celebration?
}

// =====================================================
// ENHANCED DATABASE INTEGRATION TYPES
// =====================================================

export interface EnhancedTender extends Tender {
  tender_id: string;
  department?: string;
  contact_person?: string;
  contact_email?: string;
  contact_phone?: string;
  currency: string;
  budget_range?: {
    min_value?: number;
    max_value?: number;
  };
  publish_date?: string;
  briefing_date?: string;
  briefing_location?: string;
  award_date?: string;
  project_start_date?: string;
  project_end_date?: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
  service_area?: string[];
  subcategory?: string;
  tender_type: 'open' | 'closed' | 'limited' | 'emergency' | 'framework';
  procurement_method: 'competitive' | 'negotiated' | 'single_source' | 'framework';
  technical_requirements?: Record<string, any>;
  commercial_requirements?: Record<string, any>;
  cidb_grade_required?: string;
  tax_clearance_required: boolean;
  documents?: TenderDocument[];
  specifications_url?: string;
  processing_status: 'pending' | 'processing' | 'processed' | 'failed';
  ai_analysis?: Record<string, any>;
  competition_level: 'low' | 'medium' | 'high' | 'extreme';
  source_id?: string;
  external_id?: string;
  source_url?: string;
  reference_number?: string;
  scraped_tender_id?: string;
  view_count: number;
  bid_count: number;
  download_count: number;
  metadata?: Record<string, any>;
}

export interface TenderDocument {
  id: string;
  name: string;
  type: string;
  url: string;
  size?: number;
  download_count?: number;
}

export interface SupplierQuote {
  id: string;
  quote_id: string;
  tender_id: string;
  supplier_id: string;
  rfq_id?: string;
  quote_number?: string;
  total_amount: number;
  currency: string;
  line_items?: QuoteLineItem[];
  delivery_timeframe?: string;
  delivery_cost: number;
  delivery_location?: string;
  validity_period_days: number;
  payment_terms?: string;
  warranty_terms?: string;
  compliance_documents?: string[];
  certifications?: string[];
  bbbee_certificate_url?: string;
  tax_clearance_url?: string;
  status: 'draft' | 'submitted' | 'under_review' | 'accepted' | 'rejected' | 'expired' | 'withdrawn';
  submission_date?: string;
  review_date?: string;
  decision_date?: string;
  expiry_date?: string;
  evaluation_score?: number;
  technical_score?: number;
  commercial_score?: number;
  compliance_score?: number;
  evaluation_notes?: string;
  evaluator_id?: string;
  trust_score: number;
  competitive_score: number;
  ai_analysis?: Record<string, any>;
  commission_rate: number;
  estimated_commission: number;
  commission_status: 'pending' | 'calculated' | 'approved' | 'paid';
  smart_contract_enabled: boolean;
  smart_contract_address?: string;
  blockchain_hash?: string;
  quote_document_url?: string;
  supporting_documents?: QuoteDocument[];
  supplier_name?: string;
  supplier_location?: string;
  supplier_bbbee_level?: number;
  view_count: number;
  download_count: number;
  version: number;
  parent_quote_id?: string;
  is_latest_version: boolean;
  notes?: string;
  internal_notes?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface QuoteLineItem {
  id?: string;
  description: string;
  quantity: number;
  unit_price: number;
  total: number;
  unit_of_measurement?: string;
  specifications?: Record<string, any>;
}

export interface QuoteDocument {
  id: string;
  name: string;
  type: string;
  url: string;
  size?: number;
}

export interface TenderBid {
  id: string;
  bid_id: string;
  tender_id: string;
  bidder_id: string;
  bid_amount: number;
  currency: string;
  submission_method: 'online' | 'physical' | 'email' | 'hybrid';
  submission_date: string;
  submission_location?: string;
  bid_documents?: BidDocument[];
  technical_proposal_url?: string;
  commercial_proposal_url?: string;
  compliance_documents_url?: string;
  status: 'draft' | 'submitted' | 'under_review' | 'shortlisted' | 'awarded' | 'not_awarded' | 'disqualified' | 'withdrawn';
  technical_score?: number;
  commercial_score?: number;
  compliance_score?: number;
  total_score?: number;
  ranking?: number;
  evaluation_notes?: string;
  awarded: boolean;
  award_date?: string;
  award_amount?: number;
  award_reason?: string;
  bbbee_points?: number;
  local_content_percentage?: number;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface BidDocument {
  id: string;
  name: string;
  type: string;
  url: string;
  size?: number;
}
