/**
 * SA Compliance Tool Types
 * TypeScript interfaces for bid protest and compliance management
 */

// Core Compliance Types
export interface BidProtest {
  id: string;
  tenantId: string;
  tenderReference: string;
  procuringEntity: string;
  issueType: ProtestGround;
  description: string;
  status: ProtestStatus;
  deadline: string;
  createdAt: string;
  updatedAt: string;
  
  // Evidence links
  beeEvidence?: BEEEvidence;
  taxEvidence?: TaxEvidence;
  cipcEvidence?: CIPCEvidence;
  
  // Generated documents
  documents: ComplianceDocument[];
  
  // Lifecycle tracking
  lifecycleData?: TenderLifecycleData;
  
  // SME-specific data
  smeProfile?: SMEComplianceProfile;
}

export enum ProtestStatus {
  DRAFT = 'draft',
  REVIEW = 'review',
  SUBMITTED = 'submitted',
  RESOLVED = 'resolved',
  APPEALED = 'appealed',
  CLOSED = 'closed'
}

export enum ProtestGround {
  SCORING = 'scoring',
  SPECS = 'specs',
  BBBEE = 'bbbee',
  CIDB = 'cidb',
  DEADLINE = 'deadline',
  PRICE = 'price',
  LOCAL = 'local',
  SME_SETASIDE = 'sme_setaside',
  UNFAIR_SPECS = 'unfair_specs',
  INADEQUATE_BRIEFING = 'inadequate_briefing',
  UNREASONABLE_CRITERIA = 'unreasonable_criteria',
  INSUFFICIENT_TIME = 'insufficient_time',
  DISCRIMINATORY_EVAL = 'discriminatory_eval',
  LOCAL_CONTENT_BIAS = 'local_content_bias',
  SUBCONTRACTING_ISSUES = 'subcontracting_issues',
  PAYMENT_TERMS = 'payment_terms',
  CAPACITY_REQUIREMENTS = 'capacity_requirements'
}

// Tender Lifecycle Data
export interface TenderLifecycleData {
  id: string;
  tenderReference: string;
  
  // Advertisement Phase
  advertisedDate: string;
  advertisementSource: string;
  estimatedValue: number;
  smeSetasidePercentage?: number;
  
  // Briefing Phase
  briefingSessions: BriefingSession[];
  questionsAsked: TenderQuestion[];
  clarificationsIssued: TenderClarification[];
  
  // Bidding Phase
  submissionDeadline: string;
  bidSubmittedAt?: string;
  bidAmount?: number;
  
  // Evaluation Phase
  evaluationCriteria: Record<string, any>;
  scoringMethodology: string;
  evaluationPeriod?: number;
  
  // Award Phase
  awardDate?: string;
  winningBidder?: string;
  winningAmount?: number;
  awardJustification?: string;
  
  // SME Participation
  smeParticipationRate?: number;
  smeAwardRate?: number;
}

export interface BriefingSession {
  id: string;
  date: string;
  location: string;
  attendees: number;
  mandatory: boolean;
  minutes?: string;
}

export interface TenderQuestion {
  id: string;
  question: string;
  answer?: string;
  askedDate: string;
  answeredDate?: string;
  askedBy: string;
}

export interface TenderClarification {
  id: string;
  clarification: string;
  issuedDate: string;
  affectsEvaluation: boolean;
}

// SME Compliance Profile
export interface SMEComplianceProfile {
  id: string;
  tenantId: string;
  
  // SME Classification
  isSme: boolean;
  smeClassification: SMEClassification;
  
  // Capacity Indicators
  annualTurnover: number;
  employeeCount: number;
  yearsInBusiness: number;
  
  // Compliance Metrics
  complianceScore: number;
  weakAreas: string[];
  improvementRecommendations: string[];
  
  // Success Metrics
  bidSuccessRate: number;
  protestSuccessRate: number;
  
  // Industry Context
  industry: string;
  primaryServices: string[];
  geographicFocus: string[];
}

export enum SMEClassification {
  MICRO = 'micro',
  VERY_SMALL = 'very_small',
  SMALL = 'small',
  MEDIUM = 'medium'
}

// Evidence Types
export interface BEEEvidence {
  id: string;
  overallLevel: string;
  certificateExpiryDate: string;
  issuingBody: string;
  certificateNumber: string;
  verificationStatus: VerificationStatus;
}

export interface TaxEvidence {
  id: string;
  isCompliant: boolean;
  sarsRefNumber: string;
  expiryDate: string;
  verificationStatus: VerificationStatus;
}

export interface CIPCEvidence {
  id: string;
  cipcNumber: string;
  companyStatus: string;
  isCompliant: boolean;
  verificationStatus: VerificationStatus;
}

export enum VerificationStatus {
  PENDING = 'pending',
  VERIFIED = 'verified',
  FAILED = 'failed',
  EXPIRED = 'expired'
}

// Document Types
export interface ComplianceDocument {
  id: string;
  title: string;
  documentType: DocumentType;
  filePath: string;
  fileSize: number;
  mimeType: string;
  uploadedAt: string;
  uploadedBy: string;
  
  // Template information
  templateUsed?: string;
  autoGenerated: boolean;
  
  // Lifecycle
  status: DocumentStatus;
  version: number;
  parentDocumentId?: string;
}

export enum DocumentType {
  PROTEST_LETTER = 'protest_letter',
  RFI = 'rfi',
  CLARIFICATION_REQUEST = 'clarification_request',
  FEEDBACK_REQUEST = 'feedback_request',
  INFORMAL_INQUIRY = 'informal_inquiry',
  APPEAL_LETTER = 'appeal_letter',
  SETTLEMENT_PROPOSAL = 'settlement_proposal',
  EVIDENCE_DOCUMENT = 'evidence_document',
  SUPPORTING_DOCUMENT = 'supporting_document'
}

export enum DocumentStatus {
  DRAFT = 'draft',
  REVIEW = 'review',
  APPROVED = 'approved',
  SENT = 'sent',
  RESPONDED = 'responded'
}

// Template Types
export interface DocumentTemplate {
  id: string;
  name: string;
  templateType: DocumentType;
  escalationLevel: EscalationLevel;
  templateContent: string;
  isSMEFocused: boolean;
  legalFramework: string[];
  successRate: number;
  
  // Usage statistics
  timesUsed: number;
  averageSuccessRate: number;
  lastUpdated: string;
}

export enum EscalationLevel {
  INFORMATION_SEEKING = 1,
  FORMAL_INQUIRY = 2,
  PROTEST = 3,
  APPEAL = 4
}

// Protest Analysis Types
export interface ProtestViabilityAnalysis {
  viable: boolean;
  score: number;
  recommendations: string[];
  suggestedApproach: string[];
  estimatedCost: CostEstimate;
  timeline: TimelineEstimate;
  riskAssessment: RiskAssessment;
}

export interface CostEstimate {
  estimatedCost: string;
  costFactors: string[];
  confidenceLevel: number;
}

export interface TimelineEstimate {
  rfiResponse?: string;
  protestPreparation?: string;
  formalResponse?: string;
  totalTimeline: string;
  criticalDeadlines: string[];
}

export interface RiskAssessment {
  overallRisk: RiskLevel;
  riskFactors: RiskFactor[];
  mitigationStrategies: string[];
}

export enum RiskLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface RiskFactor {
  factor: string;
  impact: RiskLevel;
  probability: number;
  description: string;
}

// Irregularity Detection
export interface ComplianceIssue {
  code: string;
  description: string;
  severity: IssueSeverity;
  category: IssueCategory;
  evidence: string[];
  recommendedAction: string;
  legalBasis: string[];
}

export enum IssueSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum IssueCategory {
  PROCEDURAL = 'procedural',
  EVALUATION = 'evaluation',
  COMPLIANCE = 'compliance',
  DISCRIMINATION = 'discrimination',
  TRANSPARENCY = 'transparency'
}

// Deadline Management
export interface ProtestDeadline {
  id: string;
  protestId: string;
  deadlineType: DeadlineType;
  deadline: string;
  jurisdiction: Jurisdiction;
  daysRemaining: number;
  urgencyStatus: UrgencyStatus;
  urgencyColor: string;
}

export enum DeadlineType {
  PROTEST_SUBMISSION = 'protest_submission',
  RESPONSE_EXPECTED = 'response_expected',
  APPEAL_DEADLINE = 'appeal_deadline',
  SETTLEMENT_DEADLINE = 'settlement_deadline'
}

export enum Jurisdiction {
  NATIONAL = 'national',
  PROVINCIAL = 'provincial',
  MUNICIPAL = 'municipal',
  SOE = 'soe'
}

export enum UrgencyStatus {
  NORMAL = 'normal',
  MODERATE = 'moderate',
  URGENT = 'urgent',
  CRITICAL = 'critical'
}

// API Request/Response Types
export interface CreateProtestRequest {
  tenderReference: string;
  procuringEntity: string;
  issueType: ProtestGround;
  description: string;
  deadline: string;
  
  // Optional evidence
  beeEvidenceId?: string;
  taxEvidenceId?: string;
  cipcEvidenceId?: string;
  
  // Lifecycle data
  lifecycleDataId?: string;
}

export interface ProtestViabilityRequest {
  tenantId: string;
  tenderData: {
    value: number;
    jurisdiction: Jurisdiction;
    smeSetaside?: number;
    competitorCount?: number;
    evaluationCriteria?: Record<string, any>;
  };
}

export interface GenerateDocumentRequest {
  protestId: string;
  templateType: DocumentType;
  customizations?: Record<string, any>;
  includeEvidence: boolean;
}

export interface IrregularityDetectionRequest {
  tenderData: {
    estimatedValue: number;
    preferencePoints?: number;
    requiredCidbGrade?: number;
    winnerCidbGrade?: number;
    localContentRequired?: number;
    winnerLocalContent?: number;
    evaluationCriteria: Record<string, any>;
    smeSetaside?: number;
    smeParticipation?: number;
  };
}

// Response Types
export interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// Filter and Search Types
export interface ProtestFilter {
  status?: ProtestStatus[];
  issueType?: ProtestGround[];
  dateFrom?: string;
  dateTo?: string;
  procuringEntity?: string;
  jurisdiction?: Jurisdiction;
}

export interface SMEAnalysisFilter {
  classification?: SMEClassification[];
  industry?: string[];
  complianceScoreMin?: number;
  complianceScoreMax?: number;
  successRateMin?: number;
}

// Dashboard Types
export interface ComplianceDashboardData {
  activeProtests: number;
  pendingDeadlines: number;
  complianceScore: number;
  successRate: number;
  recentActivity: ComplianceActivity[];
  upcomingDeadlines: ProtestDeadline[];
  riskAlerts: ComplianceIssue[];
}

export interface ComplianceActivity {
  id: string;
  type: ActivityType;
  description: string;
  timestamp: string;
  relatedEntityId?: string;
  relatedEntityType?: string;
}

export enum ActivityType {
  PROTEST_CREATED = 'protest_created',
  DOCUMENT_GENERATED = 'document_generated',
  DEADLINE_APPROACHING = 'deadline_approaching',
  RESPONSE_RECEIVED = 'response_received',
  EVIDENCE_UPLOADED = 'evidence_uploaded',
  ANALYSIS_COMPLETED = 'analysis_completed'
}
