/**
 * Tender Management System Types
 * Real business data models for functional MVP
 */

// Tender Status Enum
export enum TenderStatus {
  OPEN = 'open',
  CLOSING_SOON = 'closing_soon',
  CLOSED = 'closed',
  AWARDED = 'awarded',
  CANCELLED = 'cancelled'
}

// Bid Status Enum
export enum BidStatus {
  DRAFT = 'draft',
  IN_PROGRESS = 'in_progress',
  READY_TO_SUBMIT = 'ready_to_submit',
  SUBMITTED = 'submitted',
  UNDER_REVIEW = 'under_review',
  SHORTLISTED = 'shortlisted',
  WON = 'won',
  LOST = 'lost',
  WITHDRAWN = 'withdrawn'
}

// Document Status Enum
export enum DocumentStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  VERIFIED = 'verified',
  REJECTED = 'rejected'
}

// Priority Levels
export enum Priority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// Tender Categories
export enum TenderCategory {
  CONSTRUCTION = 'construction',
  IT_SERVICES = 'it_services',
  CONSULTING = 'consulting',
  SUPPLIES = 'supplies',
  MAINTENANCE = 'maintenance',
  SECURITY = 'security',
  CLEANING = 'cleaning',
  CATERING = 'catering',
  TRANSPORT = 'transport',
  OTHER = 'other'
}

// Compliance Requirements
export interface ComplianceRequirement {
  id: string;
  name: string;
  description: string;
  mandatory: boolean;
  documentType: string;
  status: DocumentStatus;
  dueDate?: string;
  submittedDate?: string;
  verifiedDate?: string;
  rejectionReason?: string;
}

// Document Interface
export interface Document {
  id: string;
  name: string;
  type: string;
  size: number;
  uploadDate: string;
  status: DocumentStatus;
  url?: string;
  version: number;
  uploadedBy: string;
  verifiedBy?: string;
  notes?: string;
}

// Tender Interface
export interface Tender {
  id: string;
  title: string;
  description: string;
  organization: string;
  organizationType: 'government' | 'private' | 'parastatal';
  category: TenderCategory;
  value: number;
  currency: string;
  location: string;
  province: string;
  publishDate: string;
  closingDate: string;
  briefingDate?: string;
  briefingLocation?: string;
  status: TenderStatus;
  priority: Priority;
  
  // Requirements
  complianceRequirements: ComplianceRequirement[];
  technicalRequirements: string[];
  financialRequirements: string[];
  
  // Metadata
  referenceNumber: string;
  contactPerson?: string;
  contactEmail?: string;
  contactPhone?: string;
  website?: string;
  
  // Tracking
  viewedDate?: string;
  bookmarked: boolean;
  matchScore?: number;
  estimatedCost?: number;
  estimatedDuration?: number;
  
  // Documents
  tenderDocuments: Document[];
  clarificationDeadline?: string;
  clarifications: Clarification[];
}

// Clarification Interface
export interface Clarification {
  id: string;
  question: string;
  answer?: string;
  askedBy: string;
  askedDate: string;
  answeredDate?: string;
  public: boolean;
}

// Bid Interface
export interface Bid {
  id: string;
  tenderId: string;
  tenderTitle: string;
  status: BidStatus;
  priority: Priority;
  
  // Bid Details
  bidAmount: number;
  currency: string;
  validityPeriod: number; // days
  deliveryPeriod: number; // days
  
  // Dates
  createdDate: string;
  lastModified: string;
  submissionDeadline: string;
  submittedDate?: string;
  
  // Team
  leadPerson: string;
  teamMembers: string[];
  
  // Documents
  documents: Document[];
  requiredDocuments: ComplianceRequirement[];
  
  // Progress
  completionPercentage: number;
  estimatedHoursRemaining: number;
  
  // Financial
  estimatedCost: number;
  actualCost?: number;
  profitMargin: number;
  
  // Submission
  submissionMethod: 'online' | 'physical' | 'email';
  submissionReference?: string;
  submissionConfirmation?: string;
  
  // Outcome
  result?: 'won' | 'lost' | 'pending';
  feedback?: string;
  awardDate?: string;
  awardAmount?: number;
  
  // Notes
  notes: string[];
  riskAssessment?: string;
  competitorAnalysis?: string;
}

// Activity Log
export interface ActivityLog {
  id: string;
  type: 'tender_viewed' | 'bid_created' | 'document_uploaded' | 'bid_submitted' | 'deadline_reminder' | 'status_changed';
  title: string;
  description: string;
  timestamp: string;
  userId: string;
  relatedId?: string; // tender or bid ID
  priority: Priority;
  read: boolean;
}

// Notification Interface
export interface Notification {
  id: string;
  type: 'deadline' | 'status_update' | 'new_tender' | 'document_required' | 'compliance_issue';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  actionRequired: boolean;
  actionUrl?: string;
  priority: Priority;
  relatedId?: string;
}

// Dashboard Summary
export interface DashboardSummary {
  // Metrics
  activeBids: number;
  pendingTenders: number;
  successRate: number;
  totalEarnings: number;
  
  // Trends
  bidsThisMonth: number;
  bidsLastMonth: number;
  successRateChange: number;
  earningsChange: number;
  
  // Urgent Items
  urgentDeadlines: number;
  missingDocuments: number;
  complianceIssues: number;
  
  // Recent Activity
  recentActivity: ActivityLog[];
  notifications: Notification[];
  
  // Quick Stats
  totalBidsSubmitted: number;
  totalTendersViewed: number;
  averageBidValue: number;
  winRate: number;
}

// Calendar Event
export interface CalendarEvent {
  id: string;
  title: string;
  type: 'deadline' | 'briefing' | 'submission' | 'meeting' | 'reminder';
  date: string;
  time?: string;
  description: string;
  location?: string;
  priority: Priority;
  relatedId?: string;
  completed: boolean;
}

// Search Filters
export interface TenderFilters {
  categories: TenderCategory[];
  locations: string[];
  valueRange: {
    min: number;
    max: number;
  };
  status: TenderStatus[];
  closingDateRange: {
    from: string;
    to: string;
  };
  organizationType: ('government' | 'private' | 'parastatal')[];
  keywords: string;
  bookmarkedOnly: boolean;
  matchScoreMin: number;
}

// User Preferences
export interface UserPreferences {
  defaultCurrency: string;
  notificationSettings: {
    email: boolean;
    sms: boolean;
    push: boolean;
    deadlineReminder: number; // days before
  };
  dashboardLayout: string;
  favoriteCategories: TenderCategory[];
  autoBookmarkCriteria: TenderFilters;
}
