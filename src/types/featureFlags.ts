/**
 * Feature Flag System Types
 * Controls progressive feature activation for MVP to full platform evolution
 */

// Core Feature Categories
export enum FeatureCategory {
  CORE = 'core',
  COMPLIANCE = 'compliance',
  GAMIFICATION = 'gamification',
  ECOSYSTEM = 'ecosystem',
  ANALYTICS = 'analytics',
  PREMIUM = 'premium',
  EXPERIMENTAL = 'experimental'
}

// Feature Flag Status
export enum FeatureStatus {
  DISABLED = 'disabled',
  BETA = 'beta',
  ENABLED = 'enabled',
  DEPRECATED = 'deprecated'
}

// User Segment Targeting
export enum UserSegment {
  ALL = 'all',
  FREE_USERS = 'free_users',
  PAID_USERS = 'paid_users',
  SME_USERS = 'sme_users',
  ENTERPRISE_USERS = 'enterprise_users',
  BETA_TESTERS = 'beta_testers',
  ADMIN_USERS = 'admin_users'
}

// Feature Flag Definition
export interface FeatureFlag {
  id: string;
  name: string;
  description: string;
  category: FeatureCategory;
  status: FeatureStatus;
  
  // Targeting
  userSegments: UserSegment[];
  subscriptionTiers: string[];
  
  // Rollout Control
  rolloutPercentage: number; // 0-100
  enabledForUserIds: string[];
  disabledForUserIds: string[];
  
  // Business Logic
  dependencies: string[]; // Other feature flags this depends on
  conflicts: string[]; // Features that conflict with this one
  
  // Metadata
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  tags: string[];
  
  // Configuration
  config?: Record<string, any>;
}

// Feature Flag Groups for MVP Evolution
export interface FeatureFlagGroup {
  id: string;
  name: string;
  description: string;
  features: string[]; // Feature flag IDs
  mvpLevel: MVPLevel;
  businessValue: BusinessValue;
  technicalComplexity: TechnicalComplexity;
  activationOrder: number;
}

export enum MVPLevel {
  MVP_CORE = 'mvp_core',           // Essential for launch
  MVP_ENHANCED = 'mvp_enhanced',   // Nice to have for launch
  GROWTH = 'growth',               // For user acquisition
  SCALE = 'scale',                 // For scaling business
  ENTERPRISE = 'enterprise'        // For enterprise clients
}

export enum BusinessValue {
  CRITICAL = 'critical',           // Core business functionality
  HIGH = 'high',                   // Significant revenue/retention impact
  MEDIUM = 'medium',               // Moderate business impact
  LOW = 'low',                     // Minor improvement
  EXPERIMENTAL = 'experimental'    // Testing new concepts
}

export enum TechnicalComplexity {
  LOW = 'low',                     // Simple toggle
  MEDIUM = 'medium',               // Some integration required
  HIGH = 'high',                   // Complex implementation
  VERY_HIGH = 'very_high'          // Major architectural changes
}

// Predefined Feature Flags for BidBeez Platform
export const BIDBEEZ_FEATURE_FLAGS: Record<string, FeatureFlag> = {
  // ===== CORE FEATURES (MVP Level) =====
  TENDER_SEARCH: {
    id: 'tender_search',
    name: 'Tender Search & Discovery',
    description: 'Basic tender search and filtering functionality',
    category: FeatureCategory.CORE,
    status: FeatureStatus.ENABLED,
    userSegments: [UserSegment.ALL],
    subscriptionTiers: ['free', 'basic', 'professional', 'enterprise'],
    rolloutPercentage: 100,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: [],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['core', 'mvp', 'essential']
  },

  BID_SUBMISSION: {
    id: 'bid_submission',
    name: 'Bid Creation & Submission',
    description: 'Core bid creation and submission functionality',
    category: FeatureCategory.CORE,
    status: FeatureStatus.ENABLED,
    userSegments: [UserSegment.ALL],
    subscriptionTiers: ['free', 'basic', 'professional', 'enterprise'],
    rolloutPercentage: 100,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: ['tender_search'],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['core', 'mvp', 'essential']
  },

  USER_DASHBOARD: {
    id: 'user_dashboard',
    name: 'User Dashboard',
    description: 'Basic user dashboard with key metrics',
    category: FeatureCategory.CORE,
    status: FeatureStatus.ENABLED,
    userSegments: [UserSegment.ALL],
    subscriptionTiers: ['free', 'basic', 'professional', 'enterprise'],
    rolloutPercentage: 100,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: [],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['core', 'mvp', 'essential']
  },

  // ===== COMPLIANCE FEATURES (Premium) =====
  SA_COMPLIANCE_TOOLS: {
    id: 'sa_compliance_tools',
    name: 'SA Compliance Tools',
    description: 'Full South African legal compliance features',
    category: FeatureCategory.COMPLIANCE,
    status: FeatureStatus.BETA,
    userSegments: [UserSegment.PAID_USERS, UserSegment.BETA_TESTERS],
    subscriptionTiers: ['professional', 'compliance_pro', 'enterprise'],
    rolloutPercentage: 50,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: ['user_dashboard'],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['compliance', 'premium', 'legal'],
    config: {
      maxProtestsPerMonth: 10,
      templatesIncluded: true,
      legalFrameworkAccess: true
    }
  },

  BID_PROTEST_MANAGEMENT: {
    id: 'bid_protest_management',
    name: 'Bid Protest Management',
    description: 'Professional bid protest creation and management',
    category: FeatureCategory.COMPLIANCE,
    status: FeatureStatus.DISABLED,
    userSegments: [UserSegment.PAID_USERS],
    subscriptionTiers: ['compliance_pro', 'enterprise'],
    rolloutPercentage: 0,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: ['sa_compliance_tools'],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['compliance', 'premium', 'legal', 'protest']
  },

  // ===== GAMIFICATION FEATURES =====
  ACHIEVEMENT_SYSTEM: {
    id: 'achievement_system',
    name: 'Achievement System',
    description: 'Badges, XP, and achievement tracking',
    category: FeatureCategory.GAMIFICATION,
    status: FeatureStatus.DISABLED,
    userSegments: [UserSegment.ALL],
    subscriptionTiers: ['free', 'basic', 'professional', 'enterprise'],
    rolloutPercentage: 0,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: ['user_dashboard'],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['gamification', 'engagement', 'retention']
  },

  LEADERBOARDS: {
    id: 'leaderboards',
    name: 'Leaderboards & Competition',
    description: 'User rankings and competitive features',
    category: FeatureCategory.GAMIFICATION,
    status: FeatureStatus.DISABLED,
    userSegments: [UserSegment.ALL],
    subscriptionTiers: ['basic', 'professional', 'enterprise'],
    rolloutPercentage: 0,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: ['achievement_system'],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['gamification', 'social', 'competition']
  },

  // ===== ECOSYSTEM FEATURES =====
  SKILLSYNC_INTEGRATION: {
    id: 'skillsync_integration',
    name: 'SkillSync Integration',
    description: 'Professional skills marketplace integration',
    category: FeatureCategory.ECOSYSTEM,
    status: FeatureStatus.DISABLED,
    userSegments: [UserSegment.PAID_USERS],
    subscriptionTiers: ['professional', 'enterprise'],
    rolloutPercentage: 0,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: ['user_dashboard'],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['ecosystem', 'integration', 'skills']
  },

  TOOLSYNC_INTEGRATION: {
    id: 'toolsync_integration',
    name: 'ToolSync Integration',
    description: 'Equipment and tool rental marketplace',
    category: FeatureCategory.ECOSYSTEM,
    status: FeatureStatus.DISABLED,
    userSegments: [UserSegment.PAID_USERS],
    subscriptionTiers: ['professional', 'enterprise'],
    rolloutPercentage: 0,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: ['user_dashboard'],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['ecosystem', 'integration', 'tools']
  },

  COURIER_DISPATCH: {
    id: 'courier_dispatch',
    name: 'Courier Dispatch System',
    description: 'Multi-modal delivery optimization with Queen Bee integration',
    category: FeatureCategory.ECOSYSTEM,
    status: FeatureStatus.ENABLED,
    userSegments: [UserSegment.PAID_USERS, UserSegment.ENTERPRISE_USERS],
    subscriptionTiers: ['basic', 'professional', 'enterprise'],
    rolloutPercentage: 100,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: ['user_dashboard'],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['courier', 'delivery', 'queen-bee', 'logistics', 'ecosystem']
  },

  // ===== ANALYTICS FEATURES =====
  ADVANCED_ANALYTICS: {
    id: 'advanced_analytics',
    name: 'Advanced Analytics',
    description: 'Detailed performance analytics and insights',
    category: FeatureCategory.ANALYTICS,
    status: FeatureStatus.DISABLED,
    userSegments: [UserSegment.PAID_USERS],
    subscriptionTiers: ['professional', 'enterprise'],
    rolloutPercentage: 0,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: ['user_dashboard'],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['analytics', 'insights', 'premium']
  },

  AI_INSIGHTS: {
    id: 'ai_insights',
    name: 'AI-Powered Insights',
    description: 'Machine learning insights and recommendations',
    category: FeatureCategory.ANALYTICS,
    status: FeatureStatus.DISABLED,
    userSegments: [UserSegment.PAID_USERS],
    subscriptionTiers: ['professional', 'enterprise'],
    rolloutPercentage: 0,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: ['advanced_analytics'],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['ai', 'insights', 'premium', 'experimental']
  },

  // ===== NEUROMARKETING FEATURES =====
  NEUROMARKETING_ENGINE: {
    id: 'neuromarketing_engine',
    name: 'NeuroMarketing Engine',
    description: 'Psychological optimization and adaptive interface',
    category: FeatureCategory.EXPERIMENTAL,
    status: FeatureStatus.BETA,
    userSegments: [UserSegment.BETA_TESTERS, UserSegment.PAID_USERS],
    subscriptionTiers: ['professional', 'enterprise'],
    rolloutPercentage: 25,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: [],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['neuromarketing', 'psychology', 'experimental', 'ux']
  },

  // ===== BID ANALYTICS FEATURES =====
  BID_ANALYTICS: {
    id: 'bid_analytics',
    name: 'Bid Analytics & Summaries',
    description: 'Comprehensive bid performance analytics and insights',
    category: FeatureCategory.ANALYTICS,
    status: FeatureStatus.ENABLED,
    userSegments: [UserSegment.ALL],
    subscriptionTiers: ['basic', 'professional', 'enterprise'],
    rolloutPercentage: 100,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: ['user_dashboard'],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['analytics', 'performance', 'insights', 'core']
  },

  PSYCHOLOGICAL_ANALYTICS: {
    id: 'psychological_analytics',
    name: 'Psychological Analytics',
    description: 'Behavioral pattern analysis and psychological insights',
    category: FeatureCategory.ANALYTICS,
    status: FeatureStatus.ENABLED,
    userSegments: [UserSegment.PAID_USERS],
    subscriptionTiers: ['professional', 'enterprise'],
    rolloutPercentage: 100,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: ['bid_analytics', 'neuromarketing_engine'],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['psychology', 'analytics', 'behavioral', 'premium']
  },

  FINANCIAL_ANALYTICS: {
    id: 'financial_analytics',
    name: 'Financial Analytics',
    description: 'Revenue, profit, and ROI analysis for bidding performance',
    category: FeatureCategory.ANALYTICS,
    status: FeatureStatus.ENABLED,
    userSegments: [UserSegment.PAID_USERS],
    subscriptionTiers: ['professional', 'enterprise'],
    rolloutPercentage: 100,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: ['bid_analytics'],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['financial', 'analytics', 'revenue', 'premium']
  },

  // ===== WHATSAPP AUTO-BIDDING FEATURES =====
  WHATSAPP_AUTOBID: {
    id: 'whatsapp_autobid',
    name: 'WhatsApp Auto-Bidding',
    description: 'WhatsApp integration for automated bid notifications and responses',
    category: FeatureCategory.PREMIUM,
    status: FeatureStatus.ENABLED,
    userSegments: [UserSegment.PAID_USERS],
    subscriptionTiers: ['professional', 'enterprise'],
    rolloutPercentage: 100,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: ['user_dashboard'],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['whatsapp', 'automation', 'messaging', 'premium'],
    config: {
      maxDailyAutoBids: 10,
      maxMonthlyAutoBids: 100,
      webhookEnabled: true,
      messageProcessingEnabled: true
    }
  },

  WHATSAPP_WEBHOOKS: {
    id: 'whatsapp_webhooks',
    name: 'WhatsApp Webhook Management',
    description: 'Advanced webhook configuration and monitoring for WhatsApp',
    category: FeatureCategory.PREMIUM,
    status: FeatureStatus.ENABLED,
    userSegments: [UserSegment.ENTERPRISE_USERS],
    subscriptionTiers: ['enterprise'],
    rolloutPercentage: 100,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: ['whatsapp_autobid'],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['whatsapp', 'webhooks', 'enterprise', 'advanced']
  },

  // ===== SUPPLIER REVENUE FEATURES =====
  SUPPLIER_REVENUE: {
    id: 'supplier_revenue',
    name: 'Supplier Revenue Features',
    description: 'Complete supplier management and revenue optimization',
    category: FeatureCategory.ECOSYSTEM,
    status: FeatureStatus.ENABLED,
    userSegments: [UserSegment.ALL],
    subscriptionTiers: ['basic', 'professional', 'enterprise'],
    rolloutPercentage: 100,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: ['user_dashboard'],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['supplier', 'revenue', 'ecosystem', 'core']
  },

  SALES_REP_CENTRE: {
    id: 'sales_rep_centre',
    name: 'Sales Rep Centre',
    description: 'Sales representative onboarding and behavioral engagement',
    category: FeatureCategory.ECOSYSTEM,
    status: FeatureStatus.ENABLED,
    userSegments: [UserSegment.PAID_USERS],
    subscriptionTiers: ['professional', 'enterprise'],
    rolloutPercentage: 100,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: ['supplier_revenue', 'neuromarketing_engine'],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['sales', 'rep', 'behavioral', 'ecosystem']
  },

  PRODUCT_SPECIFICATIONS: {
    id: 'product_specifications',
    name: 'Product Specifications Module',
    description: 'AI-powered product specification matching and compliance',
    category: FeatureCategory.ECOSYSTEM,
    status: FeatureStatus.ENABLED,
    userSegments: [UserSegment.PAID_USERS],
    subscriptionTiers: ['professional', 'enterprise'],
    rolloutPercentage: 100,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: ['supplier_revenue'],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['specifications', 'ai', 'matching', 'compliance']
  },

  // ===== PREMIUM FEATURES =====
  TEAM_COLLABORATION: {
    id: 'team_collaboration',
    name: 'Team Collaboration',
    description: 'Multi-user team features and collaboration tools',
    category: FeatureCategory.PREMIUM,
    status: FeatureStatus.DISABLED,
    userSegments: [UserSegment.ENTERPRISE_USERS],
    subscriptionTiers: ['enterprise'],
    rolloutPercentage: 0,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: ['user_dashboard'],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['premium', 'enterprise', 'collaboration']
  },

  REVENUE_OPTIMIZATION: {
    id: 'revenue_optimization',
    name: 'Revenue Optimization',
    description: 'AI-powered revenue optimization and pricing strategies',
    category: FeatureCategory.PREMIUM,
    status: FeatureStatus.ENABLED,
    userSegments: [UserSegment.ENTERPRISE_USERS],
    subscriptionTiers: ['enterprise'],
    rolloutPercentage: 100,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: ['supplier_revenue', 'financial_analytics'],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['revenue', 'optimization', 'ai', 'enterprise']
  },

  API_ACCESS: {
    id: 'api_access',
    name: 'API Access',
    description: 'Full API access for integrations and custom development',
    category: FeatureCategory.PREMIUM,
    status: FeatureStatus.ENABLED,
    userSegments: [UserSegment.ENTERPRISE_USERS],
    subscriptionTiers: ['enterprise'],
    rolloutPercentage: 100,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: ['user_dashboard'],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['api', 'integration', 'enterprise', 'development']
  },

  REALTIME_ANALYTICS: {
    id: 'realtime_analytics',
    name: 'Real-time Analytics',
    description: 'Live performance tracking and real-time market intelligence',
    category: FeatureCategory.PREMIUM,
    status: FeatureStatus.DISABLED,
    userSegments: [UserSegment.ENTERPRISE_USERS],
    subscriptionTiers: ['enterprise'],
    rolloutPercentage: 0,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: ['bid_analytics', 'advanced_analytics'],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['realtime', 'analytics', 'enterprise', 'premium']
  },

  // ===== DEMO FEATURES =====
  PSYCHOLOGICAL_DEMO: {
    id: 'psychological_demo',
    name: 'Psychological Profiling Demo',
    description: 'Interactive demonstration of psychological profiling capabilities',
    category: FeatureCategory.EXPERIMENTAL,
    status: FeatureStatus.BETA,
    userSegments: [UserSegment.BETA_TESTERS, UserSegment.ADMIN_USERS],
    subscriptionTiers: ['professional', 'enterprise'],
    rolloutPercentage: 100,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: [],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['demo', 'psychological', 'beta'],
    config: {
      enableRealTimeSimulation: true,
      showAdaptationIndicators: true,
      demoArchetypes: ['Achiever', 'Hunter', 'Analyst', 'Relationship Builder']
    }
  },

  ADAPTIVE_INTERFACE_DEMO: {
    id: 'adaptive_interface_demo',
    name: 'Adaptive Interface Demo',
    description: 'Demonstration of UI adaptation based on psychological state',
    category: FeatureCategory.EXPERIMENTAL,
    status: FeatureStatus.BETA,
    userSegments: [UserSegment.BETA_TESTERS, UserSegment.ADMIN_USERS],
    subscriptionTiers: ['basic', 'professional', 'enterprise'],
    rolloutPercentage: 100,
    enabledForUserIds: [],
    disabledForUserIds: [],
    dependencies: ['psychological_demo'],
    conflicts: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'system',
    tags: ['demo', 'ui', 'adaptive', 'beta']
  }
};

// Feature Flag Groups for Progressive Rollout
export const FEATURE_FLAG_GROUPS: Record<string, FeatureFlagGroup> = {
  MVP_CORE: {
    id: 'mvp_core',
    name: 'MVP Core Features',
    description: 'Essential features required for platform launch',
    features: ['tender_search', 'bid_submission', 'user_dashboard'],
    mvpLevel: MVPLevel.MVP_CORE,
    businessValue: BusinessValue.CRITICAL,
    technicalComplexity: TechnicalComplexity.LOW,
    activationOrder: 1
  },

  COMPLIANCE_SUITE: {
    id: 'compliance_suite',
    name: 'SA Compliance Suite',
    description: 'Complete South African legal compliance tools',
    features: ['sa_compliance_tools', 'bid_protest_management'],
    mvpLevel: MVPLevel.GROWTH,
    businessValue: BusinessValue.HIGH,
    technicalComplexity: TechnicalComplexity.HIGH,
    activationOrder: 3
  },

  ENGAGEMENT_FEATURES: {
    id: 'engagement_features',
    name: 'User Engagement Features',
    description: 'Gamification and social features for retention',
    features: ['achievement_system', 'leaderboards'],
    mvpLevel: MVPLevel.GROWTH,
    businessValue: BusinessValue.MEDIUM,
    technicalComplexity: TechnicalComplexity.MEDIUM,
    activationOrder: 4
  },

  ECOSYSTEM_INTEGRATION: {
    id: 'ecosystem_integration',
    name: 'Ecosystem Integration',
    description: 'Partner marketplace integrations',
    features: ['skillsync_integration', 'toolsync_integration'],
    mvpLevel: MVPLevel.SCALE,
    businessValue: BusinessValue.HIGH,
    technicalComplexity: TechnicalComplexity.VERY_HIGH,
    activationOrder: 5
  },

  BID_ANALYTICS_SUITE: {
    id: 'bid_analytics_suite',
    name: 'Bid Analytics Suite',
    description: 'Complete bid performance analytics and insights',
    features: ['bid_analytics', 'psychological_analytics', 'financial_analytics'],
    mvpLevel: MVPLevel.GROWTH,
    businessValue: BusinessValue.HIGH,
    technicalComplexity: TechnicalComplexity.MEDIUM,
    activationOrder: 2
  },

  WHATSAPP_INTEGRATION: {
    id: 'whatsapp_integration',
    name: 'WhatsApp Integration Suite',
    description: 'Complete WhatsApp auto-bidding and messaging features',
    features: ['whatsapp_autobid', 'whatsapp_webhooks'],
    mvpLevel: MVPLevel.SCALE,
    businessValue: BusinessValue.HIGH,
    technicalComplexity: TechnicalComplexity.HIGH,
    activationOrder: 4
  },

  SUPPLIER_ECOSYSTEM: {
    id: 'supplier_ecosystem',
    name: 'Supplier Ecosystem',
    description: 'Complete supplier revenue and management features',
    features: ['supplier_revenue', 'sales_rep_centre', 'product_specifications'],
    mvpLevel: MVPLevel.GROWTH,
    businessValue: BusinessValue.CRITICAL,
    technicalComplexity: TechnicalComplexity.HIGH,
    activationOrder: 3
  },

  ADVANCED_FEATURES: {
    id: 'advanced_features',
    name: 'Advanced Analytics & AI',
    description: 'Premium analytics and AI-powered features',
    features: ['advanced_analytics', 'ai_insights', 'neuromarketing_engine'],
    mvpLevel: MVPLevel.ENTERPRISE,
    businessValue: BusinessValue.HIGH,
    technicalComplexity: TechnicalComplexity.VERY_HIGH,
    activationOrder: 6
  },

  ENTERPRISE_FEATURES: {
    id: 'enterprise_features',
    name: 'Enterprise Features',
    description: 'Advanced enterprise-level features and integrations',
    features: ['team_collaboration', 'revenue_optimization', 'api_access', 'realtime_analytics'],
    mvpLevel: MVPLevel.ENTERPRISE,
    businessValue: BusinessValue.CRITICAL,
    technicalComplexity: TechnicalComplexity.VERY_HIGH,
    activationOrder: 7
  }
};

// Feature Flag Evaluation Context
export interface FeatureFlagContext {
  userId: string;
  userSegment: UserSegment;
  subscriptionTier: string;
  isAdmin: boolean;
  isBetaTester: boolean;
  companySize?: string;
  location?: string;
  experimentGroups?: string[];
}

// Feature Flag Evaluation Result
export interface FeatureFlagResult {
  enabled: boolean;
  reason: string;
  config?: Record<string, any>;
  variant?: string;
}

// API Types
export interface UpdateFeatureFlagRequest {
  status?: FeatureStatus;
  rolloutPercentage?: number;
  userSegments?: UserSegment[];
  subscriptionTiers?: string[];
  config?: Record<string, any>;
}

export interface FeatureFlagAnalytics {
  flagId: string;
  totalUsers: number;
  enabledUsers: number;
  adoptionRate: number;
  conversionImpact: number;
  retentionImpact: number;
  revenueImpact: number;
  userFeedback: {
    positive: number;
    negative: number;
    neutral: number;
  };
}
