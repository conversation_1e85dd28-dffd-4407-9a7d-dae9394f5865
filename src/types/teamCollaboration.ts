/**
 * Team Collaboration Types
 * Comprehensive type definitions for multi-user/enterprise team collaboration
 */

import { BidWorkflowState, BidInterest, TenderNotification, ActiveBidWorkspace } from './bidWorkflow';

// ===== ORGANIZATION & TEAM MANAGEMENT =====

export interface Organization {
  id: string;
  name: string;
  type: 'sole_proprietor' | 'small_company' | 'medium_enterprise' | 'large_corporation';
  industry: string[];
  cidbGrade: string;
  bbbeeLevel: number;
  registrationNumber: string;
  taxNumber: string;
  address: Address;
  contactInfo: ContactInfo;
  settings: OrganizationSettings;
  subscription: SubscriptionTier;
  createdAt: string;
  updatedAt: string;
}

export interface Address {
  street: string;
  city: string;
  province: string;
  postalCode: string;
  country: string;
}

export interface ContactInfo {
  primaryEmail: string;
  primaryPhone: string;
  website?: string;
  emergencyContact: EmergencyContact;
}

export interface EmergencyContact {
  name: string;
  phone: string;
  email: string;
  relationship: string;
}

export interface OrganizationSettings {
  defaultWorkflowTemplate: string;
  autoAssignBeeWorkers: boolean;
  requireApprovalForBids: boolean;
  notificationPreferences: NotificationPreferences;
  collaborationSettings: CollaborationSettings;
}

export interface NotificationPreferences {
  emailEnabled: boolean;
  smsEnabled: boolean;
  whatsappEnabled: boolean;
  pushEnabled: boolean;
  digestFrequency: 'immediate' | 'hourly' | 'daily' | 'weekly';
}

export interface CollaborationSettings {
  allowGuestAccess: boolean;
  requireApprovalForDocuments: boolean;
  enableRealTimeChat: boolean;
  enableVideoMeetings: boolean;
  documentVersionControl: boolean;
}

export type SubscriptionTier = 'free' | 'professional' | 'enterprise' | 'custom';

// ===== USER ROLES & PERMISSIONS =====

export interface TeamMember {
  id: string;
  userId: string;
  organizationId: string;
  role: UserRole;
  permissions: Permission[];
  department?: string;
  title: string;
  specializations: string[];
  availability: Availability;
  workload: WorkloadStatus;
  joinedAt: string;
  lastActive: string;
  status: 'active' | 'inactive' | 'suspended';
}

export type UserRole = 
  | 'owner'           // Full access, billing, team management
  | 'admin'           // Team management, most features
  | 'project_manager' // Bid management, team coordination
  | 'estimator'       // Cost analysis, technical review
  | 'legal_counsel'   // Compliance, contract review
  | 'technical_lead'  // Technical specifications, drawings
  | 'business_dev'    // Opportunity identification, client relations
  | 'finance'         // Budget approval, financial analysis
  | 'viewer'          // Read-only access
  | 'guest';          // Limited temporary access

export interface Permission {
  resource: string;
  actions: ('create' | 'read' | 'update' | 'delete' | 'approve')[];
  conditions?: PermissionCondition[];
}

export interface PermissionCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'in' | 'not_in' | 'greater_than' | 'less_than';
  value: any;
}

export interface Availability {
  timezone: string;
  workingHours: WorkingHours;
  unavailableDates: UnavailableDate[];
  maxConcurrentBids: number;
}

export interface WorkingHours {
  monday: DaySchedule;
  tuesday: DaySchedule;
  wednesday: DaySchedule;
  thursday: DaySchedule;
  friday: DaySchedule;
  saturday: DaySchedule;
  sunday: DaySchedule;
}

export interface DaySchedule {
  available: boolean;
  startTime: string; // HH:MM format
  endTime: string;   // HH:MM format
  breaks: TimeSlot[];
}

export interface TimeSlot {
  startTime: string;
  endTime: string;
  description?: string;
}

export interface UnavailableDate {
  startDate: string;
  endDate: string;
  reason: string;
  type: 'vacation' | 'sick_leave' | 'training' | 'other';
}

export type WorkloadStatus = 'available' | 'busy' | 'overloaded' | 'unavailable';

// ===== TEAM BID COLLABORATION =====

export interface TeamBidInterest extends BidInterest {
  organizationId: string;
  teamMembers: TeamMemberAssignment[];
  approvalWorkflow: ApprovalWorkflow;
  collaborationStatus: CollaborationStatus;
  teamDecision: TeamDecision;
  budgetAllocation: BudgetAllocation;
}

export interface TeamMemberAssignment {
  memberId: string;
  role: UserRole;
  responsibilities: string[];
  assignedAt: string;
  assignedBy: string;
  status: 'assigned' | 'accepted' | 'declined' | 'completed';
  workloadImpact: number; // 0-100 percentage
}

export interface ApprovalWorkflow {
  id: string;
  steps: ApprovalStep[];
  currentStep: number;
  status: 'pending' | 'in_progress' | 'approved' | 'rejected' | 'cancelled';
  createdAt: string;
  completedAt?: string;
}

export interface ApprovalStep {
  id: string;
  name: string;
  description: string;
  approverRole: UserRole;
  approverIds: string[];
  requiredApprovals: number;
  currentApprovals: Approval[];
  deadline?: string;
  status: 'pending' | 'in_progress' | 'approved' | 'rejected' | 'skipped';
  dependencies: string[]; // Other step IDs that must complete first
}

export interface Approval {
  approverId: string;
  decision: 'approved' | 'rejected' | 'needs_changes';
  comments: string;
  timestamp: string;
  conditions?: string[];
}

export interface CollaborationStatus {
  activeMembers: number;
  totalMembers: number;
  lastActivity: string;
  unreadMessages: number;
  pendingTasks: number;
  completedTasks: number;
  overallProgress: number;
}

export interface TeamDecision {
  decision: 'proceed' | 'decline' | 'needs_discussion' | 'pending';
  votingResults?: VotingResults;
  finalDecisionBy: string;
  finalDecisionAt: string;
  reasoning: string;
  conditions?: string[];
}

export interface VotingResults {
  totalVotes: number;
  proceedVotes: number;
  declineVotes: number;
  abstainVotes: number;
  votingDeadline: string;
  votingStatus: 'open' | 'closed' | 'cancelled';
}

export interface BudgetAllocation {
  totalBudget: number;
  allocatedBudget: number;
  remainingBudget: number;
  departmentAllocations: DepartmentAllocation[];
  approvedBy: string;
  approvedAt: string;
  budgetStatus: 'draft' | 'pending_approval' | 'approved' | 'rejected';
}

export interface DepartmentAllocation {
  department: string;
  allocatedAmount: number;
  spentAmount: number;
  remainingAmount: number;
  responsibleMember: string;
}

// ===== TEAM WORKSPACE =====

export interface TeamActiveWorkspace extends ActiveBidWorkspace {
  teamBidInterest: TeamBidInterest;
  teamAnalysis: TeamAnalysisResults;
  teamTasks: TeamTaskBoard;
  teamDiscussion: TeamDiscussion;
  teamMeetings: TeamMeeting[];
  teamDocuments: TeamDocumentWorkspace;
  teamProgress: TeamProgressTracking;
}

export interface TeamAnalysisResults {
  individualAnalyses: IndividualAnalysis[];
  consensusAnalysis: ConsensusAnalysis;
  conflictingViews: ConflictingView[];
  recommendationSummary: RecommendationSummary;
}

export interface IndividualAnalysis {
  memberId: string;
  memberRole: UserRole;
  feasibilityScore: number;
  riskAssessment: string;
  recommendation: 'proceed' | 'decline' | 'needs_more_info';
  reasoning: string;
  confidence: number;
  submittedAt: string;
}

export interface ConsensusAnalysis {
  averageFeasibilityScore: number;
  consensusLevel: number; // 0-100
  majorityRecommendation: 'proceed' | 'decline' | 'needs_more_info';
  keyAgreements: string[];
  keyDisagreements: string[];
  finalRecommendation: string;
}

export interface ConflictingView {
  topic: string;
  positions: Position[];
  resolutionStatus: 'unresolved' | 'discussing' | 'resolved';
  resolutionMethod?: 'consensus' | 'vote' | 'authority_decision';
}

export interface Position {
  memberId: string;
  stance: string;
  reasoning: string;
  supportingEvidence: string[];
}

export interface RecommendationSummary {
  overallRecommendation: 'proceed' | 'decline' | 'needs_more_info';
  confidence: number;
  keyFactors: string[];
  riskMitigation: string[];
  nextSteps: string[];
  timelineEstimate: string;
}

// ===== TEAM TASK MANAGEMENT =====

export interface TeamTaskBoard {
  id: string;
  name: string;
  columns: TaskColumn[];
  tasks: TeamTask[];
  members: TaskBoardMember[];
  settings: TaskBoardSettings;
  createdAt: string;
  updatedAt: string;
}

export interface TaskColumn {
  id: string;
  name: string;
  position: number;
  color: string;
  taskLimit?: number;
  isCompleted: boolean;
}

export interface TeamTask {
  id: string;
  title: string;
  description: string;
  columnId: string;
  assigneeIds: string[];
  creatorId: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'not_started' | 'in_progress' | 'review' | 'completed' | 'blocked';
  tags: string[];
  dueDate?: string;
  estimatedHours?: number;
  actualHours?: number;
  dependencies: string[]; // Other task IDs
  attachments: TaskAttachment[];
  comments: TaskComment[];
  checklist: ChecklistItem[];
  createdAt: string;
  updatedAt: string;
}

export interface TaskBoardMember {
  memberId: string;
  role: 'owner' | 'editor' | 'viewer';
  joinedAt: string;
}

export interface TaskBoardSettings {
  allowGuestAccess: boolean;
  requireApprovalForTaskCreation: boolean;
  enableTimeTracking: boolean;
  enableNotifications: boolean;
  autoArchiveCompleted: boolean;
}

export interface TaskAttachment {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
  uploadedBy: string;
  uploadedAt: string;
}

export interface TaskComment {
  id: string;
  authorId: string;
  content: string;
  mentions: string[];
  attachments: TaskAttachment[];
  createdAt: string;
  updatedAt?: string;
  isEdited: boolean;
}

export interface ChecklistItem {
  id: string;
  text: string;
  completed: boolean;
  assigneeId?: string;
  completedAt?: string;
  completedBy?: string;
}

// ===== TEAM COMMUNICATION =====

export interface TeamDiscussion {
  id: string;
  tenderId: string;
  organizationId: string;
  channels: DiscussionChannel[];
  participants: DiscussionParticipant[];
  settings: DiscussionSettings;
  createdAt: string;
}

export interface DiscussionChannel {
  id: string;
  name: string;
  description: string;
  type: 'general' | 'technical' | 'legal' | 'financial' | 'private';
  messages: DiscussionMessage[];
  participants: string[];
  settings: ChannelSettings;
  createdAt: string;
}

export interface DiscussionMessage {
  id: string;
  authorId: string;
  content: string;
  type: 'text' | 'file' | 'image' | 'voice' | 'system';
  mentions: string[];
  reactions: MessageReaction[];
  attachments: MessageAttachment[];
  replyToId?: string;
  editHistory: MessageEdit[];
  createdAt: string;
  updatedAt?: string;
}

export interface DiscussionParticipant {
  memberId: string;
  joinedAt: string;
  lastReadAt: string;
  role: 'moderator' | 'participant' | 'observer';
  permissions: DiscussionPermission[];
}

export interface DiscussionSettings {
  allowFileSharing: boolean;
  allowVoiceMessages: boolean;
  requireModeration: boolean;
  retentionPeriod: number; // days
  enableNotifications: boolean;
}

export interface ChannelSettings {
  isPrivate: boolean;
  allowInvites: boolean;
  requireApprovalToJoin: boolean;
  muteNotifications: boolean;
}

export interface MessageReaction {
  emoji: string;
  userIds: string[];
  count: number;
}

export interface MessageAttachment {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
  thumbnail?: string;
}

export interface MessageEdit {
  editedAt: string;
  editedBy: string;
  previousContent: string;
  reason?: string;
}

export type DiscussionPermission = 
  | 'send_messages'
  | 'edit_messages'
  | 'delete_messages'
  | 'manage_channels'
  | 'invite_members'
  | 'moderate_content';

// ===== TEAM MEETINGS =====

export interface TeamMeeting {
  id: string;
  title: string;
  description: string;
  type: 'kickoff' | 'review' | 'decision' | 'status_update' | 'presentation' | 'other';
  organizerId: string;
  participants: MeetingParticipant[];
  scheduledStart: string;
  scheduledEnd: string;
  actualStart?: string;
  actualEnd?: string;
  location?: MeetingLocation;
  agenda: AgendaItem[];
  minutes?: MeetingMinutes;
  recordings?: MeetingRecording[];
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled' | 'postponed';
  createdAt: string;
  updatedAt: string;
}

export interface MeetingParticipant {
  memberId: string;
  role: 'organizer' | 'presenter' | 'participant' | 'optional';
  status: 'invited' | 'accepted' | 'declined' | 'tentative' | 'attended' | 'no_show';
  joinedAt?: string;
  leftAt?: string;
}

export interface MeetingLocation {
  type: 'physical' | 'virtual' | 'hybrid';
  address?: string;
  room?: string;
  virtualLink?: string;
  dialInNumber?: string;
  accessCode?: string;
}

export interface AgendaItem {
  id: string;
  title: string;
  description: string;
  presenterId?: string;
  estimatedDuration: number; // minutes
  actualDuration?: number;
  order: number;
  status: 'pending' | 'in_progress' | 'completed' | 'skipped';
}

export interface MeetingMinutes {
  id: string;
  content: string;
  keyDecisions: Decision[];
  actionItems: ActionItem[];
  attendees: string[];
  nextMeetingDate?: string;
  recordedBy: string;
  approvedBy?: string;
  createdAt: string;
  approvedAt?: string;
}

export interface Decision {
  id: string;
  description: string;
  decisionMaker: string;
  impact: 'low' | 'medium' | 'high';
  implementationDate?: string;
  responsibleParty?: string;
}

export interface ActionItem {
  id: string;
  description: string;
  assigneeId: string;
  dueDate: string;
  priority: 'low' | 'medium' | 'high';
  status: 'pending' | 'in_progress' | 'completed' | 'overdue';
  completedAt?: string;
}

export interface MeetingRecording {
  id: string;
  name: string;
  duration: number; // seconds
  size: number; // bytes
  url: string;
  thumbnail?: string;
  transcription?: string;
  createdAt: string;
}

// ===== TEAM DOCUMENT COLLABORATION =====

export interface TeamDocumentWorkspace {
  id: string;
  documents: CollaborativeDocument[];
  folders: DocumentFolder[];
  permissions: DocumentPermission[];
  versionControl: VersionControlSettings;
  collaborationSettings: DocumentCollaborationSettings;
}

export interface CollaborativeDocument {
  id: string;
  name: string;
  type: 'tender_response' | 'technical_spec' | 'financial_proposal' | 'compliance_doc' | 'other';
  folderId?: string;
  content: string;
  collaborators: DocumentCollaborator[];
  versions: DocumentVersion[];
  comments: DocumentComment[];
  status: 'draft' | 'review' | 'approved' | 'final' | 'archived';
  lockStatus: DocumentLock;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface DocumentFolder {
  id: string;
  name: string;
  parentId?: string;
  permissions: FolderPermission[];
  createdAt: string;
}

export interface DocumentPermission {
  documentId: string;
  memberId: string;
  permission: 'view' | 'comment' | 'edit' | 'admin';
  grantedBy: string;
  grantedAt: string;
}

export interface FolderPermission {
  folderId: string;
  memberId: string;
  permission: 'view' | 'upload' | 'edit' | 'admin';
  grantedBy: string;
  grantedAt: string;
}

export interface VersionControlSettings {
  enableAutoSave: boolean;
  saveInterval: number; // minutes
  maxVersions: number;
  enableBranching: boolean;
  requireApprovalForMerge: boolean;
}

export interface DocumentCollaborationSettings {
  enableRealTimeEditing: boolean;
  enableComments: boolean;
  enableSuggestions: boolean;
  requireApprovalForChanges: boolean;
  notifyOnChanges: boolean;
}

export interface DocumentCollaborator {
  memberId: string;
  permission: 'view' | 'comment' | 'edit';
  lastAccessed: string;
  isOnline: boolean;
  cursorPosition?: number;
}

export interface DocumentVersion {
  id: string;
  versionNumber: string;
  content: string;
  changes: DocumentChange[];
  createdBy: string;
  createdAt: string;
  comment?: string;
  isMajor: boolean;
}

export interface DocumentChange {
  type: 'insert' | 'delete' | 'modify';
  position: number;
  length: number;
  oldContent?: string;
  newContent?: string;
  authorId: string;
  timestamp: string;
}

export interface DocumentComment {
  id: string;
  authorId: string;
  content: string;
  position: number;
  resolved: boolean;
  replies: CommentReply[];
  createdAt: string;
  resolvedAt?: string;
  resolvedBy?: string;
}

export interface CommentReply {
  id: string;
  authorId: string;
  content: string;
  createdAt: string;
}

export interface DocumentLock {
  isLocked: boolean;
  lockedBy?: string;
  lockedAt?: string;
  lockReason?: string;
  autoUnlockAt?: string;
}

// ===== TEAM PROGRESS TRACKING =====

export interface TeamProgressTracking {
  overallProgress: number;
  departmentProgress: DepartmentProgress[];
  milestones: TeamMilestone[];
  deadlines: TeamDeadline[];
  criticalPath: CriticalPathItem[];
  performanceMetrics: TeamPerformanceMetrics;
  riskIndicators: TeamRiskIndicator[];
}

export interface DepartmentProgress {
  department: string;
  progress: number;
  tasksCompleted: number;
  tasksTotal: number;
  membersActive: number;
  membersTotal: number;
  lastUpdate: string;
}

export interface TeamMilestone {
  id: string;
  name: string;
  description: string;
  targetDate: string;
  completedDate?: string;
  progress: number;
  dependencies: string[];
  responsibleMembers: string[];
  status: 'not_started' | 'in_progress' | 'completed' | 'delayed' | 'at_risk';
}

export interface TeamDeadline {
  id: string;
  name: string;
  date: string;
  type: 'submission' | 'review' | 'approval' | 'meeting' | 'other';
  priority: 'low' | 'medium' | 'high' | 'critical';
  daysRemaining: number;
  status: 'upcoming' | 'due_soon' | 'overdue' | 'completed';
  responsibleMembers: string[];
  dependencies: string[];
}

export interface CriticalPathItem {
  id: string;
  name: string;
  startDate: string;
  endDate: string;
  duration: number; // days
  dependencies: string[];
  slack: number; // days of buffer
  isCritical: boolean;
}

export interface TeamPerformanceMetrics {
  velocityTrend: VelocityPoint[];
  qualityMetrics: QualityMetric[];
  collaborationScore: number;
  communicationFrequency: number;
  decisionMakingSpeed: number;
  memberSatisfaction: number;
}

export interface VelocityPoint {
  date: string;
  tasksCompleted: number;
  storyPoints: number;
  teamSize: number;
}

export interface QualityMetric {
  metric: 'defect_rate' | 'rework_rate' | 'approval_rate' | 'client_satisfaction';
  value: number;
  trend: 'improving' | 'stable' | 'declining';
  target: number;
}

export interface TeamRiskIndicator {
  type: 'timeline' | 'quality' | 'resource' | 'communication' | 'external';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  probability: number;
  impact: number;
  mitigation: string;
  owner: string;
  status: 'identified' | 'mitigating' | 'resolved' | 'accepted';
}
