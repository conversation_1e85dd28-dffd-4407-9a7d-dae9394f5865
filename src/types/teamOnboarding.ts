/**
 * Team Member Onboarding & Registration Types
 * Complete system for inviting, registering, and onboarding team members
 */

import { UserRole, TeamMember } from './teamCollaboration';

// ===== TEAM MEMBER INVITATION =====

export interface TeamMemberInvitation {
  id: string;
  organizationId: string;
  organizationName: string;
  invitedEmail: string;
  invitedRole: UserRole;
  invitedBy: string;
  inviterName: string;
  inviterRole: UserRole;
  
  // Invitation details
  personalMessage?: string;
  suggestedTitle: string;
  suggestedDepartment?: string;
  expectedResponsibilities: string[];
  
  // Invitation status
  status: InvitationStatus;
  sentAt: string;
  expiresAt: string;
  respondedAt?: string;
  acceptedAt?: string;
  declinedAt?: string;
  
  // Invitation metadata
  invitationToken: string;
  remindersSent: number;
  lastReminderAt?: string;
  
  // Onboarding preparation
  onboardingTemplate: string;
  requiredDocuments: string[];
  accessLevel: 'immediate' | 'pending_approval' | 'restricted';
  
  createdAt: string;
  updatedAt: string;
}

export type InvitationStatus = 
  | 'pending'
  | 'sent'
  | 'viewed'
  | 'accepted'
  | 'declined'
  | 'expired'
  | 'cancelled'
  | 'completed';

export interface InvitationResponse {
  invitationId: string;
  response: 'accept' | 'decline';
  message?: string;
  availabilityInfo?: {
    startDate: string;
    timezone: string;
    workingHours: any;
    unavailableDates: string[];
  };
  personalInfo?: {
    fullName: string;
    phone?: string;
    linkedIn?: string;
    bio?: string;
  };
  professionalInfo?: {
    experience: string;
    specializations: string[];
    certifications: string[];
    previousCompanies: string[];
  };
  respondedAt: string;
}

// ===== TEAM MEMBER ONBOARDING =====

export interface TeamMemberOnboarding {
  id: string;
  invitationId: string;
  userId: string;
  organizationId: string;
  memberId?: string; // Set after successful onboarding
  
  // Onboarding flow
  currentStep: number;
  totalSteps: number;
  steps: OnboardingStep[];
  
  // Member information
  role: UserRole;
  personalInfo: PersonalInfo;
  professionalInfo: ProfessionalInfo;
  organizationInfo: OrganizationInfo;
  
  // Onboarding progress
  completedSteps: string[];
  skippedSteps: string[];
  documentsUploaded: UploadedDocument[];
  
  // Approval workflow
  requiresApproval: boolean;
  approvalStatus: ApprovalStatus;
  approvedBy?: string;
  approvedAt?: string;
  approvalComments?: string;
  
  // Onboarding completion
  completedAt?: string;
  activatedAt?: string;
  onboardingScore: number; // 0-100
  
  createdAt: string;
  updatedAt: string;
}

export interface OnboardingStep {
  id: string;
  name: string;
  title: string;
  description: string;
  type: OnboardingStepType;
  required: boolean;
  order: number;
  
  // Step configuration
  config: OnboardingStepConfig;
  
  // Completion tracking
  completed: boolean;
  completedAt?: string;
  data?: Record<string, any>;
  
  // Validation
  validationRules: ValidationRule[];
  errors?: string[];
}

export type OnboardingStepType = 
  | 'welcome'
  | 'personal_info'
  | 'professional_info'
  | 'role_training'
  | 'document_upload'
  | 'team_introduction'
  | 'system_access'
  | 'compliance_training'
  | 'security_setup'
  | 'completion';

export interface OnboardingStepConfig {
  component: string;
  props?: Record<string, any>;
  skipConditions?: SkipCondition[];
  dependencies?: string[];
  timeEstimate?: number; // minutes
  helpResources?: HelpResource[];
}

export interface SkipCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'in' | 'not_in';
  value: any;
}

export interface HelpResource {
  type: 'video' | 'document' | 'link' | 'contact';
  title: string;
  url?: string;
  description?: string;
}

export interface ValidationRule {
  field: string;
  rule: 'required' | 'email' | 'phone' | 'min_length' | 'max_length' | 'pattern' | 'file_type';
  value?: any;
  message: string;
}

// ===== MEMBER INFORMATION =====

export interface PersonalInfo {
  fullName: string;
  email: string;
  phone?: string;
  avatar?: string;
  bio?: string;
  
  // Contact preferences
  preferredContactMethod: 'email' | 'phone' | 'teams' | 'whatsapp';
  timezone: string;
  language: string;
  
  // Personal details
  dateOfBirth?: string;
  emergencyContact?: EmergencyContact;
  
  // Social profiles
  linkedIn?: string;
  twitter?: string;
  github?: string;
}

export interface EmergencyContact {
  name: string;
  relationship: string;
  phone: string;
  email?: string;
}

export interface ProfessionalInfo {
  title: string;
  department?: string;
  reportsTo?: string;
  startDate: string;
  
  // Experience and skills
  yearsOfExperience: number;
  specializations: string[];
  certifications: Certification[];
  skills: Skill[];
  
  // Work preferences
  workingHours: any; // From teamCollaboration types
  maxConcurrentBids: number;
  preferredProjectTypes: string[];
  
  // Previous experience
  previousCompanies: PreviousCompany[];
  education: Education[];
  
  // Performance expectations
  performanceGoals: string[];
  kpis: KPI[];
}

export interface Certification {
  name: string;
  issuingOrganization: string;
  issueDate: string;
  expiryDate?: string;
  credentialId?: string;
  verificationUrl?: string;
}

export interface Skill {
  name: string;
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  yearsOfExperience: number;
  lastUsed: string;
  endorsed: boolean;
}

export interface PreviousCompany {
  name: string;
  position: string;
  startDate: string;
  endDate: string;
  description?: string;
  achievements?: string[];
}

export interface Education {
  institution: string;
  degree: string;
  field: string;
  startDate: string;
  endDate: string;
  gpa?: number;
  achievements?: string[];
}

export interface KPI {
  name: string;
  description: string;
  target: number;
  unit: string;
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'annually';
}

export interface OrganizationInfo {
  organizationId: string;
  organizationName: string;
  role: UserRole;
  department?: string;
  teamLead?: string;
  buddyMember?: string; // Assigned buddy for onboarding
  
  // Access and permissions
  accessLevel: 'full' | 'limited' | 'restricted';
  permissions: string[];
  systemAccess: SystemAccess[];
  
  // Integration accounts
  integrationAccounts: IntegrationAccount[];
}

export interface SystemAccess {
  system: string;
  accessLevel: 'admin' | 'user' | 'readonly';
  accountCreated: boolean;
  credentials?: {
    username?: string;
    temporaryPassword?: string;
    resetRequired: boolean;
  };
}

export interface IntegrationAccount {
  platform: string;
  accountId?: string;
  accessToken?: string;
  refreshToken?: string;
  expiresAt?: string;
  scopes: string[];
  connected: boolean;
}

// ===== DOCUMENT MANAGEMENT =====

export interface UploadedDocument {
  id: string;
  name: string;
  type: DocumentType;
  category: DocumentCategory;
  fileUrl: string;
  fileSize: number;
  mimeType: string;
  
  // Upload tracking
  uploadedAt: string;
  uploadedBy: string;
  
  // Verification
  verified: boolean;
  verifiedBy?: string;
  verifiedAt?: string;
  verificationNotes?: string;
  
  // Compliance
  required: boolean;
  expiryDate?: string;
  reminderDate?: string;
}

export type DocumentType = 
  | 'identity'
  | 'qualification'
  | 'certification'
  | 'contract'
  | 'tax'
  | 'insurance'
  | 'reference'
  | 'other';

export type DocumentCategory = 
  | 'personal_identification'
  | 'professional_qualification'
  | 'work_authorization'
  | 'tax_documents'
  | 'insurance_documents'
  | 'employment_contract'
  | 'reference_letters'
  | 'compliance_certificates';

// ===== APPROVAL WORKFLOW =====

export type ApprovalStatus = 
  | 'not_required'
  | 'pending'
  | 'under_review'
  | 'approved'
  | 'rejected'
  | 'conditional';

export interface ApprovalWorkflow {
  id: string;
  onboardingId: string;
  steps: ApprovalStep[];
  currentStep: number;
  status: ApprovalStatus;
  
  // Workflow metadata
  initiatedBy: string;
  initiatedAt: string;
  completedAt?: string;
  
  // Decision tracking
  finalDecision?: 'approved' | 'rejected';
  finalDecisionBy?: string;
  finalDecisionAt?: string;
  decisionReason?: string;
}

export interface ApprovalStep {
  id: string;
  name: string;
  description: string;
  approverRole: UserRole;
  approverIds: string[];
  
  // Step requirements
  requiredApprovals: number;
  currentApprovals: ApprovalDecision[];
  
  // Step status
  status: 'pending' | 'in_progress' | 'approved' | 'rejected' | 'skipped';
  startedAt?: string;
  completedAt?: string;
  
  // Dependencies
  dependencies: string[];
  autoApprove?: boolean;
  timeoutHours?: number;
}

export interface ApprovalDecision {
  approverId: string;
  approverName: string;
  decision: 'approve' | 'reject' | 'request_changes';
  comments?: string;
  decidedAt: string;
  
  // Conditional approval
  conditions?: string[];
  followUpRequired?: boolean;
}

// ===== ONBOARDING ANALYTICS =====

export interface OnboardingAnalytics {
  organizationId: string;
  period: {
    start: string;
    end: string;
  };
  
  // Invitation metrics
  invitationsSent: number;
  invitationsAccepted: number;
  invitationsDeclined: number;
  invitationsExpired: number;
  acceptanceRate: number;
  
  // Onboarding metrics
  onboardingsStarted: number;
  onboardingsCompleted: number;
  onboardingsAbandoned: number;
  completionRate: number;
  averageCompletionTime: number; // hours
  
  // Step analysis
  stepCompletionRates: Record<string, number>;
  commonDropOffPoints: string[];
  averageStepTimes: Record<string, number>;
  
  // Role-based metrics
  roleMetrics: Record<UserRole, RoleOnboardingMetrics>;
  
  // Quality metrics
  averageOnboardingScore: number;
  documentVerificationRate: number;
  approvalRate: number;
  
  // Satisfaction
  satisfactionScore?: number;
  feedbackComments: string[];
}

export interface RoleOnboardingMetrics {
  role: UserRole;
  invitationsSent: number;
  completionRate: number;
  averageCompletionTime: number;
  commonChallenges: string[];
  successFactors: string[];
}

// ===== ONBOARDING EVENTS =====

export interface OnboardingEvent {
  id: string;
  type: OnboardingEventType;
  onboardingId?: string;
  invitationId?: string;
  userId?: string;
  organizationId: string;
  
  // Event data
  data: Record<string, any>;
  timestamp: string;
  
  // Processing
  processed: boolean;
  processedAt?: string;
  
  // Notifications
  notificationsSent: string[];
  followUpRequired: boolean;
}

export type OnboardingEventType = 
  | 'invitation_sent'
  | 'invitation_viewed'
  | 'invitation_accepted'
  | 'invitation_declined'
  | 'onboarding_started'
  | 'step_completed'
  | 'document_uploaded'
  | 'approval_requested'
  | 'approval_granted'
  | 'approval_rejected'
  | 'onboarding_completed'
  | 'member_activated'
  | 'onboarding_abandoned';

// ===== BUDDY SYSTEM =====

export interface BuddyAssignment {
  id: string;
  onboardingId: string;
  buddyMemberId: string;
  newMemberId: string;
  
  // Assignment details
  assignedBy: string;
  assignedAt: string;
  expectedDuration: number; // days
  
  // Buddy responsibilities
  responsibilities: string[];
  checkInSchedule: CheckInSchedule[];
  
  // Progress tracking
  checkInsCompleted: number;
  lastCheckIn?: string;
  buddyRating?: number;
  feedback?: string;
  
  // Status
  status: 'active' | 'completed' | 'reassigned' | 'cancelled';
  completedAt?: string;
}

export interface CheckInSchedule {
  day: number; // Day of onboarding (1, 3, 7, 14, 30)
  type: 'call' | 'meeting' | 'message' | 'survey';
  duration?: number; // minutes
  topics: string[];
  completed: boolean;
  completedAt?: string;
  notes?: string;
}
