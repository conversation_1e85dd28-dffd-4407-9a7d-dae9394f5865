{"version": 2, "buildCommand": "npm run build", "devCommand": "npm run dev", "framework": "nextjs", "installCommand": "npm install", "outputDirectory": ".next", "env": {"SUPABASE_URL": "@supabase_url", "SUPABASE_ANON_KEY": "@supabase_anon_key", "BACKEND_URL": "@backend_url"}, "functions": {"pages/api/**/*": {"maxDuration": 30}}, "rewrites": [{"source": "/api/backend/(.*)", "destination": "https://your-backend-domain.com/api/$1"}]}