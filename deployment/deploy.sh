#!/bin/bash

# BidBeez Psychological Systems API Deployment Script

set -e

echo "🚀 Starting BidBeez API Deployment..."

# Check if required environment variables are set
if [ -z "$SUPABASE_URL" ] || [ -z "$SUPABASE_ANON_KEY" ]; then
    echo "❌ Error: SUPABASE_URL and SUPABASE_ANON_KEY must be set"
    exit 1
fi

# Build and test locally first
echo "🔨 Building application..."
docker build -f deployment/Dockerfile -t bidbeez-api .

echo "🧪 Running health check..."
docker run --rm -d --name bidbeez-test -p 8001:8000 \
    -e SUPABASE_URL="$SUPABASE_URL" \
    -e SUPABASE_ANON_KEY="$SUPABASE_ANON_KEY" \
    bidbeez-api

# Wait for container to start
sleep 10

# Health check
if curl -f http://localhost:8001/health; then
    echo "✅ Health check passed"
else
    echo "❌ Health check failed"
    docker stop bidbeez-test
    exit 1
fi

# Stop test container
docker stop bidbeez-test

echo "🚀 Deploying to production..."

# Deploy based on platform
if [ "$DEPLOY_PLATFORM" = "railway" ]; then
    echo "🚂 Deploying to Railway..."
    railway up
elif [ "$DEPLOY_PLATFORM" = "vercel" ]; then
    echo "▲ Deploying to Vercel..."
    vercel --prod
elif [ "$DEPLOY_PLATFORM" = "docker" ]; then
    echo "🐳 Deploying with Docker..."
    docker tag bidbeez-api bidbeez-api:latest
    docker push bidbeez-api:latest
else
    echo "ℹ️ No deployment platform specified. Built image: bidbeez-api"
    echo "ℹ️ Available platforms: railway, vercel, docker"
fi

echo "✅ Deployment completed successfully!"
echo "📊 API Documentation: https://your-domain.com/docs"
echo "🏥 Health Check: https://your-domain.com/health"
