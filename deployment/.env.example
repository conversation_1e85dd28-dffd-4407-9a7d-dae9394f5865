# BYDER BY BIDBEEZ Frontend Environment Variables

# Supabase Configuration
SUPABASE_URL=https://uvksgkpxeyyssvdsxbts.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_KEY=your_supabase_service_key_here

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4
API_RELOAD=false

# Security
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30

# Redis (for caching and sessions)
REDIS_URL=redis://localhost:6379

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Monitoring
SENTRY_DSN=your_sentry_dsn_here
ENABLE_METRICS=true

# Feature Flags
ENABLE_BEHAVIORAL_ENGINE=true
ENABLE_ONBOARDING_ENGINE=true
ENABLE_CONTRACTOR_ACCESS=true

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Email (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password

# Development
DEBUG=false
TESTING=false
